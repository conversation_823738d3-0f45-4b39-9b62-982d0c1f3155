#!/usr/bin/env python3
"""
调试完整策略启动过程
"""

import sys
import os
import requests
import json

# 添加项目路径
sys.path.append(os.path.dirname(__file__))

def test_api_startup():
    """测试API启动过程"""
    try:
        print("🔍 测试API启动过程...")
        
        # 启动一个策略
        test_data = {
            "strategy_name": "bollinger_bands",
            "initial_capital": 100000,
            "commission": 0.001,
            "stock_codes": ["000001.SZ"],
            "paper_trading": True,
            "strategy_params": {
                "period": 20,
                "std_dev": 2.0
            }
        }
        
        print("📤 发送启动请求...")
        print(f"   股票代码: {test_data['stock_codes']}")
        
        response = requests.post(
            'http://localhost:8000/api/live/start',
            json=test_data,
            headers={'Content-Type': 'application/json'}
        )
        
        print(f"📋 响应状态: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"📋 响应数据: {data}")
            
            if data.get('success'):
                print("✅ API调用成功")
                return True, data.get('task_id')
            else:
                print(f"❌ API调用失败: {data.get('message', 'Unknown error')}")
                return False, None
        else:
            print(f"❌ HTTP错误: {response.text}")
            return False, None
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False, None

def test_engine_direct():
    """测试直接调用引擎"""
    try:
        print("\n🔍 测试直接调用引擎...")
        
        from backend.live.simple_live_engine import SimpleLiveEngine
        
        engine = SimpleLiveEngine()
        
        config = {
            "strategy_name": "bollinger_bands",
            "initial_capital": 100000,
            "commission": 0.001,
            "stock_codes": ["000001.SZ"],
            "paper_trading": True,
            "strategy_params": {
                "period": 20,
                "std_dev": 2.0
            }
        }
        
        print("📤 直接调用引擎启动...")
        print(f"   股票代码: {config['stock_codes']}")
        
        try:
            task_id = engine.start_strategy(config)
            print(f"✅ 引擎启动成功: {task_id}")
            return True, task_id
        except Exception as e:
            print(f"❌ 引擎启动失败: {e}")
            import traceback
            print(f"详细错误: {traceback.format_exc()}")
            return False, None
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False, None

def test_cerebro_creation():
    """测试Cerebro创建过程"""
    try:
        print("\n🔍 测试Cerebro创建过程...")
        
        import backtrader as bt
        from datetime import datetime, timedelta
        from backend.stores.qmt_store import QMTStore
        
        # 创建Cerebro
        cerebro = bt.Cerebro()
        print("✅ Cerebro创建成功")
        
        # 创建QMT Store
        qmt_store = QMTStore()
        print("✅ QMTStore创建成功")
        
        # 设置broker
        cerebro.broker = qmt_store.getbroker(cash=100000.0)
        print("✅ Broker设置成功")
        
        # 添加数据源
        stock_codes = ["000001.SZ"]
        
        for stock_code in stock_codes:
            print(f"📤 添加数据源: {stock_code}")
            
            try:
                data = qmt_store.getdata(
                    dataname=stock_code,
                    historical=True,
                    live=True,
                    fromdate=datetime.now() - timedelta(days=30),
                    todate=datetime.now()
                )
                
                cerebro.adddata(data, name=stock_code)
                print(f"✅ 数据源添加成功: {stock_code}")
                
            except Exception as e:
                print(f"❌ 数据源添加失败: {e}")
                import traceback
                print(f"详细错误: {traceback.format_exc()}")
                return False
        
        print("✅ 所有数据源添加成功")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    print("🧪 完整策略启动调试")
    print("=" * 60)
    
    # 测试API启动
    api_ok, task_id = test_api_startup()
    
    if not api_ok:
        print("\n❌ API启动失败，测试直接调用引擎...")
        
        # 测试直接调用引擎
        engine_ok, task_id = test_engine_direct()
        
        if not engine_ok:
            print("\n❌ 引擎直接调用失败，测试Cerebro创建...")
            
            # 测试Cerebro创建
            cerebro_ok = test_cerebro_creation()
            
            if cerebro_ok:
                print("\n✅ Cerebro创建成功，问题可能在策略运行阶段")
            else:
                print("\n❌ Cerebro创建失败，问题在数据源创建阶段")
        else:
            print("\n✅ 引擎直接调用成功，问题可能在API层")
    else:
        print("\n✅ API启动成功")
        
        if task_id:
            print(f"   任务ID: {task_id}")
            
            # 清理测试任务
            try:
                import time
                time.sleep(2)
                requests.post(f'http://localhost:8000/api/live/stop/{task_id}')
                time.sleep(1)
                requests.delete(f'http://localhost:8000/api/live/delete/{task_id}')
                print("✅ 测试任务已清理")
            except:
                print("⚠️ 清理失败，请手动清理")
    
    print("\n" + "=" * 60)
    print("🎯 调试完成")
    print("请检查上述输出找出问题所在")
