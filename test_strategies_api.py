#!/usr/bin/env python3
"""
测试策略API响应
"""

import sys
import os
import requests
import json

# 添加项目路径
sys.path.append(os.path.dirname(__file__))

def test_strategies_api():
    """测试策略API"""
    try:
        print("🔍 测试策略API...")
        
        # 测试API响应
        response = requests.get('http://localhost:8000/api/backtest/strategies')
        
        print(f"状态码: {response.status_code}")
        print(f"响应头: {response.headers.get('content-type')}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"响应数据类型: {type(data)}")
            print(f"响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
            
            # 检查数据结构
            if isinstance(data, dict) and 'success' in data:
                if data['success'] and 'data' in data:
                    strategies = data['data']
                    print(f"\n✅ 策略数量: {len(strategies)}")
                    
                    for i, strategy in enumerate(strategies):
                        print(f"\n策略 {i+1}:")
                        print(f"  名称: {strategy.get('name')}")
                        print(f"  显示名: {strategy.get('display_name')}")
                        print(f"  参数: {strategy.get('parameters')}")
                        print(f"  参数类型: {type(strategy.get('parameters'))}")
                        
                        # 检查参数结构
                        params = strategy.get('parameters', {})
                        if isinstance(params, dict):
                            for param_name, param_config in params.items():
                                print(f"    {param_name}: {param_config} (类型: {type(param_config)})")
                else:
                    print(f"❌ API返回失败: {data}")
            else:
                print(f"❌ 响应格式异常: {data}")
        else:
            print(f"❌ API请求失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    test_strategies_api()
