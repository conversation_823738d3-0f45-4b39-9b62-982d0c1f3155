import * as React from 'react'
import { ChevronUp, ChevronDown } from 'lucide-react'
import { cn } from '../../lib/utils'

export const InputNumber = React.forwardRef(({ 
  className,
  value,
  onChange,
  min,
  max,
  step = 1,
  precision,
  formatter,
  parser,
  size = 'default',
  disabled = false,
  controls = true,
  error = false,
  ...props 
}, ref) => {
  const [internalValue, setInternalValue] = React.useState(value || 0)
  const [displayValue, setDisplayValue] = React.useState('')

  React.useEffect(() => {
    const numValue = Number(value)
    if (!isNaN(numValue)) {
      setInternalValue(numValue)
      if (formatter) {
        setDisplayValue(formatter(numValue))
      } else if (precision !== undefined) {
        setDisplayValue(numValue.toFixed(precision))
      } else {
        setDisplayValue(String(numValue))
      }
    }
  }, [value, formatter, precision])

  const sizeClasses = {
    small: 'h-8 text-sm',
    default: 'h-9 text-sm', 
    large: 'h-10 text-base'
  }

  const handleInputChange = (e) => {
    let inputValue = e.target.value
    
    if (parser) {
      inputValue = parser(inputValue)
    }
    
    setDisplayValue(e.target.value)
    
    const numValue = Number(inputValue)
    if (!isNaN(numValue)) {
      let finalValue = numValue
      
      if (min !== undefined && finalValue < min) finalValue = min
      if (max !== undefined && finalValue > max) finalValue = max
      if (precision !== undefined) finalValue = Number(finalValue.toFixed(precision))
      
      setInternalValue(finalValue)
      onChange && onChange(finalValue)
    }
  }

  const handleStep = (direction) => {
    if (disabled) return
    
    let newValue = internalValue + (direction * step)
    
    if (min !== undefined && newValue < min) newValue = min
    if (max !== undefined && newValue > max) newValue = max
    if (precision !== undefined) newValue = Number(newValue.toFixed(precision))
    
    setInternalValue(newValue)
    onChange && onChange(newValue)
  }

  const handleBlur = () => {
    // 在失焦时重新格式化显示值
    if (formatter) {
      setDisplayValue(formatter(internalValue))
    } else if (precision !== undefined) {
      setDisplayValue(internalValue.toFixed(precision))
    } else {
      setDisplayValue(String(internalValue))
    }
  }

  return (
    <div className={cn('relative flex', className)}>
      <input
        ref={ref}
        type="text"
        value={displayValue}
        onChange={handleInputChange}
        onBlur={handleBlur}
        disabled={disabled}
        className={cn(
          'flex w-full rounded-md border border-gray-200 bg-white px-3 ring-offset-white placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',
          error && 'border-red-500 focus-visible:ring-red-500',
          controls && 'pr-8',
          sizeClasses[size]
        )}
        {...props}
      />
      
      {controls && (
        <div className="absolute right-1 top-1/2 -translate-y-1/2 flex flex-col">
          <button
            type="button"
            disabled={disabled || (max !== undefined && internalValue >= max)}
            onClick={() => handleStep(1)}
            className="flex h-4 w-6 items-center justify-center rounded-sm hover:bg-gray-100 disabled:cursor-not-allowed disabled:opacity-50"
          >
            <ChevronUp className="h-3 w-3" />
          </button>
          <button
            type="button"
            disabled={disabled || (min !== undefined && internalValue <= min)}
            onClick={() => handleStep(-1)}
            className="flex h-4 w-6 items-center justify-center rounded-sm hover:bg-gray-100 disabled:cursor-not-allowed disabled:opacity-50"
          >
            <ChevronDown className="h-3 w-3" />
          </button>
        </div>
      )}
    </div>
  )
})

InputNumber.displayName = 'InputNumber'
