#!/usr/bin/env python3
"""
多信号策略 - Backtrader原生版本
直接继承ConfigurableStrategy，无需适配器
"""

import backtrader as bt
import pandas as pd
import numpy as np
from typing import Dict, Any, List
from backend.strategies.base_strategy_new import ConfigurableStrategy, register_strategy
from backend.core.logger import get_logger

logger = get_logger(__name__)

class MultiSignalStrategyBT(ConfigurableStrategy):
    """多信号策略 - Backtrader原生版本"""
    
    # 定义策略参数
    params = (
        # 买入信号开关
        ('buy_signal_ma_cross', True),
        ('buy_signal_rsi_oversold', False),
        ('buy_signal_volume_breakout', True),
        ('buy_signal_price_breakout', False),
        
        # 卖出信号开关
        ('sell_signal_ma_cross', True),
        ('sell_signal_rsi_overbought', False),
        ('sell_signal_stop_loss', True),
        
        # 技术指标参数
        ('ma_short_period', 5),
        ('ma_long_period', 20),
        ('rsi_period', 14),
        ('rsi_oversold', 30),
        ('rsi_overbought', 70),
        ('volume_multiplier', 2.0),
        ('stop_loss_pct', 0.05),
        
        # 资金管理参数
        ('max_positions', 5),
        ('position_size', 0.2),
    )
    
    def __init__(self):
        super().__init__()

        # 应用用户配置的参数
        self._apply_user_params()

        # 技术指标
        self.ma_short = {}
        self.ma_long = {}
        self.rsi = {}
        self.volume_sma = {}
        
        # 为每个数据源创建指标
        for i, data in enumerate(self.datas):
            stock_code = data._name
            
            # 移动平均线
            self.ma_short[stock_code] = bt.indicators.SMA(
                data.close, period=self.params.ma_short_period
            )
            self.ma_long[stock_code] = bt.indicators.SMA(
                data.close, period=self.params.ma_long_period
            )
            
            # RSI指标
            self.rsi[stock_code] = bt.indicators.RSI(
                data.close, period=self.params.rsi_period
            )
            
            # 成交量均线
            if hasattr(data, 'volume'):
                self.volume_sma[stock_code] = bt.indicators.SMA(
                    data.volume, period=20
                )
        
        # 记录参数
        logger.info(f"✅ 多信号策略(BT版)初始化完成")
        logger.info(f"📊 买入信号: MA金叉={self.params.buy_signal_ma_cross}, "
                   f"RSI超卖={self.params.buy_signal_rsi_oversold}, "
                   f"成交量突破={self.params.buy_signal_volume_breakout}")
        logger.info(f"📊 卖出信号: MA死叉={self.params.sell_signal_ma_cross}, "
                   f"RSI超买={self.params.sell_signal_rsi_overbought}, "
                   f"止损={self.params.sell_signal_stop_loss}")
        logger.info(f"📊 均线参数: 短期{self.params.ma_short_period}日, "
                   f"长期{self.params.ma_long_period}日")

    def _apply_user_params(self):
        """应用用户配置的参数"""
        if hasattr(self, 'strategy_params') and self.strategy_params:
            logger.info(f"🔧 应用用户参数: {len(self.strategy_params)} 个")

            # 动态更新参数
            for param_name, param_value in self.strategy_params.items():
                if hasattr(self.params, param_name):
                    # 更新参数值
                    setattr(self.params, param_name, param_value)
                    logger.info(f"  ✅ {param_name}: {getattr(self.params, param_name, '默认值')} -> {param_value}")
                else:
                    logger.warning(f"  ⚠️ 未知参数: {param_name}")
        else:
            logger.info("📊 使用默认参数")
    
    def next(self):
        """主要的策略逻辑"""
        # 记录每日收益
        self.record_daily_return()

        for i, data in enumerate(self.datas):
            stock_code = data._name

            # 检查数据是否足够
            if len(data) < max(self.params.ma_long_period, self.params.rsi_period):
                logger.debug(f"数据不足: {stock_code}, 长度={len(data)}, 需要={max(self.params.ma_long_period, self.params.rsi_period)}")
                continue

            position = self.getposition(data)

            if position.size == 0:
                # 没有持仓，检查买入信号
                if self._should_buy(stock_code, data):
                    buy_signals = self._evaluate_buy_signals(stock_code, data)
                    if buy_signals:
                        self._execute_buy(stock_code, data, buy_signals)
                    else:
                        # 调试：记录没有信号的情况
                        if len(data) % 20 == 0:  # 每20天记录一次
                            ma_short = self.ma_short[stock_code]
                            ma_long = self.ma_long[stock_code]
                            logger.debug(f"{stock_code} 无买入信号: MA短={ma_short[0]:.2f}, MA长={ma_long[0]:.2f}")
            else:
                # 有持仓，检查卖出信号
                if self._should_sell(stock_code, data):
                    sell_signals = self._evaluate_sell_signals(stock_code, data, position)
                    if sell_signals:
                        self._execute_sell(stock_code, data, sell_signals)
    
    def _should_buy(self, stock_code: str, data) -> bool:
        """检查是否可以买入"""
        # 检查最大持仓数
        current_positions = sum(1 for d in self.datas if self.getposition(d).size > 0)
        if current_positions >= self.params.max_positions:
            return False
        
        # 检查资金是否充足
        if self.broker.get_cash() < 10000:
            return False
        
        return True
    
    def _should_sell(self, stock_code: str, data) -> bool:
        """检查是否可以卖出"""
        position = self.getposition(data)
        return position.size > 0
    
    def _evaluate_buy_signals(self, stock_code: str, data) -> List[str]:
        """评估买入信号"""
        signals = []

        # 均线金叉信号
        if self.params.buy_signal_ma_cross:
            if self._check_ma_golden_cross(stock_code):
                signals.append('ma_cross')
                logger.info(f"🔥 {stock_code} 均线金叉信号触发")

        # RSI超卖信号
        if self.params.buy_signal_rsi_oversold:
            if self._check_rsi_oversold(stock_code):
                signals.append('rsi_oversold')
                logger.info(f"🔥 {stock_code} RSI超卖信号触发")

        # 成交量突破信号
        if self.params.buy_signal_volume_breakout:
            if self._check_volume_breakout(stock_code, data):
                signals.append('volume_breakout')
                logger.info(f"🔥 {stock_code} 成交量突破信号触发")

        if signals:
            logger.info(f"📈 {stock_code} 买入信号: {','.join(signals)}")

        return signals
    
    def _evaluate_sell_signals(self, stock_code: str, data, position) -> List[str]:
        """评估卖出信号"""
        signals = []
        
        # 均线死叉信号
        if self.params.sell_signal_ma_cross:
            if self._check_ma_death_cross(stock_code):
                signals.append('ma_cross')
        
        # RSI超买信号
        if self.params.sell_signal_rsi_overbought:
            if self._check_rsi_overbought(stock_code):
                signals.append('rsi_overbought')
        
        # 止损信号
        if self.params.sell_signal_stop_loss:
            if self._check_stop_loss(data, position):
                signals.append('stop_loss')
        
        return signals
    
    def _check_ma_golden_cross(self, stock_code: str) -> bool:
        """检查均线金叉"""
        ma_short = self.ma_short[stock_code]
        ma_long = self.ma_long[stock_code]
        
        # 当前短期均线 > 长期均线，且前一天短期均线 <= 长期均线
        return (ma_short[0] > ma_long[0] and ma_short[-1] <= ma_long[-1])
    
    def _check_ma_death_cross(self, stock_code: str) -> bool:
        """检查均线死叉"""
        ma_short = self.ma_short[stock_code]
        ma_long = self.ma_long[stock_code]
        
        # 当前短期均线 < 长期均线，且前一天短期均线 >= 长期均线
        return (ma_short[0] < ma_long[0] and ma_short[-1] >= ma_long[-1])
    
    def _check_rsi_oversold(self, stock_code: str) -> bool:
        """检查RSI超卖"""
        rsi = self.rsi[stock_code]
        return rsi[0] < self.params.rsi_oversold
    
    def _check_rsi_overbought(self, stock_code: str) -> bool:
        """检查RSI超买"""
        rsi = self.rsi[stock_code]
        return rsi[0] > self.params.rsi_overbought
    
    def _check_volume_breakout(self, stock_code: str, data) -> bool:
        """检查成交量突破"""
        if stock_code not in self.volume_sma or not hasattr(data, 'volume'):
            return False
        
        volume_sma = self.volume_sma[stock_code]
        current_volume = data.volume[0]
        avg_volume = volume_sma[0]
        
        return current_volume > (avg_volume * self.params.volume_multiplier)
    
    def _check_stop_loss(self, data, position) -> bool:
        """检查止损"""
        current_price = data.close[0]
        entry_price = position.price
        
        loss_pct = (entry_price - current_price) / entry_price
        return loss_pct > self.params.stop_loss_pct
    
    def _execute_buy(self, stock_code: str, data, signals: List[str]):
        """执行买入"""
        current_price = data.close[0]
        
        # 计算买入数量
        available_cash = self.broker.get_cash()
        position_value = available_cash * self.params.position_size
        shares = int(position_value / current_price / 100) * 100  # 整手
        
        if shares >= 100:
            order = self.buy(data=data, size=shares)
            logger.info(f"📈 买入信号: {stock_code} {shares}股 @{current_price:.2f} "
                       f"信号: {','.join(signals)}")
    
    def _execute_sell(self, stock_code: str, data, signals: List[str]):
        """执行卖出"""
        position = self.getposition(data)
        current_price = data.close[0]
        
        order = self.sell(data=data, size=position.size)
        logger.info(f"📉 卖出信号: {stock_code} {position.size}股 @{current_price:.2f} "
                   f"信号: {','.join(signals)}")
    
    def notify_order(self, order):
        """订单状态通知"""
        if order.status in [order.Completed]:
            stock_code = order.data._name
            if order.isbuy():
                logger.info(f"✅ 买入完成: {stock_code} {order.executed.size}股 "
                           f"@{order.executed.price:.2f}")
                # 记录买入交易
                self.record_trade(
                    date=order.data.datetime.date(0).strftime('%Y-%m-%d'),
                    time=order.data.datetime.time(0).strftime('%H:%M:%S'),
                    stock_code=stock_code,
                    action='buy',
                    price=order.executed.price,
                    quantity=order.executed.size,
                    reason=f"多信号策略-买入",
                    order_ref=order.ref
                )
            else:
                logger.info(f"✅ 卖出完成: {stock_code} {order.executed.size}股 "
                           f"@{order.executed.price:.2f}")
                # 记录卖出交易
                self.record_trade(
                    date=order.data.datetime.date(0).strftime('%Y-%m-%d'),
                    time=order.data.datetime.time(0).strftime('%H:%M:%S'),
                    stock_code=stock_code,
                    action='sell',
                    price=order.executed.price,
                    quantity=order.executed.size,
                    reason=f"多信号策略-卖出",
                    order_ref=order.ref
                )

    def notify_trade(self, trade):
        """交易完成通知"""
        if not trade.isclosed:
            return

        # 获取交易数据
        profit = trade.pnl if trade.pnl is not None else 0
        pnlcomm = trade.pnlcomm if trade.pnlcomm is not None else 0
        value = trade.value if trade.value is not None else 0

        # 计算盈亏比例
        if value > 0:
            profit_rate = (pnlcomm / value * 100)
        else:
            profit_rate = 0

        logger.info(f"交易完成: 盈亏 {profit:.2f} ({profit_rate:.2f}%)")

# 注册策略
register_strategy(
    MultiSignalStrategyBT,
    strategy_name="multi_signal_strategy_bt",
    display_name="多信号策略(BT版)",
    description="基于backtrader的多信号策略，性能更好，适合实盘"
)
