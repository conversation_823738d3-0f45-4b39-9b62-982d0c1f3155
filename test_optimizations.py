#!/usr/bin/env python3
"""
测试系统优化效果
"""

import sys
import os
import time
import asyncio
import requests
import json

# 添加项目路径
sys.path.append(os.path.dirname(__file__))

def test_broker_optimizations():
    """测试QMTBroker优化"""
    print("🔧 测试QMTBroker优化...")
    
    try:
        from backend.stores.qmt_broker import QMTBroker
        
        # 创建broker实例
        broker = QMTBroker(cash=100000)
        
        print(f"✅ Broker初始化成功")
        print(f"   初始资金: {broker.getcash():.2f}")
        print(f"   总资产: {broker.getvalue():.2f}")
        print(f"   风险控制: 最大持仓{broker.max_position_ratio*100:.0f}%, 最大订单{broker.max_order_ratio*100:.0f}%")
        
        # 测试订单创建
        class MockData:
            def __init__(self, stock_code):
                self.stock_code = stock_code
                self.close = [100.0]  # 模拟价格
        
        data = MockData('000001.SZ')
        
        # 测试买入订单
        print(f"\n📋 测试买入订单...")
        order = broker.buy(owner=None, data=data, size=100, price=100.0)
        if order:
            print(f"✅ 买入订单创建成功: {order.ref}")
            print(f"   现金余额: {broker.getcash():.2f}")
            print(f"   总资产: {broker.getvalue():.2f}")
            
            # 测试持仓查询
            position = broker.getposition(data)
            print(f"   持仓: {position.size}股 @ {position.price:.2f}")
        else:
            print(f"❌ 买入订单创建失败")
        
        # 测试风险控制
        print(f"\n🛡️ 测试风险控制...")
        large_order = broker.buy(owner=None, data=data, size=50000, price=100.0)  # 超大订单
        if large_order:
            print(f"⚠️ 大订单被允许: {large_order.ref}")
        else:
            print(f"✅ 大订单被风险控制拒绝")
        
        return True
        
    except Exception as e:
        print(f"❌ Broker测试失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False

def test_data_source_optimizations():
    """测试数据源优化"""
    print("\n📊 测试数据源优化...")
    
    try:
        from backend.stores.qmt_store import QMTStore
        from backend.stores.qmt_data import QMTData
        
        # 创建store和数据源
        store = QMTStore()
        
        # 测试数据源创建
        data = store.getdata(dataname='000001.SZ', historical=True, live=False)
        
        print(f"✅ 数据源创建成功")
        print(f"   股票代码: {getattr(data, 'stock_code', 'NOT_SET')}")
        print(f"   历史模式: {getattr(data, 'historical_mode', 'NOT_SET')}")
        print(f"   实时模式: {getattr(data, 'live_mode', 'NOT_SET')}")
        
        # 测试启动和停止
        data.start()
        print(f"✅ 数据源启动成功")
        
        time.sleep(1)  # 等待一秒
        
        data.stop()
        print(f"✅ 数据源停止成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据源测试失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False

async def test_api_performance():
    """测试API性能"""
    print("\n🚀 测试API性能...")
    
    try:
        base_url = "http://localhost:8000"
        
        # 测试策略启动性能
        start_time = time.time()
        
        test_data = {
            "strategy_name": "bollinger_bands",
            "initial_capital": 100000,
            "commission": 0.001,
            "stock_codes": ["000001.SZ"],
            "paper_trading": True,
            "strategy_params": {
                "period": 20,
                "std_dev": 2.0
            }
        }
        
        response = requests.post(f"{base_url}/api/live/start", json=test_data, timeout=10)
        
        end_time = time.time()
        response_time = (end_time - start_time) * 1000
        
        if response.status_code == 200:
            result = response.json()
            task_id = result.get('task_id')
            
            print(f"✅ API调用成功")
            print(f"   响应时间: {response_time:.2f}ms")
            print(f"   任务ID: {task_id}")
            
            # 等待策略启动
            await asyncio.sleep(2)
            
            # 检查策略状态
            status_response = requests.get(f"{base_url}/api/live/status", timeout=5)
            if status_response.status_code == 200:
                status_data = status_response.json()
                print(f"✅ 状态查询成功")
                print(f"   运行中策略: {len(status_data.get('running_strategies', []))}")
            
            # 清理测试任务
            if task_id:
                cleanup_response = requests.delete(f"{base_url}/api/live/stop/{task_id}", timeout=5)
                if cleanup_response.status_code == 200:
                    print(f"✅ 测试任务清理成功")
            
            return True
        else:
            print(f"❌ API调用失败: {response.status_code}")
            print(f"   响应: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ API性能测试失败: {e}")
        return False

def test_error_handling():
    """测试错误处理优化"""
    print("\n🛠️ 测试错误处理优化...")
    
    try:
        from backend.live.simple_live_engine import SimpleLiveEngine
        
        engine = SimpleLiveEngine()
        
        # 模拟各种错误
        test_errors = [
            (NotImplementedError("getcash"), "NotImplementedError"),
            (AttributeError("'QMTBroker' object has no attribute 'get_notification'"), "AttributeError"),
            (ValueError("股票代码不能为空"), "ValueError"),
        ]
        
        for error, error_type in test_errors:
            print(f"\n   测试 {error_type}...")
            engine._analyze_error(error, "test_task_id")
        
        print(f"✅ 错误处理测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 错误处理测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("🧪 系统优化测试")
    print("=" * 60)
    
    test_results = []
    
    # 测试各个组件
    test_results.append(("QMTBroker优化", test_broker_optimizations()))
    test_results.append(("数据源优化", test_data_source_optimizations()))
    test_results.append(("API性能", await test_api_performance()))
    test_results.append(("错误处理", test_error_handling()))
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("🎯 测试结果汇总:")
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📊 总体结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有优化测试通过！系统已完全优化。")
    else:
        print("⚠️ 部分测试失败，可能需要进一步优化。")
    
    # 性能建议
    print(f"\n💡 性能优化建议:")
    print(f"   1. 确保后端服务器已重启以应用所有优化")
    print(f"   2. 监控内存使用情况，特别是实时数据更新")
    print(f"   3. 考虑添加数据库缓存以提高数据访问速度")
    print(f"   4. 实施更精细的风险控制策略")
    print(f"   5. 添加性能监控和告警机制")

if __name__ == "__main__":
    asyncio.run(main())
