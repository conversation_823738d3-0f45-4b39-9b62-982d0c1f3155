import React, { useState, useEffect } from 'react';
import {
  Square,
  DollarSign,
  TrendingUp,
  TrendingDown,
  RefreshCw,
  Plus,
  BarChart3,
  Activity,
  Eye,
  Trash2,
  RotateCcw,
  Settings
} from 'lucide-react';
import { Card as <PERSON>had<PERSON><PERSON>, <PERSON><PERSON>ead<PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, CardTitle as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, CardContent as <PERSON>had<PERSON>ardContent } from '../components/UI/card.jsx';
import { Button as ShadButton } from '../components/UI/button.jsx';
import { Alert as ShadAlert } from '../components/UI/alert.jsx';
import { Stat } from '../components/UI/stat.jsx';
import { DataTable } from '../components/UI/table.jsx';
import { Tag } from '../components/UI/tag.jsx';
import { Modal } from '../components/UI/modal.jsx';
import { toast } from 'sonner';
import { liveAPI } from '../services/api';
import LiveTradingConfig from '../components/LiveTradingConfig.jsx';
import LiveTradingMonitor from '../components/LiveTradingMonitor.jsx';

const MultiStrategyLiveTradingPage = () => {
  const [loading, setLoading] = useState(false);
  const [runningStrategies, setRunningStrategies] = useState([]);
  const [accountInfo, setAccountInfo] = useState(null);
  const [error, setError] = useState(null);
  const [addStrategyModalVisible, setAddStrategyModalVisible] = useState(false);
  const [strategyDetailModalVisible, setStrategyDetailModalVisible] = useState(false);
  const [selectedStrategy, setSelectedStrategy] = useState(null);
  const [recovering, setRecovering] = useState(false);
  const [recoveryMessage, setRecoveryMessage] = useState(null);



  // 加载账户信息
  const loadAccountInfo = async () => {
    try {
      const response = await liveAPI.getAccountInfo();
      if (response && response.success) {
        setAccountInfo(response.data);
      }
    } catch (err) {
      console.log('账户信息获取失败:', err.message);
    }
  };

  // 加载运行中的策略（使用新的多策略服务）
  const loadRunningStrategies = async () => {
    try {
      // 使用新的多策略API
      const response = await fetch('/api/live/strategies');
      if (response.ok) {
        const strategies = await response.json();
        setRunningStrategies(strategies);
      } else {
        console.error('获取策略列表失败:', response.status);
      }
    } catch (err) {
      console.log('获取运行策略失败:', err.message);
    }
  };

  useEffect(() => {
    loadAccountInfo();
    loadRunningStrategies();

    // 定时刷新
    const accountInterval = setInterval(loadAccountInfo, 10000);
    const strategiesInterval = setInterval(loadRunningStrategies, 5000);
    
    return () => {
      clearInterval(accountInterval);
      clearInterval(strategiesInterval);
    };
  }, []);

  // 启动策略（使用新的多策略服务）
  const handleStartStrategy = async (configData) => {
    setLoading(true);
    setError(null);

    try {
      // 转换配置数据格式为新的多策略服务格式
      const strategyData = {
        name: configData.strategy_name || '未命名策略',
        strategy_type: configData.strategy_name || 'bollinger_bands',
        config: {
          initial_capital: configData.initial_capital || 50000,
          max_positions: 5,
          risk_limit: 0.02,
          stock_codes: configData.stock_universe?.stock_codes || configData.stock_codes || [],
          paper_trading: configData.paper_trading !== undefined ? configData.paper_trading : true, // 默认纸面交易
          ...configData.strategy_params
        }
      };

      const response = await fetch('/api/live/strategies', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(strategyData),
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          const strategyId = result.strategy_id;
          toast.success(`策略启动成功！ID: ${strategyId.slice(0, 8)}...`);
          setAddStrategyModalVisible(false);
          await loadRunningStrategies();
        } else {
          throw new Error(result.message || '启动失败');
        }
      } else {
        const error = await response.json();
        throw new Error(error.detail || '启动失败');
      }
    } catch (err) {
      console.error('启动策略失败:', err);
      toast.error(`启动策略失败: ${err.message}`);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };



  // 停止策略（新的多策略服务只有停止功能，没有删除）
  const handleDeleteStrategy = async (strategy) => {
    if (!window.confirm(`确定要停止策略 ${strategy.name} 吗？`)) {
      return;
    }

    try {
      const response = await fetch(`/api/live/strategies/${strategy.id}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          toast.success(`策略 ${strategy.name} 已停止`);
          await loadRunningStrategies();
        } else {
          throw new Error(result.message || '停止失败');
        }
      } else {
        const error = await response.json();
        throw new Error(error.detail || '停止失败');
      }
    } catch (err) {
      toast.error(`停止策略失败: ${err.message}`);
    }
  };

  // 恢复持久化策略
  const recoverPersistedStrategies = async () => {
    setRecovering(true);
    setRecoveryMessage(null);

    try {
      const response = await fetch('/api/live/strategies/recover', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const result = await response.json();
        setRecoveryMessage({
          type: 'success',
          text: result.message,
          details: `恢复了 ${result.recovered_count} 个策略`
        });
        toast.success(result.message);

        // 刷新策略列表
        await loadRunningStrategies();
      } else {
        const error = await response.json();
        throw new Error(error.detail || '恢复策略失败');
      }
    } catch (err) {
      console.error('恢复策略失败:', err);
      setRecoveryMessage({ type: 'error', text: err.message });
      toast.error(`恢复策略失败: ${err.message}`);
      setError(err.message);
    } finally {
      setRecovering(false);
    }
  };

  // 停止策略（使用新的多策略服务）
  const handleStopStrategy = async (strategy) => {
    return handleDeleteStrategy(strategy);
  };



  // 查看策略详情
  const handleViewStrategy = (strategy) => {
    setSelectedStrategy(strategy);
    setStrategyDetailModalVisible(true);
  };

  // 运行中策略表格列
  const strategyColumns = [
    {
      accessorKey: 'name',
      header: '策略名称',
      cell: ({ row }) => (
        <div className="font-medium">{row.getValue('name')}</div>
      ),
    },
    {
      accessorKey: 'strategy_type',
      header: '策略类型',
      cell: ({ row }) => (
        <Tag color="blue">{row.getValue('strategy_type')}</Tag>
      ),
    },

    {
      accessorKey: 'status',
      header: '状态',
      cell: ({ row }) => {
        const status = row.getValue('status');
        let color, text;

        switch (status) {
          case 'running':
            color = 'green';
            text = '运行中';
            break;
          case 'paused':
            color = 'orange';
            text = '已暂停';
            break;
          case 'error':
            color = 'red';
            text = '错误';
            break;
          default:
            color = 'gray';
            text = '未知';
        }

        return <Tag color={color}>{text}</Tag>;
      },
    },
    {
      accessorKey: 'trading_type',
      header: '交易类型',
      cell: ({ row }) => {
        const strategy = row.original;
        const isPaperTrading = strategy.config?.paper_trading !== false; // 默认为纸面交易
        return (
          <Tag color={isPaperTrading ? 'blue' : 'red'}>
            {isPaperTrading ? '纸面交易' : '实盘交易'}
          </Tag>
        );
      },
    },
    {
      accessorKey: 'positions',
      header: '持仓数',
      cell: ({ row }) => row.getValue('positions') || 0,
    },
    {
      accessorKey: 'pnl',
      header: '盈亏',
      cell: ({ row }) => {
        const pnl = row.getValue('pnl') || 0;
        return (
          <span className={pnl >= 0 ? 'text-green-600 font-semibold' : 'text-red-600 font-semibold'}>
            ¥{pnl.toLocaleString()}
          </span>
        );
      },
    },
    {
      accessorKey: 'current_capital',
      header: '当前资金',
      cell: ({ row }) => {
        const capital = row.getValue('current_capital') || 0;
        return (
          <span className="font-mono">
            ¥{capital.toLocaleString()}
          </span>
        );
      },
    },
    {
      accessorKey: 'total_return',
      header: '总收益率',
      cell: ({ row }) => {
        const returnRate = (row.getValue('total_return') || 0) * 100;
        return (
          <span className={returnRate >= 0 ? 'text-green-600 font-semibold' : 'text-red-600 font-semibold'}>
            {returnRate >= 0 ? '+' : ''}{returnRate.toFixed(2)}%
          </span>
        );
      },
    },
    {
      accessorKey: 'start_time',
      header: '启动时间',
      cell: ({ row }) => {
        const time = row.getValue('start_time');
        return time ? new Date(time).toLocaleString() : '-';
      },
    },
    {
      id: 'actions',
      header: '操作',
      cell: ({ row }) => {
        const strategy = row.original;
        const isRunning = strategy.status === 'running';
        const isPaused = strategy.status === 'paused';
        const isError = strategy.status === 'error';

        return (
          <div className="flex items-center gap-1">
            {/* 查看按钮 */}
            <ShadButton
              variant="ghost"
              size="sm"
              onClick={() => handleViewStrategy(strategy)}
              className="h-8 w-8 p-0 text-blue-600 hover:text-blue-700 hover:bg-blue-50"
              title="查看策略详情"
            >
              <Eye className="h-4 w-4" />
            </ShadButton>

            {/* 停止按钮 - 只在运行状态显示 */}
            {isRunning && (
              <ShadButton
                variant="ghost"
                size="sm"
                onClick={() => handleDeleteStrategy(strategy)}
                className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
                title="停止策略"
              >
                <Square className="h-4 w-4" />
              </ShadButton>
            )}

            {/* 删除按钮 - 在暂停或错误状态显示 */}
            {(isPaused || isError) && (
              <ShadButton
                variant="ghost"
                size="sm"
                onClick={() => handleDeleteStrategy(strategy)}
                className="h-8 w-8 p-0 text-gray-600 hover:text-red-600 hover:bg-red-50"
                title="删除策略"
              >
                <Trash2 className="h-4 w-4" />
              </ShadButton>
            )}
          </div>
        );
      },
    },
  ];

  // 持仓表格列
  const positionColumns = [
    {
      accessorKey: 'stock_code',
      header: '股票代码',
      cell: ({ row }) => (
        <div className="font-mono">{row.getValue('stock_code')}</div>
      ),
    },
    {
      accessorKey: 'strategy_name',
      header: '所属策略',
      cell: ({ row }) => (
        <Tag color="blue">{row.getValue('strategy_name')}</Tag>
      ),
    },
    {
      accessorKey: 'quantity',
      header: '持仓数量',
      cell: ({ row }) => `${row.getValue('quantity')}股`,
    },
    {
      accessorKey: 'avg_price',
      header: '均价',
      cell: ({ row }) => `¥${row.getValue('avg_price')?.toFixed(2) || '0.00'}`,
    },
    {
      accessorKey: 'current_price',
      header: '现价',
      cell: ({ row }) => `¥${row.getValue('current_price')?.toFixed(2) || '0.00'}`,
    },
    {
      accessorKey: 'market_value',
      header: '市值',
      cell: ({ row }) => `¥${row.getValue('market_value')?.toLocaleString() || '0'}`,
    },
    {
      accessorKey: 'pnl',
      header: '盈亏',
      cell: ({ row }) => {
        const pnl = row.getValue('pnl') || 0;
        return (
          <span className={pnl >= 0 ? 'text-green-600 font-semibold' : 'text-red-600 font-semibold'}>
            ¥{pnl.toLocaleString()}
          </span>
        );
      },
    },
    {
      accessorKey: 'pnl_ratio',
      header: '盈亏比例',
      cell: ({ row }) => {
        const ratio = row.getValue('pnl_ratio') || 0;
        return (
          <span className={ratio >= 0 ? 'text-green-600 font-semibold' : 'text-red-600 font-semibold'}>
            {(ratio * 100).toFixed(2)}%
          </span>
        );
      },
    },
  ];

  return (
    <div className="space-y-6 p-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">多策略实盘交易</h1>
        <div className="flex gap-2">
          <ShadButton onClick={loadRunningStrategies} variant="outline">
            <RefreshCw className="h-4 w-4 mr-2" />
            刷新
          </ShadButton>
          <ShadButton
            onClick={recoverPersistedStrategies}
            variant="outline"
            disabled={recovering}
          >
            <RotateCcw className={`h-4 w-4 mr-2 ${recovering ? 'animate-spin' : ''}`} />
            {recovering ? '恢复中...' : '恢复策略'}
          </ShadButton>
          <ShadButton onClick={() => setAddStrategyModalVisible(true)}>
            <Plus className="h-4 w-4 mr-2" />
            添加策略
          </ShadButton>
        </div>
      </div>

      {error && (
        <ShadAlert
          title="错误"
          description={typeof error === 'string' ? error : JSON.stringify(error)}
          variant="error"
          closable
          onClose={() => setError(null)}
        />
      )}

      {recoveryMessage && (
        <ShadAlert
          title={recoveryMessage.type === 'success' ? '恢复成功' : '恢复失败'}
          description={
            <div>
              {recoveryMessage.text}
              {recoveryMessage.details && (
                <div className="text-sm mt-1 opacity-80">{recoveryMessage.details}</div>
              )}
            </div>
          }
          variant={recoveryMessage.type === 'success' ? 'default' : 'error'}
          closable
          onClose={() => setRecoveryMessage(null)}
        />
      )}

      {/* 安全提醒 */}
      <ShadAlert
        title="安全提醒"
        description="多策略实盘交易涉及真实资金，请谨慎操作。建议先进行充分的回测验证，并从小资金开始。"
        variant="warning"
      />

      {/* 持久化功能说明 */}
      <ShadAlert
        title="策略持久化"
        description={
          <div className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            <span>系统会自动保存运行中的策略，重启后可通过"恢复策略"按钮恢复之前的任务。</span>
          </div>
        }
        variant="default"
      />

      {/* 账户概览 */}
      {accountInfo && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Stat
            title="总资产"
            value={`¥${accountInfo.total_value?.toLocaleString() || '0'}`}
            icon={<DollarSign className="h-6 w-6" />}
            color="blue"
          />
          <Stat
            title="可用资金"
            value={`¥${accountInfo.available_cash?.toLocaleString() || '0'}`}
            icon={<TrendingUp className="h-6 w-6" />}
            color="green"
          />
          <Stat
            title="持仓市值"
            value={`¥${accountInfo.market_value?.toLocaleString() || '0'}`}
            icon={<BarChart3 className="h-6 w-6" />}
            color="orange"
          />
          <Stat
            title="今日盈亏"
            value={`¥${accountInfo.daily_pnl?.toLocaleString() || '0'}`}
            icon={accountInfo.daily_pnl >= 0 ? <TrendingUp className="h-6 w-6" /> : <TrendingDown className="h-6 w-6" />}
            color={accountInfo.daily_pnl >= 0 ? "green" : "red"}
          />
        </div>
      )}

      {/* 实时监控 */}
      <LiveTradingMonitor
        taskIds={runningStrategies.map(s => s.task_id).filter(Boolean)}
      />

      {/* 运行中的策略 */}
      <ShadCard>
        <ShadCardHeader>
          <ShadCardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            运行中的策略 ({runningStrategies.length})
          </ShadCardTitle>
        </ShadCardHeader>
        <ShadCardContent>
          {runningStrategies.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <Activity className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>暂无运行中的策略</p>
              <p className="text-sm">点击"添加策略"开始实盘交易</p>
            </div>
          ) : (
            <DataTable
              data={runningStrategies}
              columns={strategyColumns}
              loading={loading}
            />
          )}
        </ShadCardContent>
      </ShadCard>

      {/* 持仓明细 */}
      {accountInfo?.positions && accountInfo.positions.length > 0 && (
        <ShadCard>
          <ShadCardHeader>
            <ShadCardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              持仓明细 ({accountInfo.positions.length})
            </ShadCardTitle>
          </ShadCardHeader>
          <ShadCardContent>
            <DataTable
              data={accountInfo.positions}
              columns={positionColumns}
            />
          </ShadCardContent>
        </ShadCard>
      )}

      {/* 实盘交易配置模态框 */}
      <LiveTradingConfig
        visible={addStrategyModalVisible}
        onClose={() => setAddStrategyModalVisible(false)}
        onSubmit={handleStartStrategy}
        loading={loading}
      />

      {/* 策略详情模态框 */}
      <Modal
        visible={strategyDetailModalVisible}
        title={`策略详情 - ${selectedStrategy?.name}`}
        onCancel={() => setStrategyDetailModalVisible(false)}
        width={800}
        footer={
          <div className="flex justify-end gap-2">
            <ShadButton
              variant="outline"
              onClick={() => setStrategyDetailModalVisible(false)}
            >
              关闭
            </ShadButton>
            {selectedStrategy && (
              <ShadButton
                variant="destructive"
                onClick={() => {
                  handleStopStrategy(selectedStrategy.id, selectedStrategy.name);
                  setStrategyDetailModalVisible(false);
                }}
              >
                停止策略
              </ShadButton>
            )}
          </div>
        }
      >
        {selectedStrategy && (
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">策略名称</label>
                <div className="text-sm">{selectedStrategy.name}</div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">策略类型</label>
                <Tag color="blue">{selectedStrategy.strategy_type}</Tag>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">运行状态</label>
                <Tag color={selectedStrategy.status === 'running' ? 'green' : 'red'}>
                  {selectedStrategy.status === 'running' ? '运行中' : '已停止'}
                </Tag>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">启动时间</label>
                <div className="text-sm">
                  {selectedStrategy.start_time ? new Date(selectedStrategy.start_time).toLocaleString() : '-'}
                </div>
              </div>
            </div>

            <div className="grid grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">持仓数量</label>
                <div className="text-lg font-semibold">{selectedStrategy.positions || 0}</div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">总盈亏</label>
                <div className={`text-lg font-semibold ${
                  (selectedStrategy.pnl || 0) >= 0 ? 'text-green-600' : 'text-red-600'
                }`}>
                  ¥{(selectedStrategy.pnl || 0).toLocaleString()}
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">交易次数</label>
                <div className="text-lg font-semibold">{selectedStrategy.trade_count || 0}</div>
              </div>
            </div>

            {selectedStrategy.config && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">策略配置</label>
                <div className="bg-gray-50 p-3 rounded-md">
                  <pre className="text-xs text-gray-600">
                    {JSON.stringify(selectedStrategy.config, null, 2)}
                  </pre>
                </div>
              </div>
            )}

            {selectedStrategy.recent_trades && selectedStrategy.recent_trades.length > 0 && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">最近交易</label>
                <div className="space-y-2 max-h-40 overflow-y-auto">
                  {selectedStrategy.recent_trades.map((trade, index) => (
                    <div key={index} className="flex justify-between items-center p-2 bg-gray-50 rounded">
                      <div className="text-sm">
                        <span className="font-medium">{trade.stock_code}</span>
                        <span className={`ml-2 ${trade.action === 'buy' ? 'text-green-600' : 'text-red-600'}`}>
                          {trade.action === 'buy' ? '买入' : '卖出'}
                        </span>
                      </div>
                      <div className="text-sm text-gray-600">
                        {trade.quantity}股 @ ¥{trade.price}
                      </div>
                      <div className="text-xs text-gray-500">
                        {new Date(trade.time).toLocaleString()}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}
      </Modal>
    </div>
  );
};

export default MultiStrategyLiveTradingPage;
