#!/usr/bin/env python3
"""
选股预设配置 - 提供常用的选股策略模板
"""

from typing import Dict, List, Any
from backend.stock_selection.stock_selector import SelectionCriteria

class SelectionPresets:
    """选股预设管理器"""
    
    @staticmethod
    def get_all_presets() -> Dict[str, Dict[str, Any]]:
        """获取所有预设选股策略"""
        return {
            # ETF相关策略
            "all_etfs": {
                "display_name": "所有ETF",
                "description": "选择所有可交易的ETF基金，适合ETF投资组合构建",
                "category": "ETF策略",
                "risk_level": "中等",
                "criteria": SelectionCriteria(
                    only_etf=True,
                    volume_ratio_min=0.3,
                    avg_amount_min=100,
                    condition_logic="flexible",
                    lookback_days=30,
                    min_trading_days=20
                )
            },
            
            "active_etfs": {
                "display_name": "活跃ETF",
                "description": "选择成交活跃、流动性好的ETF基金",
                "category": "ETF策略", 
                "risk_level": "中等",
                "criteria": SelectionCriteria(
                    only_etf=True,
                    volume_ratio_min=1.0,
                    avg_amount_min=1000,
                    turnover_rate_min=1.0,
                    condition_logic="flexible"
                )
            },
            
            "stock_etfs": {
                "display_name": "股票型ETF",
                "description": "选择跟踪股票指数的ETF，适合股票市场投资",
                "category": "ETF策略",
                "risk_level": "中高",
                "criteria": SelectionCriteria(
                    only_etf=True,
                    etf_types=['stock'],
                    volume_ratio_min=0.8,
                    avg_amount_min=500,
                    condition_logic="flexible"
                )
            },
            
            "bond_etfs": {
                "display_name": "债券型ETF",
                "description": "选择债券型ETF，适合稳健投资和资产配置",
                "category": "ETF策略",
                "risk_level": "低",
                "criteria": SelectionCriteria(
                    only_etf=True,
                    etf_types=['bond'],
                    volume_ratio_min=0.5,
                    volatility_max=0.15,
                    condition_logic="flexible"
                )
            },
            
            # 动量策略
            "strong_momentum": {
                "display_name": "强势动量股",
                "description": "选择具有强劲上涨动量的股票，适合趋势投资",
                "category": "动量策略",
                "risk_level": "高",
                "criteria": SelectionCriteria(
                    exclude_etf=True,
                    momentum_1m_min=0.08,    # 1月涨幅>8%
                    momentum_3m_min=0.20,    # 3月涨幅>20%
                    volume_ratio_min=1.5,    # 成交活跃
                    rsi_max=80,              # 避免过度超买
                    ma_arrangement='bullish', # 均线多头排列
                    condition_logic="flexible",
                    lookback_days=90,
                    min_trading_days=60
                )
            },
            
            "moderate_momentum": {
                "display_name": "温和动量股",
                "description": "选择具有适度上涨动量的股票，平衡收益与风险",
                "category": "动量策略",
                "risk_level": "中高",
                "criteria": SelectionCriteria(
                    exclude_etf=True,
                    momentum_1m_min=0.03,    # 1月涨幅>3%
                    momentum_1m_max=0.15,    # 1月涨幅<15%
                    momentum_3m_min=0.05,    # 3月涨幅>5%
                    momentum_3m_max=0.40,    # 3月涨幅<40%
                    volume_ratio_min=1.0,
                    volatility_max=0.35,
                    condition_logic="flexible"
                )
            },
            
            "reversal_momentum": {
                "display_name": "反转动量股",
                "description": "选择可能出现反转的股票，适合逆向投资",
                "category": "动量策略",
                "risk_level": "高",
                "criteria": SelectionCriteria(
                    exclude_etf=True,
                    momentum_1m_max=-0.05,   # 1月跌幅>5%
                    momentum_3m_max=-0.10,   # 3月跌幅>10%
                    rsi_max=35,              # 超卖状态
                    volume_ratio_min=1.2,    # 放量下跌
                    avg_amount_min=1000,     # 流动性好
                    condition_logic="flexible"
                )
            },
            
            # 价值策略
            "value_stocks": {
                "display_name": "价值股票",
                "description": "选择估值合理的价值股，适合长期投资",
                "category": "价值策略",
                "risk_level": "中等",
                "criteria": SelectionCriteria(
                    exclude_etf=True,
                    pe_max=25,               # 市盈率<25
                    pb_max=3,                # 市净率<3
                    dividend_yield_min=0.02, # 股息率>2%
                    roe_min=10,              # ROE>10%
                    volatility_max=0.30,     # 控制波动
                    avg_amount_min=2000,     # 大盘股
                    condition_logic="flexible"
                )
            },
            
            "growth_stocks": {
                "display_name": "成长股票",
                "description": "选择具有良好成长性的股票",
                "category": "成长策略",
                "risk_level": "中高",
                "criteria": SelectionCriteria(
                    exclude_etf=True,
                    revenue_growth_min=15,   # 营收增长>15%
                    profit_growth_min=20,    # 利润增长>20%
                    eps_growth_min=15,       # EPS增长>15%
                    roe_min=15,              # ROE>15%
                    pe_max=50,               # 合理估值
                    condition_logic="flexible"
                )
            },
            
            # 质量策略
            "quality_stocks": {
                "display_name": "优质股票",
                "description": "选择财务质量优秀的股票",
                "category": "质量策略",
                "risk_level": "中等",
                "criteria": SelectionCriteria(
                    exclude_etf=True,
                    roe_min=15,              # ROE>15%
                    roa_min=8,               # ROA>8%
                    gross_margin_min=30,     # 毛利率>30%
                    volatility_max=0.25,     # 低波动
                    beta_max=1.2,            # 稳定Beta
                    avg_amount_min=3000,     # 大盘股
                    condition_logic="strict"  # 严格模式
                )
            },
            
            # 风险控制策略
            "low_risk_stocks": {
                "display_name": "低风险股票",
                "description": "选择低风险稳健的股票，适合保守投资",
                "category": "风险控制",
                "risk_level": "低",
                "criteria": SelectionCriteria(
                    exclude_etf=True,
                    volatility_max=0.20,     # 低波动率
                    beta_min=0.7,
                    beta_max=1.1,            # 稳定Beta
                    max_drawdown_max=0.15,   # 低回撤
                    avg_amount_min=5000,     # 大盘蓝筹
                    dividend_yield_min=0.025, # 有分红
                    condition_logic="strict"
                )
            },
            
            "defensive_stocks": {
                "display_name": "防御性股票",
                "description": "选择具有防御特性的股票，适合市场下跌时期",
                "category": "风险控制",
                "risk_level": "低",
                "criteria": SelectionCriteria(
                    exclude_etf=True,
                    beta_max=0.8,            # 低Beta
                    volatility_max=0.18,     # 极低波动
                    dividend_yield_min=0.03, # 高分红
                    roe_min=12,              # 稳定盈利
                    avg_amount_min=8000,     # 超大盘股
                    condition_logic="strict"
                )
            },
            
            # 活跃交易策略
            "active_trading": {
                "display_name": "活跃交易股",
                "description": "选择适合短期交易的活跃股票",
                "category": "交易策略",
                "risk_level": "高",
                "criteria": SelectionCriteria(
                    exclude_etf=True,
                    volume_ratio_min=2.0,    # 高量比
                    turnover_rate_min=5.0,   # 高换手率
                    turnover_rate_max=20.0,  # 控制上限
                    volatility_min=0.25,     # 有波动才有机会
                    volatility_max=0.50,     # 控制风险
                    avg_amount_min=1000,     # 流动性好
                    condition_logic="flexible"
                )
            },
            
            "swing_trading": {
                "display_name": "波段交易股",
                "description": "选择适合波段操作的股票",
                "category": "交易策略", 
                "risk_level": "中高",
                "criteria": SelectionCriteria(
                    exclude_etf=True,
                    volatility_min=0.20,
                    volatility_max=0.35,     # 适中波动
                    volume_ratio_min=1.2,
                    rsi_min=25,
                    rsi_max=75,              # RSI适中
                    ma_arrangement='bullish', # 趋势向上
                    avg_amount_min=2000,
                    condition_logic="flexible"
                )
            },
            
            # 综合策略
            "balanced_portfolio": {
                "display_name": "均衡组合",
                "description": "选择适合构建均衡投资组合的股票",
                "category": "综合策略",
                "risk_level": "中等",
                "criteria": SelectionCriteria(
                    exclude_etf=True,
                    pe_max=30,               # 合理估值
                    roe_min=12,              # 良好盈利
                    volatility_max=0.30,     # 控制风险
                    beta_max=1.3,
                    momentum_3m_min=0.02,    # 轻微正动量
                    volume_ratio_min=0.8,
                    avg_amount_min=1500,
                    condition_logic="flexible"
                )
            },
            
            "all_weather": {
                "display_name": "全天候策略",
                "description": "选择在各种市场环境下都能表现的股票",
                "category": "综合策略",
                "risk_level": "中等",
                "criteria": SelectionCriteria(
                    exclude_etf=True,
                    volatility_max=0.25,     # 稳定性
                    beta_min=0.8,
                    beta_max=1.2,            # 适中Beta
                    roe_min=15,              # 优秀盈利
                    dividend_yield_min=0.02, # 有分红
                    avg_amount_min=3000,     # 大盘股
                    max_drawdown_max=0.20,   # 控制回撤
                    condition_logic="strict"
                )
            }
        }
    
    @staticmethod
    def get_preset(preset_name: str) -> Dict[str, Any]:
        """获取指定的预设策略"""
        presets = SelectionPresets.get_all_presets()
        return presets.get(preset_name)
    
    @staticmethod
    def get_presets_by_category(category: str) -> Dict[str, Dict[str, Any]]:
        """按类别获取预设策略"""
        all_presets = SelectionPresets.get_all_presets()
        return {k: v for k, v in all_presets.items() if v.get('category') == category}
    
    @staticmethod
    def get_presets_by_risk_level(risk_level: str) -> Dict[str, Dict[str, Any]]:
        """按风险等级获取预设策略"""
        all_presets = SelectionPresets.get_all_presets()
        return {k: v for k, v in all_presets.items() if v.get('risk_level') == risk_level}
    
    @staticmethod
    def get_categories() -> List[str]:
        """获取所有策略类别"""
        all_presets = SelectionPresets.get_all_presets()
        categories = set(preset.get('category', '其他') for preset in all_presets.values())
        return sorted(list(categories))
    
    @staticmethod
    def get_risk_levels() -> List[str]:
        """获取所有风险等级"""
        return ['低', '中等', '中高', '高']

# 全局实例
selection_presets = SelectionPresets()
