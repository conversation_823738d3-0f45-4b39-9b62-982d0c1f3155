#!/usr/bin/env python3
"""
测试新的回测配置格式
"""

import requests
import time
import json

def test_new_backtest_format():
    """测试新的回测配置格式"""
    
    # 使用新的日期格式启动回测
    print("启动回测（新格式）...")
    response = requests.post('http://localhost:8001/api/backtest/start', json={
        'strategy_name': 'new_format_test',
        'strategy_config': {},
        'start_date': '2024-01-01',
        'end_date': '2024-12-31',
        'initial_capital': 1000000,
        'commission': 0.0003
    })
    
    if response.status_code != 200:
        print(f"启动失败: {response.status_code} - {response.text}")
        return
    
    result = response.json()
    print(f"启动成功: {result}")
    task_id = result['data']['task_id']
    
    # 等待完成
    print("等待回测完成...")
    for i in range(10):
        time.sleep(1)
        status_response = requests.get('http://localhost:8001/api/backtest/status')
        if status_response.status_code == 200:
            status_data = status_response.json()
            print(f"状态 {i+1}: {status_data['data']}")
            
            if status_data['data']['status'] in ['completed', 'failed']:
                break
    
    # 获取结果
    print("获取回测结果...")
    result_response = requests.get(f'http://localhost:8001/api/backtest/results/{task_id}')
    print(f"结果状态码: {result_response.status_code}")
    
    if result_response.status_code == 200:
        result_data = result_response.json()
        print(f"回测成功完成!")
        print(f"策略: {result_data['data']['strategy_name']}")
        print(f"时间范围: {result_data['data']['start_date']} 到 {result_data['data']['end_date']}")
        print(f"总收益率: {result_data['data']['total_return']:.2%}")
        print(f"最大回撤: {result_data['data']['max_drawdown']:.2%}")
        print(f"夏普比率: {result_data['data']['sharpe_ratio']:.2f}")
    else:
        print(f"获取结果失败: {result_response.text}")

if __name__ == "__main__":
    test_new_backtest_format()
