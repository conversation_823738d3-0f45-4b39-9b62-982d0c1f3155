#!/usr/bin/env python3
"""
QMT Store模式使用示例
展示如何让现有的backtrader策略直接用于实盘交易
"""

import sys
import os
from datetime import datetime, timedelta

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

import backtrader as bt
from backend.stores import QMTStore
from backend.core.logger import get_logger

logger = get_logger(__name__)

class SimpleMAStrategy(bt.Strategy):
    """
    简单均线策略示例
    
    这是一个标准的backtrader策略，无需任何修改就能用于实盘交易
    """
    
    params = (
        ('ma_period', 20),      # 均线周期
        ('position_size', 0.1), # 仓位大小
    )
    
    def __init__(self):
        # 计算移动平均线
        self.ma = bt.indicators.SimpleMovingAverage(
            self.data.close, 
            period=self.params.ma_period
        )
        
        # 记录订单
        self.order = None
        
        logger.info(f"📊 初始化策略: MA周期={self.params.ma_period}")
    
    def notify_order(self, order):
        """订单状态通知"""
        if order.status in [order.Submitted, order.Accepted]:
            return
        
        if order.status in [order.Completed]:
            if order.isbuy():
                logger.info(f"📈 买入成交: 价格={order.executed.price:.2f}, "
                           f"数量={order.executed.size}, 手续费={order.executed.comm:.2f}")
            else:
                logger.info(f"📉 卖出成交: 价格={order.executed.price:.2f}, "
                           f"数量={order.executed.size}, 手续费={order.executed.comm:.2f}")
        
        elif order.status in [order.Canceled, order.Margin, order.Rejected]:
            logger.warning(f"❌ 订单失败: {order.status}")
        
        self.order = None
    
    def notify_trade(self, trade):
        """交易通知"""
        if not trade.isclosed:
            return
        
        logger.info(f"💰 交易完成: 盈亏={trade.pnl:.2f}, 净盈亏={trade.pnlcomm:.2f}")
    
    def next(self):
        """策略主逻辑"""
        # 如果有未完成订单，等待
        if self.order:
            return
        
        # 获取当前价格和均线值
        current_price = self.data.close[0]
        ma_value = self.ma[0]
        
        # 如果没有持仓
        if not self.position:
            # 价格突破均线，买入
            if current_price > ma_value:
                # 计算买入数量
                cash = self.broker.getcash()
                size = int(cash * self.params.position_size / current_price / 100) * 100
                
                if size > 0:
                    self.order = self.buy(size=size)
                    logger.info(f"📈 发出买入信号: 价格={current_price:.2f}, "
                               f"均线={ma_value:.2f}, 数量={size}")
        
        # 如果有持仓
        else:
            # 价格跌破均线，卖出
            if current_price < ma_value:
                self.order = self.sell(size=self.position.size)
                logger.info(f"📉 发出卖出信号: 价格={current_price:.2f}, "
                           f"均线={ma_value:.2f}, 数量={self.position.size}")

def run_backtest_example():
    """回测示例"""
    logger.info("🔄 运行回测示例")
    
    # 创建Cerebro
    cerebro = bt.Cerebro()
    
    # 添加策略
    cerebro.addstrategy(SimpleMAStrategy, ma_period=10)
    
    # 添加数据（使用QMT历史数据）
    qmt_store = QMTStore()
    data = qmt_store.getdata(
        dataname='000001.SZ',
        historical=True,
        fromdate=datetime.now() - timedelta(days=60),
        todate=datetime.now()
    )
    cerebro.adddata(data)
    
    # 设置初始资金
    cerebro.broker.setcash(100000.0)
    
    # 设置手续费
    cerebro.broker.setcommission(commission=0.001)
    
    # 运行回测
    logger.info("🚀 开始回测")
    results = cerebro.run()
    
    # 显示结果
    final_value = cerebro.broker.getvalue()
    logger.info(f"✅ 回测完成")
    logger.info(f"   初始资金: 100,000.00")
    logger.info(f"   最终资金: {final_value:,.2f}")
    logger.info(f"   总收益: {final_value - 100000:.2f}")
    logger.info(f"   收益率: {(final_value - 100000) / 100000 * 100:.2f}%")

def run_live_example():
    """实盘示例"""
    logger.info("🔄 运行实盘示例")
    
    # 创建Cerebro
    cerebro = bt.Cerebro()
    
    # 添加策略
    cerebro.addstrategy(SimpleMAStrategy, ma_period=10)
    
    # 创建QMT Store
    qmt_store = QMTStore()
    
    # 设置broker（使用QMT实盘broker）
    cerebro.broker = qmt_store.getbroker(cash=100000.0)
    
    # 添加实时数据
    data = qmt_store.getdata(
        dataname='000001.SZ',
        historical=True,  # 先加载历史数据
        live=True,        # 然后切换到实时数据
        fromdate=datetime.now() - timedelta(days=30)
    )
    cerebro.adddata(data)
    
    # 设置手续费
    cerebro.broker.setcommission(commission=0.001)
    
    # 运行实盘交易
    logger.info("🚀 开始实盘交易")
    logger.info("⚠️ 注意：这将执行真实交易！")
    
    # 在实际使用中，这里会持续运行
    # results = cerebro.run()

def run_paper_trading_example():
    """纸上交易示例"""
    logger.info("🔄 运行纸上交易示例")
    
    # 创建Cerebro
    cerebro = bt.Cerebro()
    
    # 添加策略
    cerebro.addstrategy(SimpleMAStrategy, ma_period=10)
    
    # 创建QMT Store
    qmt_store = QMTStore()
    
    # 添加实时数据（但不执行真实交易）
    data = qmt_store.getdata(
        dataname='000001.SZ',
        historical=True,
        live=True,
        fromdate=datetime.now() - timedelta(days=30)
    )
    cerebro.adddata(data)
    
    # 使用默认的模拟broker（不是QMT broker）
    cerebro.broker.setcash(100000.0)
    cerebro.broker.setcommission(commission=0.001)
    
    # 运行纸上交易
    logger.info("🚀 开始纸上交易")
    logger.info("✅ 使用实时数据，但不执行真实交易")
    
    # results = cerebro.run()

def main():
    """主函数"""
    logger.info("🎯 QMT Store模式示例")
    
    print("\n选择运行模式：")
    print("1. 回测模式（使用历史数据）")
    print("2. 纸上交易模式（使用实时数据，模拟交易）")
    print("3. 实盘交易模式（使用实时数据，真实交易）")
    
    choice = input("\n请选择 (1-3): ").strip()
    
    if choice == '1':
        run_backtest_example()
    elif choice == '2':
        run_paper_trading_example()
    elif choice == '3':
        confirm = input("⚠️ 确认要执行真实交易吗？(yes/no): ").strip().lower()
        if confirm == 'yes':
            run_live_example()
        else:
            logger.info("❌ 已取消实盘交易")
    else:
        logger.error("❌ 无效选择")

if __name__ == "__main__":
    main()
