#!/usr/bin/env python3
"""
选股模块与策略系统集成工具
"""

import os
import json
from typing import List, Dict, Any, Optional
from pathlib import Path

from backend.stock_selection.stock_selector import StockSelector
from backend.core.logger import get_logger

logger = get_logger(__name__)

# 创建选股器实例（延迟初始化）
_stock_selector = None

def get_stock_selector():
    """获取选股器实例"""
    global _stock_selector
    if _stock_selector is None:
        _stock_selector = StockSelector()
    return _stock_selector

class StockSelectionIntegration:
    """选股模块集成类"""
    
    def __init__(self):
        self.selection_dir = Path("data/stock_selection")
        self.universe_dir = Path("data/universes")
        self.universe_dir.mkdir(parents=True, exist_ok=True)
    
    def create_universe_from_selection(self, selection_filename: str, universe_name: str, 
                                     description: str = "") -> bool:
        """从选股结果创建股票池"""
        try:
            # 加载选股结果
            selector = get_stock_selector()
            selected_stocks = selector.load_selection_results(selection_filename)
            
            if not selected_stocks:
                logger.error(f"无法加载选股结果: {selection_filename}")
                return False
            
            # 提取股票代码
            stock_codes = [stock.stock_code for stock in selected_stocks]
            
            # 创建股票池数据
            universe_data = {
                'name': universe_name,
                'display_name': universe_name,
                'description': description or f"基于选股结果 {selection_filename} 创建的股票池",
                'universe_type': 'selection_based',
                'source_selection': selection_filename,
                'creation_date': selected_stocks[0].selection_date if selected_stocks else '',
                'stock_codes': stock_codes,
                'stock_count': len(stock_codes),
                'metadata': {
                    'selection_criteria': 'Multiple technical indicators and Alpha101 factors',
                    'top_stocks': [
                        {
                            'stock_code': stock.stock_code,
                            'stock_name': stock.stock_name,
                            'score': stock.score
                        }
                        for stock in selected_stocks[:10]  # 前10只股票
                    ]
                }
            }
            
            # 保存股票池文件
            universe_file = self.universe_dir / f"{universe_name}.json"
            with open(universe_file, 'w', encoding='utf-8') as f:
                json.dump(universe_data, f, ensure_ascii=False, indent=2, default=str)
            
            logger.info(f"成功创建股票池: {universe_name}，包含 {len(stock_codes)} 只股票")
            return True
            
        except Exception as e:
            logger.error(f"创建股票池失败: {e}")
            return False
    
    def get_universe_stock_codes(self, universe_name: str) -> List[str]:
        """获取股票池中的股票代码"""
        try:
            universe_file = self.universe_dir / f"{universe_name}.json"
            
            if not universe_file.exists():
                logger.error(f"股票池文件不存在: {universe_name}")
                return []
            
            with open(universe_file, 'r', encoding='utf-8') as f:
                universe_data = json.load(f)
            
            return universe_data.get('stock_codes', [])
            
        except Exception as e:
            logger.error(f"获取股票池代码失败: {e}")
            return []
    
    def list_selection_universes(self) -> List[Dict[str, Any]]:
        """列出基于选股结果的股票池"""
        try:
            universes = []
            
            for universe_file in self.universe_dir.glob("*.json"):
                try:
                    with open(universe_file, 'r', encoding='utf-8') as f:
                        universe_data = json.load(f)
                    
                    if universe_data.get('universe_type') == 'selection_based':
                        universes.append({
                            'name': universe_data.get('name', ''),
                            'display_name': universe_data.get('display_name', ''),
                            'description': universe_data.get('description', ''),
                            'stock_count': universe_data.get('stock_count', 0),
                            'source_selection': universe_data.get('source_selection', ''),
                            'creation_date': universe_data.get('creation_date', ''),
                            'filename': universe_file.name
                        })
                        
                except Exception as e:
                    logger.error(f"读取股票池文件失败 {universe_file}: {e}")
                    continue
            
            # 按创建日期排序
            universes.sort(key=lambda x: x['creation_date'], reverse=True)
            
            return universes
            
        except Exception as e:
            logger.error(f"列出选股股票池失败: {e}")
            return []
    
    def update_universe_from_selection(self, universe_name: str, selection_filename: str) -> bool:
        """从新的选股结果更新股票池"""
        try:
            # 加载现有股票池
            universe_file = self.universe_dir / f"{universe_name}.json"
            
            if not universe_file.exists():
                logger.error(f"股票池不存在: {universe_name}")
                return False
            
            with open(universe_file, 'r', encoding='utf-8') as f:
                universe_data = json.load(f)
            
            # 加载新的选股结果
            selector = get_stock_selector()
            selected_stocks = selector.load_selection_results(selection_filename)
            
            if not selected_stocks:
                logger.error(f"无法加载选股结果: {selection_filename}")
                return False
            
            # 更新股票池数据
            stock_codes = [stock.stock_code for stock in selected_stocks]
            
            universe_data.update({
                'source_selection': selection_filename,
                'creation_date': selected_stocks[0].selection_date if selected_stocks else '',
                'stock_codes': stock_codes,
                'stock_count': len(stock_codes),
                'last_updated': selected_stocks[0].selection_date if selected_stocks else '',
                'metadata': {
                    **universe_data.get('metadata', {}),
                    'top_stocks': [
                        {
                            'stock_code': stock.stock_code,
                            'stock_name': stock.stock_name,
                            'score': stock.score
                        }
                        for stock in selected_stocks[:10]
                    ]
                }
            })
            
            # 保存更新后的股票池
            with open(universe_file, 'w', encoding='utf-8') as f:
                json.dump(universe_data, f, ensure_ascii=False, indent=2, default=str)
            
            logger.info(f"成功更新股票池: {universe_name}，现包含 {len(stock_codes)} 只股票")
            return True
            
        except Exception as e:
            logger.error(f"更新股票池失败: {e}")
            return False
    
    def get_selection_based_universes_for_strategy(self) -> Dict[str, List[str]]:
        """获取可用于策略的选股股票池"""
        try:
            universes = self.list_selection_universes()
            
            result = {}
            for universe in universes:
                universe_name = universe['name']
                stock_codes = self.get_universe_stock_codes(universe_name)
                
                if stock_codes:
                    result[universe_name] = stock_codes
            
            return result
            
        except Exception as e:
            logger.error(f"获取策略股票池失败: {e}")
            return {}
    
    def create_backtest_config_with_selection(self, selection_filename: str, 
                                            strategy_name: str, 
                                            strategy_params: Dict[str, Any],
                                            backtest_params: Dict[str, Any]) -> Dict[str, Any]:
        """基于选股结果创建回测配置"""
        try:
            # 获取选股结果的股票代码
            selector = get_stock_selector()
            stock_codes = selector.get_stock_codes_from_file(selection_filename)
            
            if not stock_codes:
                logger.error(f"无法从选股结果获取股票代码: {selection_filename}")
                return {}
            
            # 创建回测配置
            config = {
                'strategy_name': strategy_name,
                'stock_codes': stock_codes[:20],  # 限制股票数量，避免回测时间过长
                'start_date': backtest_params.get('start_date', '2023-01-01'),
                'end_date': backtest_params.get('end_date', '2024-01-01'),
                'initial_capital': backtest_params.get('initial_capital', 100000),
                'commission': backtest_params.get('commission', 0.001),
                'strategy_params': strategy_params,
                'selection_source': {
                    'filename': selection_filename,
                    'total_stocks': len(stock_codes),
                    'selected_stocks': stock_codes[:20]
                }
            }
            
            return config
            
        except Exception as e:
            logger.error(f"创建回测配置失败: {e}")
            return {}
    
    def create_live_trading_config_with_selection(self, selection_filename: str,
                                                strategy_name: str,
                                                strategy_params: Dict[str, Any],
                                                live_params: Dict[str, Any]) -> Dict[str, Any]:
        """基于选股结果创建实盘交易配置"""
        try:
            # 获取选股结果的股票代码
            selector = get_stock_selector()
            stock_codes = selector.get_stock_codes_from_file(selection_filename)
            
            if not stock_codes:
                logger.error(f"无法从选股结果获取股票代码: {selection_filename}")
                return {}
            
            # 创建实盘交易配置
            config = {
                'strategy_name': strategy_name,
                'stock_codes': stock_codes[:10],  # 实盘交易限制更少的股票数量
                'initial_capital': live_params.get('initial_capital', 100000),
                'commission': live_params.get('commission', 0.001),
                'paper_trading': live_params.get('paper_trading', True),
                'max_positions': live_params.get('max_positions', 5),
                'risk_limit': live_params.get('risk_limit', 0.02),
                'strategy_params': strategy_params,
                'selection_source': {
                    'filename': selection_filename,
                    'total_stocks': len(stock_codes),
                    'selected_stocks': stock_codes[:10]
                }
            }
            
            return config
            
        except Exception as e:
            logger.error(f"创建实盘交易配置失败: {e}")
            return {}
    
    def get_selection_summary(self, selection_filename: str) -> Dict[str, Any]:
        """获取选股结果摘要"""
        try:
            selector = get_stock_selector()
            selected_stocks = selector.load_selection_results(selection_filename)
            
            if not selected_stocks:
                return {}
            
            # 计算统计信息
            scores = [stock.score for stock in selected_stocks]
            
            summary = {
                'filename': selection_filename,
                'total_stocks': len(selected_stocks),
                'selection_date': selected_stocks[0].selection_date if selected_stocks else '',
                'score_stats': {
                    'max_score': max(scores) if scores else 0,
                    'min_score': min(scores) if scores else 0,
                    'avg_score': sum(scores) / len(scores) if scores else 0
                },
                'top_stocks': [
                    {
                        'stock_code': stock.stock_code,
                        'stock_name': stock.stock_name,
                        'score': stock.score
                    }
                    for stock in selected_stocks[:5]
                ],
                'sector_distribution': self._analyze_sector_distribution(selected_stocks),
                'indicator_summary': self._analyze_indicator_summary(selected_stocks)
            }
            
            return summary
            
        except Exception as e:
            logger.error(f"获取选股摘要失败: {e}")
            return {}
    
    def _analyze_sector_distribution(self, selected_stocks) -> Dict[str, int]:
        """分析行业分布（简化版本）"""
        # 这里可以根据股票代码或名称进行行业分类
        # 目前返回简化的分布
        return {
            'technology': len([s for s in selected_stocks if '科技' in s.stock_name or '电子' in s.stock_name]),
            'finance': len([s for s in selected_stocks if '银行' in s.stock_name or '保险' in s.stock_name]),
            'healthcare': len([s for s in selected_stocks if '医药' in s.stock_name or '生物' in s.stock_name]),
            'others': len(selected_stocks) - sum([
                len([s for s in selected_stocks if '科技' in s.stock_name or '电子' in s.stock_name]),
                len([s for s in selected_stocks if '银行' in s.stock_name or '保险' in s.stock_name]),
                len([s for s in selected_stocks if '医药' in s.stock_name or '生物' in s.stock_name])
            ])
        }
    
    def _analyze_indicator_summary(self, selected_stocks) -> Dict[str, Any]:
        """分析技术指标摘要"""
        if not selected_stocks:
            return {}
        
        # 收集所有指标数据
        rsi_values = []
        ma_trends = []
        
        for stock in selected_stocks:
            indicators = stock.indicators
            if 'rsi' in indicators:
                rsi_values.append(indicators['rsi'])
            if 'ma_trend' in indicators:
                ma_trends.append(indicators['ma_trend'])
        
        summary = {}
        
        if rsi_values:
            summary['rsi'] = {
                'avg': sum(rsi_values) / len(rsi_values),
                'min': min(rsi_values),
                'max': max(rsi_values)
            }
        
        if ma_trends:
            trend_counts = {}
            for trend in ma_trends:
                trend_counts[trend] = trend_counts.get(trend, 0) + 1
            summary['ma_trend_distribution'] = trend_counts
        
        return summary

# 全局集成实例
selection_integration = StockSelectionIntegration()
