#!/usr/bin/env python3
"""
股票池管理器 - 将选股结果转换为策略可用的股票池
"""

import json
import os
from pathlib import Path
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass

from backend.core.logger import get_logger

logger = get_logger(__name__)

@dataclass
class StockPoolInfo:
    """股票池信息"""
    name: str
    display_name: str
    description: str
    stock_codes: List[str]
    source: str  # 'selection', 'custom', 'index'
    created_time: datetime
    total_stocks: int
    metadata: Dict[str, Any] = None

class StockPoolManager:
    """股票池管理器"""
    
    def __init__(self):
        self.selection_dir = Path("data/stock_selection")
        self.pool_dir = Path("data/stock_pools")
        self.pool_dir.mkdir(parents=True, exist_ok=True)
        
        # 确保选股结果目录存在
        self.selection_dir.mkdir(parents=True, exist_ok=True)
    
    def get_available_selection_results(self) -> List[Dict[str, Any]]:
        """获取可用的选股结果文件"""
        try:
            results = []
            
            if not self.selection_dir.exists():
                return results
            
            # 扫描选股结果文件
            for file_path in self.selection_dir.glob("*.json"):
                if file_path.name.startswith("latest-"):
                    continue  # 跳过latest文件，避免重复
                
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    
                    selection_info = data.get('selection_info', {})
                    stocks = data.get('stocks', [])
                    
                    results.append({
                        'file_name': file_path.name,
                        'file_path': str(file_path),
                        'custom_name': selection_info.get('custom_name', '未知'),
                        'date': selection_info.get('date', ''),
                        'time': selection_info.get('time', ''),
                        'total_selected': len(stocks),
                        'selection_criteria': selection_info.get('selection_criteria', ''),
                        'created_time': f"{selection_info.get('date', '')} {selection_info.get('time', '')}"
                    })
                    
                except Exception as e:
                    logger.warning(f"读取选股结果文件失败 {file_path}: {e}")
                    continue
            
            # 按创建时间排序（最新的在前）
            results.sort(key=lambda x: x['created_time'], reverse=True)
            
            logger.info(f"找到 {len(results)} 个选股结果文件")
            return results
            
        except Exception as e:
            logger.error(f"获取选股结果失败: {e}")
            return []
    
    def create_pool_from_selection(self, selection_file: str, pool_name: str, 
                                 display_name: str = None, description: str = None,
                                 max_stocks: int = None, score_threshold: float = None) -> bool:
        """从选股结果创建股票池"""
        try:
            # 读取选股结果
            selection_path = self.selection_dir / selection_file
            if not selection_path.exists():
                logger.error(f"选股结果文件不存在: {selection_file}")
                return False
            
            with open(selection_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            selection_info = data.get('selection_info', {})
            stocks = data.get('stocks', [])
            
            if not stocks:
                logger.error(f"选股结果为空: {selection_file}")
                return False
            
            # 应用筛选条件
            filtered_stocks = stocks
            
            # 按评分筛选
            if score_threshold is not None:
                filtered_stocks = [s for s in filtered_stocks if s.get('score', 0) >= score_threshold]
                logger.info(f"按评分筛选 (>={score_threshold}): {len(filtered_stocks)} 只股票")
            
            # 按评分排序
            filtered_stocks.sort(key=lambda x: x.get('score', 0), reverse=True)
            
            # 限制数量
            if max_stocks is not None and len(filtered_stocks) > max_stocks:
                filtered_stocks = filtered_stocks[:max_stocks]
                logger.info(f"限制数量 (前{max_stocks}只): {len(filtered_stocks)} 只股票")
            
            # 提取股票代码
            stock_codes = [stock['stock_code'] for stock in filtered_stocks]
            
            # 创建股票池信息
            pool_info = StockPoolInfo(
                name=pool_name,
                display_name=display_name or f"选股结果_{selection_info.get('custom_name', 'unknown')}",
                description=description or f"基于选股结果创建，原始条件: {selection_info.get('selection_criteria', '')}",
                stock_codes=stock_codes,
                source='selection',
                created_time=datetime.now(),
                total_stocks=len(stock_codes),
                metadata={
                    'source_file': selection_file,
                    'original_total': len(stocks),
                    'score_threshold': score_threshold,
                    'max_stocks': max_stocks,
                    'selection_date': selection_info.get('date'),
                    'selection_criteria': selection_info.get('selection_criteria'),
                    'top_scores': [s.get('score', 0) for s in filtered_stocks[:5]]
                }
            )
            
            # 保存股票池
            return self._save_stock_pool(pool_info)
            
        except Exception as e:
            logger.error(f"从选股结果创建股票池失败: {e}")
            return False
    
    def create_custom_pool(self, pool_name: str, stock_codes: List[str],
                          display_name: str = None, description: str = None) -> bool:
        """创建自定义股票池"""
        try:
            # 验证股票代码格式
            valid_codes = []
            for code in stock_codes:
                code = code.strip().upper()
                if self._is_valid_stock_code(code):
                    valid_codes.append(code)
                else:
                    logger.warning(f"无效的股票代码: {code}")
            
            if not valid_codes:
                logger.error("没有有效的股票代码")
                return False
            
            # 创建股票池信息
            pool_info = StockPoolInfo(
                name=pool_name,
                display_name=display_name or f"自定义股票池_{pool_name}",
                description=description or f"自定义股票池，包含{len(valid_codes)}只股票",
                stock_codes=valid_codes,
                source='custom',
                created_time=datetime.now(),
                total_stocks=len(valid_codes),
                metadata={
                    'invalid_codes': [code for code in stock_codes if code.strip().upper() not in valid_codes]
                }
            )
            
            # 保存股票池
            return self._save_stock_pool(pool_info)
            
        except Exception as e:
            logger.error(f"创建自定义股票池失败: {e}")
            return False
    
    def get_available_pools(self) -> List[Dict[str, Any]]:
        """获取可用的股票池"""
        try:
            pools = []
            
            # 扫描股票池文件
            for file_path in self.pool_dir.glob("*.json"):
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    
                    pools.append({
                        'name': data['name'],
                        'display_name': data['display_name'],
                        'description': data['description'],
                        'source': data['source'],
                        'total_stocks': data['total_stocks'],
                        'created_time': data['created_time'],
                        'file_path': str(file_path)
                    })
                    
                except Exception as e:
                    logger.warning(f"读取股票池文件失败 {file_path}: {e}")
                    continue
            
            # 按创建时间排序
            pools.sort(key=lambda x: x['created_time'], reverse=True)
            
            return pools
            
        except Exception as e:
            logger.error(f"获取股票池列表失败: {e}")
            return []
    
    def get_pool_stocks(self, pool_name: str) -> List[str]:
        """获取股票池的股票代码列表"""
        try:
            pool_file = self.pool_dir / f"{pool_name}.json"
            
            if not pool_file.exists():
                logger.error(f"股票池文件不存在: {pool_name}")
                return []
            
            with open(pool_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            return data.get('stock_codes', [])
            
        except Exception as e:
            logger.error(f"获取股票池股票列表失败: {e}")
            return []
    
    def get_pool_info(self, pool_name: str) -> Optional[Dict[str, Any]]:
        """获取股票池详细信息"""
        try:
            pool_file = self.pool_dir / f"{pool_name}.json"
            
            if not pool_file.exists():
                return None
            
            with open(pool_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            return data
            
        except Exception as e:
            logger.error(f"获取股票池信息失败: {e}")
            return None
    
    def delete_pool(self, pool_name: str) -> bool:
        """删除股票池"""
        try:
            pool_file = self.pool_dir / f"{pool_name}.json"
            
            if pool_file.exists():
                pool_file.unlink()
                logger.info(f"删除股票池: {pool_name}")
                return True
            else:
                logger.warning(f"股票池文件不存在: {pool_name}")
                return False
                
        except Exception as e:
            logger.error(f"删除股票池失败: {e}")
            return False
    
    def _save_stock_pool(self, pool_info: StockPoolInfo) -> bool:
        """保存股票池到文件"""
        try:
            pool_file = self.pool_dir / f"{pool_info.name}.json"
            
            data = {
                'name': pool_info.name,
                'display_name': pool_info.display_name,
                'description': pool_info.description,
                'stock_codes': pool_info.stock_codes,
                'source': pool_info.source,
                'created_time': pool_info.created_time.isoformat(),
                'total_stocks': pool_info.total_stocks,
                'metadata': pool_info.metadata or {}
            }
            
            with open(pool_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"保存股票池: {pool_info.display_name} ({pool_info.total_stocks}只股票)")
            return True
            
        except Exception as e:
            logger.error(f"保存股票池失败: {e}")
            return False
    
    def _is_valid_stock_code(self, code: str) -> bool:
        """验证股票代码格式"""
        if not code or len(code) < 9:
            return False
        
        # 基本格式检查：6位数字.交易所
        parts = code.split('.')
        if len(parts) != 2:
            return False
        
        stock_num, exchange = parts
        
        # 检查股票号码
        if not stock_num.isdigit() or len(stock_num) != 6:
            return False
        
        # 检查交易所
        if exchange not in ['SH', 'SZ']:
            return False
        
        return True

# 全局实例
stock_pool_manager = StockPoolManager()
