#!/usr/bin/env python3
"""
检查项目中是否还有虚假数据生成的脚本
"""

import os
import re
import sys
from pathlib import Path

def check_file_for_fake_data(file_path):
    """检查单个文件是否包含虚假数据生成"""
    fake_data_patterns = [
        r'np\.random\.',
        r'random\.random',
        r'random\.uniform',
        r'random\.randint',
        r'random\.normal',
        r'generate_mock',
        r'mock_data',
        r'simulate_data',
        r'fake_data',
        r'dummy_data',
        r'test_data.*=.*random',
        r'模拟.*数据',
        r'生成.*随机',
        r'随机.*生成',
    ]
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        issues = []
        lines = content.split('\n')
        
        for i, line in enumerate(lines, 1):
            for pattern in fake_data_patterns:
                if re.search(pattern, line, re.IGNORECASE):
                    # 排除注释行和测试文件
                    if (not line.strip().startswith('#') and 
                        not line.strip().startswith('//') and
                        'test_' not in file_path.name.lower()):
                        issues.append((i, line.strip(), pattern))
        
        return issues
        
    except Exception as e:
        print(f"❌ 检查文件失败 {file_path}: {e}")
        return []

def main():
    """主函数"""
    print("🔍 检查项目中的虚假数据生成...")
    
    # 项目根目录
    project_root = Path(__file__).parent
    
    # 需要检查的目录
    check_dirs = [
        project_root / 'backend',
        project_root / 'demos',
    ]
    
    # 需要检查的文件扩展名
    check_extensions = ['.py']
    
    # 排除的目录和文件
    exclude_patterns = [
        'test_',
        '__pycache__',
        '.git',
        'node_modules',
        '.pytest_cache',
        'check_fake_data.py',  # 排除自己
    ]
    
    total_issues = 0
    files_with_issues = 0
    
    for check_dir in check_dirs:
        if not check_dir.exists():
            continue
            
        print(f"\n📂 检查目录: {check_dir}")
        
        for file_path in check_dir.rglob('*'):
            if not file_path.is_file():
                continue
                
            # 检查文件扩展名
            if file_path.suffix not in check_extensions:
                continue
                
            # 排除特定文件和目录
            if any(pattern in str(file_path).lower() for pattern in exclude_patterns):
                continue
            
            issues = check_file_for_fake_data(file_path)
            
            if issues:
                files_with_issues += 1
                total_issues += len(issues)
                
                print(f"\n⚠️  {file_path.relative_to(project_root)}")
                for line_num, line_content, pattern in issues:
                    print(f"   第{line_num}行: {line_content}")
                    print(f"   匹配模式: {pattern}")
    
    print(f"\n{'='*60}")
    print(f"🎯 检查结果:")
    print(f"   发现问题文件: {files_with_issues} 个")
    print(f"   发现问题行数: {total_issues} 行")
    
    if total_issues == 0:
        print("✅ 恭喜！没有发现虚假数据生成代码")
        return True
    else:
        print("❌ 发现虚假数据生成代码，需要修复")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
