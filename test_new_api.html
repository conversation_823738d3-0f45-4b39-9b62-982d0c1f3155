<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试新的多策略API</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        button { padding: 10px 15px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .result { margin-top: 10px; padding: 10px; background: #f8f9fa; border-radius: 3px; white-space: pre-wrap; }
        .error { background: #f8d7da; color: #721c24; }
        .success { background: #d4edda; color: #155724; }
    </style>
</head>
<body>
    <div class="container">
        <h1>测试新的多策略API</h1>
        
        <div class="section">
            <h3>1. 启动策略</h3>
            <button onclick="startStrategy()">启动测试策略</button>
            <div id="startResult" class="result"></div>
        </div>
        
        <div class="section">
            <h3>2. 获取策略列表</h3>
            <button onclick="getStrategies()">获取策略列表</button>
            <div id="strategiesResult" class="result"></div>
        </div>
        
        <div class="section">
            <h3>3. 恢复策略</h3>
            <button onclick="recoverStrategies()">恢复策略</button>
            <div id="recoverResult" class="result"></div>
        </div>
        
        <div class="section">
            <h3>4. 停止策略</h3>
            <input type="text" id="strategyId" placeholder="输入策略ID" style="padding: 8px; margin-right: 10px;">
            <button onclick="stopStrategy()">停止策略</button>
            <div id="stopResult" class="result"></div>
        </div>
        
        <div class="section">
            <h3>5. 检查持久化文件</h3>
            <button onclick="checkPersistence()">检查持久化状态</button>
            <div id="persistenceResult" class="result"></div>
        </div>
    </div>

    <script>
        async function startStrategy() {
            const resultDiv = document.getElementById('startResult');
            resultDiv.textContent = '启动中...';
            
            try {
                const strategyData = {
                    name: '前端测试策略',
                    strategy_type: 'bollinger_bands',
                    config: {
                        initial_capital: 30000,
                        max_positions: 2,
                        risk_limit: 0.01,
                        bb_period: 20,
                        bb_std: 2.0
                    }
                };
                
                const response = await fetch('/api/live/strategies', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(strategyData),
                });
                
                const result = await response.json();
                
                if (response.ok && result.success) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ 启动成功！\n策略ID: ${result.strategy_id}\n消息: ${result.message}`;
                    document.getElementById('strategyId').value = result.strategy_id;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ 启动失败：${result.message || result.detail || '未知错误'}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 请求失败：${error.message}`;
            }
        }
        
        async function getStrategies() {
            const resultDiv = document.getElementById('strategiesResult');
            resultDiv.textContent = '获取中...';
            
            try {
                const response = await fetch('/api/live/strategies');
                const strategies = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ 获取成功！\n策略数量: ${strategies.length}\n\n策略列表:\n${JSON.stringify(strategies, null, 2)}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ 获取失败：${strategies.detail || '未知错误'}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 请求失败：${error.message}`;
            }
        }
        
        async function recoverStrategies() {
            const resultDiv = document.getElementById('recoverResult');
            resultDiv.textContent = '恢复中...';
            
            try {
                const response = await fetch('/api/live/strategies/recover', {
                    method: 'POST',
                });
                
                const result = await response.json();
                
                if (response.ok && result.success) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ 恢复成功！\n${result.message}\n恢复数量: ${result.recovered_count}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ 恢复失败：${result.message || result.detail || '未知错误'}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 请求失败：${error.message}`;
            }
        }
        
        async function stopStrategy() {
            const strategyId = document.getElementById('strategyId').value;
            const resultDiv = document.getElementById('stopResult');
            
            if (!strategyId) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '❌ 请输入策略ID';
                return;
            }
            
            resultDiv.textContent = '停止中...';
            
            try {
                const response = await fetch(`/api/live/strategies/${strategyId}`, {
                    method: 'DELETE',
                });
                
                const result = await response.json();
                
                if (response.ok && result.success) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ 停止成功！\n${result.message}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ 停止失败：${result.message || result.detail || '未知错误'}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 请求失败：${error.message}`;
            }
        }
        
        async function checkPersistence() {
            const resultDiv = document.getElementById('persistenceResult');
            resultDiv.textContent = '检查中...';
            
            try {
                // 这里我们通过获取策略列表来间接检查持久化状态
                const response = await fetch('/api/live/strategies');
                const strategies = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    let message = `✅ 持久化检查完成！\n当前运行策略数量: ${strategies.length}\n`;
                    
                    if (strategies.length > 0) {
                        message += '\n运行中的策略:\n';
                        strategies.forEach(strategy => {
                            message += `- ${strategy.name} (${strategy.id}): ${strategy.status}\n`;
                        });
                    } else {
                        message += '\n没有运行中的策略';
                    }
                    
                    resultDiv.textContent = message;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ 检查失败：${strategies.detail || '未知错误'}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 请求失败：${error.message}`;
            }
        }
        
        // 页面加载时自动获取策略列表
        window.onload = function() {
            getStrategies();
        };
    </script>
</body>
</html>
