#!/usr/bin/env python3
"""
日志系统初始化脚本
在应用启动时调用，确保日志系统正确配置
"""

import os
import sys
from backend.core.logger import logger_manager, get_core_logger

def init_logging():
    """初始化日志系统"""
    logger = get_core_logger('init')
    
    try:
        # 确保日志目录存在
        logger_manager.ensure_log_directory()
        logger.info("日志目录初始化完成")
        
        # 清理旧日志（可选）
        logger_manager.cleanup_old_logs(days=30)
        logger.info("旧日志清理完成")
        
        # 记录系统信息
        logger.info(f"Python版本: {sys.version}")
        logger.info(f"工作目录: {os.getcwd()}")
        logger.info(f"日志目录: {logger_manager.log_dir}")
        
        # 测试各模块日志器
        modules = [
            ('api', 'main'),
            ('data', 'data_manager'),
            ('backtest', 'simple_backtest'),
            ('strategies', 'position_monitor'),
            ('trading', 'live_trader'),
            ('services', 'position_monitor'),
            ('core', 'system')
        ]
        
        for module, name in modules:
            test_logger = logger_manager.get_logger(name, module)
            test_logger.info(f"{module}.{name} 日志器初始化完成")
        
        logger.info("QMT交易系统日志系统初始化完成")
        return True
        
    except Exception as e:
        print(f"日志系统初始化失败: {e}")
        return False

if __name__ == "__main__":
    init_logging()
