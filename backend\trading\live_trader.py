# -*- coding: utf-8 -*-
"""
实盘交易模块
负责与xtquant交互，执行真实交易
"""

import time
import threading
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Callable
import logging
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class OrderStatus(Enum):
    """订单状态"""
    PENDING = "pending"
    SUBMITTED = "submitted"
    FILLED = "filled"
    CANCELLED = "cancelled"
    REJECTED = "rejected"

class OrderSide(Enum):
    """订单方向"""
    BUY = "buy"
    SELL = "sell"

@dataclass
class Order:
    """订单信息"""
    order_id: str
    stock_code: str
    side: OrderSide
    quantity: int
    price: float
    status: OrderStatus
    created_at: datetime
    filled_at: Optional[datetime] = None
    filled_price: Optional[float] = None
    filled_quantity: Optional[int] = None
    message: str = ""

@dataclass
class Position:
    """持仓信息"""
    stock_code: str
    quantity: int
    avg_price: float
    market_value: float
    pnl: float
    pnl_ratio: float

@dataclass
class Account:
    """账户信息"""
    account_id: str
    total_value: float
    available_cash: float
    market_value: float
    positions: List[Position]
    updated_at: datetime

class LiveTrader:
    """实盘交易器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.account_id = config.get('account_id', '')
        self.broker = config.get('broker', 'xtquant')
        self.paper_trading = config.get('paper_trading', True)
        self.enable_trading = config.get('enable_trading', False)
        
        # 交易状态
        self.is_connected = False
        self.is_trading = False
        self.orders = {}  # order_id -> Order
        self.positions = {}  # stock_code -> Position
        self.account_info = None
        
        # 回调函数
        self.order_callback: Optional[Callable] = None
        self.position_callback: Optional[Callable] = None
        
        # xtquant相关
        self.xt_trader = None
        self.xt_account = None
        
        # 初始化
        self._init_trader()
    
    def _init_trader(self):
        """初始化交易器"""
        if not self.enable_trading:
            logger.info("实盘交易未启用，仅模拟模式")
            return
        
        if self.paper_trading:
            logger.info("启用模拟交易模式")
            self._init_paper_trading()
        else:
            logger.info("启用实盘交易模式")
            self._init_live_trading()
    
    def _init_paper_trading(self):
        """初始化模拟交易"""
        # 模拟账户信息
        self.account_info = Account(
            account_id=self.account_id,
            total_value=1000000.0,  # 100万模拟资金
            available_cash=1000000.0,
            market_value=0.0,
            positions=[],
            updated_at=datetime.now()
        )
        self.is_connected = True
        logger.info("模拟交易初始化完成")
    
    def _init_live_trading(self):
        """初始化实盘交易"""
        try:
            from xtquant import xttrader
            from xtquant.xttype import StockAccount
            
            # 创建交易实例
            # 使用固定的session_id避免随机性，基于账户ID生成
            import hashlib
            account_hash = int(hashlib.md5(str(self.account_id).encode()).hexdigest()[:8], 16)
            session_id = 100000 + (account_hash % 900000)
            self.xt_trader = xttrader.XtQuantTrader(
                path=self.config.get('xt_path', ''),
                session_id=session_id
            )
            
            # 启动并连接
            self.xt_trader.start()
            connect_result = self.xt_trader.connect()
            
            if connect_result == 0:
                # 创建账户
                self.xt_account = StockAccount(self.account_id)
                subscribe_result = self.xt_trader.subscribe(self.xt_account)
                
                if subscribe_result == 0:
                    self.is_connected = True
                    logger.info(f"实盘交易连接成功: {self.account_id}")
                    
                    # 获取账户信息
                    self._update_account_info()
                else:
                    logger.error("账户订阅失败")
            else:
                logger.error("xtquant连接失败")
                
        except ImportError:
            logger.error("xtquant模块不可用")
        except Exception as e:
            logger.error(f"实盘交易初始化失败: {e}")
    
    def _update_account_info(self):
        """更新账户信息"""
        if self.paper_trading:
            self._update_paper_account()
        else:
            self._update_live_account()
    
    def _update_paper_account(self):
        """更新模拟账户信息"""
        # 计算持仓市值
        total_market_value = 0
        positions = []
        
        for stock_code, position in self.positions.items():
            # 这里应该获取实时价格，暂时使用持仓均价
            current_price = position.avg_price * 1.01  # 模拟价格变动
            market_value = position.quantity * current_price
            pnl = market_value - (position.quantity * position.avg_price)
            pnl_ratio = pnl / (position.quantity * position.avg_price) if position.avg_price > 0 else 0
            
            updated_position = Position(
                stock_code=stock_code,
                quantity=position.quantity,
                avg_price=position.avg_price,
                market_value=market_value,
                pnl=pnl,
                pnl_ratio=pnl_ratio
            )
            
            positions.append(updated_position)
            total_market_value += market_value
        
        # 更新账户信息
        self.account_info = Account(
            account_id=self.account_id,
            total_value=self.account_info.available_cash + total_market_value,
            available_cash=self.account_info.available_cash,
            market_value=total_market_value,
            positions=positions,
            updated_at=datetime.now()
        )
    
    def _update_live_account(self):
        """更新实盘账户信息"""
        if not self.is_connected or not self.xt_trader:
            return
        
        try:
            # 获取资金信息 - 使用正确的导入方式
            from xtquant import get_trade_detail_data

            # 获取账户资金
            account_data = get_trade_detail_data(self.account_id, 'stock', 'account')
            if account_data:
                account = account_data[0]
                available_cash = account.m_dAvailable
                total_value = account.m_dTotalAsset
            else:
                available_cash = 0
                total_value = 0
            
            # 获取持仓信息
            position_data = get_trade_detail_data(self.account_id, 'stock', 'position')
            positions = []
            total_market_value = 0
            
            if position_data:
                for pos in position_data:
                    if pos.m_nVolume > 0:
                        stock_code = f"{pos.m_strInstrumentID}.{pos.m_strExchangeID}"
                        market_value = pos.m_nVolume * pos.m_dLastPrice
                        pnl = market_value - (pos.m_nVolume * pos.m_dOpenPrice)
                        pnl_ratio = pnl / (pos.m_nVolume * pos.m_dOpenPrice) if pos.m_dOpenPrice > 0 else 0
                        
                        position = Position(
                            stock_code=stock_code,
                            quantity=pos.m_nVolume,
                            avg_price=pos.m_dOpenPrice,
                            market_value=market_value,
                            pnl=pnl,
                            pnl_ratio=pnl_ratio
                        )
                        positions.append(position)
                        total_market_value += market_value
            
            # 更新账户信息
            self.account_info = Account(
                account_id=self.account_id,
                total_value=total_value,
                available_cash=available_cash,
                market_value=total_market_value,
                positions=positions,
                updated_at=datetime.now()
            )
            
        except Exception as e:
            logger.error(f"更新实盘账户信息失败: {e}")
    
    def place_order(self, stock_code: str, side: OrderSide, quantity: int, 
                   price: Optional[float] = None) -> Optional[str]:
        """下单"""
        if not self.is_connected:
            logger.error("交易器未连接")
            return None
        
        # 生成订单ID
        order_id = f"{int(time.time() * 1000)}_{stock_code}_{side.value}"
        
        # 创建订单
        order = Order(
            order_id=order_id,
            stock_code=stock_code,
            side=side,
            quantity=quantity,
            price=price or 0.0,
            status=OrderStatus.PENDING,
            created_at=datetime.now()
        )
        
        if self.paper_trading:
            # 模拟交易
            success = self._place_paper_order(order)
        else:
            # 实盘交易
            success = self._place_live_order(order)
        
        if success:
            self.orders[order_id] = order
            logger.info(f"订单提交成功: {order_id} {side.value} {stock_code} {quantity}股")
            
            # 触发回调
            if self.order_callback:
                self.order_callback(order)
            
            return order_id
        else:
            logger.error(f"订单提交失败: {side.value} {stock_code} {quantity}股")
            return None
    
    def _place_paper_order(self, order: Order) -> bool:
        """模拟下单"""
        try:
            # 模拟订单处理延迟
            def process_order():
                time.sleep(1)  # 模拟1秒延迟
                
                # 模拟成交
                order.status = OrderStatus.FILLED
                order.filled_at = datetime.now()
                order.filled_price = order.price if order.price > 0 else 10.0  # 模拟价格
                order.filled_quantity = order.quantity
                
                # 更新持仓
                self._update_position_after_fill(order)
                
                # 触发回调
                if self.order_callback:
                    self.order_callback(order)
            
            # 异步处理
            threading.Thread(target=process_order).start()
            
            order.status = OrderStatus.SUBMITTED
            return True
            
        except Exception as e:
            logger.error(f"模拟下单失败: {e}")
            order.status = OrderStatus.REJECTED
            order.message = str(e)
            return False
    
    def _place_live_order(self, order: Order) -> bool:
        """实盘下单"""
        if not self.xt_trader or not self.xt_account:
            return False
        
        try:
            from xtquant.xttrader import passorder
            
            # 确定订单类型
            order_type = 23 if order.side == OrderSide.BUY else 24  # 买入/卖出
            
            # 提交订单
            result = passorder(
                optype=order_type,
                ordertype=1101,  # 限价单
                accountid=self.account_id,
                ordercode=order.stock_code,
                prtype=5,  # 股票
                volumetype=-1,
                volume=order.quantity,
                price=order.price,
                strategyName='',
                quickTrade=0,
                userID='',
                ContextInfo=None
            )
            
            if result and result > 0:
                order.status = OrderStatus.SUBMITTED
                return True
            else:
                order.status = OrderStatus.REJECTED
                order.message = "订单提交失败"
                return False
                
        except Exception as e:
            logger.error(f"实盘下单失败: {e}")
            order.status = OrderStatus.REJECTED
            order.message = str(e)
            return False
    
    def _update_position_after_fill(self, order: Order):
        """订单成交后更新持仓"""
        stock_code = order.stock_code
        
        if order.side == OrderSide.BUY:
            # 买入
            if stock_code in self.positions:
                # 已有持仓，计算新的均价
                old_pos = self.positions[stock_code]
                total_quantity = old_pos.quantity + order.filled_quantity
                total_cost = (old_pos.quantity * old_pos.avg_price + 
                            order.filled_quantity * order.filled_price)
                new_avg_price = total_cost / total_quantity
                
                self.positions[stock_code] = Position(
                    stock_code=stock_code,
                    quantity=total_quantity,
                    avg_price=new_avg_price,
                    market_value=total_quantity * order.filled_price,
                    pnl=0,
                    pnl_ratio=0
                )
            else:
                # 新建持仓
                self.positions[stock_code] = Position(
                    stock_code=stock_code,
                    quantity=order.filled_quantity,
                    avg_price=order.filled_price,
                    market_value=order.filled_quantity * order.filled_price,
                    pnl=0,
                    pnl_ratio=0
                )
            
            # 扣减现金
            cost = order.filled_quantity * order.filled_price
            self.account_info.available_cash -= cost
            
        else:
            # 卖出
            if stock_code in self.positions:
                old_pos = self.positions[stock_code]
                remaining_quantity = old_pos.quantity - order.filled_quantity
                
                if remaining_quantity <= 0:
                    # 清仓
                    del self.positions[stock_code]
                else:
                    # 部分卖出
                    self.positions[stock_code] = Position(
                        stock_code=stock_code,
                        quantity=remaining_quantity,
                        avg_price=old_pos.avg_price,
                        market_value=remaining_quantity * order.filled_price,
                        pnl=0,
                        pnl_ratio=0
                    )
                
                # 增加现金
                proceeds = order.filled_quantity * order.filled_price
                self.account_info.available_cash += proceeds
        
        # 触发持仓回调
        if self.position_callback:
            self.position_callback(self.positions)
    
    def cancel_order(self, order_id: str) -> bool:
        """撤单"""
        if order_id not in self.orders:
            logger.error(f"订单不存在: {order_id}")
            return False
        
        order = self.orders[order_id]
        
        if order.status not in [OrderStatus.PENDING, OrderStatus.SUBMITTED]:
            logger.error(f"订单状态不允许撤销: {order.status}")
            return False
        
        # 执行撤单
        order.status = OrderStatus.CANCELLED
        logger.info(f"订单已撤销: {order_id}")
        
        # 触发回调
        if self.order_callback:
            self.order_callback(order)
        
        return True
    
    def get_account_info(self) -> Optional[Account]:
        """获取账户信息"""
        self._update_account_info()
        return self.account_info
    
    def get_positions(self) -> Dict[str, Position]:
        """获取持仓信息"""
        return self.positions.copy()
    
    def get_orders(self, status: Optional[OrderStatus] = None) -> List[Order]:
        """获取订单列表"""
        orders = list(self.orders.values())
        
        if status:
            orders = [order for order in orders if order.status == status]
        
        return orders
    
    def set_order_callback(self, callback: Callable):
        """设置订单回调"""
        self.order_callback = callback
    
    def set_position_callback(self, callback: Callable):
        """设置持仓回调"""
        self.position_callback = callback
    
    def disconnect(self):
        """断开连接"""
        if self.xt_trader:
            try:
                self.xt_trader.stop()
            except:
                pass
        
        self.is_connected = False
        logger.info("交易器已断开连接")

# 全局实盘交易器实例
live_trader = None

def get_live_trader(config: Dict[str, Any] = None) -> LiveTrader:
    """获取实盘交易器实例"""
    global live_trader
    
    if live_trader is None and config:
        live_trader = LiveTrader(config)
    
    return live_trader
