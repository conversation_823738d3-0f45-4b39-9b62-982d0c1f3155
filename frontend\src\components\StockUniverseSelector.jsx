import React, { useState, useEffect } from 'react';
import { Select, SelectItem } from './UI/select.jsx';
import { Button } from './UI/button.jsx';
import { Input } from './UI/input.jsx';
import { Modal } from './UI/modal.jsx';
import { Plus, Info } from 'lucide-react';
import { dataAPI, stockPoolAPI, apiUtils } from '../services/api';

const StockUniverseSelector = ({
  value,
  onChange,
  allowCustom = true,
  allowCreate = false,
  className = ""
}) => {
  const [stockPoolType, setStockPoolType] = useState('default');
  const [stockPools, setStockPools] = useState([]);
  const [stockList, setStockList] = useState([]);
  const [loading, setLoading] = useState(false);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [selectedUniverse, setSelectedUniverse] = useState(null);
  const [customStocks, setCustomStocks] = useState('');
  const [createForm, setCreateForm] = useState({
    name: '',
    display_name: '',
    description: '',
    stock_codes: ''
  });

  // 加载股票池列表（用户创建的股票池）
  const loadStockPools = async () => {
    try {
      const response = await stockPoolAPI.getStockPools();
      if (apiUtils.isSuccess(response)) {
        const data = apiUtils.getData(response);
        // 转换API数据格式以匹配组件期望的结构
        const transformedData = (data || []).map((pool) => ({
          id: pool.pool_id, // 使用pool_id作为唯一标识符
          name: pool.pool_name, // 使用pool_name作为显示名称
          stock_count: pool.stock_count || 0,
          description: pool.description || '',
          category: pool.category || 'custom',
          created_date: pool.created_date,
          updated_date: pool.updated_date
        }));
        setStockPools(transformedData);
      }
    } catch (error) {
      console.error('加载股票池失败:', error);
    }
  };

  // 加载股票列表（用于单只股票选择）
  const loadStockList = async () => {
    try {
      const response = await dataAPI.getStockList({ page: 1, page_size: 100 });
      if (apiUtils.isSuccess(response)) {
        const data = apiUtils.getData(response);
        // API返回的数据结构是 {success: true, data: [...]}
        // 而不是 {success: true, data: {data: [...]}}
        setStockList(data || []);
      }
    } catch (error) {
      console.error('获取股票列表失败:', error);
    }
  };

  useEffect(() => {
    loadStockPools();
    loadStockList();
  }, []);

  // 处理股票池选择
  const handleUniverseChange = async (universeValue) => {
    if (universeValue === 'custom') {
      // 自定义模式
      onChange({
        type: 'custom',
        stock_codes: customStocks.split(',').map(code => code.trim()).filter(code => code)
      });
    } else if (universeValue) {
      // 预设股票池
      try {
        const response = await fetch(`/api/data/universes/${universeValue}`);
        const data = await response.json();
        
        if (data.success) {
          onChange({
            type: 'universe',
            universe_name: universeValue,
            stock_codes: data.data.stock_codes
          });
        }
      } catch (error) {
        console.error('获取股票池详情失败:', error);
      }
    }
  };

  // 查看股票池详情
  const showUniverseDetail = async (universeName) => {
    try {
      const response = await fetch(`/api/data/universes/${universeName}`);
      const data = await response.json();
      
      if (data.success) {
        setSelectedUniverse(data.data);
        setShowDetailModal(true);
      }
    } catch (error) {
      console.error('获取股票池详情失败:', error);
    }
  };

  // 创建新股票池
  const handleCreateUniverse = async () => {
    try {
      const stockCodes = createForm.stock_codes
        .split(',')
        .map(code => code.trim())
        .filter(code => code);

      const response = await fetch('/api/data/universes', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: createForm.name,
          display_name: createForm.display_name,
          description: createForm.description,
          universe_type: 'custom',
          stock_codes: stockCodes
        })
      });

      const data = await response.json();
      
      if (data.success) {
        setShowCreateModal(false);
        setCreateForm({ name: '', display_name: '', description: '', stock_codes: '' });
        loadStockPools(); // 重新加载列表
        
        // 自动选择新创建的股票池
        // handleUniverseChange(createForm.name); // 暂时注释掉，因为UI结构已改变
      } else {
        alert('创建失败: ' + data.message);
      }
    } catch (error) {
      console.error('创建股票池失败:', error);
      alert('创建失败: ' + error.message);
    }
  };

  return (
    <div className={`space-y-3 ${className}`}>
      {/* 股票池类型选择 */}
      <div className="space-y-2">
        <label className="block text-sm font-medium text-gray-700">股票池类型</label>
        <Select
          value={stockPoolType}
          onValueChange={(type) => {
            setStockPoolType(type);
            // 根据类型更新onChange回调
            if (type === 'default') {
              onChange({ type: 'default' });
            } else if (type === 'custom_pool') {
              onChange({ type: 'custom_pool', pool_id: null });
            } else if (type === 'single_stock') {
              onChange({ type: 'single_stock', stock_code: null });
            }
          }}
          className="w-full"
        >
          <SelectItem value="default">默认股票池（系统自动选择）</SelectItem>
          <SelectItem value="custom_pool">自定义股票池</SelectItem>
          <SelectItem value="single_stock">单只股票</SelectItem>
        </Select>
      </div>

      {/* 自定义股票池选择 */}
      {stockPoolType === 'custom_pool' && (
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">选择股票池</label>
          <div className="flex gap-2">
            <Select
              value={value?.pool_id || ''}
              onValueChange={async (poolId) => {
                const selectedPool = stockPools.find(pool => pool.id === poolId);
                if (selectedPool) {
                  // 获取股票池详细信息，包括股票代码列表
                  try {
                    const detailResponse = await stockPoolAPI.getStockPool(poolId);
                    let stockCodes = [];
                    if (apiUtils.isSuccess(detailResponse)) {
                      const detailData = apiUtils.getData(detailResponse);
                      // 从stocks数组中提取stock_code
                      stockCodes = (detailData.stocks || []).map(stock => stock.stock_code);
                    }

                    onChange({
                      type: 'custom_pool',
                      pool_id: poolId,
                      pool_name: selectedPool.name,
                      stock_codes: stockCodes,
                      description: selectedPool.description
                    });
                  } catch (error) {
                    console.error('获取股票池详情失败:', error);
                    // 即使获取详情失败，也要传递基本信息
                    onChange({
                      type: 'custom_pool',
                      pool_id: poolId,
                      pool_name: selectedPool.name,
                      stock_codes: [],
                      description: selectedPool.description
                    });
                  }
                }
              }}
              placeholder="选择自定义股票池"
              className="flex-1"
            >
              {Array.isArray(stockPools) && stockPools.length > 0 ? (
                stockPools.map(pool => {
                  // 确保pool对象存在且有必要的属性
                  if (!pool || !pool.id) {
                    return null;
                  }
                  return (
                    <SelectItem key={pool.id} value={pool.id}>
                      {pool.name || '未命名股票池'} ({pool.stock_count || 0}只股票)
                    </SelectItem>
                  );
                })
              ) : (
                <SelectItem value="no-pools" disabled>
                  暂无可用的股票池
                </SelectItem>
              )}
            </Select>

            {/* 创建新股票池按钮 */}
            {allowCreate && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowCreateModal(true)}
              >
                <Plus className="w-4 h-4" />
              </Button>
            )}
          </div>
        </div>
      )}

      {/* 单只股票选择 */}
      {stockPoolType === 'single_stock' && (
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">选择股票</label>
          <Select
            value={value?.stock_code || ''}
            onValueChange={(stockCode) => {
              const selectedStock = stockList.find(stock => stock.code === stockCode);
              onChange({
                type: 'single_stock',
                stock_code: stockCode,
                stock_name: selectedStock?.name || '',
                stock_codes: [stockCode]
              });
            }}
            placeholder="选择单只股票进行交易"
            className="w-full"
          >
            {Array.isArray(stockList) && stockList.length > 0 ? (
              stockList.map(stock => {
                // 确保stock对象存在且有必要的属性
                if (!stock || !stock.code) {
                  return null;
                }
                return (
                  <SelectItem key={stock.code} value={stock.code}>
                    {stock.code} - {stock.name || '未知股票'}
                  </SelectItem>
                );
              })
            ) : (
              <SelectItem value="loading" disabled>
                加载中...
              </SelectItem>
            )}
          </Select>
        </div>
      )}

      {/* 当前选择显示 */}
      {value && (
        <div className="text-sm text-gray-600 bg-gray-50 p-3 rounded-md">
          {value.type === 'default' && (
            <div>
              <span className="font-medium">默认股票池</span>
              <div className="text-xs text-gray-500 mt-1">系统将自动选择合适的股票进行交易</div>
            </div>
          )}
          {value.type === 'custom_pool' && value.pool_name && (
            <div>
              <span className="font-medium">自定义股票池: {value.pool_name}</span>
              <div className="text-xs text-gray-500 mt-1">
                包含 {value.stock_codes?.length || 0} 只股票
                {value.stock_codes && value.stock_codes.length <= 10 && (
                  <div className="mt-1">{value.stock_codes.join(', ')}</div>
                )}
              </div>
            </div>
          )}
          {value.type === 'single_stock' && value.stock_code && (
            <div>
              <span className="font-medium">单只股票: {value.stock_code}</span>
              {value.stock_name && (
                <span className="text-gray-500"> - {value.stock_name}</span>
              )}
            </div>
          )}
        </div>
      )}

      {/* 股票池详情模态框 */}
      <Modal
        title="股票池详情"
        open={showDetailModal}
        onClose={() => setShowDetailModal(false)}
        width="600px"
      >
        {selectedUniverse && (
          <div className="space-y-4">
            <div>
              <h3 className="font-medium">{selectedUniverse.display_name}</h3>
              <p className="text-sm text-gray-600 mt-1">{selectedUniverse.description}</p>
            </div>
            
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="font-medium">股票数量:</span> {selectedUniverse.stock_count}
              </div>
              <div>
                <span className="font-medium">类型:</span> {selectedUniverse.universe_type}
              </div>
              <div>
                <span className="font-medium">创建时间:</span> {new Date(selectedUniverse.created_time).toLocaleDateString()}
              </div>
              <div>
                <span className="font-medium">更新时间:</span> {new Date(selectedUniverse.updated_time).toLocaleDateString()}
              </div>
            </div>

            <div>
              <h4 className="font-medium mb-2">股票列表:</h4>
              <div className="max-h-40 overflow-y-auto bg-gray-50 p-3 rounded text-sm">
                {selectedUniverse.stock_codes.join(', ')}
              </div>
            </div>
          </div>
        )}
      </Modal>

      {/* 创建股票池模态框 */}
      <Modal
        title="创建新股票池"
        open={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        width="600px"
      >
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-1">股票池名称 *</label>
            <Input
              value={createForm.name}
              onChange={(e) => setCreateForm(prev => ({ ...prev, name: e.target.value }))}
              placeholder="请输入股票池名称（英文，用于系统标识）"
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-1">显示名称 *</label>
            <Input
              value={createForm.display_name}
              onChange={(e) => setCreateForm(prev => ({ ...prev, display_name: e.target.value }))}
              placeholder="请输入显示名称（中文，用于界面显示）"
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-1">描述</label>
            <Input
              value={createForm.description}
              onChange={(e) => setCreateForm(prev => ({ ...prev, description: e.target.value }))}
              placeholder="请输入股票池描述"
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-1">股票代码 *</label>
            <Input
              value={createForm.stock_codes}
              onChange={(e) => setCreateForm(prev => ({ ...prev, stock_codes: e.target.value }))}
              placeholder="请输入股票代码，用逗号分隔，如：000001.SZ,000002.SZ"
            />
          </div>

          <div className="flex justify-end gap-2 pt-4">
            <Button
              variant="outline"
              onClick={() => setShowCreateModal(false)}
            >
              取消
            </Button>
            <Button
              onClick={handleCreateUniverse}
              disabled={!createForm.name || !createForm.display_name || !createForm.stock_codes}
            >
              创建
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default StockUniverseSelector;
