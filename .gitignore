# QMT-TRADER 项目 .gitignore 文件
# 适用于Python后端 + React前端的量化交易平台

# ================================
# Python 相关
# ================================

# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
#   According to pypa/pipenv#598, it is recommended to include Pipfile.lock in version control.
#   However, in case of collaboration, if having platform-specific dependencies or dependencies
#   having no cross-platform support, pipenv may install dependencies that don't work, or not
#   install all needed dependencies.
#Pipfile.lock

# PEP 582; used by e.g. github.com/David-OConnor/pyflow
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# ================================
# Node.js / React 相关
# ================================

# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage (https://gruntjs.com/creating-plugins#storing-task-files)
.grunt

# Bower dependency directory (https://bower.io/)
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons (https://nodejs.org/api/addons.html)
build/Release

# Dependency directories
jspm_packages/

# TypeScript v1 declaration files
typings/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env.local
.env.development.local
.env.test.local
.env.production.local

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# ================================
# 量化交易平台特定文件
# ================================

# 数据文件
data/
*.csv
*.h5
*.hdf5
*.pkl
*.pickle

# 日志文件
logs/
*.log
*.log.*

# 回测结果
results/
backtest_results/
*.json
*.xlsx

# 配置文件（包含敏感信息）
config/
*.ini
*.conf
settings.py
local_settings.py

# QMT相关
userdata_mini/
xtquant/
*.qmt

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 缓存文件
cache/
.cache/

# 临时文件
tmp/
temp/
*.tmp
*.temp

# ================================
# IDE 和编辑器
# ================================

# Visual Studio Code
.vscode/
*.code-workspace

# PyCharm
.idea/
*.iml
*.ipr
*.iws

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# ================================
# 操作系统
# ================================

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.stackdump
[Dd]esktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# ================================
# 安全和敏感信息
# ================================

# API密钥和令牌
*.key
*.pem
*.p12
*.pfx
secrets.json
credentials.json

# 账户信息
account_info.json
trading_config.json

# 私有配置
private_config.py
local_config.py

# ================================
# 构建和部署
# ================================

# 构建输出
build/
dist/
out/

# 部署文件
deploy/
deployment/

# Docker
Dockerfile.local
docker-compose.override.yml

# ================================
# 测试文件
# ================================

# 测试输出
test_results/
test_output/
coverage_html_report/

# 测试数据
test_data/
mock_data/

# ================================
# 文档
# ================================

# 生成的文档
docs/_build/
docs/build/

# ================================
# 其他
# ================================

# 备份文件
*.bak
*.backup
*.old

# 压缩文件
*.zip
*.tar.gz
*.rar
*.7z

# 大文件
*.bin
*.exe
*.dmg
*.pkg

# 自定义忽略（项目特定）
# 在这里添加项目特定的忽略规则

# 示例数据（如果不需要版本控制）
# sample_data/
# example_configs/

# 性能分析文件
*.prof
*.profile

# 内存转储
*.dump
*.dmp
