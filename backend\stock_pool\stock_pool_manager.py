# -*- coding: utf-8 -*-
"""
股票池管理模块
提供股票池的创建、添加、删除、查看等基础功能
支持将智能选股结果加入股票池
"""

import os
import json
import uuid
from datetime import datetime
from typing import List, Dict, Any, Optional
from pathlib import Path
from dataclasses import dataclass, asdict
from backend.core.logger import get_logger

logger = get_logger(__name__)

@dataclass
class StockPoolItem:
    """股票池项目"""
    stock_code: str
    stock_name: str
    added_date: str
    added_reason: str = ""
    tags: List[str] = None
    notes: str = ""
    
    def __post_init__(self):
        if self.tags is None:
            self.tags = []

@dataclass
class StockPool:
    """股票池"""
    pool_id: str
    pool_name: str
    description: str
    created_date: str
    updated_date: str
    category: str = "custom"  # custom, selection, strategy
    stocks: List[StockPoolItem] = None
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.stocks is None:
            self.stocks = []
        if self.metadata is None:
            self.metadata = {}

class StockPoolManager:
    """股票池管理器"""
    
    def __init__(self):
        self.pools_dir = Path("data/stock_pools")
        self.pools_dir.mkdir(parents=True, exist_ok=True)
        
        # 初始化默认股票池
        self._init_default_pools()
    
    def _init_default_pools(self):
        """初始化默认股票池"""
        default_pools = [
            {
                "pool_name": "自选股",
                "description": "用户自定义的自选股票池",
                "category": "custom"
            },
            {
                "pool_name": "关注股",
                "description": "重点关注的股票池",
                "category": "custom"
            },
            {
                "pool_name": "价值股",
                "description": "价值投资相关股票",
                "category": "strategy"
            },
            {
                "pool_name": "成长股",
                "description": "成长投资相关股票",
                "category": "strategy"
            }
        ]
        
        for pool_config in default_pools:
            if not self._pool_exists(pool_config["pool_name"]):
                self.create_pool(
                    pool_name=pool_config["pool_name"],
                    description=pool_config["description"],
                    category=pool_config["category"]
                )
    
    def _pool_exists(self, pool_name: str) -> bool:
        """检查股票池是否存在"""
        pools = self.list_pools()
        return any(pool["pool_name"] == pool_name for pool in pools)
    
    def create_pool(self, pool_name: str, description: str = "", category: str = "custom") -> str:
        """创建股票池"""
        pool_id = str(uuid.uuid4())
        current_time = datetime.now().isoformat()
        
        pool = StockPool(
            pool_id=pool_id,
            pool_name=pool_name,
            description=description,
            created_date=current_time,
            updated_date=current_time,
            category=category,
            stocks=[],
            metadata={}
        )
        
        # 保存到文件
        pool_file = self.pools_dir / f"{pool_id}.json"
        with open(pool_file, 'w', encoding='utf-8') as f:
            json.dump(asdict(pool), f, ensure_ascii=False, indent=2)
        
        logger.info(f"创建股票池成功: {pool_name} (ID: {pool_id})")
        return pool_id
    
    def list_pools(self) -> List[Dict[str, Any]]:
        """获取所有股票池列表"""
        pools = []
        
        for pool_file in self.pools_dir.glob("*.json"):
            try:
                with open(pool_file, 'r', encoding='utf-8') as f:
                    pool_data = json.load(f)
                
                pools.append({
                    "pool_id": pool_data["pool_id"],
                    "pool_name": pool_data["pool_name"],
                    "description": pool_data["description"],
                    "category": pool_data.get("category", "custom"),
                    "stock_count": len(pool_data.get("stocks", [])),
                    "created_date": pool_data["created_date"],
                    "updated_date": pool_data["updated_date"]
                })
            except Exception as e:
                logger.error(f"读取股票池文件失败 {pool_file}: {e}")
                continue
        
        # 按更新时间排序
        pools.sort(key=lambda x: x["updated_date"], reverse=True)
        return pools
    
    def get_pool(self, pool_id: str) -> Optional[StockPool]:
        """获取股票池详情"""
        pool_file = self.pools_dir / f"{pool_id}.json"
        
        if not pool_file.exists():
            return None
        
        try:
            with open(pool_file, 'r', encoding='utf-8') as f:
                pool_data = json.load(f)
            
            # 转换为StockPool对象
            stocks = [StockPoolItem(**stock) for stock in pool_data.get("stocks", [])]
            pool_data["stocks"] = stocks
            
            return StockPool(**pool_data)
        except Exception as e:
            logger.error(f"读取股票池失败 {pool_id}: {e}")
            return None
    
    def update_pool(self, pool_id: str, pool_name: str = None, description: str = None) -> bool:
        """更新股票池基本信息"""
        pool = self.get_pool(pool_id)
        if not pool:
            return False
        
        if pool_name is not None:
            pool.pool_name = pool_name
        if description is not None:
            pool.description = description
        
        pool.updated_date = datetime.now().isoformat()
        
        return self._save_pool(pool)
    
    def delete_pool(self, pool_id: str) -> bool:
        """删除股票池"""
        pool_file = self.pools_dir / f"{pool_id}.json"
        
        if not pool_file.exists():
            return False
        
        try:
            pool_file.unlink()
            logger.info(f"删除股票池成功: {pool_id}")
            return True
        except Exception as e:
            logger.error(f"删除股票池失败 {pool_id}: {e}")
            return False
    
    def add_stock(self, pool_id: str, stock_code: str, stock_name: str, 
                  added_reason: str = "", tags: List[str] = None, notes: str = "") -> bool:
        """向股票池添加股票"""
        pool = self.get_pool(pool_id)
        if not pool:
            return False
        
        # 检查股票是否已存在
        if any(stock.stock_code == stock_code for stock in pool.stocks):
            logger.warning(f"股票 {stock_code} 已存在于股票池 {pool_id} 中")
            return False
        
        # 添加股票
        stock_item = StockPoolItem(
            stock_code=stock_code,
            stock_name=stock_name,
            added_date=datetime.now().isoformat(),
            added_reason=added_reason,
            tags=tags or [],
            notes=notes
        )
        
        pool.stocks.append(stock_item)
        pool.updated_date = datetime.now().isoformat()
        
        return self._save_pool(pool)
    
    def remove_stock(self, pool_id: str, stock_code: str) -> bool:
        """从股票池移除股票"""
        pool = self.get_pool(pool_id)
        if not pool:
            return False
        
        # 查找并移除股票
        original_count = len(pool.stocks)
        pool.stocks = [stock for stock in pool.stocks if stock.stock_code != stock_code]
        
        if len(pool.stocks) == original_count:
            logger.warning(f"股票 {stock_code} 不存在于股票池 {pool_id} 中")
            return False
        
        pool.updated_date = datetime.now().isoformat()
        return self._save_pool(pool)
    
    def update_stock(self, pool_id: str, stock_code: str, **kwargs) -> bool:
        """更新股票池中的股票信息"""
        pool = self.get_pool(pool_id)
        if not pool:
            return False
        
        # 查找股票
        stock_item = None
        for stock in pool.stocks:
            if stock.stock_code == stock_code:
                stock_item = stock
                break
        
        if not stock_item:
            logger.warning(f"股票 {stock_code} 不存在于股票池 {pool_id} 中")
            return False
        
        # 更新股票信息
        for key, value in kwargs.items():
            if hasattr(stock_item, key):
                setattr(stock_item, key, value)
        
        pool.updated_date = datetime.now().isoformat()
        return self._save_pool(pool)
    
    def add_selection_results(self, selection_results: List[Dict[str, Any]],
                             pool_name: str = None, create_new_pool: bool = True) -> Dict[str, Any]:
        """将智能选股结果加入股票池

        Args:
            selection_results: 选股结果列表
            pool_name: 目标股票池名称，如果为None则自动生成
            create_new_pool: 是否创建新股票池，False则添加到现有池

        Returns:
            Dict包含操作结果信息
        """
        if not selection_results:
            return {"success": False, "message": "选股结果为空"}

        # 生成股票池名称
        if not pool_name:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            pool_name = f"智能选股_{timestamp}"

        pool_id = None

        if create_new_pool:
            # 创建新股票池
            description = f"智能选股结果，共{len(selection_results)}只股票，创建时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            pool_id = self.create_pool(
                pool_name=pool_name,
                description=description,
                category="selection"
            )
        else:
            # 查找现有股票池
            pools = self.list_pools()
            target_pool = None
            for pool in pools:
                if pool["pool_name"] == pool_name:
                    target_pool = pool
                    break

            if target_pool:
                pool_id = target_pool["pool_id"]
            else:
                # 如果找不到现有池，创建新的
                description = f"智能选股结果，共{len(selection_results)}只股票"
                pool_id = self.create_pool(
                    pool_name=pool_name,
                    description=description,
                    category="selection"
                )

        if not pool_id:
            return {"success": False, "message": "创建或获取股票池失败"}

        # 添加股票到池中
        added_count = 0
        skipped_count = 0
        errors = []

        for result in selection_results:
            stock_code = result.get("stock_code")
            stock_name = result.get("stock_name", stock_code)
            score = result.get("score", 0)

            # 构建添加原因
            indicators = result.get("indicators", {})
            reason_parts = [f"评分: {score:.1f}"]

            # 添加主要指标信息
            if "momentum_1m" in indicators:
                momentum = indicators["momentum_1m"] * 100
                reason_parts.append(f"1月动量: {momentum:.1f}%")

            if "volatility" in indicators:
                volatility = indicators["volatility"] * 100
                reason_parts.append(f"波动率: {volatility:.1f}%")

            if "rsi" in indicators:
                reason_parts.append(f"RSI: {indicators['rsi']:.1f}")

            added_reason = "智能选股 - " + ", ".join(reason_parts)

            # 生成标签
            tags = ["智能选股"]
            if score > 60:
                tags.append("高分")
            elif score > 40:
                tags.append("中等")
            else:
                tags.append("低分")

            # 添加股票
            success = self.add_stock(
                pool_id=pool_id,
                stock_code=stock_code,
                stock_name=stock_name,
                added_reason=added_reason,
                tags=tags,
                notes=f"选股时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            )

            if success:
                added_count += 1
            else:
                skipped_count += 1
                errors.append(f"{stock_code}: 可能已存在")

        # 更新股票池元数据
        pool = self.get_pool(pool_id)
        if pool:
            pool.metadata.update({
                "selection_date": datetime.now().isoformat(),
                "selection_count": len(selection_results),
                "added_count": added_count,
                "skipped_count": skipped_count
            })
            self._save_pool(pool)

        result = {
            "success": True,
            "pool_id": pool_id,
            "pool_name": pool_name,
            "total_results": len(selection_results),
            "added_count": added_count,
            "skipped_count": skipped_count,
            "message": f"成功添加{added_count}只股票到股票池'{pool_name}'"
        }

        if errors:
            result["errors"] = errors

        logger.info(f"智能选股结果已加入股票池: {pool_name}, 添加{added_count}只，跳过{skipped_count}只")
        return result

    def get_pool_statistics(self, pool_id: str) -> Dict[str, Any]:
        """获取股票池统计信息"""
        pool = self.get_pool(pool_id)
        if not pool:
            return {"success": False, "message": "股票池不存在"}

        stats = {
            "pool_info": {
                "pool_id": pool.pool_id,
                "pool_name": pool.pool_name,
                "description": pool.description,
                "category": pool.category,
                "created_date": pool.created_date,
                "updated_date": pool.updated_date
            },
            "stock_count": len(pool.stocks),
            "category_distribution": {},
            "tag_distribution": {},
            "recent_additions": []
        }

        # 统计分类分布
        category_count = {"custom": 0, "selection": 0, "strategy": 0}
        category_count[pool.category] = len(pool.stocks)
        stats["category_distribution"] = category_count

        # 统计标签分布
        tag_count = {}
        for stock in pool.stocks:
            for tag in stock.tags:
                tag_count[tag] = tag_count.get(tag, 0) + 1
        stats["tag_distribution"] = tag_count

        # 最近添加的股票（最多10只）
        recent_stocks = sorted(pool.stocks, key=lambda x: x.added_date, reverse=True)[:10]
        stats["recent_additions"] = [
            {
                "stock_code": stock.stock_code,
                "stock_name": stock.stock_name,
                "added_date": stock.added_date,
                "added_reason": stock.added_reason,
                "tags": stock.tags
            }
            for stock in recent_stocks
        ]

        return {"success": True, "data": stats}

    def search_stocks(self, query: str, pool_id: str = None) -> List[Dict[str, Any]]:
        """在股票池中搜索股票"""
        results = []

        if pool_id:
            # 在指定股票池中搜索
            pool = self.get_pool(pool_id)
            if pool:
                pools_to_search = [pool]
            else:
                pools_to_search = []
        else:
            # 在所有股票池中搜索
            pool_list = self.list_pools()
            pools_to_search = [self.get_pool(p["pool_id"]) for p in pool_list]
            pools_to_search = [p for p in pools_to_search if p is not None]

        query_lower = query.lower()

        for pool in pools_to_search:
            for stock in pool.stocks:
                # 搜索股票代码、名称、标签、备注
                if (query_lower in stock.stock_code.lower() or
                    query_lower in stock.stock_name.lower() or
                    any(query_lower in tag.lower() for tag in stock.tags) or
                    query_lower in stock.notes.lower()):

                    results.append({
                        "pool_id": pool.pool_id,
                        "pool_name": pool.pool_name,
                        "stock_code": stock.stock_code,
                        "stock_name": stock.stock_name,
                        "added_date": stock.added_date,
                        "added_reason": stock.added_reason,
                        "tags": stock.tags,
                        "notes": stock.notes
                    })

        return results

    def _save_pool(self, pool: StockPool) -> bool:
        """保存股票池到文件"""
        try:
            pool_file = self.pools_dir / f"{pool.pool_id}.json"
            with open(pool_file, 'w', encoding='utf-8') as f:
                json.dump(asdict(pool), f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            logger.error(f"保存股票池失败 {pool.pool_id}: {e}")
            return False

# 全局股票池管理器实例
stock_pool_manager = StockPoolManager()
