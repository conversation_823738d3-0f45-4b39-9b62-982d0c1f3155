#!/usr/bin/env python3
"""
调试策略执行问题 - 检查数据源、交易时间、日志等
"""

import requests
import json
import time
from datetime import datetime, time as dt_time
import pytz

BASE_URL = "http://localhost:8000"

def check_trading_time():
    """检查当前是否在交易时间"""
    print("=== 检查交易时间 ===")
    
    # 中国股市交易时间
    china_tz = pytz.timezone('Asia/Shanghai')
    now = datetime.now(china_tz)
    current_time = now.time()
    current_weekday = now.weekday()  # 0=Monday, 6=Sunday
    
    print(f"当前时间: {now.strftime('%Y-%m-%d %H:%M:%S %Z')}")
    print(f"星期: {['周一', '周二', '周三', '周四', '周五', '周六', '周日'][current_weekday]}")
    
    # 交易时间段
    morning_start = dt_time(9, 30)
    morning_end = dt_time(11, 30)
    afternoon_start = dt_time(13, 0)
    afternoon_end = dt_time(15, 0)
    
    # 检查是否为工作日
    is_weekday = current_weekday < 5  # 周一到周五
    
    # 检查是否在交易时间段
    is_morning_session = morning_start <= current_time <= morning_end
    is_afternoon_session = afternoon_start <= current_time <= afternoon_end
    is_trading_time = is_weekday and (is_morning_session or is_afternoon_session)
    
    print(f"是否工作日: {'是' if is_weekday else '否'}")
    print(f"上午交易时间 (09:30-11:30): {'是' if is_morning_session else '否'}")
    print(f"下午交易时间 (13:00-15:00): {'是' if is_afternoon_session else '否'}")
    print(f"当前是否交易时间: {'是' if is_trading_time else '否'}")
    
    if not is_trading_time:
        print("⚠️ 当前不在交易时间，策略可能不会产生交易")
        if not is_weekday:
            print("💡 建议在工作日测试")
        else:
            print("💡 建议在交易时间测试 (09:30-11:30, 13:00-15:00)")
    else:
        print("✅ 当前在交易时间内")
    
    return is_trading_time

def check_data_source():
    """检查数据源是否可用"""
    print("\n=== 检查数据源 ===")
    
    try:
        # 检查是否有数据获取API
        test_stocks = ['000001.SZ', '000002.SZ', '000858.SZ']
        
        for stock in test_stocks:
            print(f"\n检查股票数据: {stock}")
            
            # 尝试获取股票数据
            response = requests.get(f"{BASE_URL}/api/market/stock/{stock}")
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 数据获取成功: {json.dumps(data, ensure_ascii=False)}")
            else:
                print(f"❌ 数据获取失败: {response.status_code}")
                
                # 尝试其他可能的数据API
                response = requests.get(f"{BASE_URL}/api/data/stock/{stock}")
                if response.status_code == 200:
                    data = response.json()
                    print(f"✅ 备用数据源成功: {json.dumps(data, ensure_ascii=False)}")
                else:
                    print(f"❌ 备用数据源也失败: {response.status_code}")
        
        # 检查历史数据
        print(f"\n检查历史数据API:")
        response = requests.get(f"{BASE_URL}/api/market/history/000001.SZ?days=5")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 历史数据获取成功: {len(data.get('data', []))} 条记录")
        else:
            print(f"❌ 历史数据获取失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 检查数据源异常: {e}")

def test_bollinger_bands_strategy():
    """测试更活跃的bollinger_bands策略"""
    print("\n=== 测试bollinger_bands策略 ===")
    
    # 清理现有策略
    response = requests.get(f"{BASE_URL}/api/live/strategies")
    if response.status_code == 200:
        strategies = response.json()
        for strategy in strategies:
            if 'test' in strategy['name'].lower() or 'bollinger' in strategy['name'].lower():
                print(f"清理现有策略: {strategy['name']}")
                requests.delete(f"{BASE_URL}/api/live/strategies/{strategy['id']}")
    
    # 启动bollinger_bands策略
    strategy_data = {
        'name': 'bollinger_bands活跃测试',
        'strategy_type': 'bollinger_bands',
        'config': {
            'initial_capital': 100000,
            'paper_trading': True,
            'stock_codes': ['000001.SZ', '000002.SZ'],  # 使用活跃股票
            'bb_period': 10,  # 较短周期，更敏感
            'bb_std': 1.5,    # 较小标准差，更容易触发
            'max_positions': 2,
            'risk_limit': 0.05
        }
    }
    
    response = requests.post(f"{BASE_URL}/api/live/strategies", json=strategy_data)
    if response.status_code == 200:
        result = response.json()
        if result.get('success'):
            strategy_id = result['strategy_id']
            print(f"✅ bollinger_bands策略启动成功: {strategy_id}")
            
            # 监控策略执行
            print(f"\n监控策略执行 (30秒):")
            for i in range(10):  # 监控10次，每次3秒
                time.sleep(3)
                
                response = requests.get(f"{BASE_URL}/api/live/strategies/{strategy_id}")
                if response.status_code == 200:
                    strategy = response.json()
                    print(f"[{i+1:2d}/10] 持仓:{strategy['positions']} 交易:{strategy['trade_count']} 盈亏:¥{strategy['pnl']:.2f}")
                    
                    if strategy['trade_count'] > 0:
                        print(f"✅ 策略开始交易！")
                        recent_trades = strategy.get('recent_trades', [])
                        for trade in recent_trades[:3]:
                            print(f"  交易: {trade}")
                        break
                    elif strategy.get('error_message'):
                        print(f"❌ 策略错误: {strategy['error_message']}")
                        break
                else:
                    print(f"❌ 获取策略状态失败")
                    break
            
            return strategy_id
        else:
            print(f"❌ 策略启动失败: {result.get('message')}")
            return None
    else:
        print(f"❌ 策略启动请求失败: {response.status_code}")
        return None

def check_backend_logs():
    """检查后端日志"""
    print("\n=== 检查后端日志 ===")
    
    try:
        # 尝试获取日志API
        response = requests.get(f"{BASE_URL}/api/logs/recent")
        if response.status_code == 200:
            logs = response.json()
            print(f"✅ 获取到 {len(logs)} 条日志")
            
            # 显示最近的策略相关日志
            strategy_logs = [log for log in logs if any(keyword in log.lower() for keyword in ['strategy', 'trading', 'buy', 'sell', 'error'])]
            
            print(f"\n最近的策略相关日志:")
            for log in strategy_logs[-10:]:  # 最近10条
                print(f"  {log}")
                
        else:
            print(f"❌ 日志API不可用: {response.status_code}")
            print(f"💡 请检查后端控制台输出或日志文件")
            
    except Exception as e:
        print(f"❌ 检查日志异常: {e}")

def check_live_engine_status():
    """检查实盘引擎状态"""
    print("\n=== 检查实盘引擎状态 ===")
    
    try:
        # 获取所有实盘交易结果
        response = requests.get(f"{BASE_URL}/api/live/results")
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                results = data.get('data', [])
                print(f"实盘引擎任务数量: {len(results)}")
                
                for result in results:
                    print(f"\n任务详情:")
                    print(f"  task_id: {result.get('task_id')}")
                    print(f"  策略类型: {result.get('strategy_name')}")
                    print(f"  状态: {result.get('status')}")
                    print(f"  股票池: {result.get('stock_codes', [])}")
                    print(f"  初始资金: ¥{result.get('initial_capital', 0):,.2f}")
                    print(f"  当前资金: ¥{result.get('current_capital', 0):,.2f}")
                    print(f"  持仓数: {len(result.get('positions', []))}")
                    print(f"  交易数: {len(result.get('trades', []))}")
                    print(f"  纸面交易: {result.get('paper_trading', True)}")
                    
                    # 显示最近的交易
                    trades = result.get('trades', [])
                    if trades:
                        print(f"  最近交易:")
                        for trade in trades[-3:]:
                            print(f"    - {trade}")
                    else:
                        print(f"  最近交易: 无")
                    
                    # 显示持仓
                    positions = result.get('positions', [])
                    if positions:
                        print(f"  当前持仓:")
                        for pos in positions:
                            print(f"    - {pos}")
                    else:
                        print(f"  当前持仓: 无")
            else:
                print(f"❌ 获取实盘引擎结果失败: {data.get('message')}")
        else:
            print(f"❌ 实盘引擎API调用失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 检查实盘引擎状态异常: {e}")

def debug_specific_strategy(strategy_id):
    """调试特定策略的详细信息"""
    print(f"\n=== 调试策略详情: {strategy_id} ===")
    
    try:
        # 获取策略详情
        response = requests.get(f"{BASE_URL}/api/live/strategies/{strategy_id}")
        if response.status_code == 200:
            strategy = response.json()
            print(f"策略完整信息:")
            print(json.dumps(strategy, indent=2, ensure_ascii=False))
            
            # 如果有task_id，获取对应的实盘引擎任务
            task_id = strategy.get('task_id')
            if task_id:
                print(f"\n对应的实盘引擎任务:")
                response = requests.get(f"{BASE_URL}/api/live/results")
                if response.status_code == 200:
                    data = response.json()
                    if data.get('success'):
                        results = data.get('data', [])
                        for result in results:
                            if result.get('task_id') == task_id:
                                print(json.dumps(result, indent=2, ensure_ascii=False))
                                break
                        else:
                            print(f"❌ 未找到对应的实盘引擎任务: {task_id}")
            else:
                print(f"❌ 策略没有task_id")
        else:
            print(f"❌ 获取策略详情失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 调试策略详情异常: {e}")

def main():
    """主调试函数"""
    print("🔍 开始调试策略执行问题")
    
    # 1. 检查交易时间
    is_trading_time = check_trading_time()
    
    # 2. 检查数据源
    check_data_source()
    
    # 3. 检查实盘引擎状态
    check_live_engine_status()
    
    # 4. 测试bollinger_bands策略
    strategy_id = test_bollinger_bands_strategy()
    
    # 5. 检查后端日志
    check_backend_logs()
    
    # 6. 如果有策略ID，进行详细调试
    if strategy_id:
        debug_specific_strategy(strategy_id)
        
        # 清理测试策略
        print(f"\n清理测试策略...")
        requests.delete(f"{BASE_URL}/api/live/strategies/{strategy_id}")
    
    print(f"\n🎯 调试总结:")
    print(f"1. 交易时间: {'✅ 在交易时间' if is_trading_time else '⚠️ 不在交易时间'}")
    print(f"2. 数据源: 请查看上面的数据源检查结果")
    print(f"3. 实盘引擎: 请查看上面的引擎状态")
    print(f"4. 策略执行: 请查看上面的策略测试结果")
    
    print(f"\n💡 建议:")
    if not is_trading_time:
        print(f"- 在交易时间 (工作日 09:30-11:30, 13:00-15:00) 测试")
    print(f"- 检查数据源是否正常工作")
    print(f"- 查看后端控制台日志获取详细错误信息")
    print(f"- 确保股票代码正确且有数据")

if __name__ == "__main__":
    main()
