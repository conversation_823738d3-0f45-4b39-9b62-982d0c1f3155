# 持仓监控策略使用指南

## 功能概述

持仓监控策略是一个自动化的风险管理系统，可以实时监控您的持仓股票，并在满足预设条件时发出止损信号或自动执行卖出操作。

## 主要功能

### 1. 亏损止损
- **功能**: 当股票亏损超过设定阈值时触发止损
- **默认阈值**: 5%
- **计算方式**: (当前价格 - 成本价格) / 成本价格 ≤ -阈值

### 2. 移动止损
- **功能**: 从股票最高点下跌超过设定阈值时触发止损
- **默认阈值**: 5%
- **计算方式**: (最高价 - 当前价格) / 最高价 ≥ 阈值

### 3. ATR止损
- **功能**: 基于平均真实波幅(ATR)的动态止损
- **默认倍数**: 1.5倍ATR
- **计算方式**: 当前价格 ≤ 成本价格 - (ATR × 倍数)

### 4. 组合止损
- **功能**: 单只股票亏损超过总资产一定比例时触发止损
- **默认阈值**: 3%
- **计算方式**: 股票亏损金额 / 总资产 ≥ 阈值

## 使用步骤

### 1. 访问监控页面
打开浏览器访问: `http://localhost:8000/frontend/position-monitor.html`

### 2. 配置监控参数
- **策略开关**: 选择要启用的止损策略
- **参数设置**: 调整各策略的触发阈值
- **监控间隔**: 设置检查频率（建议60秒）
- **自动卖出**: 选择是否自动执行卖出（建议先关闭，仅提醒）

### 3. 启动监控
点击"启动监控"按钮开始实时监控

### 4. 查看信号
在页面下方查看触发的止损信号

## 配置说明

### 策略开关
```json
{
  "enable_loss_stop": true,      // 启用亏损止损
  "enable_trailing_stop": true,  // 启用移动止损
  "enable_atr_stop": true,       // 启用ATR止损
  "enable_portfolio_stop": true, // 启用组合止损
  "enable_auto_sell": false      // 启用自动卖出
}
```

### 参数配置
```json
{
  "loss_stop_threshold": 0.05,      // 亏损止损阈值 (5%)
  "trailing_stop_threshold": 0.05,  // 移动止损阈值 (5%)
  "atr_multiplier": 1.5,            // ATR倍数
  "atr_period": 14,                 // ATR计算周期
  "portfolio_loss_threshold": 0.03, // 组合止损阈值 (3%)
  "monitor_interval": 60            // 监控间隔 (秒)
}
```

## API接口

### 获取监控状态
```http
GET /api/position-monitor/status
```

### 启动/停止监控
```http
POST /api/position-monitor/start
POST /api/position-monitor/stop
```

### 配置管理
```http
GET /api/position-monitor/config
POST /api/position-monitor/config
```

### 获取信号历史
```http
GET /api/position-monitor/signals?limit=100
```

### 手动检查
```http
POST /api/position-monitor/check
```

## 安全提醒

### ⚠️ 重要注意事项

1. **测试模式**: 首次使用时请关闭"自动卖出"功能，仅使用提醒模式
2. **参数调整**: 根据您的风险承受能力调整止损阈值
3. **网络连接**: 确保QMT连接稳定，避免因网络问题导致误判
4. **市场时间**: 监控仅在交易时间内有效

### 建议配置

**保守型投资者**:
- 亏损止损: 3%
- 移动止损: 3%
- ATR倍数: 1.0
- 组合止损: 2%

**激进型投资者**:
- 亏损止损: 8%
- 移动止损: 8%
- ATR倍数: 2.0
- 组合止损: 5%

## 故障排除

### 常见问题

1. **监控无法启动**
   - 检查QMT是否正常连接
   - 确认账户信息可以正常获取

2. **价格数据获取失败**
   - 检查股票代码是否正确
   - 确认股票是否正常交易

3. **ATR计算失败**
   - 检查股票历史数据是否充足
   - 确认股票上市时间是否满足ATR周期要求

### 日志查看
监控运行日志会显示在后端控制台，包含详细的检查过程和触发信息。

## 技术架构

- **后端**: FastAPI + asyncio 异步监控
- **前端**: Bootstrap + JavaScript 实时界面
- **数据源**: QMT xtquant 实时数据
- **配置**: JSON文件持久化存储

## 更新日志

- v1.0.0: 初始版本，支持四种止损策略
- 支持实时监控和手动检查
- 支持配置持久化和Web界面管理
