import React, { useState, useEffect } from 'react';
import { <PERSON>, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { RefreshCw, Play, AlertCircle, CheckCircle, Clock, Settings } from 'lucide-react';

const StrategyRecovery = () => {
  const [strategies, setStrategies] = useState([]);
  const [loading, setLoading] = useState(false);
  const [recovering, setRecovering] = useState(false);
  const [message, setMessage] = useState(null);
  const [persistenceInfo, setPersistenceInfo] = useState(null);

  // 获取当前运行的策略
  const fetchStrategies = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/live/strategies');
      if (response.ok) {
        const data = await response.json();
        setStrategies(data);
      }
    } catch (error) {
      console.error('获取策略列表失败:', error);
      setMessage({ type: 'error', text: '获取策略列表失败' });
    } finally {
      setLoading(false);
    }
  };

  // 恢复策略
  const recoverStrategies = async () => {
    setRecovering(true);
    setMessage(null);
    
    try {
      const response = await fetch('/api/live/strategies/recover', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      if (response.ok) {
        const result = await response.json();
        setMessage({ 
          type: 'success', 
          text: result.message,
          details: `恢复了 ${result.recovered_count} 个策略`
        });
        
        // 刷新策略列表
        await fetchStrategies();
      } else {
        const error = await response.json();
        setMessage({ type: 'error', text: error.detail || '恢复策略失败' });
      }
    } catch (error) {
      console.error('恢复策略失败:', error);
      setMessage({ type: 'error', text: '恢复策略失败' });
    } finally {
      setRecovering(false);
    }
  };

  // 检查持久化文件信息
  const checkPersistenceInfo = async () => {
    try {
      // 这里可以添加一个API来获取持久化文件信息
      // 暂时使用模拟数据
      setPersistenceInfo({
        fileExists: true,
        lastUpdated: new Date().toISOString(),
        strategiesCount: strategies.length
      });
    } catch (error) {
      console.error('检查持久化信息失败:', error);
    }
  };

  useEffect(() => {
    fetchStrategies();
    checkPersistenceInfo();
  }, []);

  useEffect(() => {
    checkPersistenceInfo();
  }, [strategies]);

  const getStatusColor = (status) => {
    switch (status) {
      case 'running': return 'bg-green-100 text-green-800';
      case 'stopped': return 'bg-gray-100 text-gray-800';
      case 'error': return 'bg-red-100 text-red-800';
      default: return 'bg-blue-100 text-blue-800';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'running': return <Play className="w-3 h-3" />;
      case 'stopped': return <Clock className="w-3 h-3" />;
      case 'error': return <AlertCircle className="w-3 h-3" />;
      default: return <Settings className="w-3 h-3" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* 标题和操作区 */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">策略持久化管理</h2>
          <p className="text-gray-600 mt-1">管理和恢复多策略任务</p>
        </div>
        <div className="flex gap-3">
          <Button
            variant="outline"
            onClick={fetchStrategies}
            disabled={loading}
            className="flex items-center gap-2"
          >
            <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
            刷新
          </Button>
          <Button
            onClick={recoverStrategies}
            disabled={recovering}
            className="flex items-center gap-2"
          >
            <Play className={`w-4 h-4 ${recovering ? 'animate-pulse' : ''}`} />
            {recovering ? '恢复中...' : '恢复策略'}
          </Button>
        </div>
      </div>

      {/* 消息提示 */}
      {message && (
        <Alert className={message.type === 'error' ? 'border-red-200 bg-red-50' : 'border-green-200 bg-green-50'}>
          <div className="flex items-center gap-2">
            {message.type === 'error' ? 
              <AlertCircle className="w-4 h-4 text-red-600" /> : 
              <CheckCircle className="w-4 h-4 text-green-600" />
            }
            <AlertDescription className={message.type === 'error' ? 'text-red-800' : 'text-green-800'}>
              {message.text}
              {message.details && (
                <div className="text-sm mt-1 opacity-80">{message.details}</div>
              )}
            </AlertDescription>
          </div>
        </Alert>
      )}

      {/* 持久化信息 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="w-5 h-5" />
            持久化状态
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-center gap-3">
              <div className={`w-3 h-3 rounded-full ${persistenceInfo?.fileExists ? 'bg-green-500' : 'bg-red-500'}`}></div>
              <div>
                <div className="font-medium">持久化文件</div>
                <div className="text-sm text-gray-600">
                  {persistenceInfo?.fileExists ? '已创建' : '不存在'}
                </div>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <Clock className="w-4 h-4 text-blue-500" />
              <div>
                <div className="font-medium">最后更新</div>
                <div className="text-sm text-gray-600">
                  {persistenceInfo?.lastUpdated ? 
                    new Date(persistenceInfo.lastUpdated).toLocaleString() : 
                    'N/A'
                  }
                </div>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <Play className="w-4 h-4 text-green-500" />
              <div>
                <div className="font-medium">运行策略</div>
                <div className="text-sm text-gray-600">
                  {strategies.length} 个
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 当前运行的策略 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Play className="w-5 h-5" />
            当前运行的策略 ({strategies.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-8">
              <RefreshCw className="w-8 h-8 animate-spin mx-auto mb-4 text-gray-400" />
              <p className="text-gray-600">加载中...</p>
            </div>
          ) : strategies.length > 0 ? (
            <div className="space-y-4">
              {strategies.map((strategy) => (
                <div key={strategy.id} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center gap-4">
                    <div className="flex items-center gap-2">
                      {getStatusIcon(strategy.status)}
                      <Badge className={getStatusColor(strategy.status)}>
                        {strategy.status}
                      </Badge>
                    </div>
                    <div>
                      <div className="font-medium">{strategy.name}</div>
                      <div className="text-sm text-gray-600">
                        类型: {strategy.strategy_type} | ID: {strategy.id}
                      </div>
                      {strategy.start_time && (
                        <div className="text-xs text-gray-500">
                          启动时间: {new Date(strategy.start_time).toLocaleString()}
                        </div>
                      )}
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-sm">
                      <span className="text-gray-600">持仓:</span> {strategy.positions}
                    </div>
                    <div className="text-sm">
                      <span className="text-gray-600">盈亏:</span> 
                      <span className={strategy.pnl >= 0 ? 'text-green-600' : 'text-red-600'}>
                        ¥{strategy.pnl.toFixed(2)}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              <Play className="w-12 h-12 mx-auto mb-4 opacity-50" />
              <p>暂无运行中的策略</p>
              <p className="text-sm mt-2">点击"恢复策略"按钮来恢复之前的任务</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* 使用说明 */}
      <Card>
        <CardHeader>
          <CardTitle>使用说明</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3 text-sm text-gray-600">
            <div className="flex items-start gap-2">
              <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
              <div>
                <strong>自动恢复:</strong> 系统重启后会自动恢复之前运行的策略任务
              </div>
            </div>
            <div className="flex items-start gap-2">
              <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
              <div>
                <strong>手动恢复:</strong> 点击"恢复策略"按钮可以手动恢复策略
              </div>
            </div>
            <div className="flex items-start gap-2">
              <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
              <div>
                <strong>持久化存储:</strong> 策略状态保存在 <code>data/multi_strategy_tasks.json</code> 文件中
              </div>
            </div>
            <div className="flex items-start gap-2">
              <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
              <div>
                <strong>状态同步:</strong> 策略启动、停止时会自动更新持久化文件
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default StrategyRecovery;
