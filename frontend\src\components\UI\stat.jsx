import * as React from 'react'

export function Stat({ title, value, suffix, precision = 0 }) {
  const display = typeof value === 'number' ? value.toFixed(precision) : value
  return (
    <div className="p-3 rounded border bg-white">
      <div className="text-xs text-gray-500 mb-1">{title}</div>
      <div className="text-xl font-semibold">
        {display}{suffix || ''}
      </div>
    </div>
  )
}

