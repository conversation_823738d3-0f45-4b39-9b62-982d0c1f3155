#!/usr/bin/env python3
"""
测试API JSON修复
"""

import requests
import time
import json

def test_api_json_fix():
    """测试API JSON修复"""
    print("🔍 测试API JSON序列化修复")
    print("=" * 50)
    
    base_url = "http://localhost:8000"
    
    # 1. 测试健康检查
    try:
        response = requests.get(f"{base_url}/health", timeout=5)
        if response.status_code == 200:
            print("✅ 后端服务正常运行")
        else:
            print(f"❌ 后端服务异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 无法连接后端服务: {e}")
        print("请确保后端服务正在运行：python -c \"import uvicorn; uvicorn.run('backend.api.main:app', host='0.0.0.0', port=8000)\"")
        return False
    
    # 2. 测试回测历史记录API（可能包含NaN值）
    print(f"\n📊 测试回测历史记录API...")
    try:
        response = requests.get(f"{base_url}/api/backtest/history", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 回测历史记录API正常: 返回 {len(data.get('data', []))} 条记录")
            
            # 检查是否有数据
            if data.get('data'):
                first_record = data['data'][0]
                print(f"   第一条记录ID: {first_record.get('task_id', 'N/A')}")
                print(f"   策略名称: {first_record.get('strategy_name', 'N/A')}")
                print(f"   总收益率: {first_record.get('total_return', 'N/A')}")
                print(f"   夏普比率: {first_record.get('sharpe_ratio', 'N/A')}")
        else:
            print(f"❌ 回测历史记录API失败: {response.status_code}")
            print(f"   响应: {response.text}")
            return False
    except Exception as e:
        print(f"❌ 回测历史记录API异常: {e}")
        return False
    
    # 3. 测试具体的回测结果API（最可能包含NaN值）
    print(f"\n📈 测试回测结果API...")
    
    # 先获取一个回测任务ID
    try:
        response = requests.get(f"{base_url}/api/backtest/history?limit=1", timeout=10)
        if response.status_code == 200:
            data = response.json()
            if data.get('data') and len(data['data']) > 0:
                task_id = data['data'][0]['task_id']
                print(f"   使用任务ID: {task_id}")
                
                # 测试获取详细结果
                result_response = requests.get(f"{base_url}/api/backtest/results/{task_id}", timeout=10)
                if result_response.status_code == 200:
                    result_data = result_response.json()
                    print(f"✅ 回测结果API正常")
                    
                    if result_data.get('success') and result_data.get('data'):
                        result = result_data['data']
                        print(f"   总收益率: {result.get('total_return', 'N/A')}")
                        print(f"   夏普比率: {result.get('sharpe_ratio', 'N/A')}")
                        print(f"   最大回撤: {result.get('max_drawdown', 'N/A')}")
                        print(f"   胜率: {result.get('win_rate', 'N/A')}")
                        
                        # 检查是否有NaN值被正确处理
                        nan_fields = []
                        for field in ['sharpe_ratio', 'max_drawdown', 'win_rate']:
                            value = result.get(field)
                            if value is None:
                                nan_fields.append(field)
                        
                        if nan_fields:
                            print(f"   ✅ NaN值被正确处理为null: {nan_fields}")
                        else:
                            print(f"   📊 所有数值字段都有有效值")
                    
                elif result_response.status_code == 404:
                    print(f"⚠️ 回测结果不存在（可能已过期）")
                else:
                    print(f"❌ 回测结果API失败: {result_response.status_code}")
                    print(f"   响应: {result_response.text}")
                    return False
            else:
                print(f"⚠️ 没有可用的回测记录进行测试")
        else:
            print(f"❌ 无法获取回测记录: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 回测结果API异常: {e}")
        return False
    
    # 4. 测试股票池API（新功能）
    print(f"\n🎯 测试股票池API...")
    try:
        response = requests.get(f"{base_url}/api/stock-pools/", timeout=10)
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                pools = data.get('data', [])
                print(f"✅ 股票池API正常: 返回 {len(pools)} 个股票池")
                
                if pools:
                    first_pool = pools[0]
                    print(f"   第一个股票池: {first_pool.get('display_name', 'N/A')}")
                    print(f"   股票数量: {first_pool.get('total_stocks', 'N/A')}")
                    print(f"   来源: {first_pool.get('source', 'N/A')}")
            else:
                print(f"❌ 股票池API返回失败: {data.get('message', '未知错误')}")
                return False
        else:
            print(f"❌ 股票池API失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 股票池API异常: {e}")
        return False
    
    print(f"\n" + "=" * 50)
    print(f"✅ API JSON序列化修复测试完成！")
    print(f"\n💡 修复效果:")
    print(f"   - 回测结果中的NaN值被安全转换为null")
    print(f"   - 无穷大值被安全转换为null")
    print(f"   - API响应不再出现JSON序列化错误")
    print(f"   - 前端可以正常获取和显示回测结果")
    
    return True

def main():
    """主函数"""
    try:
        success = test_api_json_fix()
        
        if success:
            print(f"\n🎉 所有API测试通过！")
            print(f"\n📝 使用说明:")
            print(f"   1. 后端服务已修复JSON序列化问题")
            print(f"   2. 前端可以正常获取回测结果")
            print(f"   3. 股票池功能API正常工作")
            print(f"   4. 可以继续使用所有功能")
        else:
            print(f"\n❌ 部分API测试失败")
            print(f"   请检查后端服务状态和日志")
            
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
