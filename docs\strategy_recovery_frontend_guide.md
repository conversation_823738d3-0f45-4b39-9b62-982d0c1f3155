# 策略恢复功能前端使用指南

## 功能位置

策略恢复功能已集成到 **多策略交易** 页面中，访问路径：
- 前端地址：`http://localhost:3000/multi-strategy`
- 导航菜单：侧边栏 → "多策略交易"

## 界面元素

### 1. 恢复策略按钮
- **位置**：页面顶部，"刷新" 和 "添加策略" 按钮之间
- **图标**：旋转箭头图标 (RotateCcw)
- **文本**：
  - 正常状态：`恢复策略`
  - 恢复中：`恢复中...`（图标会旋转）
- **功能**：点击后从持久化文件恢复之前运行的策略

### 2. 持久化功能说明
- **位置**：安全提醒下方
- **类型**：信息提示框
- **内容**：说明系统会自动保存运行中的策略，重启后可通过恢复按钮恢复

### 3. 恢复结果提示
- **位置**：错误提示下方
- **类型**：成功/错误提示框
- **内容**：
  - 成功：显示恢复的策略数量
  - 失败：显示错误信息
- **可关闭**：用户可以手动关闭提示

## 使用流程

### 场景1：正常使用流程
1. 启动一个或多个策略
2. 策略会自动保存到持久化文件
3. 系统重启后，访问多策略交易页面
4. 点击 "恢复策略" 按钮
5. 系统显示恢复结果，策略列表更新

### 场景2：系统重启后自动恢复
1. 系统重启时会自动尝试恢复策略
2. 如果自动恢复失败，可以手动点击 "恢复策略" 按钮
3. 查看恢复结果提示

### 场景3：异常情况处理
1. 如果恢复失败，会显示错误提示
2. 可以多次尝试恢复
3. 检查后端日志获取详细错误信息

## 界面状态说明

### 按钮状态
- **正常状态**：蓝色边框，可点击
- **恢复中状态**：禁用状态，图标旋转，显示"恢复中..."
- **完成状态**：恢复到正常状态

### 提示信息
- **成功提示**：绿色边框，显示恢复的策略数量
- **错误提示**：红色边框，显示错误信息
- **自动消失**：Toast 消息会自动消失
- **手动关闭**：Alert 提示可以手动关闭

## 技术实现

### 前端实现
```javascript
// 恢复策略函数
const recoverPersistedStrategies = async () => {
  setRecovering(true);
  try {
    const response = await fetch('/api/live/strategies/recover', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
    });
    
    if (response.ok) {
      const result = await response.json();
      toast.success(result.message);
      await loadRunningStrategies(); // 刷新策略列表
    }
  } catch (err) {
    toast.error(`恢复策略失败: ${err.message}`);
  } finally {
    setRecovering(false);
  }
};
```

### API 调用
- **接口**：`POST /api/live/strategies/recover`
- **返回格式**：
```json
{
  "success": true,
  "message": "策略恢复完成: 原有 0 个，恢复 2 个",
  "recovered_count": 2,
  "previous_count": 0
}
```

## 用户体验优化

### 1. 视觉反馈
- 按钮点击后立即显示加载状态
- 图标旋转动画提供视觉反馈
- 成功/失败使用不同颜色区分

### 2. 信息提示
- Toast 消息提供即时反馈
- Alert 提示提供详细信息
- 可关闭的提示避免界面混乱

### 3. 状态同步
- 恢复完成后自动刷新策略列表
- 实时更新策略数量显示
- 保持界面数据一致性

## 常见问题

### Q1: 点击恢复按钮没有反应？
**A**: 检查网络连接和后端服务状态，查看浏览器控制台是否有错误信息。

### Q2: 恢复的策略数量不对？
**A**: 只有状态为 `running` 的策略会被恢复，已停止的策略不会恢复。

### Q3: 恢复失败怎么办？
**A**: 
1. 检查持久化文件是否存在：`data/multi_strategy_tasks.json`
2. 查看后端日志获取详细错误信息
3. 尝试重新启动后端服务

### Q4: 如何确认策略已经持久化？
**A**: 
1. 启动策略后，检查 `data/multi_strategy_tasks.json` 文件
2. 文件中应该包含策略的详细信息
3. 策略列表中的策略会自动保存

## 开发说明

### 文件修改
- **前端文件**：`frontend/src/pages/MultiStrategyLiveTradingPage.js`
- **后端文件**：`backend/services/multi_strategy_service.py`
- **API接口**：`backend/api/main.py`

### 新增功能
1. 恢复策略按钮和处理函数
2. 恢复状态管理
3. 恢复结果提示
4. 持久化功能说明

### 测试验证
- 启动策略后检查持久化文件
- 重启系统测试自动恢复
- 手动点击恢复按钮测试
- 异常情况处理测试

## 总结

策略恢复功能现在已经完全集成到前端界面中，用户可以：
1. 通过直观的按钮操作恢复策略
2. 获得清晰的状态反馈
3. 了解持久化功能的工作原理
4. 处理各种异常情况

这个功能大大提升了系统的可用性，确保用户在系统重启后不会丢失之前配置的策略任务。
