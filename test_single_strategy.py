#!/usr/bin/env python3
"""
单策略测试
专门测试单个策略，便于调试
"""

import asyncio
import traceback
from backend.backtest.simple_backtest_engine import simple_backtest_engine
from backend.strategies.base_strategy_new import list_strategies


async def test_single_strategy():
    """测试单个策略"""
    print("🔧 单策略调试测试")
    print("=" * 40)
    
    # 使用最简单的配置
    config = {
        'strategy_name': 'buy_hold',
        'strategy_config': {
            'max_stocks': 2,
            'position_size': 0.5
        },
        'start_date': '2024-11-01',
        'end_date': '2024-12-31',
        'initial_capital': 100000,
        'commission': 0.0003
    }
    
    try:
        print(f"测试配置: {config}")
        
        # 启动回测
        task_id = await simple_backtest_engine.start_backtest(config)
        print(f"任务启动: {task_id}")
        
        # 等待完成
        for i in range(30):
            await asyncio.sleep(1)
            
            task_status = simple_backtest_engine.get_task_status(task_id)
            if task_status:
                status = task_status['status']
                progress = task_status['progress']
                message = task_status['message']
                
                print(f"进度: {progress}% - {message}")
                
                if status == 'completed':
                    print("✅ 回测完成")
                    break
                elif status == 'failed':
                    error = task_status.get('error', '未知错误')
                    print(f"❌ 回测失败: {error}")
                    return False
        
        # 获取结果
        result = simple_backtest_engine.get_result(task_id)
        if result:
            print(f"\n📊 回测结果:")
            print(f"策略: {result.strategy_name}")
            print(f"总收益率: {result.total_return:.2%}")
            print(f"年化收益率: {result.annual_return:.2%}")
            print(f"最大回撤: {result.max_drawdown:.2%}")
            print(f"夏普比率: {result.sharpe_ratio:.2f}")
            print(f"胜率: {result.win_rate:.1%}")
            print(f"总交易次数: {result.total_trades}")
            
            if result.trades:
                print(f"\n📋 交易记录 (前3笔):")
                for i, trade in enumerate(result.trades[:3]):
                    print(f"  {i+1}. {trade['date']} {trade['action_name']} {trade['stock_code']} "
                          f"{trade['quantity']}股 @¥{trade['price']:.2f}")
            
            return True
        else:
            print("❌ 无法获取结果")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        traceback.print_exc()
        return False


async def test_strategy_list():
    """测试策略列表"""
    print("\n📋 策略列表测试")
    print("-" * 30)
    
    try:
        strategies = list_strategies()
        print(f"可用策略数量: {len(strategies)}")
        
        for strategy in strategies:
            print(f"  • {strategy['name']}: {strategy['display_name']}")
            print(f"    {strategy['description']}")
        
        return True
    except Exception as e:
        print(f"❌ 策略列表测试失败: {e}")
        traceback.print_exc()
        return False


async def main():
    """主函数"""
    print("🎯 单策略调试测试")
    print("目标: 找出并修复格式化错误")
    print()
    
    # 测试策略列表
    list_success = await test_strategy_list()
    
    # 测试单个策略
    strategy_success = await test_single_strategy()
    
    print(f"\n📊 测试结果:")
    print(f"策略列表: {'✅' if list_success else '❌'}")
    print(f"单策略回测: {'✅' if strategy_success else '❌'}")
    
    if list_success and strategy_success:
        print("\n🎉 基础功能正常！")
    else:
        print("\n🔧 需要进一步调试")


if __name__ == "__main__":
    asyncio.run(main())
