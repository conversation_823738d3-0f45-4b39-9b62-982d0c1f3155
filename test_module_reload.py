#!/usr/bin/env python3
"""
测试模块重新加载
"""

import sys
import os
import importlib

# 添加项目路径
sys.path.append(os.path.dirname(__file__))

def force_reload_qmt_modules():
    """强制重新加载QMT模块"""
    try:
        print("🔄 强制重新加载QMT模块...")
        
        # 要重新加载的模块列表
        modules_to_reload = [
            'backend.stores.qmt_data',
            'backend.stores.qmt_store',
            'backend.stores.qmt_broker',
            'backend.live.simple_live_engine'
        ]
        
        for module_name in modules_to_reload:
            if module_name in sys.modules:
                print(f"  🔄 重新加载: {module_name}")
                importlib.reload(sys.modules[module_name])
            else:
                print(f"  ⚠️ 模块未加载: {module_name}")
        
        print("✅ 模块重新加载完成")
        return True
        
    except Exception as e:
        print(f"❌ 模块重新加载失败: {e}")
        return False

def test_qmt_data_after_reload():
    """测试重新加载后的QMTData"""
    try:
        print("\n🔍 测试重新加载后的QMTData...")
        
        # 强制重新导入
        if 'backend.stores.qmt_data' in sys.modules:
            del sys.modules['backend.stores.qmt_data']
        
        from backend.stores.qmt_data import QMTData
        import backtrader as bt
        
        print(f"✅ QMTData导入成功")
        print(f"   类型: {QMTData}")
        print(f"   父类: {QMTData.__bases__}")
        
        # 检查是否正确继承
        if issubclass(QMTData, bt.feeds.PandasData):
            print("✅ 正确继承自PandasData")
            return True
        elif issubclass(QMTData, bt.feeds.DataBase):
            print("❌ 仍然继承自DataBase，需要重启服务器")
            return False
        else:
            print("❌ 继承关系异常")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False

def test_api_with_reload():
    """测试重新加载后的API"""
    try:
        print("\n🔍 测试重新加载后的API...")
        
        import requests
        import time
        
        # 启动一个策略
        test_data = {
            "strategy_name": "bollinger_bands",
            "initial_capital": 100000,
            "commission": 0.001,
            "stock_codes": ["000001.SZ"],
            "paper_trading": True,
            "strategy_params": {
                "period": 20,
                "std_dev": 2.0
            }
        }
        
        print("📤 启动策略...")
        response = requests.post(
            'http://localhost:8000/api/live/start',
            json=test_data,
            headers={'Content-Type': 'application/json'}
        )
        
        print(f"📋 响应状态: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                task_id = data.get('task_id')
                print(f"✅ 策略启动成功: {task_id[:8]}...")
                
                # 等待并检查状态
                time.sleep(5)
                
                response = requests.get('http://localhost:8000/api/live/results')
                if response.status_code == 200:
                    data = response.json()
                    if data.get('success'):
                        results = data.get('data', [])
                        strategy = next((r for r in results if r['task_id'] == task_id), None)
                        
                        if strategy:
                            status = strategy.get('status', 'unknown')
                            print(f"📊 策略状态: {status}")
                            
                            if status == 'running':
                                print("🎉 策略运行正常！")
                                
                                # 清理
                                requests.post(f'http://localhost:8000/api/live/stop/{task_id}')
                                time.sleep(1)
                                requests.delete(f'http://localhost:8000/api/live/delete/{task_id}')
                                
                                return True
                            else:
                                print(f"⚠️ 策略状态: {status}")
                                return False
                        else:
                            print("❌ 未找到策略")
                            return False
            else:
                print(f"❌ 策略启动失败: {data.get('message', 'Unknown error')}")
                return False
        else:
            print(f"❌ HTTP错误: {response.text}")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🧪 模块重新加载测试")
    print("=" * 50)
    
    # 强制重新加载模块
    reload_ok = force_reload_qmt_modules()
    
    if reload_ok:
        # 测试QMTData
        data_ok = test_qmt_data_after_reload()
        
        if data_ok:
            # 测试API
            api_ok = test_api_with_reload()
            
            if api_ok:
                print("\n🎉 所有测试通过！")
                print("✅ 模块重新加载成功")
                print("✅ QMTData正确更新")
                print("✅ 策略启动正常")
            else:
                print("\n⚠️ QMTData更新成功，但API仍有问题")
                print("   可能需要重启后端服务器")
        else:
            print("\n❌ QMTData仍未正确更新")
            print("   必须重启后端服务器")
    else:
        print("\n❌ 模块重新加载失败")
    
    print("\n" + "=" * 50)
    print("💡 建议:")
    print("如果测试仍然失败，请重启后端服务器:")
    print("1. 停止当前后端服务器 (Ctrl+C)")
    print("2. 重新启动: python -m uvicorn backend.api.main:app --host 0.0.0.0 --port 8000 --reload")
    print("3. 重新测试策略启动")
