# QMT Store模式使用指南

## 🎯 概述

QMT Store模式已完整实现，现在您可以让所有现有的backtrader策略直接用于实盘交易，无需修改任何策略代码！

## 🚀 快速开始

### 1. 启动系统

```bash
# 1. 启动后端API服务
cd backend
python api/main.py

# 2. 启动前端（新终端）
cd frontend
npm start
```

### 2. 访问实盘交易页面

打开浏览器访问：`http://localhost:3000`

导航到：**多策略实盘交易** 页面

### 3. 启动实盘交易

1. 点击 **"添加策略"** 按钮
2. 在弹出的配置窗口中：
   - 选择策略（如：BollingerBandsStrategy）
   - 设置初始资金（如：100,000元）
   - 输入股票代码（如：000001.SZ,000002.SZ）
   - 选择交易模式（建议先选择"纸上交易"）
   - 配置策略参数
3. 点击 **"启动交易"**

## 📊 功能特性

### ✅ 已实现的功能

#### 1. 核心Store组件
- **QMTStore**: 核心适配器，连接QMT系统
- **QMTData**: 数据适配器，转换QMT数据为backtrader格式
- **QMTBroker**: 交易适配器，转换backtrader订单为QMT交易

#### 2. 实盘交易引擎
- **SimpleLiveEngine**: 复用回测引擎架构的实盘引擎
- 支持多策略并行运行
- 完整的任务生命周期管理

#### 3. 风险管理系统
- **RiskManager**: 实时风险监控
- 回撤控制、持仓集中度监控
- 交易频率限制、异常告警

#### 4. 实时监控系统
- **WebSocket推送**: 实时数据推送
- **LiveTradingMonitor**: 前端实时监控组件
- 策略状态、风险指标、告警信息实时更新

#### 5. 前端界面
- **LiveTradingConfig**: 策略配置组件
- **MultiStrategyLiveTradingPage**: 多策略管理页面
- **useLiveTradingWebSocket**: WebSocket Hook

### 📋 API接口

#### 实盘交易接口
- `POST /api/live/start` - 启动实盘交易
- `POST /api/live/stop/{task_id}` - 停止实盘交易
- `GET /api/live/result/{task_id}` - 获取交易结果
- `GET /api/live/results` - 获取所有交易结果
- `GET /api/live/running` - 获取运行中的策略

#### WebSocket接口
- `WS /ws/live-trading/{client_id}` - 实时数据推送

#### 风险管理接口
- `GET /api/risk/metrics` - 获取风险指标
- `GET /api/risk/alerts` - 获取风险告警
- `POST /api/risk/clear-alerts` - 清理旧告警

## 🔧 使用示例

### 1. 直接使用Store模式

```python
import backtrader as bt
from backend.stores import QMTStore

# 创建Cerebro（与回测完全相同）
cerebro = bt.Cerebro()

# 创建QMT Store
qmt_store = QMTStore()

# 设置broker（使用QMT实盘broker）
cerebro.broker = qmt_store.getbroker(cash=100000)

# 添加数据源（使用QMT实时数据）
data = qmt_store.getdata('000001.SZ')
cerebro.adddata(data)

# 添加现有策略（无需修改）
from backend.strategies.bollinger_bands_strategy import BollingerBandsStrategy
cerebro.addstrategy(BollingerBandsStrategy)

# 运行实盘交易
cerebro.run()
```

### 2. 使用API接口

```python
import requests

# 启动实盘交易
response = requests.post('http://localhost:8000/api/live/start', json={
    'strategy_name': 'BollingerBandsStrategy',
    'initial_capital': 100000,
    'stock_codes': ['000001.SZ', '000002.SZ'],
    'paper_trading': True,
    'strategy_params': {
        'bb_period': 20,
        'bb_std': 2.0
    }
})

task_id = response.json()['task_id']
print(f"策略启动成功，任务ID: {task_id}")

# 获取交易结果
result = requests.get(f'http://localhost:8000/api/live/result/{task_id}')
print(result.json())
```

### 3. 前端WebSocket使用

```javascript
import { useLiveTradingWebSocket } from '../hooks/useLiveTradingWebSocket';

function MyComponent() {
  const {
    isConnected,
    strategies,
    riskAlerts,
    subscribe
  } = useLiveTradingWebSocket();

  useEffect(() => {
    if (isConnected) {
      subscribe(['task_id_1', 'task_id_2']);
    }
  }, [isConnected]);

  return (
    <div>
      <p>连接状态: {isConnected ? '已连接' : '未连接'}</p>
      <p>策略数量: {Object.keys(strategies).length}</p>
      <p>风险告警: {riskAlerts.length}</p>
    </div>
  );
}
```

## ⚠️ 重要注意事项

### 1. 安全提醒
- **首次使用请选择"纸上交易"模式**
- 实盘交易涉及真实资金，请谨慎操作
- 建议先进行充分的回测验证

### 2. 系统要求
- QMT客户端需要正常运行
- 确保网络连接稳定
- 建议在交易时间内使用

### 3. 风险控制
- 系统内置风险管理功能
- 支持最大回撤、持仓集中度等风险控制
- 异常情况会自动告警和停止

## 🐛 故障排除

### 1. 连接问题
```bash
# 检查QMT连接
python test_qmt_store_complete.py
```

### 2. 策略不运行
- 检查策略名称是否正确
- 确认股票代码格式（如：000001.SZ）
- 查看后端日志输出

### 3. WebSocket连接失败
- 确认后端API服务正常运行
- 检查防火墙设置
- 查看浏览器控制台错误信息

## 📈 性能优化建议

### 1. 策略数量
- 建议同时运行的策略不超过10个
- 根据系统性能调整

### 2. 数据频率
- 实时数据推送间隔为2秒
- 可根据需要调整推送频率

### 3. 风险监控
- 风险指标每次交易后更新
- 告警信息保留24小时

## 🎉 总结

QMT Store模式完美实现了：

1. **零代码修改**：现有策略直接用于实盘
2. **统一体验**：回测和实盘操作完全一致
3. **实时监控**：WebSocket实时数据推送
4. **风险控制**：完整的风险管理系统
5. **易于使用**：友好的前端界面

现在您可以享受从回测到实盘的无缝体验！
