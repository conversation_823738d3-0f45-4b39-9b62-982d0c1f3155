<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>错误处理测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        pre {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
        }
        button {
            padding: 8px 16px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
            cursor: pointer;
        }
        .btn-success { background: #28a745; color: white; }
        .btn-error { background: #dc3545; color: white; }
    </style>
</head>
<body>
    <h1>错误处理测试</h1>
    
    <div class="test-section">
        <h2>1. 测试正常启动</h2>
        <button class="btn-success" onclick="testNormalStart()">测试正常启动</button>
        <div id="normal-result"></div>
    </div>
    
    <div class="test-section">
        <h2>2. 测试错误处理</h2>
        <button class="btn-error" onclick="testErrorHandling()">测试错误响应</button>
        <div id="error-result"></div>
    </div>
    
    <div class="test-section">
        <h2>3. 测试网络错误</h2>
        <button class="btn-error" onclick="testNetworkError()">测试网络错误</button>
        <div id="network-result"></div>
    </div>

    <script>
        // 模拟apiUtils.handleError函数
        const apiUtils = {
            handleError: (error, defaultMessage = '操作失败') => {
                console.error('API Error:', error);
                
                // 如果是字符串，直接返回
                if (typeof error === 'string') {
                    return error;
                }
                
                // 如果有message属性，返回message
                if (error && error.message) {
                    return error.message;
                }
                
                // 如果是HTTP错误响应
                if (error && error.response && error.response.data) {
                    const data = error.response.data;
                    if (typeof data === 'string') {
                        return data;
                    }
                    if (data.detail) {
                        return data.detail;
                    }
                    if (data.message) {
                        return data.message;
                    }
                }
                
                // 如果是对象，尝试序列化
                if (typeof error === 'object' && error !== null) {
                    try {
                        return JSON.stringify(error);
                    } catch (e) {
                        return defaultMessage;
                    }
                }
                
                return defaultMessage;
            },
            
            isSuccess: (response) => {
                return response && response.success === true;
            }
        };

        async function testNormalStart() {
            const resultDiv = document.getElementById('normal-result');
            resultDiv.innerHTML = '<p>测试中...</p>';
            
            try {
                const testData = {
                    "strategy_name": "bollinger_bands",
                    "initial_capital": 100000,
                    "commission": 0.001,
                    "stock_codes": ["000001.SZ", "000002.SZ"],
                    "paper_trading": true,
                    "strategy_params": {
                        "period": 20,
                        "std_dev": 2.0,
                        "max_positions": 5,
                        "position_size": 0.2
                    }
                };

                const response = await fetch('http://localhost:8000/api/live/start', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(testData)
                });

                const data = await response.json();
                
                if (apiUtils.isSuccess(data)) {
                    resultDiv.innerHTML = `
                        <p class="success">✅ 启动成功</p>
                        <p>任务ID: ${data.task_id}</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    const errorMsg = apiUtils.handleError(data, '启动失败');
                    resultDiv.innerHTML = `
                        <p class="error">❌ 启动失败</p>
                        <p>错误信息: ${errorMsg}</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                const errorMsg = apiUtils.handleError(error, '网络错误');
                resultDiv.innerHTML = `
                    <p class="error">❌ 网络错误</p>
                    <p>错误信息: ${errorMsg}</p>
                `;
            }
        }

        async function testErrorHandling() {
            const resultDiv = document.getElementById('error-result');
            resultDiv.innerHTML = '<p>测试中...</p>';
            
            try {
                // 发送无效数据来触发错误
                const invalidData = {
                    "strategy_name": "invalid_strategy",
                    "initial_capital": -1000,  // 无效的负数
                    "stock_codes": []  // 空的股票列表
                };

                const response = await fetch('http://localhost:8000/api/live/start', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(invalidData)
                });

                const data = await response.json();
                const errorMsg = apiUtils.handleError(data, '测试错误处理');
                
                resultDiv.innerHTML = `
                    <p class="warning">⚠️ 错误处理测试</p>
                    <p>状态码: ${response.status}</p>
                    <p>处理后的错误信息: ${errorMsg}</p>
                    <p>错误信息类型: ${typeof errorMsg}</p>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                const errorMsg = apiUtils.handleError(error, '网络错误');
                resultDiv.innerHTML = `
                    <p class="error">❌ 网络错误</p>
                    <p>错误信息: ${errorMsg}</p>
                    <p>错误信息类型: ${typeof errorMsg}</p>
                `;
            }
        }

        async function testNetworkError() {
            const resultDiv = document.getElementById('network-result');
            resultDiv.innerHTML = '<p>测试中...</p>';
            
            try {
                // 请求不存在的端点
                const response = await fetch('http://localhost:9999/api/live/start', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({})
                });
            } catch (error) {
                const errorMsg = apiUtils.handleError(error, '网络连接失败');
                
                resultDiv.innerHTML = `
                    <p class="error">❌ 网络错误测试</p>
                    <p>处理后的错误信息: ${errorMsg}</p>
                    <p>错误信息类型: ${typeof errorMsg}</p>
                    <p>原始错误: ${error.toString()}</p>
                `;
            }
        }

        // 页面加载时自动运行正常测试
        window.onload = function() {
            console.log('页面加载完成，可以开始测试');
        };
    </script>
</body>
</html>
