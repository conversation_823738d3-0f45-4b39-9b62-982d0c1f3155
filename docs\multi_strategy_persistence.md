# 多策略持久化功能文档

## 功能概述

多策略持久化功能解决了系统重启后策略任务丢失的问题。通过将运行中的策略任务详情保存到JSON文件中，系统可以在重启后自动恢复之前运行的策略。

## 核心特性

### ✅ 自动持久化
- 策略启动时自动保存到持久化文件
- 策略停止时自动更新持久化状态
- 策略状态变更时实时同步

### ✅ 自动恢复
- 系统启动时自动读取持久化文件
- 只恢复状态为 `running` 的策略
- 验证策略配置的有效性
- 自动启动监控服务

### ✅ 手动恢复
- 提供API接口手动触发恢复
- 支持前端界面操作
- 可以在异常情况下重新恢复策略

### ✅ 错误处理
- 文件不存在时正常初始化
- 无效JSON时优雅降级
- 策略配置验证失败时跳过恢复

## 文件结构

### 持久化文件位置
```
data/multi_strategy_tasks.json
```

### 文件格式
```json
{
  "last_updated": "2025-09-08T08:06:08.074126",
  "running_strategies": {
    "strategy_1757289968": {
      "id": "strategy_1757289968",
      "name": "测试持久化策略",
      "strategy_type": "bollinger_bands",
      "config": {
        "initial_capital": 100000,
        "max_positions": 3,
        "risk_limit": 0.02,
        "bb_period": 20,
        "bb_std": 2.0
      },
      "status": "running",
      "start_time": "2025-09-08T08:06:08.074126",
      "stop_time": null,
      "positions": 0,
      "pnl": 0.0,
      "trade_count": 0,
      "error_message": null
    }
  }
}
```

## API接口

### 1. 获取运行中的策略
```http
GET /api/live/strategies
```

**响应示例:**
```json
[
  {
    "id": "strategy_1757289968",
    "name": "测试持久化策略",
    "strategy_type": "bollinger_bands",
    "status": "running",
    "start_time": "2025-09-08T08:06:08.074126",
    "positions": 0,
    "pnl": 0.0,
    "trade_count": 0
  }
]
```

### 2. 启动新策略
```http
POST /api/live/strategies
Content-Type: application/json

{
  "name": "我的策略",
  "strategy_type": "bollinger_bands",
  "config": {
    "initial_capital": 50000,
    "max_positions": 5,
    "risk_limit": 0.02
  }
}
```

### 3. 停止策略
```http
DELETE /api/live/strategies/{strategy_id}
```

### 4. 手动恢复策略
```http
POST /api/live/strategies/recover
```

**响应示例:**
```json
{
  "success": true,
  "message": "策略恢复完成: 原有 0 个，恢复 2 个",
  "recovered_count": 2,
  "previous_count": 0
}
```

## 使用场景

### 场景1: 正常重启恢复
1. 用户启动了多个策略
2. 系统因维护需要重启
3. 重启后策略自动恢复运行

### 场景2: 异常恢复
1. 系统异常崩溃
2. 重启后部分策略可能未恢复
3. 用户可以手动触发恢复

### 场景3: 选择性恢复
1. 用户想要重新加载策略配置
2. 调用手动恢复API
3. 系统重新读取持久化文件并恢复

## 前端界面

### 策略恢复管理页面
- 显示当前运行的策略列表
- 显示持久化状态信息
- 提供手动恢复按钮
- 实时状态更新

### 主要功能
- **刷新**: 重新获取策略列表
- **恢复策略**: 手动触发策略恢复
- **状态监控**: 显示持久化文件状态
- **策略详情**: 显示每个策略的详细信息

## 技术实现

### 核心类和方法

#### MultiStrategyService
- `_save_tasks_state()`: 保存任务状态到文件
- `_load_tasks_state()`: 从文件加载任务状态
- `_auto_recover_strategies()`: 自动恢复策略
- `recover_strategies()`: 手动恢复API接口

#### 关键流程
1. **启动策略时**: 调用 `_save_tasks_state()` 保存状态
2. **停止策略时**: 更新状态并调用 `_save_tasks_state()`
3. **服务初始化**: 自动调用 `_auto_recover_strategies()`
4. **手动恢复**: 通过API调用 `recover_strategies()`

## 配置说明

### 策略配置验证
系统会验证恢复的策略配置，确保必要参数存在：
- `initial_capital`: 初始资金（默认50000）
- `max_positions`: 最大持仓数（默认5）
- `risk_limit`: 风险限制（默认0.02）

### 策略类型特定配置
- **bollinger_bands**: `bb_period`, `bb_std`
- **ma_strategy**: `ma_short`, `ma_long`

## 监控和日志

### 日志级别
- **INFO**: 正常的恢复操作
- **WARNING**: 配置验证失败等警告
- **ERROR**: 文件读写错误等异常

### 关键日志示例
```
[INFO] 策略自动恢复完成: 成功 2 个，失败 0 个
[INFO] 策略恢复成功: 测试策略 (strategy_001)
[WARNING] 策略配置验证失败，跳过恢复: 无效策略
[ERROR] 加载任务状态失败: 文件格式错误
```

## 最佳实践

### 1. 定期备份
建议定期备份 `data/multi_strategy_tasks.json` 文件

### 2. 监控日志
关注系统启动时的恢复日志，确保策略正确恢复

### 3. 配置验证
确保策略配置完整，避免恢复失败

### 4. 手动验证
重启后手动检查策略状态，必要时使用手动恢复功能

## 故障排除

### 问题1: 策略未自动恢复
**可能原因**: 持久化文件损坏或不存在
**解决方案**: 检查文件是否存在，使用手动恢复功能

### 问题2: 部分策略恢复失败
**可能原因**: 策略配置验证失败
**解决方案**: 检查日志，修复配置问题

### 问题3: 持久化文件过大
**可能原因**: 历史数据积累过多
**解决方案**: 定期清理停止的策略记录

## 版本历史

### v1.0.0 (2025-09-08)
- ✅ 实现基本的持久化功能
- ✅ 支持自动恢复
- ✅ 提供手动恢复API
- ✅ 添加前端管理界面
- ✅ 完善错误处理机制
