#!/usr/bin/env python3
"""
测试股票池管理功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import requests
import json
import time
from datetime import datetime
from backend.stock_pool.stock_pool_manager import stock_pool_manager
from backend.core.logger import get_logger

logger = get_logger(__name__)

def test_stock_pool_manager():
    """测试股票池管理器基础功能"""
    print("🗂️ 测试股票池管理器基础功能")
    print("=" * 60)
    
    # 1. 测试创建股票池
    print("1️⃣ 测试创建股票池...")
    pool_id = stock_pool_manager.create_pool(
        pool_name="测试股票池",
        description="用于测试的股票池",
        category="custom"
    )
    print(f"✅ 创建股票池成功: {pool_id}")
    
    # 2. 测试获取股票池列表
    print("\n2️⃣ 测试获取股票池列表...")
    pools = stock_pool_manager.list_pools()
    print(f"✅ 获取到 {len(pools)} 个股票池")
    for pool in pools[:3]:  # 显示前3个
        print(f"   • {pool['pool_name']} ({pool['category']}) - {pool['stock_count']}只股票")
    
    # 3. 测试添加股票
    print("\n3️⃣ 测试添加股票...")
    test_stocks = [
        {"code": "000001.SZ", "name": "平安银行", "reason": "银行龙头", "tags": ["银行", "蓝筹"]},
        {"code": "000002.SZ", "name": "万科A", "reason": "地产龙头", "tags": ["地产", "蓝筹"]},
        {"code": "600036.SH", "name": "招商银行", "reason": "零售银行之王", "tags": ["银行", "优质"]},
        {"code": "600519.SH", "name": "贵州茅台", "reason": "白酒龙头", "tags": ["白酒", "消费"]},
        {"code": "000858.SZ", "name": "五粮液", "reason": "白酒次龙头", "tags": ["白酒", "消费"]}
    ]
    
    added_count = 0
    for stock in test_stocks:
        success = stock_pool_manager.add_stock(
            pool_id=pool_id,
            stock_code=stock["code"],
            stock_name=stock["name"],
            added_reason=stock["reason"],
            tags=stock["tags"]
        )
        if success:
            added_count += 1
            print(f"   ✅ 添加股票: {stock['code']} - {stock['name']}")
        else:
            print(f"   ❌ 添加失败: {stock['code']}")
    
    print(f"✅ 成功添加 {added_count} 只股票")
    
    # 4. 测试获取股票池详情
    print("\n4️⃣ 测试获取股票池详情...")
    pool = stock_pool_manager.get_pool(pool_id)
    if pool:
        print(f"✅ 股票池详情:")
        print(f"   名称: {pool.pool_name}")
        print(f"   描述: {pool.description}")
        print(f"   股票数量: {len(pool.stocks)}")
        print(f"   股票列表:")
        for stock in pool.stocks:
            print(f"     • {stock.stock_code} - {stock.stock_name} ({', '.join(stock.tags)})")
    
    # 5. 测试搜索功能
    print("\n5️⃣ 测试搜索功能...")
    search_results = stock_pool_manager.search_stocks("银行")
    print(f"✅ 搜索'银行'找到 {len(search_results)} 个结果:")
    for result in search_results:
        print(f"   • {result['stock_code']} - {result['stock_name']} (池: {result['pool_name']})")
    
    # 6. 测试统计信息
    print("\n6️⃣ 测试统计信息...")
    stats = stock_pool_manager.get_pool_statistics(pool_id)
    if stats["success"]:
        data = stats["data"]
        print(f"✅ 统计信息:")
        print(f"   股票数量: {data['stock_count']}")
        print(f"   标签分布: {data['tag_distribution']}")
        print(f"   最近添加: {len(data['recent_additions'])} 只")
    
    return pool_id

def test_selection_results_integration():
    """测试智能选股结果集成功能"""
    print("\n🎯 测试智能选股结果集成功能")
    print("=" * 60)
    
    # 模拟智能选股结果
    mock_selection_results = [
        {
            "stock_code": "300750.SZ",
            "stock_name": "宁德时代",
            "score": 85.6,
            "indicators": {
                "momentum_1m": 0.08,
                "volatility": 0.25,
                "rsi": 65.2,
                "pe_ttm": 28.5,
                "roe": 22.3
            }
        },
        {
            "stock_code": "002415.SZ",
            "stock_name": "海康威视",
            "score": 78.9,
            "indicators": {
                "momentum_1m": 0.05,
                "volatility": 0.18,
                "rsi": 58.7,
                "pe_ttm": 15.8,
                "roe": 18.9
            }
        },
        {
            "stock_code": "000063.SZ",
            "stock_name": "中兴通讯",
            "score": 72.3,
            "indicators": {
                "momentum_1m": 0.03,
                "volatility": 0.22,
                "rsi": 52.1,
                "pe_ttm": 18.6,
                "roe": 15.2
            }
        },
        {
            "stock_code": "002594.SZ",
            "stock_name": "比亚迪",
            "score": 88.2,
            "indicators": {
                "momentum_1m": 0.12,
                "volatility": 0.28,
                "rsi": 68.9,
                "pe_ttm": 35.2,
                "roe": 25.8
            }
        }
    ]
    
    print(f"📊 模拟选股结果: {len(mock_selection_results)} 只股票")
    for result in mock_selection_results:
        print(f"   • {result['stock_code']} - {result['stock_name']} (评分: {result['score']:.1f})")
    
    # 测试添加到新股票池
    print(f"\n1️⃣ 测试添加到新股票池...")
    result = stock_pool_manager.add_selection_results(
        selection_results=mock_selection_results,
        pool_name="智能选股_科技股",
        create_new_pool=True
    )
    
    if result["success"]:
        print(f"✅ 成功创建股票池: {result['pool_name']}")
        print(f"   池ID: {result['pool_id']}")
        print(f"   添加股票: {result['added_count']} 只")
        print(f"   跳过股票: {result['skipped_count']} 只")
        
        # 查看创建的股票池
        pool = stock_pool_manager.get_pool(result['pool_id'])
        if pool:
            print(f"\n📋 股票池详情:")
            print(f"   名称: {pool.pool_name}")
            print(f"   描述: {pool.description}")
            print(f"   类别: {pool.category}")
            print(f"   股票列表:")
            for stock in pool.stocks:
                print(f"     • {stock.stock_code} - {stock.stock_name}")
                print(f"       原因: {stock.added_reason}")
                print(f"       标签: {', '.join(stock.tags)}")
                print()
        
        return result['pool_id']
    else:
        print(f"❌ 添加失败: {result['message']}")
        return None

def test_api_endpoints():
    """测试API接口"""
    print("\n🌐 测试API接口")
    print("=" * 60)
    
    base_url = "http://localhost:8000"
    
    # 测试获取股票池列表
    print("1️⃣ 测试获取股票池列表API...")
    try:
        response = requests.get(f"{base_url}/api/stock-pool/pools", timeout=5)
        if response.status_code == 200:
            data = response.json()
            if data.get("success"):
                pools = data.get("data", [])
                print(f"✅ API返回 {len(pools)} 个股票池")
                for pool in pools[:3]:
                    print(f"   • {pool['pool_name']} - {pool['stock_count']}只股票")
            else:
                print(f"❌ API返回失败: {data.get('message')}")
        else:
            print(f"❌ API请求失败: {response.status_code}")
    except Exception as e:
        print(f"❌ API测试失败: {e}")
        print("💡 请确保后端服务正在运行")
    
    # 测试创建股票池API
    print(f"\n2️⃣ 测试创建股票池API...")
    try:
        create_data = {
            "pool_name": "API测试池",
            "description": "通过API创建的测试股票池",
            "category": "custom"
        }
        response = requests.post(f"{base_url}/api/stock-pool/pools", json=create_data, timeout=5)
        if response.status_code == 200:
            data = response.json()
            if data.get("success"):
                pool_id = data.get("data", {}).get("pool_id")
                print(f"✅ API创建股票池成功: {pool_id}")
                
                # 测试添加股票API
                print(f"\n3️⃣ 测试添加股票API...")
                add_stock_data = {
                    "stock_code": "000001.SZ",
                    "stock_name": "平安银行",
                    "added_reason": "API测试添加",
                    "tags": ["测试", "银行"],
                    "notes": "通过API添加的测试股票"
                }
                response = requests.post(f"{base_url}/api/stock-pool/pools/{pool_id}/stocks", json=add_stock_data, timeout=5)
                if response.status_code == 200:
                    data = response.json()
                    if data.get("success"):
                        print(f"✅ API添加股票成功")
                    else:
                        print(f"❌ API添加股票失败: {data.get('message')}")
                else:
                    print(f"❌ 添加股票API请求失败: {response.status_code}")
            else:
                print(f"❌ API创建失败: {data.get('message')}")
        else:
            print(f"❌ 创建股票池API请求失败: {response.status_code}")
    except Exception as e:
        print(f"❌ API测试失败: {e}")

def main():
    """主函数"""
    print("🗂️ 股票池管理功能测试")
    print("=" * 80)
    
    try:
        # 测试基础功能
        pool_id = test_stock_pool_manager()
        
        # 测试选股结果集成
        selection_pool_id = test_selection_results_integration()
        
        # 测试API接口
        test_api_endpoints()
        
        print(f"\n" + "=" * 80)
        print(f"✅ 股票池管理功能测试完成！")
        
        print(f"\n💡 实现的核心功能:")
        print(f"   ✅ 股票池创建、删除、更新")
        print(f"   ✅ 股票添加、移除、更新")
        print(f"   ✅ 股票池搜索和统计")
        print(f"   ✅ 智能选股结果集成")
        print(f"   ✅ 完整的API接口")
        print(f"   ✅ 数据持久化存储")
        
        print(f"\n🎯 使用场景:")
        print(f"   • 管理自选股票池")
        print(f"   • 保存智能选股结果")
        print(f"   • 按策略分类股票")
        print(f"   • 股票标签和备注管理")
        print(f"   • 股票池统计分析")
        
        if pool_id:
            print(f"\n📋 测试创建的股票池ID: {pool_id}")
        if selection_pool_id:
            print(f"📋 选股结果股票池ID: {selection_pool_id}")
            
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
