# QMT交易系统 - 统一日志系统使用指南

## 概述

本系统实现了一个统一的日志管理系统，支持按模块分类记录日志到不同文件，同时输出到控制台。

## 特性

- ✅ **模块化日志**: 按模块分类记录到不同目录和文件
- ✅ **自动轮转**: 支持日志文件大小限制和自动备份
- ✅ **统一格式**: 包含时间、模块、级别、文件位置等详细信息
- ✅ **错误聚合**: 所有错误日志统一记录到errors.log
- ✅ **管理工具**: 提供日志查看、搜索、清理等工具
- ✅ **配置化**: 支持YAML配置文件自定义设置

## 目录结构

```
backend/logs/
├── api/
│   ├── api.log              # API模块总日志
│   ├── main.log             # 主API服务日志
│   └── requests.log         # 请求日志
├── data/
│   ├── data.log             # 数据模块总日志
│   ├── data_manager.log     # 数据管理器日志
│   └── qmt_connector.log    # QMT连接器日志
├── backtest/
│   ├── backtest.log         # 回测模块总日志
│   ├── simple_backtest.log  # 简单回测引擎日志
│   └── results.log          # 回测结果日志
├── strategies/
│   ├── strategies.log       # 策略模块总日志
│   ├── position_monitor.log # 持仓监控策略日志
│   └── bollinger_bands.log  # 布林带策略日志
├── trading/
│   ├── trading.log          # 交易模块总日志
│   └── live_trader.log      # 实盘交易日志
├── services/
│   ├── services.log         # 服务模块总日志
│   └── position_monitor_service.log
├── core/
│   ├── core.log             # 核心模块总日志
│   └── system.log           # 系统日志
└── errors.log               # 全局错误日志
```

## 使用方法

### 1. 在代码中使用日志

```python
# 方法1: 使用便捷函数
from backend.core.logger import get_api_logger, get_data_logger, get_backtest_logger

# API模块
logger = get_api_logger('main')
logger.info("API服务启动")

# 数据模块
logger = get_data_logger('data_manager')
logger.info("数据管理器初始化")

# 回测模块
logger = get_backtest_logger('simple_backtest')
logger.info("开始回测")

# 方法2: 使用通用函数
from backend.core.logger import get_logger

logger = get_logger('custom_name', 'strategies')
logger.info("自定义策略日志")
```

### 2. 支持的模块类型

- `api`: API接口相关
- `data`: 数据获取和管理
- `backtest`: 回测引擎
- `strategies`: 交易策略
- `trading`: 实盘交易
- `services`: 后台服务
- `core`: 核心功能

### 3. 日志级别

```python
logger.debug("调试信息")      # DEBUG
logger.info("一般信息")       # INFO
logger.warning("警告信息")    # WARNING
logger.error("错误信息")      # ERROR
logger.critical("严重错误")   # CRITICAL
```

## 日志管理工具

### 查看日志信息

```bash
# 查看所有日志文件信息
python backend/tools/log_manager.py info

# 查看特定模块日志
python backend/tools/log_manager.py info --module api
```

### 查看日志内容

```bash
# 查看日志文件末尾50行
python backend/tools/log_manager.py tail --file api/main.log

# 查看指定行数
python backend/tools/log_manager.py tail --file data/data_manager.log --lines 100
```

### 搜索日志

```bash
# 搜索关键词
python backend/tools/log_manager.py search --keyword "错误"

# 在特定模块中搜索
python backend/tools/log_manager.py search --keyword "QMT" --module data

# 搜索最近3天的日志
python backend/tools/log_manager.py search --keyword "回测" --days 3
```

### 清理旧日志

```bash
# 预览要删除的文件（不实际删除）
python backend/tools/log_manager.py cleanup --days 30

# 实际执行清理
python backend/tools/log_manager.py cleanup --days 30 --execute
```

### 监控错误日志

```bash
# 查看最近的错误
python backend/tools/log_manager.py errors

# 实时监控错误日志
python backend/tools/log_manager.py errors --follow
```

## 配置文件

日志配置位于 `backend/config/logging.yaml`：

```yaml
logging:
  level: INFO
  log_dir: logs
  
  file:
    max_bytes: 10485760  # 10MB
    backup_count: 5
    
  modules:
    api:
      level: INFO
      files:
        - main.log
        - requests.log
```

## 最佳实践

### 1. 日志级别使用建议

- **DEBUG**: 详细的调试信息，仅在开发时使用
- **INFO**: 正常的业务流程信息
- **WARNING**: 警告信息，不影响正常运行
- **ERROR**: 错误信息，需要关注但不会导致程序崩溃
- **CRITICAL**: 严重错误，可能导致程序无法继续运行

### 2. 日志内容建议

```python
# ✅ 好的日志
logger.info(f"开始回测: 策略={strategy_name}, 时间范围={start_date}到{end_date}")
logger.warning(f"股票{stock_code}数据不足: 只有{len(data)}条记录")
logger.error(f"QMT连接失败: {error_message}")

# ❌ 不好的日志
logger.info("开始处理")
logger.error("出错了")
```

### 3. 异常处理

```python
try:
    result = some_operation()
    logger.info(f"操作成功: {result}")
except Exception as e:
    logger.error(f"操作失败: {e}", exc_info=True)  # exc_info=True 会记录完整的堆栈信息
    raise
```

### 4. 性能考虑

```python
# ✅ 使用字符串格式化
logger.debug(f"处理数据: {data}")

# ✅ 对于复杂操作，先检查日志级别
if logger.isEnabledFor(logging.DEBUG):
    expensive_debug_info = calculate_debug_info()
    logger.debug(f"调试信息: {expensive_debug_info}")
```

## 故障排除

### 1. 日志文件权限问题

```bash
# 检查日志目录权限
ls -la backend/logs/

# 修复权限
chmod -R 755 backend/logs/
```

### 2. 日志文件过大

```bash
# 手动清理大文件
python backend/tools/log_manager.py cleanup --days 7 --execute

# 或者直接删除
rm backend/logs/*/large_file.log
```

### 3. 查看系统日志

```bash
# 查看系统初始化日志
python backend/tools/log_manager.py tail --file core/system.log

# 查看错误汇总
python backend/tools/log_manager.py tail --file errors.log
```

## 集成到现有代码

### 替换现有日志

```python
# 旧代码
import logging
logger = logging.getLogger(__name__)

# 新代码
from backend.core.logger import get_data_logger  # 根据模块选择
logger = get_data_logger('module_name')
```

### 批量替换

可以使用以下脚本批量替换现有的日志导入：

```bash
# 查找需要替换的文件
grep -r "logging.getLogger" backend/

# 根据模块类型逐个替换
```

## 监控和告警

### 1. 错误日志监控

```bash
# 设置定时任务监控错误日志
crontab -e

# 每5分钟检查一次错误日志
*/5 * * * * python /path/to/backend/tools/log_manager.py errors | grep -q "ERROR" && echo "发现新错误" | mail -s "QMT系统错误" <EMAIL>
```

### 2. 日志大小监控

```bash
# 检查日志目录大小
du -sh backend/logs/

# 自动清理脚本
python backend/tools/log_manager.py cleanup --days 30 --execute
```

这个统一的日志系统将帮助您更好地管理和监控QMT交易系统的运行状态！
