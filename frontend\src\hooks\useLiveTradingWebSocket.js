import { useState, useEffect, useRef, useCallback } from 'react';
import { toast } from 'sonner';

/**
 * 实盘交易WebSocket Hook
 * 提供实时数据推送功能
 */
export const useLiveTradingWebSocket = (clientId = null) => {
  const [isConnected, setIsConnected] = useState(false);
  const [strategies, setStrategies] = useState({});
  const [riskMetrics, setRiskMetrics] = useState({});
  const [riskAlerts, setRiskAlerts] = useState([]);
  const [connectionError, setConnectionError] = useState(null);
  
  const wsRef = useRef(null);
  const reconnectTimeoutRef = useRef(null);
  const reconnectAttempts = useRef(0);
  const maxReconnectAttempts = 5;
  const subscriptions = useRef(new Set());
  const subscribeTimeoutRef = useRef(null);

  // 生成客户端ID
  const getClientId = useCallback(() => {
    if (clientId) return clientId;
    return `client_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }, [clientId]);

  // 连接WebSocket
  const connect = useCallback(() => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      return;
    }

    try {
      const wsUrl = `ws://localhost:8000/ws/live-trading/${getClientId()}`;
      wsRef.current = new WebSocket(wsUrl);

      wsRef.current.onopen = () => {
        console.log('✅ WebSocket连接成功');
        setIsConnected(true);
        setConnectionError(null);
        reconnectAttempts.current = 0;

        // 重新订阅之前的任务
        if (subscriptions.current.size > 0) {
          subscribe(Array.from(subscriptions.current));
        }
      };

      wsRef.current.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          handleMessage(data);
        } catch (error) {
          console.error('❌ 解析WebSocket消息失败:', error);
        }
      };

      wsRef.current.onclose = (event) => {
        console.log('🔌 WebSocket连接关闭:', event.code, event.reason);
        setIsConnected(false);
        
        // 自动重连
        if (reconnectAttempts.current < maxReconnectAttempts) {
          const delay = Math.pow(2, reconnectAttempts.current) * 1000; // 指数退避
          reconnectTimeoutRef.current = setTimeout(() => {
            reconnectAttempts.current++;
            console.log(`🔄 尝试重连 (${reconnectAttempts.current}/${maxReconnectAttempts})`);
            connect();
          }, delay);
        } else {
          setConnectionError('WebSocket连接失败，请刷新页面重试');
        }
      };

      wsRef.current.onerror = (error) => {
        console.error('❌ WebSocket错误:', error);
        setConnectionError('WebSocket连接错误');
      };

    } catch (error) {
      console.error('❌ 创建WebSocket连接失败:', error);
      setConnectionError('无法创建WebSocket连接');
    }
  }, [getClientId]);

  // 断开连接
  const disconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
    }
    
    if (wsRef.current) {
      wsRef.current.close();
      wsRef.current = null;
    }
    
    setIsConnected(false);
    reconnectAttempts.current = 0;
  }, []);

  // 发送消息
  const sendMessage = useCallback((message) => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      wsRef.current.send(JSON.stringify(message));
    } else {
      console.warn('⚠️ WebSocket未连接，无法发送消息');
    }
  }, []);

  // 订阅策略任务（带防抖）
  const subscribe = useCallback((taskIds) => {
    if (!Array.isArray(taskIds)) {
      taskIds = [taskIds];
    }

    // 清除之前的防抖定时器
    if (subscribeTimeoutRef.current) {
      clearTimeout(subscribeTimeoutRef.current);
    }

    // 防抖处理，避免频繁订阅
    subscribeTimeoutRef.current = setTimeout(() => {
      // 过滤出新的任务ID，避免重复订阅
      const newTaskIds = taskIds.filter(taskId => !subscriptions.current.has(taskId));

      if (newTaskIds.length === 0) {
        return; // 没有新的任务需要订阅
      }

      newTaskIds.forEach(taskId => subscriptions.current.add(taskId));

      sendMessage({
        type: 'subscribe',
        task_ids: newTaskIds
      });
    }, 100); // 100ms防抖
  }, [sendMessage]);

  // 取消订阅
  const unsubscribe = useCallback((taskIds) => {
    if (!Array.isArray(taskIds)) {
      taskIds = [taskIds];
    }

    // 过滤出已订阅的任务ID，避免重复取消订阅
    const subscribedTaskIds = taskIds.filter(taskId => subscriptions.current.has(taskId));

    if (subscribedTaskIds.length === 0) {
      return; // 没有已订阅的任务需要取消
    }

    subscribedTaskIds.forEach(taskId => subscriptions.current.delete(taskId));

    sendMessage({
      type: 'unsubscribe',
      task_ids: subscribedTaskIds
    });
  }, [sendMessage]);

  // 处理WebSocket消息
  const handleMessage = useCallback((data) => {
    const { type, task_id, data: messageData, timestamp } = data;

    switch (type) {
      case 'connection':
        console.log('📡 WebSocket连接确认:', messageData);
        break;

      case 'strategy_status':
      case 'strategy_update':
        setStrategies(prev => ({
          ...prev,
          [task_id]: {
            ...prev[task_id],
            ...messageData,
            lastUpdate: timestamp
          }
        }));
        break;

      case 'risk_metrics':
        setRiskMetrics(prev => ({
          ...prev,
          [task_id]: {
            ...messageData,
            lastUpdate: timestamp
          }
        }));
        break;

      case 'risk_alerts':
        setRiskAlerts(messageData || []);
        
        // 显示高级别告警
        messageData?.forEach(alert => {
          if (alert.level === 'critical' || alert.level === 'high') {
            toast.error(`风险告警: ${alert.message}`, {
              duration: 10000,
              action: {
                label: '查看详情',
                onClick: () => console.log('查看风险告警详情:', alert)
              }
            });
          }
        });
        break;

      case 'pong':
        // 心跳响应
        break;

      default:
        console.log('📨 未知消息类型:', type, data);
    }
  }, []);

  // 发送心跳
  const sendHeartbeat = useCallback(() => {
    sendMessage({ type: 'ping' });
  }, [sendMessage]);

  // 初始化和清理
  useEffect(() => {
    connect();

    // 设置心跳
    const heartbeatInterval = setInterval(sendHeartbeat, 30000); // 30秒心跳

    return () => {
      clearInterval(heartbeatInterval);
      if (subscribeTimeoutRef.current) {
        clearTimeout(subscribeTimeoutRef.current);
      }
      disconnect();
    };
  }, [connect, disconnect, sendHeartbeat]);

  // 获取策略数据
  const getStrategy = useCallback((taskId) => {
    return strategies[taskId] || null;
  }, [strategies]);

  // 获取风险指标
  const getRiskMetrics = useCallback((taskId) => {
    return riskMetrics[taskId] || null;
  }, [riskMetrics]);

  // 获取所有策略列表
  const getAllStrategies = useCallback(() => {
    return Object.values(strategies);
  }, [strategies]);

  return {
    // 连接状态
    isConnected,
    connectionError,
    
    // 数据
    strategies,
    riskMetrics,
    riskAlerts,
    
    // 方法
    connect,
    disconnect,
    subscribe,
    unsubscribe,
    sendMessage,
    getStrategy,
    getRiskMetrics,
    getAllStrategies,
    
    // 统计信息
    connectedStrategies: Object.keys(strategies).length,
    totalAlerts: riskAlerts.length,
    criticalAlerts: riskAlerts.filter(alert => alert.level === 'critical').length
  };
};
