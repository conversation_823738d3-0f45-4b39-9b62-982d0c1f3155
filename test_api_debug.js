// 测试API调用的调试脚本
// 在浏览器控制台中运行

// 测试1: 直接调用API
console.log('=== 测试1: 直接API调用 ===');
fetch('http://localhost:8001/api/data/stocks?page=1&page_size=100')
  .then(response => {
    console.log('Response status:', response.status);
    console.log('Response headers:', response.headers);
    return response.json();
  })
  .then(data => {
    console.log('Response data:', data);
  })
  .catch(error => {
    console.error('Error:', error);
  });

// 测试2: 检查参数构造
console.log('=== 测试2: 参数构造 ===');
const params = { page: 1, page_size: 100 };
const queryParams = new URLSearchParams();
queryParams.append('page', params.page);
queryParams.append('page_size', params.page_size);
const url = `/data/stocks?${queryParams.toString()}`;
console.log('构造的URL:', url);

// 测试3: 检查参数类型
console.log('=== 测试3: 参数类型检查 ===');
console.log('page type:', typeof params.page, 'value:', params.page);
console.log('page_size type:', typeof params.page_size, 'value:', params.page_size);

// 测试4: 模拟前端调用
console.log('=== 测试4: 模拟前端调用 ===');
const testParams = {
  page: 1,
  page_size: 100
};

console.log('测试参数:', testParams);

// 模拟API调用
const mockApiCall = (params = {}) => {
  const { page = 1, page_size = 100, limit = null } = params;
  console.log('解构后的参数:');
  console.log('  page:', page, typeof page);
  console.log('  page_size:', page_size, typeof page_size);
  console.log('  limit:', limit, typeof limit);
  
  const queryParams = new URLSearchParams();
  queryParams.append('page', page);
  queryParams.append('page_size', page_size);
  if (limit !== null) {
    queryParams.append('limit', limit);
  }
  
  const finalUrl = `/data/stocks?${queryParams.toString()}`;
  console.log('最终URL:', finalUrl);
  
  return finalUrl;
};

// 测试不同的调用方式
console.log('--- 正常调用 ---');
mockApiCall({ page: 1, page_size: 100 });

console.log('--- 无参数调用 ---');
mockApiCall();

console.log('--- 部分参数调用 ---');
mockApiCall({ page: 2 });

// 测试5: 检查可能的问题参数
console.log('=== 测试5: 问题参数检查 ===');
const problematicParams = [
  {},
  { page: undefined, page_size: undefined },
  { page: null, page_size: null },
  { page: 'string', page_size: 'string' },
  { page: [1], page_size: [100] },
  { page: {}, page_size: {} }
];

problematicParams.forEach((params, index) => {
  console.log(`--- 测试参数 ${index + 1}:`, params, '---');
  try {
    const result = mockApiCall(params);
    console.log('结果:', result);
  } catch (error) {
    console.error('错误:', error);
  }
});
