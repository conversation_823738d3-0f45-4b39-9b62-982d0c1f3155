#!/usr/bin/env python3
"""
实盘策略基类 - 支持多策略实盘交易
"""

import asyncio
import pandas as pd
import numpy as np
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum

from backend.core.logger import get_logger

logger = get_logger(__name__)

class StrategyStatus(Enum):
    """策略状态"""
    STOPPED = "stopped"      # 已停止
    STARTING = "starting"    # 启动中
    RUNNING = "running"      # 运行中
    PAUSED = "paused"       # 已暂停
    ERROR = "error"         # 错误状态

class SignalType(Enum):
    """信号类型"""
    BUY = "buy"
    SELL = "sell"
    HOLD = "hold"

@dataclass
class TradingSignal:
    """交易信号"""
    strategy_id: str
    stock_code: str
    signal_type: SignalType
    price: float
    quantity: int
    confidence: float  # 信号置信度 0-1
    reason: str       # 信号原因
    timestamp: datetime
    
    def to_dict(self):
        return {
            'strategy_id': self.strategy_id,
            'stock_code': self.stock_code,
            'signal_type': self.signal_type.value,
            'price': self.price,
            'quantity': self.quantity,
            'confidence': self.confidence,
            'reason': self.reason,
            'timestamp': self.timestamp.isoformat()
        }

@dataclass
class StrategyPosition:
    """策略持仓"""
    strategy_id: str
    stock_code: str
    quantity: int
    avg_price: float
    current_price: float
    market_value: float
    unrealized_pnl: float
    realized_pnl: float
    last_update: datetime
    
    def to_dict(self):
        return {
            'strategy_id': self.strategy_id,
            'stock_code': self.stock_code,
            'quantity': self.quantity,
            'avg_price': self.avg_price,
            'current_price': self.current_price,
            'market_value': self.market_value,
            'unrealized_pnl': self.unrealized_pnl,
            'realized_pnl': self.realized_pnl,
            'last_update': self.last_update.isoformat()
        }

@dataclass
class StrategyPerformance:
    """策略绩效"""
    strategy_id: str
    total_trades: int
    winning_trades: int
    losing_trades: int
    total_pnl: float
    win_rate: float
    avg_win: float
    avg_loss: float
    max_drawdown: float
    sharpe_ratio: float
    last_update: datetime
    
    def to_dict(self):
        return {
            'strategy_id': self.strategy_id,
            'total_trades': self.total_trades,
            'winning_trades': self.winning_trades,
            'losing_trades': self.losing_trades,
            'total_pnl': self.total_pnl,
            'win_rate': self.win_rate,
            'avg_win': self.avg_win,
            'avg_loss': self.avg_loss,
            'max_drawdown': self.max_drawdown,
            'sharpe_ratio': self.sharpe_ratio,
            'last_update': self.last_update.isoformat()
        }

class LiveStrategyBase(ABC):
    """实盘策略基类"""
    
    def __init__(self, strategy_id: str, config: Dict[str, Any]):
        self.strategy_id = strategy_id
        self.config = config
        self.status = StrategyStatus.STOPPED
        self.allocated_capital = config.get('allocated_capital', 100000)
        self.max_positions = config.get('max_positions', 5)
        self.position_size = config.get('position_size', 0.2)
        
        # 策略状态
        self.positions: Dict[str, StrategyPosition] = {}
        self.signals: List[TradingSignal] = []
        self.performance = StrategyPerformance(
            strategy_id=strategy_id,
            total_trades=0,
            winning_trades=0,
            losing_trades=0,
            total_pnl=0.0,
            win_rate=0.0,
            avg_win=0.0,
            avg_loss=0.0,
            max_drawdown=0.0,
            sharpe_ratio=0.0,
            last_update=datetime.now()
        )
        
        # 数据缓存
        self.market_data: Dict[str, pd.DataFrame] = {}
        self.last_update = datetime.now()
        
        logger.info(f"🎯 策略 {strategy_id} 初始化完成，分配资金: {self.allocated_capital:,.0f}")
    
    @abstractmethod
    async def generate_signals(self, market_data: Dict[str, pd.DataFrame]) -> List[TradingSignal]:
        """生成交易信号"""
        pass
    
    @abstractmethod
    def get_strategy_name(self) -> str:
        """获取策略名称"""
        pass
    
    @abstractmethod
    def get_strategy_description(self) -> str:
        """获取策略描述"""
        pass
    
    async def start(self):
        """启动策略"""
        self.status = StrategyStatus.STARTING
        logger.info(f"🚀 策略 {self.strategy_id} 启动中...")
        
        try:
            await self._initialize()
            self.status = StrategyStatus.RUNNING
            logger.info(f"✅ 策略 {self.strategy_id} 启动成功")
        except Exception as e:
            self.status = StrategyStatus.ERROR
            logger.error(f"❌ 策略 {self.strategy_id} 启动失败: {e}")
            raise
    
    async def stop(self):
        """停止策略"""
        logger.info(f"🛑 策略 {self.strategy_id} 停止中...")
        self.status = StrategyStatus.STOPPED
        await self._cleanup()
        logger.info(f"✅ 策略 {self.strategy_id} 已停止")
    
    async def pause(self):
        """暂停策略"""
        logger.info(f"⏸️ 策略 {self.strategy_id} 已暂停")
        self.status = StrategyStatus.PAUSED
    
    async def resume(self):
        """恢复策略"""
        logger.info(f"▶️ 策略 {self.strategy_id} 已恢复")
        self.status = StrategyStatus.RUNNING
    
    async def update_market_data(self, market_data: Dict[str, pd.DataFrame]):
        """更新市场数据"""
        self.market_data = market_data
        self.last_update = datetime.now()
        
        if self.status == StrategyStatus.RUNNING:
            # 生成交易信号
            new_signals = await self.generate_signals(market_data)
            self.signals.extend(new_signals)
            
            # 记录新信号
            for signal in new_signals:
                logger.info(f"📊 策略 {self.strategy_id} 生成信号: "
                           f"{signal.signal_type.value} {signal.stock_code} "
                           f"{signal.quantity}股 @{signal.price:.2f} - {signal.reason}")
    
    def update_position(self, stock_code: str, quantity: int, price: float):
        """更新持仓"""
        if stock_code in self.positions:
            position = self.positions[stock_code]
            # 更新平均成本
            total_quantity = position.quantity + quantity
            if total_quantity > 0:
                position.avg_price = (position.avg_price * position.quantity + price * quantity) / total_quantity
            position.quantity = total_quantity
        else:
            # 新建持仓
            self.positions[stock_code] = StrategyPosition(
                strategy_id=self.strategy_id,
                stock_code=stock_code,
                quantity=quantity,
                avg_price=price,
                current_price=price,
                market_value=quantity * price,
                unrealized_pnl=0.0,
                realized_pnl=0.0,
                last_update=datetime.now()
            )
        
        logger.info(f"📈 策略 {self.strategy_id} 持仓更新: {stock_code} {quantity}股 @{price:.2f}")
    
    def get_status_info(self) -> Dict[str, Any]:
        """获取策略状态信息"""
        return {
            'strategy_id': self.strategy_id,
            'strategy_name': self.get_strategy_name(),
            'status': self.status.value,
            'allocated_capital': self.allocated_capital,
            'positions_count': len(self.positions),
            'total_market_value': sum(pos.market_value for pos in self.positions.values()),
            'total_pnl': self.performance.total_pnl,
            'last_update': self.last_update.isoformat(),
            'performance': self.performance.to_dict()
        }
    
    def get_positions(self) -> List[Dict[str, Any]]:
        """获取持仓列表"""
        return [pos.to_dict() for pos in self.positions.values()]
    
    def get_recent_signals(self, limit: int = 10) -> List[Dict[str, Any]]:
        """获取最近的信号"""
        recent_signals = sorted(self.signals, key=lambda x: x.timestamp, reverse=True)[:limit]
        return [signal.to_dict() for signal in recent_signals]
    
    async def _initialize(self):
        """策略初始化"""
        pass
    
    async def _cleanup(self):
        """策略清理"""
        pass
