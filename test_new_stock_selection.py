#!/usr/bin/env python3
"""
测试新的选股界面和API
"""

import requests
import json
import time

def test_stock_selection_api():
    """测试选股API"""
    print("🧪 测试新的选股API...")
    
    # 测试条件：RSI超卖 + 成交量放大
    test_criteria = {
        "rsi_max": 30,          # RSI超卖
        "volume_min": 1.5,      # 成交量放大
        "condition_logic": "flexible"  # 灵活模式
    }
    
    payload = {
        "criteria": test_criteria,
        "custom_name": "测试_RSI超卖_成交量放大",
        "max_results": 10
    }
    
    try:
        print(f"📤 发送选股请求...")
        print(f"   条件: {json.dumps(test_criteria, indent=2, ensure_ascii=False)}")
        
        response = requests.post(
            'http://localhost:8000/api/stock-selection/select',
            json=payload,
            timeout=60
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                results = data.get('data', {})
                selected_stocks = results.get('selected_stocks', [])
                
                print(f"✅ 选股成功!")
                print(f"   共选中: {len(selected_stocks)} 只股票")
                print(f"   文件名: {results.get('filename', 'N/A')}")
                
                if selected_stocks:
                    print(f"\n📊 前5只股票:")
                    for i, stock in enumerate(selected_stocks[:5]):
                        print(f"   {i+1}. {stock.get('code')} - {stock.get('name')} (评分: {stock.get('score', 0):.2f})")
                        
                        # 显示技术指标
                        indicators = stock.get('indicators', {})
                        if indicators:
                            rsi = indicators.get('rsi', 'N/A')
                            volume_ratio = indicators.get('volume_ratio', 'N/A')
                            print(f"      RSI: {rsi}, 成交量比: {volume_ratio}")
                
                return True
            else:
                print(f"❌ 选股失败: {data.get('message', '未知错误')}")
                return False
        else:
            print(f"❌ API请求失败: {response.status_code}")
            print(f"   响应: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("⏰ 请求超时，选股可能需要更长时间")
        return False
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def test_presets_api():
    """测试预设条件API"""
    print("\n🧪 测试预设条件API...")
    
    try:
        response = requests.get('http://localhost:8000/api/stock-selection/presets', timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                presets = data.get('data', {}).get('presets', {})
                print(f"✅ 获取预设条件成功!")
                print(f"   共有 {len(presets)} 个预设策略:")
                
                for key, preset in presets.items():
                    print(f"   - {key}: {preset.get('name', 'N/A')}")
                
                return True
            else:
                print(f"❌ 获取预设条件失败: {data.get('message', '未知错误')}")
                return False
        else:
            print(f"❌ API请求失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def test_files_api():
    """测试选股文件API"""
    print("\n🧪 测试选股文件API...")
    
    try:
        response = requests.get('http://localhost:8000/api/stock-selection/files', timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                files = data.get('data', [])
                print(f"✅ 获取选股文件成功!")
                print(f"   共有 {len(files)} 个历史文件")
                
                if files:
                    print("   最近的文件:")
                    for file_info in files[:3]:
                        if isinstance(file_info, dict):
                            print(f"   - {file_info.get('filename', 'N/A')} ({file_info.get('total_selected', 0)} 只股票)")
                        else:
                            print(f"   - {file_info}")
                
                return True
            else:
                print(f"❌ 获取选股文件失败: {data.get('message', '未知错误')}")
                return False
        else:
            print(f"❌ API请求失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试新的选股系统")
    print("=" * 60)
    
    # 测试各个API
    tests = [
        ("预设条件API", test_presets_api),
        ("选股文件API", test_files_api),
        ("选股执行API", test_stock_selection_api),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🔍 测试 {test_name}...")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试 {test_name} 异常: {e}")
            results.append((test_name, False))
    
    # 总结测试结果
    print("\n" + "=" * 60)
    print("📋 测试结果总结:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        print("🎉 所有测试通过！新的选股系统工作正常！")
        print("\n📱 您现在可以:")
        print("   1. 访问 http://localhost:3000/stock-selection")
        print("   2. 勾选您感兴趣的选股条件")
        print("   3. 选择条件组合模式（灵活/严格/宽松）")
        print("   4. 点击'开始选股'按钮")
        print("   5. 查看选股结果")
    else:
        print("⚠️ 部分测试失败，请检查系统状态")

if __name__ == "__main__":
    main()
