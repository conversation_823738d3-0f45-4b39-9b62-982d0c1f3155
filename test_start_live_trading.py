#!/usr/bin/env python3
"""
测试启动实盘交易API
"""

import sys
import os
import requests
import json

# 添加项目路径
sys.path.append(os.path.dirname(__file__))

def test_start_live_trading():
    """测试启动实盘交易API"""
    try:
        print("🔍 测试启动实盘交易API...")
        
        # 测试数据
        test_data = {
            "strategy_name": "bollinger_bands",
            "initial_capital": 100000,
            "commission": 0.001,
            "stock_codes": ["000001.SZ", "000002.SZ"],
            "paper_trading": True,
            "strategy_params": {
                "period": 20,
                "std_dev": 2.0,
                "max_positions": 5,
                "position_size": 0.2
            }
        }
        
        print(f"请求数据: {json.dumps(test_data, indent=2, ensure_ascii=False)}")
        
        # 发送请求
        response = requests.post(
            'http://localhost:8000/api/live/start',
            json=test_data,
            headers={'Content-Type': 'application/json'}
        )
        
        print(f"状态码: {response.status_code}")
        print(f"响应头: {response.headers.get('content-type')}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
            
            if data.get('success'):
                print("✅ API调用成功")
                task_id = data.get('task_id')
                if task_id:
                    print(f"任务ID: {task_id}")
                else:
                    print("⚠️ 响应中没有task_id")
            else:
                print(f"❌ API返回失败: {data}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            try:
                error_data = response.json()
                print(f"错误详情: {json.dumps(error_data, indent=2, ensure_ascii=False)}")
            except:
                print(f"错误文本: {response.text}")
                
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def test_strategies_api():
    """测试策略列表API"""
    try:
        print("\n🔍 测试策略列表API...")
        
        response = requests.get('http://localhost:8000/api/backtest/strategies')
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success') and data.get('data'):
                strategies = data['data']
                print(f"✅ 获取到 {len(strategies)} 个策略")
                
                # 显示第一个策略的详细信息
                if strategies:
                    first_strategy = strategies[0]
                    print(f"第一个策略: {first_strategy.get('name')}")
                    print(f"参数: {list(first_strategy.get('parameters', {}).keys())}")
            else:
                print(f"❌ 策略API返回异常: {data}")
        else:
            print(f"❌ 策略API HTTP错误: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 策略API测试失败: {e}")

if __name__ == "__main__":
    test_strategies_api()
    test_start_live_trading()
