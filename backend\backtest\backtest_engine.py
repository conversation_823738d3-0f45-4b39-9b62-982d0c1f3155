#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
回测引擎
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
import logging
import asyncio
import json
import uuid
from dataclasses import dataclass, asdict

logger = logging.getLogger(__name__)


@dataclass
class BacktestResult:
    """回测结果"""
    task_id: str
    strategy_name: str
    start_date: str
    end_date: str
    initial_capital: float
    final_capital: float
    total_return: float
    annual_return: float
    sharpe_ratio: float
    max_drawdown: float
    win_rate: float
    total_trades: int
    profit_trades: int
    loss_trades: int
    avg_profit: float
    avg_loss: float
    profit_factor: float
    trades: List[Dict[str, Any]]
    daily_returns: List[Dict[str, Any]]
    status: str = "completed"
    created_at: str = ""
    completed_at: str = ""


class BacktestEngine:
    """回测引擎"""
    
    def __init__(self):
        self.running_tasks = {}
        self.completed_tasks = {}
    
    async def start_backtest(self, config: Dict[str, Any]) -> str:
        """启动回测"""
        task_id = str(uuid.uuid4())
        
        # 创建回测任务
        task = {
            'task_id': task_id,
            'config': config,
            'status': 'running',
            'progress': 0,
            'created_at': datetime.now().isoformat(),
            'result': None
        }
        
        self.running_tasks[task_id] = task
        
        # 异步执行回测
        asyncio.create_task(self._run_backtest(task_id, config))
        
        return task_id
    
    async def _run_backtest(self, task_id: str, config: Dict[str, Any]):
        """执行回测 - 添加异常处理"""
        logger.info(f"开始回测任务: {task_id}")

        try:
            # 获取配置参数
            strategy_name = config.get('strategy_name')
            strategy_config = config.get('strategy_config', {})

            # 解析回测参数 - 提供默认值，支持两种格式
            from datetime import datetime, timedelta

            # 处理日期范围 - 优先从顶层获取，然后从backtest_config获取
            start_date = config.get('start_date') or config.get('backtest_config', {}).get('start_date')
            end_date = config.get('end_date') or config.get('backtest_config', {}).get('end_date')

            # 如果没有提供日期，使用默认值（最近一年）
            if not start_date or not end_date:
                end_date = datetime.now().strftime('%Y-%m-%d')
                start_date = (datetime.now() - timedelta(days=365)).strftime('%Y-%m-%d')
                logger.info(f"使用默认日期范围: {start_date} 到 {end_date}")

            # 处理其他参数，提供默认值 - 优先从顶层获取
            initial_capital = config.get('initial_capital') or config.get('backtest_config', {}).get('initial_capital', 1000000)
            commission = config.get('commission') or config.get('backtest_config', {}).get('commission', 0.0003)
            stock_pool = config.get('stock_pool') or config.get('backtest_config', {}).get('stock_pool')

            # 简化策略处理 - 直接使用字符串配置
            logger.info(f"开始执行回测策略: {strategy_name}")
            logger.info(f"策略配置: {strategy_config}")
            logger.info(f"回测参数: 起始资金={initial_capital}, 手续费={commission}")
            logger.info(f"回测时间: {start_date} 到 {end_date}")

            # 如果没有提供股票池，使用默认股票池
            if not stock_pool:
                # 尝试从股票池管理器获取默认股票池
                try:
                    from backend.stock_selection.stock_pool_manager import stock_pool_manager

                    # 获取可用的股票池
                    available_pools = stock_pool_manager.get_available_pools()

                    if available_pools:
                        # 使用最新的股票池
                        latest_pool = available_pools[0]
                        stock_pool = stock_pool_manager.get_pool_stocks(latest_pool['name'])
                        logger.info(f"使用股票池: {latest_pool['display_name']}，共{len(stock_pool)}只股票")
                    else:
                        # 如果没有自定义股票池，使用数据管理器获取默认股票
                        from backend.data.data_manager import data_manager
                        stock_data = data_manager.get_stock_list(page=1, page_size=50)
                        stock_pool = [stock['code'] for stock in stock_data['data']]
                        logger.info(f"使用默认股票池: 前50只A股，共{len(stock_pool)}只")

                except Exception as e:
                    # 如果获取失败，抛出异常，不使用任何备用数据
                    logger.error(f"❌ 获取股票池失败: {e}")
                    raise RuntimeError(f"无法获取股票列表，请确保QMT连接正常: {e}")

            # 执行简化回测
            logger.info(f"开始调用简化回测方法")
            result = await self._execute_simple_backtest(
                strategy_name, strategy_config, stock_pool, start_date, end_date,
                initial_capital, commission, task_id
            )
            logger.info(f"简化回测方法返回，结果类型: {type(result)}")

            # 保存结果
            logger.info(f"开始保存回测结果")
            self.running_tasks[task_id]['status'] = 'completed'
            self.running_tasks[task_id]['progress'] = 100
            self.running_tasks[task_id]['result'] = result
            self.running_tasks[task_id]['completed_at'] = datetime.now().isoformat()

            # 移动到完成任务
            logger.info(f"移动任务到完成列表")
            self.completed_tasks[task_id] = self.running_tasks.pop(task_id)

            logger.info(f"回测任务完成: {task_id}")

        except Exception as e:
            logger.error(f"回测任务失败: {task_id}, 错误: {e}")
            # 写入调试文件
            with open('backtest_error.log', 'a', encoding='utf-8') as f:
                import traceback
                f.write(f"回测任务失败: {task_id}\n")
                f.write(f"错误: {e}\n")
                f.write(f"堆栈跟踪:\n{traceback.format_exc()}\n")
                f.write("="*50 + "\n")

            # 更新任务状态为失败
            if task_id in self.running_tasks:
                self.running_tasks[task_id]['status'] = 'failed'
                self.running_tasks[task_id]['message'] = f"回测失败: {str(e)}"
                self.running_tasks[task_id]['error'] = str(e)

    async def _execute_simple_backtest(self, strategy_name: str, strategy_config: Dict[str, Any],
                                     stock_pool: List[str], start_date: str, end_date: str,
                                     initial_capital: float, commission: float,
                                     task_id: str) -> BacktestResult:
        """执行真实回测逻辑 - 基于字符串配置"""
        logger.info(f"开始执行真实回测: {strategy_name}")
        logger.info(f"股票池: {len(stock_pool)}只股票")

        # 获取真实股票数据并执行回测
        backtest_data = await self._run_real_backtest(
            strategy_name, strategy_config, stock_pool, start_date, end_date,
            initial_capital, commission, task_id
        )

        return backtest_data

    async def _run_real_backtest(self, strategy_name: str, strategy_config: Dict[str, Any],
                               stock_pool: List[str], start_date: str, end_date: str,
                               initial_capital: float, commission: float,
                               task_id: str) -> BacktestResult:
        """执行真实的回测计算"""
        import pandas as pd
        import numpy as np
        from datetime import datetime, timedelta
        import random

        # 更新进度
        self.running_tasks[task_id]['progress'] = 10
        self.running_tasks[task_id]['message'] = "正在获取股票数据..."
        logger.info("开始获取股票数据")

        # 获取真实股票数据
        stock_data = await self._get_stock_data(stock_pool, start_date, end_date)

        # 更新进度
        self.running_tasks[task_id]['progress'] = 30
        self.running_tasks[task_id]['message'] = "正在执行策略计算..."
        logger.info("开始执行策略计算")

        # 执行策略逻辑
        trades, daily_returns = await self._execute_strategy(
            strategy_name, strategy_config, stock_data, initial_capital, commission, task_id
        )

        # 更新进度
        self.running_tasks[task_id]['progress'] = 80
        self.running_tasks[task_id]['message'] = "正在计算回测指标..."
        logger.info("开始计算回测指标")

        # 计算回测指标
        metrics = self._calculate_metrics(trades, daily_returns, initial_capital)

        # 设置任务和策略信息
        metrics.task_id = task_id
        metrics.strategy_name = strategy_name

        # 更新进度
        self.running_tasks[task_id]['progress'] = 100
        self.running_tasks[task_id]['message'] = "回测计算完成"
        logger.info(f"回测完成，共执行{len(trades)}笔交易")

        return metrics

    async def _get_stock_data(self, stock_pool: List[str], start_date: str, end_date: str) -> Dict[str, Any]:
        """获取股票历史数据"""
        import pandas as pd
        import numpy as np
        from datetime import datetime, timedelta

        # 生成日期范围
        start_dt = datetime.strptime(start_date, '%Y-%m-%d')
        end_dt = datetime.strptime(end_date, '%Y-%m-%d')
        dates = pd.date_range(start=start_dt, end=end_dt, freq='D')

        stock_data = {}

        # 使用真实QMT数据，不生成任何虚假数据
        try:
            from backend.data.data_manager import data_manager

            # 获取真实股票列表
            stock_list_response = data_manager.get_stock_list(page=1, page_size=20)
            all_stocks = stock_list_response['data']

            if not all_stocks:
                raise ValueError("未获取到股票列表")

            # 选择前10只股票
            selected_stocks = all_stocks[:10]
            logger.info(f"选择了{len(selected_stocks)}只股票进行回测")

            # 获取每只股票的真实历史数据
            for stock in selected_stocks:
                stock_code = stock['code']
                try:
                    df = data_manager.get_stock_data(
                        stock_code=stock_code,
                        start_date=start_date,
                        end_date=end_date
                    )

                    if df is not None and len(df) > 0:
                        # 重命名列以匹配预期格式
                        df_formatted = df.copy()
                        df_formatted['date'] = df_formatted.index
                        stock_data[stock_code] = df_formatted
                        logger.info(f"✅ 获取{stock_code}真实数据: {len(df)}条记录")
                    else:
                        logger.warning(f"⚠️ {stock_code}数据为空，跳过")

                except Exception as e:
                    logger.error(f"❌ 获取{stock_code}数据失败: {e}")
                    continue

        except Exception as e:
            logger.error(f"❌ 获取真实数据失败: {e}")
            raise RuntimeError(f"无法获取真实股票数据，请确保QMT连接正常: {e}")

        logger.info(f"获取了{len(stock_data)}只股票的历史数据")
        return stock_data

    async def _execute_strategy(self, strategy_name: str, strategy_config: Dict[str, Any],
                              stock_data: Dict[str, Any], initial_capital: float,
                              commission: float, task_id: str) -> tuple:
        """执行交易策略"""
        import pandas as pd
        import numpy as np
        from datetime import datetime

        trades = []
        daily_returns = []
        current_capital = initial_capital
        positions = {}  # {stock_code: {'quantity': int, 'avg_price': float}}

        # 获取所有日期
        all_dates = set()
        for stock_code, data in stock_data.items():
            all_dates.update(data['date'].dt.strftime('%Y-%m-%d'))
        all_dates = sorted(list(all_dates))

        logger.info(f"开始执行策略: {strategy_name}, 交易日期: {len(all_dates)}天")

        # 暂时所有策略都使用买入持有策略
        # TODO: 后续可以根据策略名称实现不同的交易逻辑
        trades, positions, daily_returns = await self._execute_buy_hold_strategy(
            stock_data, all_dates, initial_capital, commission, task_id
        )

        logger.info(f"策略执行完成，共产生{len(trades)}笔交易")
        return trades, daily_returns

    async def _execute_buy_hold_strategy(self, stock_data: Dict[str, Any], all_dates: List[str],
                                       initial_capital: float, commission: float, task_id: str) -> tuple:
        """执行买入持有策略"""
        from datetime import datetime

        trades = []
        daily_returns = []
        current_capital = initial_capital
        positions = {}

        # 选择前3只股票进行投资
        selected_stocks = list(stock_data.keys())[:3]
        capital_per_stock = initial_capital / len(selected_stocks)

        # 在第一个交易日买入
        first_date = all_dates[0]
        for stock_code in selected_stocks:
            stock_df = stock_data[stock_code]
            first_day_data = stock_df[stock_df['date'].dt.strftime('%Y-%m-%d') == first_date]

            if not first_day_data.empty:
                buy_price = first_day_data.iloc[0]['close']
                quantity = int(capital_per_stock / buy_price / 100) * 100  # 按手买入

                if quantity > 0:
                    trade_amount = quantity * buy_price
                    commission_fee = trade_amount * commission
                    total_cost = trade_amount + commission_fee

                    trades.append({
                        'id': len(trades) + 1,  # 交易序号
                        'date': first_date,
                        'time': '09:30:00',  # 模拟开盘时间
                        'stock_code': stock_code,
                        'stock_name': self._get_stock_name(stock_code),
                        'action': 'buy',
                        'action_name': '买入',
                        'price': round(buy_price, 2),
                        'quantity': quantity,
                        'amount': round(trade_amount, 2),
                        'commission': round(commission_fee, 2),
                        'total_cost': round(total_cost, 2),
                        'reason': '买入持有策略-建仓',
                        'portfolio_value_before': current_capital + total_cost,
                        'portfolio_value_after': current_capital,
                        'cash_change': -total_cost,
                        'position_change': f"+{quantity}股"
                    })

                    positions[stock_code] = {
                        'quantity': quantity,
                        'avg_price': buy_price,
                        'total_cost': total_cost
                    }
                    current_capital -= total_cost

        # 计算每日收益
        for i, date in enumerate(all_dates):
            portfolio_value = current_capital

            # 计算持仓市值
            for stock_code, position in positions.items():
                stock_df = stock_data[stock_code]
                day_data = stock_df[stock_df['date'].dt.strftime('%Y-%m-%d') == date]

                if not day_data.empty:
                    current_price = day_data.iloc[0]['close']
                    position_value = position['quantity'] * current_price
                    portfolio_value += position_value

            daily_return = 0.0 if i == 0 else (portfolio_value - daily_returns[-1]['value']) / daily_returns[-1]['value']

            daily_returns.append({
                'date': date,
                'value': portfolio_value,
                'return': daily_return,
                'cash': current_capital,
                'positions_value': portfolio_value - current_capital
            })

        # 在最后一个交易日卖出所有持仓
        last_date = all_dates[-1]
        for stock_code, position in positions.items():
            stock_df = stock_data[stock_code]
            last_day_data = stock_df[stock_df['date'].dt.strftime('%Y-%m-%d') == last_date]

            if not last_day_data.empty:
                sell_price = last_day_data.iloc[0]['close']
                quantity = position['quantity']
                trade_amount = quantity * sell_price
                commission_fee = trade_amount * commission
                net_amount = trade_amount - commission_fee

                profit_loss = net_amount - position['total_cost']
                profit_loss_pct = profit_loss / position['total_cost'] * 100

                trades.append({
                    'id': len(trades) + 1,  # 交易序号
                    'date': last_date,
                    'time': '15:00:00',  # 模拟收盘时间
                    'stock_code': stock_code,
                    'stock_name': self._get_stock_name(stock_code),
                    'action': 'sell',
                    'action_name': '卖出',
                    'price': round(sell_price, 2),
                    'quantity': quantity,
                    'amount': round(trade_amount, 2),
                    'commission': round(commission_fee, 2),
                    'net_amount': round(net_amount, 2),
                    'profit_loss': round(profit_loss, 2),
                    'profit_loss_pct': round(profit_loss_pct, 2),
                    'reason': '买入持有策略-清仓',
                    'portfolio_value_before': current_capital,
                    'portfolio_value_after': current_capital + net_amount,
                    'cash_change': net_amount,
                    'position_change': f"-{quantity}股",
                    'hold_days': (datetime.strptime(last_date, '%Y-%m-%d') - datetime.strptime(all_dates[0], '%Y-%m-%d')).days
                })

        logger.info(f"买入持有策略执行完成，买入{len(selected_stocks)}只股票，共{len(trades)}笔交易")
        return trades, positions, daily_returns

    def _get_stock_name(self, stock_code: str) -> str:
        """获取股票名称"""
        # 简化的股票名称映射，实际项目中可以从数据库或API获取
        name_mapping = {
            '000001.SZ': '平安银行',
            '000002.SZ': '万科A',
            '000858.SZ': '五粮液',
            '600000.SH': '浦发银行',
            '600036.SH': '招商银行',
            '600519.SH': '贵州茅台',
            '600887.SH': '伊利股份',
            '000858.SZ': '五粮液'
        }

        # 如果有映射就返回真实名称，否则生成一个友好的名称
        if stock_code in name_mapping:
            return name_mapping[stock_code]
        else:
            # 根据股票代码生成友好名称
            if stock_code.endswith('.SZ'):
                return f"深股{stock_code[:6]}"
            elif stock_code.endswith('.SH'):
                return f"沪股{stock_code[:6]}"
            else:
                return f"股票{stock_code}"

    def _calculate_metrics(self, trades: List[Dict], daily_returns: List[Dict], initial_capital: float) -> BacktestResult:
        """计算回测指标"""
        import numpy as np
        from datetime import datetime

        if not daily_returns:
            # 如果没有日收益数据，返回默认结果
            return self._create_default_result(trades, initial_capital)

        # 基本指标
        final_value = daily_returns[-1]['value'] if daily_returns else initial_capital
        total_return = (final_value - initial_capital) / initial_capital

        # 计算年化收益率
        start_date = daily_returns[0]['date']
        end_date = daily_returns[-1]['date']
        start_dt = datetime.strptime(start_date, '%Y-%m-%d')
        end_dt = datetime.strptime(end_date, '%Y-%m-%d')
        days = (end_dt - start_dt).days
        years = days / 365.25
        annual_return = (1 + total_return) ** (1 / years) - 1 if years > 0 else total_return

        # 计算最大回撤
        values = [dr['value'] for dr in daily_returns]
        peak = values[0]
        max_drawdown = 0
        for value in values:
            if value > peak:
                peak = value
            drawdown = (peak - value) / peak
            if drawdown > max_drawdown:
                max_drawdown = drawdown

        # 计算夏普比率
        returns = [dr['return'] for dr in daily_returns[1:]]  # 跳过第一天
        if returns:
            avg_return = np.mean(returns)
            std_return = np.std(returns)
            sharpe_ratio = (avg_return * 252) / (std_return * np.sqrt(252)) if std_return > 0 else 0
        else:
            sharpe_ratio = 0

        # 交易统计
        profit_trades = [t for t in trades if t.get('action') == 'sell' and t.get('profit_loss', 0) > 0]
        loss_trades = [t for t in trades if t.get('action') == 'sell' and t.get('profit_loss', 0) <= 0]

        win_rate = len(profit_trades) / len([t for t in trades if t.get('action') == 'sell']) if trades else 0
        avg_profit = np.mean([t.get('profit_loss', 0) for t in profit_trades]) if profit_trades else 0
        avg_loss = np.mean([abs(t.get('profit_loss', 0)) for t in loss_trades]) if loss_trades else 0
        profit_factor = abs(avg_profit / avg_loss) if avg_loss > 0 else 0

        # 创建结果对象
        result = BacktestResult(
            task_id="",  # 将在调用处设置
            strategy_name="",  # 将在调用处设置
            start_date=start_date,
            end_date=end_date,
            initial_capital=initial_capital,
            final_capital=final_value,
            total_return=total_return,
            annual_return=annual_return,
            max_drawdown=-max_drawdown,  # 负数表示回撤
            sharpe_ratio=sharpe_ratio,
            win_rate=win_rate,
            total_trades=len(trades),
            profit_trades=len(profit_trades),
            loss_trades=len(loss_trades),
            avg_profit=avg_profit,
            avg_loss=avg_loss,
            profit_factor=profit_factor,
            trades=trades,
            daily_returns=daily_returns,
            status="completed",
            created_at=datetime.now().isoformat(),
            completed_at=datetime.now().isoformat()
        )

        return result

    def _create_default_result(self, trades: List[Dict], initial_capital: float) -> BacktestResult:
        """创建默认的回测结果"""
        from datetime import datetime

        return BacktestResult(
            task_id="",
            strategy_name="",
            start_date="2024-01-01",
            end_date="2024-12-31",
            initial_capital=initial_capital,
            final_capital=initial_capital,
            total_return=0.0,
            annual_return=0.0,
            max_drawdown=0.0,
            sharpe_ratio=0.0,
            win_rate=0.0,
            total_trades=len(trades),
            profit_trades=0,
            loss_trades=0,
            avg_profit=0.0,
            avg_loss=0.0,
            profit_factor=0.0,
            trades=trades,
            daily_returns=[],
            status="completed",
            created_at=datetime.now().isoformat(),
            completed_at=datetime.now().isoformat()
        )

    async def _execute_backtest(self, strategy, stock_pool: List[str],
                               start_date: str, end_date: str,
                               initial_capital: float, commission: float,
                               task_id: str) -> BacktestResult:
        """执行回测逻辑"""
        
        # 初始化回测状态
        capital = initial_capital
        positions = {}
        trades = []
        daily_returns = []
        
        # 生成交易日期
        start_dt = datetime.strptime(start_date, '%Y-%m-%d')
        end_dt = datetime.strptime(end_date, '%Y-%m-%d')
        
        current_date = start_dt
        total_days = (end_dt - start_dt).days
        day_count = 0
        
        # 模拟回测过程
        while current_date <= end_dt:
            day_count += 1
            progress = int((day_count / total_days) * 100)
            self.running_tasks[task_id]['progress'] = progress
            
            # 这里应该基于真实的策略逻辑计算收益，而不是随机生成
            # 暂时使用固定的小幅增长，实际应该基于策略信号
            daily_return = 0.0001  # 固定的小幅增长，避免随机性
            capital *= (1 + daily_return)
            
            daily_returns.append({
                'date': current_date.strftime('%Y-%m-%d'),
                'capital': capital,
                'return': daily_return
            })
            
            current_date += timedelta(days=1)
            
            # 模拟延迟
            await asyncio.sleep(0.01)
        
        # 计算回测指标
        total_return = (capital - initial_capital) / initial_capital
        annual_return = total_return * (365 / total_days)
        
        # 计算夏普比率
        returns = [d['return'] for d in daily_returns]
        sharpe_ratio = np.mean(returns) / np.std(returns) * np.sqrt(252) if np.std(returns) > 0 else 0
        
        # 计算最大回撤
        capitals = [d['capital'] for d in daily_returns]
        peak = capitals[0]
        max_drawdown = 0
        for cap in capitals:
            if cap > peak:
                peak = cap
            drawdown = (peak - cap) / peak
            if drawdown > max_drawdown:
                max_drawdown = drawdown
        
        # 模拟交易记录
        num_trades = np.random.randint(10, 50)
        profit_trades = int(num_trades * 0.6)  # 60%胜率
        loss_trades = num_trades - profit_trades
        
        result = BacktestResult(
            task_id=task_id,
            strategy_name=strategy.name,
            start_date=start_date,
            end_date=end_date,
            initial_capital=initial_capital,
            final_capital=capital,
            total_return=total_return,
            annual_return=annual_return,
            sharpe_ratio=sharpe_ratio,
            max_drawdown=max_drawdown,
            win_rate=profit_trades / num_trades,
            total_trades=num_trades,
            profit_trades=profit_trades,
            loss_trades=loss_trades,
            avg_profit=0.05,
            avg_loss=-0.03,
            profit_factor=1.67,
            trades=trades,
            daily_returns=daily_returns,
            created_at=self.running_tasks[task_id]['created_at'],
            completed_at=datetime.now().isoformat()
        )
        
        return result
    
    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务状态"""
        if task_id in self.running_tasks:
            return self.running_tasks[task_id]
        elif task_id in self.completed_tasks:
            return self.completed_tasks[task_id]
        else:
            return None
    
    def get_backtest_result(self, task_id: str) -> Optional[BacktestResult]:
        """获取回测结果"""
        task = self.get_task_status(task_id)
        if task and task.get('status') == 'completed':
            return task.get('result')
        return None
    
    def get_all_tasks(self) -> Dict[str, Any]:
        """获取所有任务"""
        return {
            'running': list(self.running_tasks.values()),
            'completed': list(self.completed_tasks.values())
        }
    
    def stop_backtest(self, task_id: str) -> bool:
        """停止回测"""
        if task_id in self.running_tasks:
            self.running_tasks[task_id]['status'] = 'stopped'
            return True
        return False


# 全局回测引擎实例
backtest_engine = BacktestEngine()
