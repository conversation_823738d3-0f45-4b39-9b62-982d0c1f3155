<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>task_id修复验证测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        pre {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
            max-height: 300px;
        }
        button {
            padding: 8px 16px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
            cursor: pointer;
        }
        .btn-primary { background: #007bff; color: white; }
        .fix-highlight {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>task_id 修复验证测试</h1>
    
    <div class="fix-highlight">
        <h3>🔧 修复内容</h3>
        <p><strong>问题：</strong> <code>Cannot read properties of undefined (reading 'task_id')</code></p>
        <p><strong>原因：</strong> 代码使用了 <code>response.data.task_id</code>，但API返回的是 <code>response.task_id</code></p>
        <p><strong>修复：</strong> 改为使用 <code>response.task_id</code></p>
    </div>
    
    <div class="test-section">
        <h2>1. API响应格式验证</h2>
        <button class="btn-primary" onclick="testApiResponseFormat()">验证API响应格式</button>
        <div id="api-format-result"></div>
    </div>
    
    <div class="test-section">
        <h2>2. 修复前后对比</h2>
        <button class="btn-primary" onclick="testFixComparison()">对比修复前后</button>
        <div id="fix-comparison-result"></div>
    </div>
    
    <div class="test-section">
        <h2>3. 完整启动流程测试</h2>
        <button class="btn-primary" onclick="testCompleteFlow()">测试完整流程</button>
        <div id="complete-flow-result"></div>
    </div>

    <script>
        async function testApiResponseFormat() {
            const resultDiv = document.getElementById('api-format-result');
            resultDiv.innerHTML = '<p>测试中...</p>';
            
            try {
                const testData = {
                    "strategy_name": "bollinger_bands",
                    "initial_capital": 100000,
                    "commission": 0.001,
                    "stock_codes": ["000001.SZ"],
                    "paper_trading": true,
                    "strategy_params": {
                        "period": 20,
                        "std_dev": 2.0
                    }
                };

                const response = await fetch('http://localhost:8000/api/live/start', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(testData)
                });

                const data = await response.json();
                
                resultDiv.innerHTML = `
                    <div class="success">
                        <h4>✅ API响应格式验证</h4>
                        <p><strong>响应结构：</strong></p>
                        <ul>
                            <li>success: ${data.success}</li>
                            <li>task_id: ${data.task_id ? '✅ 存在' : '❌ 不存在'}</li>
                            <li>message: ${data.message}</li>
                            <li>data字段: ${data.data ? '存在' : '不存在'}</li>
                        </ul>
                        <p><strong>完整响应：</strong></p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `<p class="error">❌ 测试失败: ${error.message}</p>`;
            }
        }

        async function testFixComparison() {
            const resultDiv = document.getElementById('fix-comparison-result');
            resultDiv.innerHTML = '<p>测试中...</p>';
            
            try {
                const testData = {
                    "strategy_name": "bollinger_bands",
                    "initial_capital": 50000,
                    "stock_codes": ["000001.SZ"],
                    "paper_trading": true,
                    "strategy_params": { "period": 20 }
                };

                const response = await fetch('http://localhost:8000/api/live/start', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(testData)
                });

                const data = await response.json();
                
                // 模拟修复前的处理
                let oldWay = '❌ 错误';
                try {
                    const taskIdOld = data.data.task_id;  // 这会出错
                    oldWay = `✅ ${taskIdOld}`;
                } catch (e) {
                    oldWay = `❌ ${e.message}`;
                }
                
                // 修复后的处理
                let newWay = '❌ 错误';
                try {
                    const taskIdNew = data.task_id;  // 正确的方式
                    newWay = taskIdNew ? `✅ ${taskIdNew}` : '❌ 未找到';
                } catch (e) {
                    newWay = `❌ ${e.message}`;
                }
                
                resultDiv.innerHTML = `
                    <div>
                        <h4>🔧 修复前后对比</h4>
                        <table border="1" style="width:100%; border-collapse: collapse;">
                            <tr>
                                <th style="padding: 8px;">方式</th>
                                <th style="padding: 8px;">代码</th>
                                <th style="padding: 8px;">结果</th>
                            </tr>
                            <tr>
                                <td style="padding: 8px;">修复前</td>
                                <td style="padding: 8px;"><code>response.data.task_id</code></td>
                                <td style="padding: 8px;">${oldWay}</td>
                            </tr>
                            <tr>
                                <td style="padding: 8px;">修复后</td>
                                <td style="padding: 8px;"><code>response.task_id</code></td>
                                <td style="padding: 8px;">${newWay}</td>
                            </tr>
                        </table>
                        <p><strong>API响应：</strong></p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `<p class="error">❌ 测试失败: ${error.message}</p>`;
            }
        }

        async function testCompleteFlow() {
            const resultDiv = document.getElementById('complete-flow-result');
            resultDiv.innerHTML = '<p>测试完整启动流程...</p>';
            
            try {
                // 模拟前端完整流程
                const configData = {
                    "strategy_name": "bollinger_bands",
                    "initial_capital": 80000,
                    "commission": 0.001,
                    "stock_codes": ["000001.SZ", "000002.SZ"],
                    "paper_trading": true,
                    "strategy_params": {
                        "period": 20,
                        "std_dev": 2.0,
                        "max_positions": 3
                    }
                };

                // 1. 发送启动请求
                const response = await fetch('http://localhost:8000/api/live/start', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(configData)
                });

                const data = await response.json();
                
                let flowResult = '<h4>🚀 完整启动流程测试</h4>';
                
                // 2. 检查响应
                if (data.success === true) {
                    flowResult += '<p class="success">✅ 步骤1: API调用成功</p>';
                    
                    // 3. 获取task_id（修复后的方式）
                    const taskId = data.task_id;
                    if (taskId) {
                        flowResult += `<p class="success">✅ 步骤2: 成功获取task_id: ${taskId.slice(0, 8)}...</p>`;
                        
                        // 4. 模拟toast消息
                        const toastMessage = `策略启动成功！任务ID: ${taskId.slice(0, 8)}...`;
                        flowResult += `<p class="success">✅ 步骤3: Toast消息: ${toastMessage}</p>`;
                        
                        // 5. 模拟关闭模态框和刷新列表
                        flowResult += '<p class="success">✅ 步骤4: 模态框关闭，策略列表刷新</p>';
                        
                        flowResult += '<p class="success"><strong>🎉 完整流程成功！</strong></p>';
                    } else {
                        flowResult += '<p class="warning">⚠️ 步骤2: 响应成功但没有task_id</p>';
                    }
                } else {
                    const errorMsg = data.message || data.detail || '启动失败';
                    flowResult += `<p class="error">❌ 步骤1: API调用失败: ${errorMsg}</p>`;
                }
                
                flowResult += `<p><strong>请求数据：</strong></p>`;
                flowResult += `<pre>${JSON.stringify(configData, null, 2)}</pre>`;
                flowResult += `<p><strong>响应数据：</strong></p>`;
                flowResult += `<pre>${JSON.stringify(data, null, 2)}</pre>`;
                
                resultDiv.innerHTML = flowResult;
                
            } catch (error) {
                resultDiv.innerHTML = `
                    <p class="error">❌ 完整流程测试失败: ${error.message}</p>
                    <pre>${error.stack}</pre>
                `;
            }
        }

        // 页面加载时显示修复说明
        window.onload = function() {
            console.log('task_id修复验证页面加载完成');
        };
    </script>
</body>
</html>
