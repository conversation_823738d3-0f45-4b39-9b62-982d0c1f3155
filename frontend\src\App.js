import React, { useState } from 'react';
import { Routes, Route } from 'react-router-dom';
import Dashboard from './pages/Dashboard';
import DataManagementPage from './pages/DataManagementPage';
import StrategyBacktestPage from './pages/StrategyBacktestPage';
import SystemConfigPage from './pages/SystemConfigPage';

import BacktestResultDetailPage from './pages/BacktestResultDetailPage';
import BacktestHistoryPage from './pages/BacktestHistoryPage';
import BacktestComparisonPage from './pages/BacktestComparisonPage';
import MultiStrategyLiveTradingPage from './pages/MultiStrategyLiveTradingPage';

import TestDataPage from './pages/TestDataPage';
import PositionMonitorPage from './pages/PositionMonitorPage.jsx';
import StockSelectionPage from './pages/StockSelectionPage';
import StockPoolPage from './pages/StockPoolPage';
import Sidebar from './components/Layout/Sidebar';
import Header from './components/Layout/Header';
import './App.css';
import { Toaster } from 'sonner';

function App() {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  return (
    <div className="flex h-screen bg-gray-100">
      {/* Sidebar */}
      <Sidebar collapsed={sidebarCollapsed} onToggle={toggleSidebar} />

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Header */}
        <Header />

        {/* Content */}
        <main className="flex-1 overflow-x-hidden overflow-y-auto bg-gray-100 p-6">
          <Routes>
            <Route path="/" element={<Dashboard />} />
            <Route path="/backtest" element={<StrategyBacktestPage />} />
            <Route path="/backtest/results/:taskId" element={<BacktestResultDetailPage />} />
            <Route path="/backtest/history" element={<BacktestHistoryPage />} />
            <Route path="/backtest/comparison" element={<BacktestComparisonPage />} />
            <Route path="/multi-strategy" element={<MultiStrategyLiveTradingPage />} />

            <Route path="/position-monitor" element={<PositionMonitorPage />} />
            <Route path="/stock-selection" element={<StockSelectionPage />} />
            <Route path="/stock-pools" element={<StockPoolPage />} />
            <Route path="/data-management" element={<DataManagementPage />} />
            <Route path="/test-data" element={<TestDataPage />} />
            <Route path="/strategy-config" element={<StrategyBacktestPage />} />
            <Route path="/system-config" element={<SystemConfigPage />} />
          </Routes>
        </main>
        <Toaster position="top-center" richColors />
      </div>
    </div>
  );
}

export default App;
