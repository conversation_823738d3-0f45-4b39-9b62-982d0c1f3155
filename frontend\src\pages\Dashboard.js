import React, { useState, useEffect } from 'react';
import { Alert as Shad<PERSON><PERSON>t } from '../components/UI/alert.jsx';
import { Spinner } from '../components/UI/spinner.jsx';
import { <PERSON><PERSON> as ShadButton } from '../components/UI/button.jsx';
import { RefreshCw } from 'lucide-react';
import { dataAPI, backtestAPI, liveAPI, apiUtils } from '../services/api';
import { Card as ShadCard, CardHeader as ShadCardHeader, CardTitle as ShadCardTitle, CardContent as ShadCardContent } from '../components/UI/card.jsx';
import { Stat } from '../components/UI/stat.jsx';

const Dashboard = () => {
  const [loading, setLoading] = useState(true);
  const [dataStatus, setDataStatus] = useState(null);
  const [backtestStatus, setBacktestStatus] = useState(null);
  const [accountInfo, setAccountInfo] = useState(null);
  const [error, setError] = useState(null);

  const fetchData = async () => {
    setLoading(true);
    setError(null);
    
    try {
      // 获取数据状态
      const dataResponse = await dataAPI.getDataStatus();
      if (apiUtils.isSuccess(dataResponse)) {
        setDataStatus(apiUtils.getData(dataResponse));
      }

      // 获取回测状态
      const backtestResponse = await backtestAPI.getBacktestStatus();
      if (apiUtils.isSuccess(backtestResponse)) {
        setBacktestStatus(apiUtils.getData(backtestResponse));
      }

      // 尝试获取账户信息
      try {
        const accountResponse = await liveAPI.getAccountInfo();
        if (apiUtils.isSuccess(accountResponse)) {
          setAccountInfo(apiUtils.getData(accountResponse));
        }
      } catch (accountError) {
        // 账户信息获取失败是正常的（可能未启动实盘交易）
        console.log('账户信息获取失败:', accountError.message);
      }

    } catch (err) {
      setError(apiUtils.handleError(err, '获取仪表板数据失败'));
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
    
    // 设置定时刷新
    const interval = setInterval(fetchData, 30000); // 30秒刷新一次
    
    return () => clearInterval(interval);
  }, []);

  const getStatusText = (status) => {
    switch (status) {
      case 'running':
        return '运行中';
      case 'completed':
        return '已完成';
      case 'failed':
        return '失败';
      case 'idle':
        return '空闲';
      default:
        return '未知';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-20">
        <div className="text-center">
          <Spinner size="lg" />
          <div className="mt-4 text-gray-600">加载仪表板数据...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-gray-900">仪表板</h1>
        <ShadButton
          onClick={fetchData}
          disabled={loading}
          className="inline-flex items-center gap-2"
        >
          <RefreshCw className="w-4 h-4" />
          刷新
        </ShadButton>
      </div>

      {/* Error Alert */}
      {error && (
        <ShadAlert
          title="错误"
          description={error}
          variant="error"
          closable
          className="mb-6"
          onClose={() => setError(null)}
        />
      )}

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <ShadCard>
          <ShadCardContent className="p-4">
            <Stat title="股票数量" value={dataStatus?.stock_count || 0} suffix="只" />
          </ShadCardContent>
        </ShadCard>
        <ShadCard>
          <ShadCardContent className="p-4">
            <Stat title="数据记录" value={dataStatus?.record_count || 0} suffix="条" />
          </ShadCardContent>
        </ShadCard>
        <ShadCard>
          <ShadCardContent className="p-4">
            <Stat title="数据源状态" value={dataStatus?.xt_available ? "可用" : "不可用"} />
          </ShadCardContent>
        </ShadCard>
        <ShadCard>
          <ShadCardContent className="p-4">
            <Stat
              title="最新更新"
              value={dataStatus?.latest_update ?
                new Date(dataStatus.latest_update).toLocaleDateString() :
                '无数据'
              }
            />
          </ShadCardContent>
        </ShadCard>
      </div>

      {/* Account Information */}
      {accountInfo && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <ShadCard>
            <ShadCardContent className="p-4">
              <Stat title="账户总值" value={accountInfo.total_value?.toLocaleString() || 0} suffix="¥" />
            </ShadCardContent>
          </ShadCard>
          <ShadCard>
            <ShadCardContent className="p-4">
              <Stat title="可用资金" value={accountInfo.available_cash?.toLocaleString() || 0} suffix="¥" />
            </ShadCardContent>
          </ShadCard>
          <ShadCard>
            <ShadCardContent className="p-4">
              <Stat title="持仓市值" value={accountInfo.market_value?.toLocaleString() || 0} suffix="¥" />
            </ShadCardContent>
          </ShadCard>
        </div>
      )}

      {/* System Status */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <ShadCard>
          <ShadCardHeader>
            <div className="flex items-center justify-between">
              <ShadCardTitle>回测状态</ShadCardTitle>
              <ShadButton
                onClick={() => window.location.href = '/backtest'}
                className="text-blue-600 hover:text-blue-800 text-sm"
                variant="ghost"
              >
                查看详情
              </ShadButton>
            </div>
          </ShadCardHeader>
          <ShadCardContent>
          {backtestStatus ? (
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <span className="text-gray-600">状态:</span>
                <span className={`font-medium ${
                  backtestStatus.status === 'running' ? 'text-blue-600' :
                  backtestStatus.status === 'completed' ? 'text-green-600' :
                  backtestStatus.status === 'failed' ? 'text-red-600' : 'text-gray-600'
                }`}>
                  {getStatusText(backtestStatus.status)}
                </span>
              </div>
              <div className="text-gray-700">{backtestStatus.message}</div>
              {backtestStatus.status === 'running' && (
                <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                  <div className="bg-blue-600 h-2 rounded-full animate-pulse" style={{width: '50%'}}></div>
                </div>
              )}
            </div>
          ) : (
            <div className="text-gray-500">无回测任务</div>
          )}
          </ShadCardContent>
        </ShadCard>

        <ShadCard>
          <ShadCardHeader>
            <div className="flex items-center justify-between">
              <ShadCardTitle>数据范围</ShadCardTitle>
              <ShadButton
                onClick={() => window.location.href = '/data-management'}
                className="text-blue-600 hover:text-blue-800 text-sm"
                variant="ghost"
              >
                管理数据
              </ShadButton>
            </div>
          </ShadCardHeader>
          <ShadCardContent>
          {dataStatus?.date_range ? (
            <div className="space-y-2">
              <div><span className="text-gray-600">开始日期:</span> {dataStatus.date_range.start || '无数据'}</div>
              <div><span className="text-gray-600">结束日期:</span> {dataStatus.date_range.end || '无数据'}</div>
              <div className="mt-2">
                <span className="text-gray-600">覆盖天数:</span> {
                  dataStatus.date_range.start && dataStatus.date_range.end ?
                  Math.ceil((new Date(dataStatus.date_range.end) - new Date(dataStatus.date_range.start)) / (1000 * 60 * 60 * 24)) :
                  0
                } 天
              </div>
            </div>
          ) : (
            <div className="text-gray-500">无数据</div>
          )}
          </ShadCardContent>
        </ShadCard>
      </div>

      {/* Holdings Information */}
      {accountInfo && accountInfo.positions && accountInfo.positions.length > 0 && (
        <ShadCard className="mt-6">
          <ShadCardHeader>
            <ShadCardTitle>当前持仓</ShadCardTitle>
          </ShadCardHeader>
          <ShadCardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {accountInfo.positions.map((position, index) => (
              <div key={index} className="bg-gray-50 rounded-lg p-4 border border-gray-200">
                <div className="font-bold text-lg text-gray-900 mb-2">{position.stock_code}</div>
                <div className="space-y-1 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">数量:</span>
                    <span>{position.quantity}股</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">均价:</span>
                    <span>¥{position.avg_price.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">市值:</span>
                    <span>¥{position.market_value.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">盈亏:</span>
                    <span className={position.pnl >= 0 ? 'text-green-600' : 'text-red-600'}>
                      ¥{position.pnl.toFixed(2)} ({(position.pnl_ratio * 100).toFixed(2)}%)
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
          </ShadCardContent>
        </ShadCard>
      )}
    </div>
  );
};

export default Dashboard;
