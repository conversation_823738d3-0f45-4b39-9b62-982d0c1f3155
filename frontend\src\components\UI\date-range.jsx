import * as React from 'react'
import * as Popover from '@radix-ui/react-popover'
import { DayPicker } from 'react-day-picker'
import { format } from 'date-fns'
import 'react-day-picker/dist/style.css'

export function DateRangePicker({ value, onChange, className = '' }) {
  const range = value || { from: undefined, to: undefined }
  return (
    <Popover.Root>
      <Popover.Trigger asChild>
        <button className={`w-full h-9 px-3 rounded-md border bg-white text-left ${className}`}>
          {range?.from && range?.to ? `${format(range.from, 'yyyy-MM-dd')} ~ ${format(range.to, 'yyyy-MM-dd')}` : '选择时间范围'}
        </button>
      </Popover.Trigger>
      <Popover.Content className="bg-white p-2 rounded-md shadow-lg border" sideOffset={4}>
        <DayPicker
          mode="range"
          selected={range}
          onSelect={(r) => onChange && onChange(r)}
          numberOfMonths={2}
        />
      </Popover.Content>
    </Popover.Root>
  )
}

