#!/usr/bin/env python3
"""
策略状态管理器
负责策略状态的持久化存储和恢复
"""

import os
import json
import asyncio
from datetime import datetime
from typing import Dict, List, Any, Optional
from pathlib import Path

from backend.core.logger import get_logger

logger = get_logger(__name__)


class StrategyStateManager:
    """策略状态管理器"""
    
    def __init__(self, state_file: str = "data/live_trading/strategy_states.json"):
        """
        初始化状态管理器
        
        Args:
            state_file: 状态文件路径
        """
        self.state_file = Path(state_file)
        self.state_file.parent.mkdir(parents=True, exist_ok=True)
        
        # 内存中的状态缓存
        self._states: Dict[str, Dict[str, Any]] = {}
        
        # 加载现有状态
        self._load_states()
        
        logger.info(f"📁 策略状态管理器初始化完成，状态文件: {self.state_file}")
    
    def _load_states(self):
        """从文件加载策略状态"""
        try:
            if self.state_file.exists():
                with open(self.state_file, 'r', encoding='utf-8') as f:
                    self._states = json.load(f)
                logger.info(f"✅ 加载策略状态: {len(self._states)} 个策略")
            else:
                self._states = {}
                logger.info(f"📝 创建新的策略状态文件")
        except Exception as e:
            logger.error(f"❌ 加载策略状态失败: {e}")
            self._states = {}
    
    async def _save_states(self):
        """保存策略状态到文件"""
        try:
            with open(self.state_file, 'w', encoding='utf-8') as f:
                json.dump(self._states, f, ensure_ascii=False, indent=2)
            logger.debug(f"💾 策略状态已保存: {len(self._states)} 个策略")
        except Exception as e:
            logger.error(f"❌ 保存策略状态失败: {e}")
    
    async def save_strategy_state(self, task_id: str, strategy_data: Dict[str, Any]):
        """
        保存策略状态
        
        Args:
            task_id: 策略任务ID
            strategy_data: 策略数据
        """
        try:
            # 更新状态
            self._states[task_id] = {
                'task_id': task_id,
                'strategy_name': strategy_data.get('strategy_name'),
                'status': strategy_data.get('status'),
                'start_time': strategy_data.get('start_time'),
                'current_time': datetime.now().isoformat(),
                'initial_capital': strategy_data.get('initial_capital'),
                'current_capital': strategy_data.get('current_capital'),
                'total_return': strategy_data.get('total_return'),
                'paper_trading': strategy_data.get('paper_trading'),
                'stock_codes': strategy_data.get('stock_codes'),
                'strategy_params': strategy_data.get('strategy_params'),
                'positions': strategy_data.get('positions', []),
                'orders': strategy_data.get('orders', []),
                'created_at': strategy_data.get('created_at'),
                'last_updated': datetime.now().isoformat()
            }
            
            # 保存到文件
            await self._save_states()
            
            logger.debug(f"✅ 策略状态已保存: {task_id}")
            
        except Exception as e:
            logger.error(f"❌ 保存策略状态失败: {task_id}, 错误: {e}")
    
    async def update_strategy_status(self, task_id: str, status: str):
        """
        更新策略状态
        
        Args:
            task_id: 策略任务ID
            status: 新状态 ('running', 'paused', 'error')
        """
        try:
            if task_id in self._states:
                self._states[task_id]['status'] = status
                self._states[task_id]['current_time'] = datetime.now().isoformat()
                self._states[task_id]['last_updated'] = datetime.now().isoformat()
                
                await self._save_states()
                logger.info(f"✅ 策略状态已更新: {task_id} -> {status}")
            else:
                logger.warning(f"⚠️ 策略不存在: {task_id}")
                
        except Exception as e:
            logger.error(f"❌ 更新策略状态失败: {task_id}, 错误: {e}")
    
    async def delete_strategy_state(self, task_id: str):
        """
        删除策略状态
        
        Args:
            task_id: 策略任务ID
        """
        try:
            if task_id in self._states:
                del self._states[task_id]
                await self._save_states()
                logger.info(f"✅ 策略状态已删除: {task_id}")
                return True
            else:
                logger.warning(f"⚠️ 策略不存在: {task_id}")
                return False
                
        except Exception as e:
            logger.error(f"❌ 删除策略状态失败: {task_id}, 错误: {e}")
            return False
    
    def get_strategy_state(self, task_id: str) -> Optional[Dict[str, Any]]:
        """
        获取策略状态
        
        Args:
            task_id: 策略任务ID
            
        Returns:
            策略状态数据
        """
        return self._states.get(task_id)
    
    def get_all_strategy_states(self) -> Dict[str, Dict[str, Any]]:
        """获取所有策略状态"""
        return self._states.copy()
    
    def get_running_strategies(self) -> List[Dict[str, Any]]:
        """获取所有运行中的策略"""
        return [
            state for state in self._states.values()
            if state.get('status') == 'running'
        ]
    
    def get_paused_strategies(self) -> List[Dict[str, Any]]:
        """获取所有暂停的策略"""
        return [
            state for state in self._states.values()
            if state.get('status') == 'paused'
        ]
    
    async def recover_strategies(self) -> List[Dict[str, Any]]:
        """
        恢复策略状态（用于系统重启后）
        
        Returns:
            需要恢复的策略列表
        """
        try:
            # 获取所有运行中的策略
            running_strategies = self.get_running_strategies()
            
            if running_strategies:
                logger.info(f"🔄 发现 {len(running_strategies)} 个需要恢复的策略")
                
                # 将运行中的策略标记为暂停（因为系统重启了）
                for strategy in running_strategies:
                    await self.update_strategy_status(strategy['task_id'], 'paused')
                
                return running_strategies
            else:
                logger.info(f"📝 没有需要恢复的策略")
                return []
                
        except Exception as e:
            logger.error(f"❌ 恢复策略状态失败: {e}")
            return []
    
    async def cleanup_old_states(self, days: int = 30):
        """
        清理旧的策略状态
        
        Args:
            days: 保留天数
        """
        try:
            from datetime import timedelta
            
            cutoff_time = datetime.now() - timedelta(days=days)
            
            to_delete = []
            for task_id, state in self._states.items():
                last_updated = state.get('last_updated', state.get('created_at'))
                if last_updated:
                    try:
                        update_time = datetime.fromisoformat(last_updated)
                        if update_time < cutoff_time:
                            to_delete.append(task_id)
                    except ValueError:
                        # 如果时间格式有问题，也删除
                        to_delete.append(task_id)
            
            # 删除旧状态
            for task_id in to_delete:
                del self._states[task_id]
            
            if to_delete:
                await self._save_states()
                logger.info(f"🧹 清理了 {len(to_delete)} 个旧策略状态")
            
        except Exception as e:
            logger.error(f"❌ 清理旧策略状态失败: {e}")


# 全局状态管理器实例
strategy_state_manager = StrategyStateManager()
