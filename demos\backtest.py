# -*- coding: utf-8 -*-
"""
布林线策略回测模块
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import logging
from config import get_config, get_preset_config
from bollinger_strategy import BollingerStrategy

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

logger = logging.getLogger(__name__)

class BacktestAccount:
    """回测虚拟账户"""

    def __init__(self, initial_capital):
        self.account_id = "BACKTEST_ACCOUNT"
        self.initial_capital = initial_capital
        self.cash = initial_capital
        self.positions = {}
        self.total_value = initial_capital

    def get_balance(self):
        """获取账户余额"""
        return self.cash

    def get_positions(self):
        """获取持仓信息"""
        return self.positions.copy()

    def get_total_value(self, current_prices):
        """计算账户总价值"""
        stock_value = 0
        for stock_code, shares in self.positions.items():
            if shares > 0 and stock_code in current_prices:
                stock_value += shares * current_prices[stock_code]

        self.total_value = self.cash + stock_value
        return self.total_value

class BacktestStrategy(BollingerStrategy):
    """回测专用策略类"""

    def __init__(self, config):
        super().__init__(config)
        # 不初始化QMT相关组件
        self.xt_trader = None
        self.account = None

    def connect_trader(self, min_path, account_id):
        """回测模式下不需要连接真实交易端"""
        return True

    def get_stock_data(self, stock_code, period=100):
        """回测模式下的数据获取（由回测引擎提供）"""
        # 这个方法在回测中不会被调用
        return None

class BacktestEngine:
    """回测引擎"""

    def __init__(self, config):
        self.config = config
        self.initial_capital = config['backtest_config']['initial_capital']
        self.commission_rate = config['backtest_config']['commission_rate']
        self.slippage = config['backtest_config']['slippage']

        # 创建虚拟账户
        self.backtest_account = BacktestAccount(self.initial_capital)

        # 回测结果
        self.trades = []
        self.daily_returns = []
        self.portfolio_values = []
        self.positions = {}
        self.cash = self.initial_capital

        # 创建回测专用策略实例
        self.strategy = BacktestStrategy(config)
        
    def get_stock_data_backtest(self, stock_code, start_date, end_date):
        """获取回测用的真实股票数据"""
        try:
            # 使用xtdata获取真实历史数据
            from xtquant import xtdata

            # 转换日期格式
            start_str = start_date.strftime('%Y%m%d')
            end_str = end_date.strftime('%Y%m%d')

            logger.info(f"获取{stock_code}历史数据: {start_str} 到 {end_str}")

            # 获取历史数据
            data = xtdata.get_market_data(
                stock_list=[stock_code],
                period='1d',
                start_time=start_str,
                end_time=end_str,
                count=-1
            )

            if stock_code in data and len(data[stock_code]) > 0:
                df = data[stock_code]
                logger.info(f"✅ 获取到{stock_code}数据: {len(df)}个交易日")

                # 返回价格数据和日期索引
                prices = df['close'].values
                dates = df.index

                return prices, dates
            else:
                logger.warning(f"❌ 未获取到{stock_code}的数据")
                return None, None

        except ImportError:
            logger.error("❌ xtdata模块未找到，无法获取真实股票数据")
            raise ImportError("必须安装xtquant模块才能获取真实股票数据，不允许使用虚假数据")
        except Exception as e:
            logger.error(f"❌ 获取{stock_code}真实数据失败: {str(e)}")
            raise RuntimeError(f"无法获取{stock_code}的真实数据，不允许使用虚假数据: {e}")


    
    def calculate_portfolio_value(self, current_prices):
        """计算投资组合总价值"""
        stock_value = 0
        for stock_code, shares in self.positions.items():
            if shares > 0 and stock_code in current_prices:
                stock_value += shares * current_prices[stock_code]
        
        return self.cash + stock_value
    
    def execute_trade(self, stock_code, action, shares, price, trade_date):
        """执行交易"""
        if action == 'buy':
            cost = shares * price * (1 + self.commission_rate + self.slippage)
            if cost <= self.cash:
                self.cash -= cost
                self.positions[stock_code] = self.positions.get(stock_code, 0) + shares

                trade = {
                    'date': trade_date,
                    'stock': stock_code,
                    'action': 'buy',
                    'shares': shares,
                    'price': price,
                    'cost': cost
                }
                self.trades.append(trade)
                logger.info(f"🛒 [{trade_date.strftime('%Y-%m-%d')}] 买入: {stock_code} {shares}股@{price:.2f}元, 成本: {cost:.2f}元")
                return True

        elif action == 'sell':
            if self.positions.get(stock_code, 0) >= shares:
                proceeds = shares * price * (1 - self.commission_rate - self.slippage)
                self.cash += proceeds
                self.positions[stock_code] -= shares

                # 计算盈亏（如果有买入记录）
                profit_info = ""
                buy_trades = [t for t in self.trades if t['stock'] == stock_code and t['action'] == 'buy']
                if buy_trades:
                    avg_buy_price = sum(t['price'] * t['shares'] for t in buy_trades) / sum(t['shares'] for t in buy_trades)
                    profit = (price - avg_buy_price) * shares
                    profit_rate = (price - avg_buy_price) / avg_buy_price * 100
                    profit_info = f", 盈亏: {profit:+.2f}元 ({profit_rate:+.2f}%)"

                trade = {
                    'date': trade_date,
                    'stock': stock_code,
                    'action': 'sell',
                    'shares': shares,
                    'price': price,
                    'proceeds': proceeds
                }
                self.trades.append(trade)
                logger.info(f"💰 [{trade_date.strftime('%Y-%m-%d')}] 卖出: {stock_code} {shares}股@{price:.2f}元, 收入: {proceeds:.2f}元{profit_info}")
                return True

        return False
    
    def run_backtest(self, start_date, end_date):
        """运行回测"""
        logger.info(f"开始回测: {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')}")

        # 获取所有股票的历史数据
        stock_data = {}
        stock_dates = {}

        for stock_code in self.config['stock_pool']:
            prices, dates = self.get_stock_data_backtest(stock_code, start_date, end_date)
            if prices is not None and len(prices) > 50:
                stock_data[stock_code] = prices
                stock_dates[stock_code] = dates

        if not stock_data:
            logger.error("没有获取到有效的股票数据")
            return

        # 获取最短的数据长度和统一的日期序列
        min_length = min(len(data) for data in stock_data.values())

        # 使用第一只股票的日期作为基准（假设所有股票的交易日相同）
        base_stock = list(stock_data.keys())[0]
        trading_dates = stock_dates[base_stock][:min_length]

        logger.info(f"回测数据准备完成，共{min_length}个交易日")

        # 初始化记录
        self.trading_dates = trading_dates

        # 逐日回测
        for day in range(50, min_length):  # 从第50天开始，确保有足够历史数据
            current_date = trading_dates[day]
            current_prices = {}

            logger.info(f"📅 回测日期: {current_date.strftime('%Y-%m-%d')} (第{day+1}个交易日)")

            # 获取当日价格和历史数据
            for stock_code, data in stock_data.items():
                current_prices[stock_code] = data[day]
                historical_prices = data[:day+1]

                # 计算布林线
                middle_line, upper_line, lower_line = self.strategy.calculate_bollinger_bands(historical_prices)
                if middle_line is None:
                    continue

                current_price = current_prices[stock_code]
                current_holding = self.positions.get(stock_code, 0)

                # 检查卖出条件
                if current_holding > 0:
                    if self.strategy.check_sell_conditions(stock_code, current_price, middle_line):
                        self.execute_trade(stock_code, 'sell', current_holding, current_price, current_date)

                # 检查买入条件
                else:
                    if self.strategy.is_uptrend_advanced(stock_code, historical_prices):
                        if self.strategy.check_buy_conditions(stock_code, current_price, middle_line, upper_line):
                            # 计算买入数量
                            position_value = self.cash * self.config['position_weight']
                            shares = int(position_value / current_price / 100) * 100

                            if shares >= 100:
                                self.execute_trade(stock_code, 'buy', shares, current_price, current_date)

            # 记录每日组合价值
            portfolio_value = self.calculate_portfolio_value(current_prices)
            self.portfolio_values.append(portfolio_value)

            # 计算日收益率
            if len(self.portfolio_values) > 1:
                daily_return = (portfolio_value - self.portfolio_values[-2]) / self.portfolio_values[-2]
                self.daily_returns.append(daily_return)

            # 每10个交易日输出一次进度
            if day % 10 == 0:
                logger.info(f"📊 组合价值: {portfolio_value:,.2f}元, 现金: {self.cash:,.2f}元, 持仓: {len([p for p in self.positions.values() if p > 0])}只")

        logger.info("回测完成")
        self.analyze_results()
    
    def analyze_results(self):
        """分析回测结果"""
        if not self.portfolio_values:
            logger.error("没有回测数据可分析")
            return
        
        # 计算关键指标
        final_value = self.portfolio_values[-1]
        total_return = (final_value - self.initial_capital) / self.initial_capital
        
        if self.daily_returns:
            daily_returns = np.array(self.daily_returns)
            annual_return = np.mean(daily_returns) * 252
            volatility = np.std(daily_returns) * np.sqrt(252)
            sharpe_ratio = annual_return / volatility if volatility > 0 else 0
            
            # 最大回撤
            cumulative_returns = np.cumprod(1 + daily_returns)
            running_max = np.maximum.accumulate(cumulative_returns)
            drawdown = (cumulative_returns - running_max) / running_max
            max_drawdown = np.min(drawdown)
        else:
            annual_return = 0
            volatility = 0
            sharpe_ratio = 0
            max_drawdown = 0
        
        # 交易统计
        total_trades = len(self.trades)
        buy_trades = len([t for t in self.trades if t['action'] == 'buy'])
        sell_trades = len([t for t in self.trades if t['action'] == 'sell'])
        
        # 输出结果
        logger.info("=" * 50)
        logger.info("回测结果分析")
        logger.info("=" * 50)
        logger.info(f"初始资金: {self.initial_capital:,.2f}元")
        logger.info(f"最终价值: {final_value:,.2f}元")
        logger.info(f"总收益率: {total_return:.2%}")
        logger.info(f"年化收益率: {annual_return:.2%}")
        logger.info(f"年化波动率: {volatility:.2%}")
        logger.info(f"夏普比率: {sharpe_ratio:.2f}")
        logger.info(f"最大回撤: {max_drawdown:.2%}")
        logger.info(f"总交易次数: {total_trades}")
        logger.info(f"买入次数: {buy_trades}")
        logger.info(f"卖出次数: {sell_trades}")
        logger.info("=" * 50)
        
        return {
            'total_return': total_return,
            'annual_return': annual_return,
            'volatility': volatility,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown,
            'total_trades': total_trades
        }
    
    def plot_results(self):
        """绘制回测结果"""
        if not self.portfolio_values or not hasattr(self, 'trading_dates'):
            logger.error("没有数据可绘制")
            return

        # 准备时间序列数据
        plot_dates = self.trading_dates[50:50+len(self.portfolio_values)]  # 对应回测开始的日期

        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(14, 10))

        # 绘制组合价值曲线
        ax1.plot(plot_dates, self.portfolio_values, label='投资组合价值', linewidth=2, color='blue')
        ax1.axhline(y=self.initial_capital, color='red', linestyle='--', label='初始资金', alpha=0.7)
        ax1.set_title('投资组合价值变化', fontsize=14, fontweight='bold')
        ax1.set_ylabel('价值（元）', fontsize=12)
        ax1.legend(fontsize=10)
        ax1.grid(True, alpha=0.3)

        # 格式化x轴日期
        ax1.tick_params(axis='x', rotation=45)

        # 添加收益率标注
        if self.portfolio_values:
            total_return = (self.portfolio_values[-1] - self.initial_capital) / self.initial_capital
            ax1.text(0.02, 0.98, f'总收益率: {total_return:.2%}',
                    transform=ax1.transAxes, fontsize=10,
                    verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))

        # 绘制日收益率
        if self.daily_returns and len(self.daily_returns) > 0:
            returns_dates = plot_dates[1:len(self.daily_returns)+1]  # 收益率比价值少一天
            ax2.plot(returns_dates, self.daily_returns, label='日收益率', alpha=0.7, color='green')
            ax2.axhline(y=0, color='red', linestyle='-', alpha=0.5)
            ax2.set_title('日收益率变化', fontsize=14, fontweight='bold')
            ax2.set_ylabel('收益率', fontsize=12)
            ax2.set_xlabel('日期', fontsize=12)
            ax2.legend(fontsize=10)
            ax2.grid(True, alpha=0.3)
            ax2.tick_params(axis='x', rotation=45)

            # 添加统计信息
            if len(self.daily_returns) > 0:
                avg_return = np.mean(self.daily_returns)
                volatility = np.std(self.daily_returns)
                ax2.text(0.02, 0.98, f'平均日收益: {avg_return:.4f}\n日波动率: {volatility:.4f}',
                        transform=ax2.transAxes, fontsize=9,
                        verticalalignment='top', bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))

        plt.tight_layout()

        # 保存图片
        filename = f'backtest_results_{datetime.now().strftime("%Y%m%d_%H%M%S")}.png'
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        logger.info(f"📊 回测结果图表已保存: {filename}")

        plt.show()

def main():
    """主函数"""
    # 设置日志
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    
    # 获取配置
    config = get_preset_config('balanced')  # 可选: 'conservative', 'aggressive', 'balanced'
    
    # 创建回测引擎
    backtest = BacktestEngine(config)
    
    # 运行回测
    start_date = datetime(2023, 1, 1)
    end_date = datetime(2024, 12, 31)
    
    backtest.run_backtest(start_date, end_date)
    
    # 绘制结果
    backtest.plot_results()

if __name__ == "__main__":
    main()
