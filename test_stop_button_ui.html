<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>停止按钮UI测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        .demo-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .button-demo {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 32px;
            height: 32px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            background: transparent;
            transition: all 0.2s;
        }
        .btn-view {
            color: #2563eb;
        }
        .btn-view:hover {
            color: #1d4ed8;
            background: #dbeafe;
        }
        .btn-stop-active {
            color: #dc2626;
        }
        .btn-stop-active:hover {
            color: #b91c1c;
            background: #fef2f2;
        }
        .btn-stop-disabled {
            color: #9ca3af;
            cursor: not-allowed;
        }
        .strategy-row {
            display: flex;
            align-items: center;
            padding: 10px;
            border: 1px solid #e5e7eb;
            border-radius: 4px;
            margin: 5px 0;
            background: white;
        }
        .strategy-info {
            flex: 1;
            margin-right: 10px;
        }
        .strategy-name {
            font-weight: 600;
            margin-bottom: 4px;
        }
        .strategy-status {
            font-size: 12px;
            padding: 2px 6px;
            border-radius: 12px;
            color: white;
        }
        .status-running { background: #10b981; }
        .status-stopped { background: #6b7280; }
        .tag {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            margin-left: 8px;
        }
        .tag-paper { background: #dcfce7; color: #166534; }
        .tag-real { background: #fee2e2; color: #991b1b; }
        .improvement-box {
            background: #f0f9ff;
            border: 1px solid #0ea5e9;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <h1>停止按钮UI改进测试</h1>
    
    <div class="improvement-box">
        <h3>🔧 改进内容</h3>
        <ul>
            <li>✅ 将 <code>Square</code> 图标改为 <code>StopCircle</code> 图标</li>
            <li>✅ 运行中策略：红色按钮，悬停时背景变红</li>
            <li>✅ 已停止策略：灰色按钮，禁用状态</li>
            <li>✅ 添加 tooltip 提示</li>
            <li>✅ 查看按钮：蓝色，悬停时背景变蓝</li>
        </ul>
    </div>

    <div class="demo-section">
        <h2>1. 按钮样式对比</h2>
        
        <h3>查看按钮（蓝色）</h3>
        <button class="button-demo btn-view" title="查看策略详情">
            👁️
        </button>
        <span>悬停试试看</span>
        
        <h3>停止按钮 - 运行中（红色）</h3>
        <button class="button-demo btn-stop-active" title="停止策略">
            ⏹️
        </button>
        <span>悬停试试看</span>
        
        <h3>停止按钮 - 已停止（灰色，禁用）</h3>
        <button class="button-demo btn-stop-disabled" title="策略已停止" disabled>
            ⏹️
        </button>
        <span>已禁用状态</span>
    </div>

    <div class="demo-section">
        <h2>2. 策略表格行示例</h2>
        
        <div class="strategy-row">
            <div class="strategy-info">
                <div class="strategy-name">bollinger_bands_12424574</div>
                <span class="strategy-status status-running">运行中</span>
                <span class="tag tag-paper">纸上交易</span>
            </div>
            <div>
                <button class="button-demo btn-view" title="查看策略详情">👁️</button>
                <button class="button-demo btn-stop-active" title="停止策略">⏹️</button>
            </div>
        </div>
        
        <div class="strategy-row">
            <div class="strategy-info">
                <div class="strategy-name">bollinger_bands_c676b3d0</div>
                <span class="strategy-status status-stopped">已停止</span>
                <span class="tag tag-real">实盘交易</span>
            </div>
            <div>
                <button class="button-demo btn-view" title="查看策略详情">👁️</button>
                <button class="button-demo btn-stop-disabled" title="策略已停止" disabled>⏹️</button>
            </div>
        </div>
    </div>

    <div class="demo-section">
        <h2>3. 代码改进对比</h2>
        
        <h3>改进前：</h3>
        <pre style="background: #f5f5f5; padding: 10px; border-radius: 4px;">
&lt;ShadButton
  variant="ghost"
  size="sm"
  onClick={() => handleStopStrategy(strategy)}
  className="h-8 w-8 p-0 text-red-600 hover:text-red-700"
  disabled={strategy.status !== 'running'}
&gt;
  &lt;Square className="h-4 w-4" /&gt;  {/* 不够明显 */}
&lt;/ShadButton&gt;</pre>
        
        <h3>改进后：</h3>
        <pre style="background: #f0f9ff; padding: 10px; border-radius: 4px;">
&lt;ShadButton
  variant="ghost"
  size="sm"
  onClick={() => handleStopStrategy(strategy)}
  className={`h-8 w-8 p-0 ${
    strategy.status === 'running' 
      ? 'text-red-600 hover:text-red-700 hover:bg-red-50' 
      : 'text-gray-400 cursor-not-allowed'
  }`}
  disabled={strategy.status !== 'running'}
  title={strategy.status === 'running' ? '停止策略' : '策略已停止'}
&gt;
  &lt;StopCircle className="h-4 w-4" /&gt;  {/* 更明显的图标 */}
&lt;/ShadButton&gt;</pre>
    </div>

    <div class="demo-section">
        <h2>4. 测试步骤</h2>
        <ol>
            <li>访问 <a href="http://localhost:3000/multi-strategy" target="_blank">多策略交易页面</a></li>
            <li>启动一个策略（选择纸上交易模式）</li>
            <li>观察策略表格中的操作按钮：
                <ul>
                    <li>👁️ 蓝色查看按钮</li>
                    <li>⏹️ 红色停止按钮（运行中时）</li>
                </ul>
            </li>
            <li>悬停在按钮上查看 tooltip 提示</li>
            <li>点击停止按钮测试功能</li>
            <li>观察停止后按钮变为灰色禁用状态</li>
        </ol>
    </div>

    <div class="demo-section">
        <h2>5. 预期效果</h2>
        <div style="background: #dcfce7; padding: 10px; border-radius: 4px; border-left: 4px solid #16a34a;">
            <h4>✅ 现在您应该能看到：</h4>
            <ul>
                <li><strong>蓝色查看按钮</strong>：👁️ 悬停时背景变蓝</li>
                <li><strong>红色停止按钮</strong>：⏹️ 悬停时背景变红</li>
                <li><strong>灰色禁用按钮</strong>：⏹️ 策略停止后不可点击</li>
                <li><strong>Tooltip提示</strong>：悬停时显示操作说明</li>
                <li><strong>交易模式标签</strong>：绿色=纸上交易，红色=实盘交易</li>
            </ul>
        </div>
    </div>

    <script>
        // 添加一些交互效果
        document.querySelectorAll('.button-demo').forEach(button => {
            button.addEventListener('click', function() {
                if (!this.disabled) {
                    const action = this.classList.contains('btn-view') ? '查看' : '停止';
                    alert(`${action}按钮被点击！`);
                }
            });
        });
    </script>
</body>
</html>
