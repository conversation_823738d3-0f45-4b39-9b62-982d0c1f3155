import React, { useState, useEffect } from 'react';
import { Button as ShadButton } from '../components/UI/button.jsx';
import { Plus, Trash2, Eye, Search, TrendingUp, BarChart3, Star, Target, RefreshCw, Upload, FileText } from 'lucide-react';
import { toast } from 'sonner';

const StockPoolPage = () => {
  const [stockPools, setStockPools] = useState([]);
  const [selectedPool, setSelectedPool] = useState(null);
  const [loading, setLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showAddStockModal, setShowAddStockModal] = useState(false);
  const [showImportModal, setShowImportModal] = useState(false);
  const [showManualImportModal, setShowManualImportModal] = useState(false);
  const [selectionResults, setSelectionResults] = useState([]);
  const [selectedResult, setSelectedResult] = useState(null);
  const [selectionStocks, setSelectionStocks] = useState([]);
  const [selectedStocks, setSelectedStocks] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [stocksPerPage] = useState(20);

  // 新建股票池表单
  const [newPool, setNewPool] = useState({
    pool_name: '',
    description: '',
    category: 'custom'
  });

  // 添加股票表单
  const [newStock, setNewStock] = useState({
    stock_code: '',
    stock_name: '',
    added_reason: '',
    tags: '',
    notes: ''
  });

  // 从选股结果导入表单
  const [importForm, setImportForm] = useState({
    filename: '',
    pool_name: '',
    description: '',
    max_stocks: 100,
    min_score: 0
  });

  // 手动导入表单
  const [manualImportForm, setManualImportForm] = useState({
    filename: '',
    target_pool_id: '',
    pool_name: '',
    description: '',
    import_mode: 'new' // new, add, replace
  });

  // 获取股票池列表
  const fetchStockPools = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/stock-pool/pools');
      const data = await response.json();
      if (data.success) {
        setStockPools(data.data);
      } else {
        toast.error('获取股票池列表失败');
      }
    } catch (error) {
      toast.error('获取股票池列表失败: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  // 获取股票池详情
  const loadPoolDetail = async (poolId) => {
    try {
      const response = await fetch(`/api/stock-pool/pools/${poolId}`);
      const data = await response.json();
      if (data.success) {
        setSelectedPool(data.data);
      }
    } catch (error) {
      console.error('加载股票池详情失败:', error);
      toast.error('加载股票池详情失败');
    }
  };

  // 创建股票池
  const createPool = async () => {
    if (!newPool.pool_name.trim()) {
      toast.error('请输入股票池名称');
      return;
    }

    try {
      const response = await fetch('/api/stock-pool/pools', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(newPool)
      });
      const data = await response.json();
      if (data.success) {
        setShowCreateModal(false);
        setNewPool({ pool_name: '', description: '', category: 'custom' });
        fetchStockPools();
        toast.success('股票池创建成功');
      } else {
        toast.error(data.message || '创建股票池失败');
      }
    } catch (error) {
      toast.error('创建股票池失败: ' + error.message);
    }
  };

  // 删除股票池
  const deletePool = async (poolId) => {
    if (!window.confirm('确定要删除这个股票池吗？')) {
      return;
    }

    try {
      const response = await fetch(`/api/stock-pool/pools/${poolId}`, {
        method: 'DELETE'
      });
      const data = await response.json();
      if (data.success) {
        toast.success('股票池删除成功');
        fetchStockPools();
        if (selectedPool && selectedPool.pool_id === poolId) {
          setSelectedPool(null);
        }
      } else {
        toast.error(data.message || '删除股票池失败');
      }
    } catch (error) {
      toast.error('删除股票池失败: ' + error.message);
    }
  };

  // 添加股票
  const addStock = async () => {
    if (!selectedPool) return;
    
    if (!newStock.stock_code.trim() || !newStock.stock_name.trim()) {
      toast.error('请输入股票代码和名称');
      return;
    }
    
    try {
      const stockData = {
        ...newStock,
        tags: newStock.tags.split(',').map(tag => tag.trim()).filter(tag => tag)
      };
      const response = await fetch(`/api/stock-pool/pools/${selectedPool.pool_id}/stocks`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(stockData)
      });
      const data = await response.json();
      if (data.success) {
        setShowAddStockModal(false);
        setNewStock({ stock_code: '', stock_name: '', added_reason: '', tags: '', notes: '' });
        loadPoolDetail(selectedPool.pool_id);
        toast.success('股票添加成功');
      } else {
        toast.error(data.message || '添加股票失败');
      }
    } catch (error) {
      toast.error('添加股票失败: ' + error.message);
    }
  };

  // 移除股票
  const removeStock = async (stockCode) => {
    if (!selectedPool) return;
    
    if (!window.confirm('确定要移除这只股票吗？')) {
      return;
    }
    
    try {
      const response = await fetch(`/api/stock-pool/pools/${selectedPool.pool_id}/stocks/${stockCode}`, {
        method: 'DELETE'
      });
      const data = await response.json();
      if (data.success) {
        loadPoolDetail(selectedPool.pool_id);
        toast.success('股票移除成功');
      } else {
        toast.error(data.message || '移除股票失败');
      }
    } catch (error) {
      toast.error('移除股票失败: ' + error.message);
    }
  };

  // 获取选股结果列表
  const fetchSelectionResults = async () => {
    try {
      const response = await fetch('/api/stock-pool/selection-results');
      const data = await response.json();
      if (data.success) {
        setSelectionResults(data.data);
      }
    } catch (error) {
      console.error('获取选股结果失败:', error);
      toast.error('获取选股结果失败');
    }
  };

  // 从选股结果创建股票池
  const createPoolFromSelection = async () => {
    if (!importForm.filename) {
      toast.error('请选择选股结果');
      return;
    }

    try {
      const response = await fetch('/api/stock-pool/pools/from-selection', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(importForm)
      });
      const data = await response.json();
      if (data.success) {
        setShowImportModal(false);
        setImportForm({
          filename: '',
          pool_name: '',
          description: '',
          max_stocks: 100,
          min_score: 0
        });
        fetchStockPools();
        toast.success(`成功创建股票池"${data.data.pool_name}"，添加了${data.data.added_count}只股票`);
      } else {
        toast.error(data.message || '创建股票池失败');
      }
    } catch (error) {
      toast.error('创建股票池失败: ' + error.message);
    }
  };

  // 获取选股结果详情
  const fetchSelectionDetail = async (filename) => {
    try {
      const response = await fetch(`/api/stock-pool/selection-results/${filename}`);
      const data = await response.json();
      if (data.success) {
        setSelectionStocks(data.data.stocks || []);
        setSelectedResult(data.data);
        setCurrentPage(1);
        setSelectedStocks([]);
      }
    } catch (error) {
      console.error('获取选股结果详情失败:', error);
      toast.error('获取选股结果详情失败');
    }
  };

  // 手动导入选中的股票
  const manualImportStocks = async () => {
    if (!manualImportForm.filename) {
      toast.error('请选择选股结果');
      return;
    }

    if (selectedStocks.length === 0) {
      toast.error('请至少选择一只股票');
      return;
    }

    if (manualImportForm.import_mode !== 'new' && !manualImportForm.target_pool_id) {
      toast.error('请选择目标股票池');
      return;
    }

    try {
      const requestData = {
        filename: manualImportForm.filename,
        selected_stocks: selectedStocks,
        target_pool_id: manualImportForm.import_mode === 'new' ? null : manualImportForm.target_pool_id,
        pool_name: manualImportForm.pool_name,
        description: manualImportForm.description,
        import_mode: manualImportForm.import_mode
      };

      const response = await fetch('/api/stock-pool/pools/manual-import', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestData)
      });
      const data = await response.json();

      if (data.success) {
        setShowManualImportModal(false);
        setManualImportForm({
          filename: '',
          target_pool_id: '',
          pool_name: '',
          description: '',
          import_mode: 'new'
        });
        setSelectedStocks([]);
        setSelectionStocks([]);
        fetchStockPools();
        toast.success(data.message);
      } else {
        toast.error(data.message || '导入失败');
      }
    } catch (error) {
      toast.error('导入失败: ' + error.message);
    }
  };

  // 辅助函数
  const getCategoryIcon = (category) => {
    switch (category) {
      case 'selection': return <Target className="w-4 h-4" />;
      case 'strategy': return <TrendingUp className="w-4 h-4" />;
      default: return <Star className="w-4 h-4" />;
    }
  };

  const getCategoryColor = (category) => {
    switch (category) {
      case 'selection': return 'bg-blue-100 text-blue-800';
      case 'strategy': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getCategoryLabel = (category) => {
    switch (category) {
      case 'selection': return '选股';
      case 'strategy': return '策略';
      default: return '自定义';
    }
  };

  // 过滤股票池
  const filteredPools = stockPools.filter(pool =>
    pool.pool_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    pool.description.toLowerCase().includes(searchQuery.toLowerCase())
  );

  useEffect(() => {
    fetchStockPools();
  }, []);

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* 页面标题 */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">股票池管理</h1>
          <p className="text-gray-600">管理您的股票池，包括自选股、选股结果和策略股票池</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* 左侧：股票池列表 */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-sm border">
              <div className="p-4 border-b">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-lg font-semibold">股票池列表</h2>
                  <div className="flex space-x-2">
                    <button
                      onClick={() => setShowCreateModal(true)}
                      className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg"
                      title="创建股票池"
                    >
                      <Plus className="w-4 h-4" />
                    </button>
                    <div className="relative group">
                      <button
                        onClick={() => {
                          fetchSelectionResults();
                          setShowImportModal(true);
                        }}
                        className="p-2 text-green-600 hover:bg-green-50 rounded-lg"
                        title="从选股结果导入"
                      >
                        <Upload className="w-4 h-4" />
                      </button>
                      {/* 下拉菜单 */}
                      <div className="absolute top-full left-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-10 min-w-48">
                        <button
                          onClick={() => {
                            fetchSelectionResults();
                            setShowImportModal(true);
                          }}
                          className="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 rounded-t-lg"
                        >
                          <div className="flex items-center space-x-2">
                            <Upload className="w-4 h-4" />
                            <span>自动导入</span>
                          </div>
                          <div className="text-xs text-gray-500 mt-1">按条件自动筛选导入</div>
                        </button>
                        <button
                          onClick={() => {
                            fetchSelectionResults();
                            setShowManualImportModal(true);
                          }}
                          className="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 rounded-b-lg"
                        >
                          <div className="flex items-center space-x-2">
                            <FileText className="w-4 h-4" />
                            <span>手动选择</span>
                          </div>
                          <div className="text-xs text-gray-500 mt-1">手动选择股票导入</div>
                        </button>
                      </div>
                    </div>
                    <button
                      onClick={fetchStockPools}
                      className="p-2 text-gray-600 hover:bg-gray-50 rounded-lg"
                      title="刷新"
                      disabled={loading}
                    >
                      <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
                    </button>
                  </div>
                </div>

                {/* 搜索框 */}
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <input
                    type="text"
                    placeholder="搜索股票池..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>

              {/* 股票池列表 */}
              <div className="max-h-96 overflow-y-auto">
                {loading ? (
                  <div className="p-4 text-center text-gray-500">加载中...</div>
                ) : filteredPools.length === 0 ? (
                  <div className="p-4 text-center text-gray-500">暂无股票池</div>
                ) : (
                  filteredPools.map((pool) => (
                    <div
                      key={pool.pool_id}
                      className={`p-4 border-b cursor-pointer hover:bg-gray-50 ${
                        selectedPool?.pool_id === pool.pool_id ? 'bg-blue-50 border-l-4 border-l-blue-500' : ''
                      }`}
                      onClick={() => loadPoolDetail(pool.pool_id)}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-1">
                            {getCategoryIcon(pool.category)}
                            <h3 className="font-medium text-gray-900">{pool.pool_name}</h3>
                            <span className={`px-2 py-1 text-xs rounded-full ${getCategoryColor(pool.category)}`}>
                              {getCategoryLabel(pool.category)}
                            </span>
                          </div>
                          <p className="text-sm text-gray-600 mb-2">{pool.description}</p>
                          <div className="flex items-center justify-between text-xs text-gray-500">
                            <span>{pool.stock_count || 0} 只股票</span>
                            <span>{pool.updated_date ? new Date(pool.updated_date).toLocaleDateString() : '未知时间'}</span>
                          </div>
                        </div>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            deletePool(pool.pool_id);
                          }}
                          className="p-1 text-red-600 hover:bg-red-50 rounded"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </div>
          </div>

          {/* 右侧：股票池详情 */}
          <div className="lg:col-span-2">
            {selectedPool ? (
              <div className="bg-white rounded-lg shadow-sm border">
                <div className="p-6 border-b">
                  <div className="flex items-center justify-between mb-4">
                    <div>
                      <h2 className="text-xl font-semibold text-gray-900">{selectedPool.pool_name}</h2>
                      <p className="text-gray-600">{selectedPool.description}</p>
                    </div>
                    <ShadButton
                      onClick={() => setShowAddStockModal(true)}
                      className="flex items-center space-x-2"
                    >
                      <Plus className="w-4 h-4" />
                      <span>添加股票</span>
                    </ShadButton>
                  </div>

                  <div className="grid grid-cols-3 gap-4 text-sm">
                    <div>
                      <span className="text-gray-500">股票数量:</span>
                      <span className="ml-2 font-medium">{selectedPool.stocks ? selectedPool.stocks.length : 0}</span>
                    </div>
                    <div>
                      <span className="text-gray-500">类别:</span>
                      <span className={`ml-2 px-2 py-1 text-xs rounded-full ${getCategoryColor(selectedPool.category)}`}>
                        {getCategoryLabel(selectedPool.category)}
                      </span>
                    </div>
                    <div>
                      <span className="text-gray-500">更新时间:</span>
                      <span className="ml-2">{selectedPool.updated_date ? new Date(selectedPool.updated_date).toLocaleString() : '未知'}</span>
                    </div>
                  </div>
                </div>

                {/* 股票列表 */}
                <div className="p-6">
                  {!selectedPool.stocks || selectedPool.stocks.length === 0 ? (
                    <div className="text-center py-8 text-gray-500">
                      <BarChart3 className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                      <p>暂无股票</p>
                      <p className="text-sm">点击"添加股票"开始管理您的股票池</p>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {selectedPool.stocks.map((stock) => (
                        <div key={stock.stock_code} className="border rounded-lg p-4 hover:bg-gray-50">
                          <div className="flex items-center justify-between">
                            <div className="flex-1">
                              <div className="flex items-center space-x-3 mb-2">
                                <h3 className="font-medium text-gray-900">{stock.stock_code}</h3>
                                <span className="text-gray-600">{stock.stock_name}</span>
                                <div className="flex space-x-1">
                                  {stock.tags && stock.tags.map((tag, index) => (
                                    <span key={index} className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                                      {tag}
                                    </span>
                                  ))}
                                </div>
                              </div>
                              {stock.added_reason && (
                                <p className="text-sm text-gray-600 mb-1">
                                  <strong>添加原因:</strong> {stock.added_reason}
                                </p>
                              )}
                              {stock.notes && (
                                <p className="text-sm text-gray-600 mb-1">
                                  <strong>备注:</strong> {stock.notes}
                                </p>
                              )}
                              <p className="text-xs text-gray-500">
                                添加时间: {stock.added_date ? new Date(stock.added_date).toLocaleString() : '未知'}
                              </p>
                            </div>
                            <button
                              onClick={() => removeStock(stock.stock_code)}
                              className="p-2 text-red-600 hover:bg-red-50 rounded-lg"
                            >
                              <Trash2 className="w-4 h-4" />
                            </button>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            ) : (
              <div className="bg-white rounded-lg shadow-sm border p-8 text-center">
                <Eye className="w-16 h-16 mx-auto mb-4 text-gray-300" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">选择股票池</h3>
                <p className="text-gray-600">从左侧列表中选择一个股票池查看详情</p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* 创建股票池模态框 */}
      {showCreateModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold mb-4">创建股票池</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">股票池名称</label>
                <input
                  type="text"
                  value={newPool.pool_name}
                  onChange={(e) => setNewPool({...newPool, pool_name: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                  placeholder="输入股票池名称"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">描述</label>
                <textarea
                  value={newPool.description}
                  onChange={(e) => setNewPool({...newPool, description: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                  rows="3"
                  placeholder="输入股票池描述"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">类别</label>
                <select
                  value={newPool.category}
                  onChange={(e) => setNewPool({...newPool, category: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                >
                  <option value="custom">自定义</option>
                  <option value="strategy">策略</option>
                  <option value="selection">选股</option>
                </select>
              </div>
            </div>
            <div className="flex justify-end space-x-3 mt-6">
              <ShadButton
                variant="outline"
                onClick={() => setShowCreateModal(false)}
              >
                取消
              </ShadButton>
              <ShadButton onClick={createPool}>
                创建
              </ShadButton>
            </div>
          </div>
        </div>
      )}

      {/* 添加股票模态框 */}
      {showAddStockModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold mb-4">添加股票</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">股票代码</label>
                <input
                  type="text"
                  value={newStock.stock_code}
                  onChange={(e) => setNewStock({...newStock, stock_code: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                  placeholder="如: 000001.SZ"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">股票名称</label>
                <input
                  type="text"
                  value={newStock.stock_name}
                  onChange={(e) => setNewStock({...newStock, stock_name: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                  placeholder="如: 平安银行"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">添加原因</label>
                <input
                  type="text"
                  value={newStock.added_reason}
                  onChange={(e) => setNewStock({...newStock, added_reason: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                  placeholder="添加这只股票的原因"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">标签 (用逗号分隔)</label>
                <input
                  type="text"
                  value={newStock.tags}
                  onChange={(e) => setNewStock({...newStock, tags: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                  placeholder="如: 银行, 蓝筹"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">备注</label>
                <textarea
                  value={newStock.notes}
                  onChange={(e) => setNewStock({...newStock, notes: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                  rows="2"
                  placeholder="其他备注信息"
                />
              </div>
            </div>
            <div className="flex justify-end space-x-3 mt-6">
              <ShadButton
                variant="outline"
                onClick={() => setShowAddStockModal(false)}
              >
                取消
              </ShadButton>
              <ShadButton onClick={addStock}>
                添加
              </ShadButton>
            </div>
          </div>
        </div>
      )}

      {/* 从选股结果导入模态框 */}
      {showImportModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[80vh] overflow-y-auto">
            <h3 className="text-lg font-semibold mb-4">从选股结果导入股票池</h3>

            <div className="space-y-6">
              {/* 选择选股结果 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">选择选股结果</label>
                <div className="max-h-48 overflow-y-auto border border-gray-300 rounded-lg">
                  {selectionResults.length === 0 ? (
                    <div className="p-4 text-center text-gray-500">
                      <FileText className="w-8 h-8 mx-auto mb-2 text-gray-300" />
                      <p>暂无选股结果</p>
                    </div>
                  ) : (
                    selectionResults.map((result) => (
                      <div
                        key={result.filename}
                        className={`p-3 border-b cursor-pointer hover:bg-gray-50 ${
                          importForm.filename === result.filename ? 'bg-blue-50 border-l-4 border-l-blue-500' : ''
                        }`}
                        onClick={() => setImportForm({...importForm, filename: result.filename})}
                      >
                        <div className="flex items-center justify-between">
                          <div>
                            <h4 className="font-medium text-gray-900">{result.name}</h4>
                            <p className="text-sm text-gray-600">{result.selection_criteria}</p>
                            <div className="flex items-center space-x-4 text-xs text-gray-500 mt-1">
                              <span>{result.total_selected} 只股票</span>
                              <span>{result.date} {result.time}</span>
                            </div>
                          </div>
                          <div className="text-right text-xs text-gray-500">
                            <div>{(result.file_size / 1024 / 1024).toFixed(1)} MB</div>
                          </div>
                        </div>
                      </div>
                    ))
                  )}
                </div>
              </div>

              {/* 导入配置 */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">股票池名称</label>
                  <input
                    type="text"
                    value={importForm.pool_name}
                    onChange={(e) => setImportForm({...importForm, pool_name: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                    placeholder="留空则自动生成"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">最多导入股票数</label>
                  <input
                    type="number"
                    value={importForm.max_stocks}
                    onChange={(e) => setImportForm({...importForm, max_stocks: parseInt(e.target.value) || 100})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                    min="1"
                    max="1000"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">最低评分要求</label>
                <input
                  type="number"
                  value={importForm.min_score}
                  onChange={(e) => setImportForm({...importForm, min_score: parseFloat(e.target.value) || 0})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                  min="0"
                  max="100"
                  step="0.1"
                  placeholder="0 表示不限制"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">描述</label>
                <textarea
                  value={importForm.description}
                  onChange={(e) => setImportForm({...importForm, description: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                  rows="2"
                  placeholder="留空则自动生成"
                />
              </div>

              {/* 预览信息 */}
              {importForm.filename && (
                <div className="bg-blue-50 p-4 rounded-lg">
                  <h4 className="font-medium text-blue-900 mb-2">导入预览</h4>
                  <div className="text-sm text-blue-800 space-y-1">
                    <p>• 选股结果: {selectionResults.find(r => r.filename === importForm.filename)?.name}</p>
                    <p>• 原始股票数: {selectionResults.find(r => r.filename === importForm.filename)?.total_selected}</p>
                    <p>• 最多导入: {importForm.max_stocks} 只</p>
                    <p>• 评分要求: {importForm.min_score > 0 ? `≥ ${importForm.min_score}` : '无限制'}</p>
                    <p>• 股票将按评分从高到低排序导入</p>
                  </div>
                </div>
              )}
            </div>

            <div className="flex justify-end space-x-3 mt-6">
              <ShadButton
                variant="outline"
                onClick={() => setShowImportModal(false)}
              >
                取消
              </ShadButton>
              <ShadButton
                onClick={createPoolFromSelection}
                disabled={!importForm.filename}
              >
                导入创建
              </ShadButton>
            </div>
          </div>
        </div>
      )}

      {/* 手动选择导入模态框 */}
      {showManualImportModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg w-full max-w-6xl max-h-[90vh] overflow-hidden flex flex-col">
            <div className="p-6 border-b">
              <h3 className="text-lg font-semibold">手动选择股票导入</h3>
              <p className="text-sm text-gray-600 mt-1">从选股结果中手动选择股票导入到股票池</p>
            </div>

            <div className="flex-1 overflow-hidden flex">
              {/* 左侧：选股结果列表 */}
              <div className="w-1/3 border-r overflow-y-auto">
                <div className="p-4">
                  <h4 className="font-medium mb-3">选择选股结果</h4>
                  <div className="space-y-2">
                    {selectionResults.map((result) => (
                      <div
                        key={result.filename}
                        className={`p-3 border rounded-lg cursor-pointer hover:bg-gray-50 ${
                          manualImportForm.filename === result.filename ? 'bg-blue-50 border-blue-500' : ''
                        }`}
                        onClick={() => {
                          setManualImportForm({...manualImportForm, filename: result.filename});
                          fetchSelectionDetail(result.filename);
                        }}
                      >
                        <h5 className="font-medium text-sm">{result.name}</h5>
                        <p className="text-xs text-gray-600 mt-1">{result.selection_criteria}</p>
                        <div className="flex justify-between text-xs text-gray-500 mt-2">
                          <span>{result.total_selected} 只股票</span>
                          <span>{result.date}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {/* 中间：股票列表 */}
              <div className="flex-1 overflow-y-auto">
                <div className="p-4">
                  <div className="flex items-center justify-between mb-3">
                    <h4 className="font-medium">
                      股票列表
                      {selectionStocks.length > 0 && (
                        <span className="text-sm text-gray-500 ml-2">
                          (共 {selectionStocks.length} 只，已选 {selectedStocks.length} 只)
                        </span>
                      )}
                    </h4>
                    {selectionStocks.length > 0 && (
                      <div className="flex space-x-2">
                        <button
                          onClick={() => {
                            const currentPageStocks = selectionStocks
                              .slice((currentPage - 1) * stocksPerPage, currentPage * stocksPerPage)
                              .map(stock => stock.stock_code);
                            setSelectedStocks([...new Set([...selectedStocks, ...currentPageStocks])]);
                          }}
                          className="px-3 py-1 text-xs bg-blue-100 text-blue-800 rounded hover:bg-blue-200"
                        >
                          全选当前页
                        </button>
                        <button
                          onClick={() => setSelectedStocks([])}
                          className="px-3 py-1 text-xs bg-gray-100 text-gray-800 rounded hover:bg-gray-200"
                        >
                          清空选择
                        </button>
                      </div>
                    )}
                  </div>

                  {selectionStocks.length === 0 ? (
                    <div className="text-center py-8 text-gray-500">
                      <FileText className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                      <p>请先选择选股结果</p>
                    </div>
                  ) : (
                    <div className="space-y-2">
                      {selectionStocks
                        .slice((currentPage - 1) * stocksPerPage, currentPage * stocksPerPage)
                        .map((stock) => (
                          <div
                            key={stock.stock_code}
                            className={`p-3 border rounded-lg cursor-pointer hover:bg-gray-50 ${
                              selectedStocks.includes(stock.stock_code) ? 'bg-blue-50 border-blue-500' : ''
                            }`}
                            onClick={() => {
                              if (selectedStocks.includes(stock.stock_code)) {
                                setSelectedStocks(selectedStocks.filter(code => code !== stock.stock_code));
                              } else {
                                setSelectedStocks([...selectedStocks, stock.stock_code]);
                              }
                            }}
                          >
                            <div className="flex items-center justify-between">
                              <div className="flex-1">
                                <div className="flex items-center space-x-3">
                                  <input
                                    type="checkbox"
                                    checked={selectedStocks.includes(stock.stock_code)}
                                    onChange={() => {}}
                                    className="rounded"
                                  />
                                  <div>
                                    <h5 className="font-medium text-sm">{stock.stock_code}</h5>
                                    <p className="text-sm text-gray-600">{stock.stock_name}</p>
                                  </div>
                                </div>
                              </div>
                              <div className="text-right">
                                <div className="text-sm font-medium">评分: {stock.score?.toFixed(1) || 'N/A'}</div>
                                <div className="text-xs text-gray-500">
                                  {stock.indicators?.ma_trend && (
                                    <span className="mr-2">趋势: {stock.indicators.ma_trend}</span>
                                  )}
                                  {stock.indicators?.rsi && (
                                    <span>RSI: {stock.indicators.rsi.toFixed(1)}</span>
                                  )}
                                </div>
                              </div>
                            </div>
                          </div>
                        ))}

                      {/* 分页 */}
                      {selectionStocks.length > stocksPerPage && (
                        <div className="flex justify-center mt-4">
                          <div className="flex space-x-2">
                            <button
                              onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                              disabled={currentPage === 1}
                              className="px-3 py-1 text-sm border rounded disabled:opacity-50"
                            >
                              上一页
                            </button>
                            <span className="px-3 py-1 text-sm">
                              {currentPage} / {Math.ceil(selectionStocks.length / stocksPerPage)}
                            </span>
                            <button
                              onClick={() => setCurrentPage(Math.min(Math.ceil(selectionStocks.length / stocksPerPage), currentPage + 1))}
                              disabled={currentPage >= Math.ceil(selectionStocks.length / stocksPerPage)}
                              className="px-3 py-1 text-sm border rounded disabled:opacity-50"
                            >
                              下一页
                            </button>
                          </div>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>

              {/* 右侧：导入配置 */}
              <div className="w-1/3 border-l overflow-y-auto">
                <div className="p-4">
                  <h4 className="font-medium mb-3">导入配置</h4>

                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">导入模式</label>
                      <select
                        value={manualImportForm.import_mode}
                        onChange={(e) => setManualImportForm({...manualImportForm, import_mode: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                      >
                        <option value="new">创建新股票池</option>
                        <option value="add">添加到现有股票池</option>
                        <option value="replace">替换现有股票池</option>
                      </select>
                    </div>

                    {manualImportForm.import_mode !== 'new' && (
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">目标股票池</label>
                        <select
                          value={manualImportForm.target_pool_id}
                          onChange={(e) => setManualImportForm({...manualImportForm, target_pool_id: e.target.value})}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                        >
                          <option value="">请选择股票池</option>
                          {stockPools.map((pool) => (
                            <option key={pool.pool_id} value={pool.pool_id}>
                              {pool.pool_name} ({pool.stock_count || 0}只股票)
                            </option>
                          ))}
                        </select>
                      </div>
                    )}

                    {manualImportForm.import_mode === 'new' && (
                      <>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">股票池名称</label>
                          <input
                            type="text"
                            value={manualImportForm.pool_name}
                            onChange={(e) => setManualImportForm({...manualImportForm, pool_name: e.target.value})}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                            placeholder="留空则自动生成"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">描述</label>
                          <textarea
                            value={manualImportForm.description}
                            onChange={(e) => setManualImportForm({...manualImportForm, description: e.target.value})}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                            rows="3"
                            placeholder="留空则自动生成"
                          />
                        </div>
                      </>
                    )}

                    {/* 预览信息 */}
                    {selectedStocks.length > 0 && (
                      <div className="bg-green-50 p-3 rounded-lg">
                        <h5 className="font-medium text-green-900 mb-2">导入预览</h5>
                        <div className="text-sm text-green-800 space-y-1">
                          <p>• 已选择: {selectedStocks.length} 只股票</p>
                          <p>• 导入模式: {
                            manualImportForm.import_mode === 'new' ? '创建新股票池' :
                            manualImportForm.import_mode === 'add' ? '添加到现有股票池' : '替换现有股票池'
                          }</p>
                          {manualImportForm.import_mode !== 'new' && manualImportForm.target_pool_id && (
                            <p>• 目标股票池: {stockPools.find(p => p.pool_id === manualImportForm.target_pool_id)?.pool_name}</p>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>

            <div className="p-6 border-t bg-gray-50">
              <div className="flex justify-end space-x-3">
                <ShadButton
                  variant="outline"
                  onClick={() => {
                    setShowManualImportModal(false);
                    setSelectedStocks([]);
                    setSelectionStocks([]);
                    setManualImportForm({
                      filename: '',
                      target_pool_id: '',
                      pool_name: '',
                      description: '',
                      import_mode: 'new'
                    });
                  }}
                >
                  取消
                </ShadButton>
                <ShadButton
                  onClick={manualImportStocks}
                  disabled={!manualImportForm.filename || selectedStocks.length === 0}
                >
                  导入 ({selectedStocks.length} 只股票)
                </ShadButton>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default StockPoolPage;
