#!/usr/bin/env python3
"""
实盘交易存储管理器
负责保存策略历史、订单历史、成交历史等
"""

import os
import json
import csv
from datetime import datetime
from typing import Dict, List, Any, Optional
from pathlib import Path

from backend.core.logger import get_logger

logger = get_logger(__name__)


class LiveTradingStorage:
    """实盘交易存储管理器"""
    
    def __init__(self, base_dir: str = "data/live_trading"):
        """
        初始化存储管理器
        
        Args:
            base_dir: 基础存储目录
        """
        self.base_dir = Path(base_dir)
        self.strategies_dir = self.base_dir / "strategies"
        self.orders_dir = self.base_dir / "orders"
        self.trades_dir = self.base_dir / "trades"
        self.history_dir = self.base_dir / "history"
        
        # 创建目录
        self._create_directories()
        
        logger.info(f"📁 实盘交易存储管理器初始化完成，存储目录: {self.base_dir}")
    
    def _create_directories(self):
        """创建必要的目录"""
        directories = [
            self.base_dir,
            self.strategies_dir,
            self.orders_dir,
            self.trades_dir,
            self.history_dir
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
    
    async def save_strategy_history(self, task_id: str, strategy_result: Any):
        """
        保存策略历史
        
        Args:
            task_id: 任务ID
            strategy_result: 策略结果对象
        """
        try:
            # 创建策略历史记录
            history_data = {
                'task_id': task_id,
                'strategy_name': strategy_result.strategy_name,
                'start_time': strategy_result.start_time,
                'end_time': strategy_result.current_time,
                'initial_capital': strategy_result.initial_capital,
                'final_capital': strategy_result.current_capital,
                'total_return': strategy_result.total_return,
                'status': strategy_result.status,
                'paper_trading': strategy_result.paper_trading,
                'positions': strategy_result.positions,
                'orders': strategy_result.orders,
                'created_at': datetime.now().isoformat()
            }
            
            # 保存到JSON文件
            history_file = self.history_dir / f"{task_id}.json"
            with open(history_file, 'w', encoding='utf-8') as f:
                json.dump(history_data, f, ensure_ascii=False, indent=2)
            
            # 保存到CSV文件（汇总信息）
            await self._save_strategy_summary(history_data)
            
            logger.info(f"✅ 策略历史保存成功: {task_id}")
            
        except Exception as e:
            logger.error(f"❌ 保存策略历史失败: {e}")
    
    async def _save_strategy_summary(self, history_data: Dict[str, Any]):
        """保存策略汇总信息到CSV"""
        try:
            csv_file = self.history_dir / "strategies_summary.csv"
            
            # 检查文件是否存在，如果不存在则创建表头
            file_exists = csv_file.exists()
            
            with open(csv_file, 'a', newline='', encoding='utf-8') as f:
                fieldnames = [
                    'task_id', 'strategy_name', 'start_time', 'end_time',
                    'initial_capital', 'final_capital', 'total_return',
                    'status', 'paper_trading', 'created_at'
                ]
                
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                
                if not file_exists:
                    writer.writeheader()
                
                # 写入数据
                row_data = {key: history_data.get(key, '') for key in fieldnames}
                writer.writerow(row_data)
            
        except Exception as e:
            logger.error(f"❌ 保存策略汇总失败: {e}")
    
    async def save_order_history(self, task_id: str, order_data: Dict[str, Any]):
        """
        保存订单历史
        
        Args:
            task_id: 任务ID
            order_data: 订单数据
        """
        try:
            # 添加时间戳
            order_data['timestamp'] = datetime.now().isoformat()
            order_data['task_id'] = task_id
            
            # 保存到JSON文件
            date_str = datetime.now().strftime('%Y%m%d')
            orders_file = self.orders_dir / f"orders_{date_str}.json"
            
            # 读取现有数据
            orders_list = []
            if orders_file.exists():
                with open(orders_file, 'r', encoding='utf-8') as f:
                    orders_list = json.load(f)
            
            # 添加新订单
            orders_list.append(order_data)
            
            # 保存回文件
            with open(orders_file, 'w', encoding='utf-8') as f:
                json.dump(orders_list, f, ensure_ascii=False, indent=2)
            
            # 保存到CSV文件
            await self._save_order_csv(order_data)
            
            logger.debug(f"📋 订单历史保存成功: {order_data.get('order_id', 'unknown')}")
            
        except Exception as e:
            logger.error(f"❌ 保存订单历史失败: {e}")
    
    async def _save_order_csv(self, order_data: Dict[str, Any]):
        """保存订单到CSV文件"""
        try:
            date_str = datetime.now().strftime('%Y%m%d')
            csv_file = self.orders_dir / f"orders_{date_str}.csv"
            
            file_exists = csv_file.exists()
            
            with open(csv_file, 'a', newline='', encoding='utf-8') as f:
                fieldnames = [
                    'timestamp', 'task_id', 'order_id', 'stock_code',
                    'direction', 'size', 'price', 'order_type', 'status'
                ]
                
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                
                if not file_exists:
                    writer.writeheader()
                
                # 写入数据
                row_data = {key: order_data.get(key, '') for key in fieldnames}
                writer.writerow(row_data)
            
        except Exception as e:
            logger.error(f"❌ 保存订单CSV失败: {e}")
    
    async def save_trade_history(self, task_id: str, trade_data: Dict[str, Any]):
        """
        保存成交历史
        
        Args:
            task_id: 任务ID
            trade_data: 成交数据
        """
        try:
            # 添加时间戳
            trade_data['timestamp'] = datetime.now().isoformat()
            trade_data['task_id'] = task_id
            
            # 保存到JSON文件
            date_str = datetime.now().strftime('%Y%m%d')
            trades_file = self.trades_dir / f"trades_{date_str}.json"
            
            # 读取现有数据
            trades_list = []
            if trades_file.exists():
                with open(trades_file, 'r', encoding='utf-8') as f:
                    trades_list = json.load(f)
            
            # 添加新成交
            trades_list.append(trade_data)
            
            # 保存回文件
            with open(trades_file, 'w', encoding='utf-8') as f:
                json.dump(trades_list, f, ensure_ascii=False, indent=2)
            
            # 保存到CSV文件
            await self._save_trade_csv(trade_data)
            
            logger.debug(f"💰 成交历史保存成功: {trade_data.get('trade_id', 'unknown')}")
            
        except Exception as e:
            logger.error(f"❌ 保存成交历史失败: {e}")
    
    async def _save_trade_csv(self, trade_data: Dict[str, Any]):
        """保存成交到CSV文件"""
        try:
            date_str = datetime.now().strftime('%Y%m%d')
            csv_file = self.trades_dir / f"trades_{date_str}.csv"
            
            file_exists = csv_file.exists()
            
            with open(csv_file, 'a', newline='', encoding='utf-8') as f:
                fieldnames = [
                    'timestamp', 'task_id', 'trade_id', 'order_id', 'stock_code',
                    'direction', 'size', 'price', 'amount', 'commission'
                ]
                
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                
                if not file_exists:
                    writer.writeheader()
                
                # 写入数据
                row_data = {key: trade_data.get(key, '') for key in fieldnames}
                writer.writerow(row_data)
            
        except Exception as e:
            logger.error(f"❌ 保存成交CSV失败: {e}")
    
    async def load_strategy_history(self, limit: int = 100) -> List[Dict[str, Any]]:
        """
        加载策略历史
        
        Args:
            limit: 限制返回数量
            
        Returns:
            策略历史列表
        """
        try:
            history_files = list(self.history_dir.glob("*.json"))
            history_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
            
            histories = []
            for file_path in history_files[:limit]:
                if file_path.name == "strategies_summary.csv":
                    continue
                    
                with open(file_path, 'r', encoding='utf-8') as f:
                    history_data = json.load(f)
                    histories.append(history_data)
            
            return histories
            
        except Exception as e:
            logger.error(f"❌ 加载策略历史失败: {e}")
            return []
    
    async def get_strategy_statistics(self) -> Dict[str, Any]:
        """获取策略统计信息"""
        try:
            histories = await self.load_strategy_history(limit=1000)
            
            total_strategies = len(histories)
            successful_strategies = len([h for h in histories if h.get('status') == 'completed'])
            paper_trading_count = len([h for h in histories if h.get('paper_trading', True)])
            live_trading_count = total_strategies - paper_trading_count
            
            # 计算平均收益率
            returns = [h.get('total_return', 0) for h in histories if h.get('total_return') is not None]
            avg_return = sum(returns) / len(returns) if returns else 0
            
            return {
                'total_strategies': total_strategies,
                'successful_strategies': successful_strategies,
                'paper_trading_count': paper_trading_count,
                'live_trading_count': live_trading_count,
                'success_rate': successful_strategies / total_strategies if total_strategies > 0 else 0,
                'average_return': avg_return,
                'last_updated': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"❌ 获取策略统计失败: {e}")
            return {}


# 全局存储管理器实例
live_trading_storage = LiveTradingStorage()
