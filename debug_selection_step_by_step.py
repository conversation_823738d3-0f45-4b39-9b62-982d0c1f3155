#!/usr/bin/env python3
"""
逐步调试选股功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from backend.stock_selection.stock_selector import StockSelector, SelectionCriteria, StockScore
from backend.core.logger import get_logger
from datetime import datetime, timedelta

logger = get_logger(__name__)

def debug_step_by_step():
    """逐步调试选股"""
    print("🔍 逐步调试选股功能")
    print("=" * 50)
    
    # 创建选股器
    selector = StockSelector()
    
    # 创建宽松的条件
    criteria = SelectionCriteria(
        volume_min=0.1,
        condition_logic="flexible",
        lookback_days=30,
        min_trading_days=20
    )
    
    print(f"📋 选股条件: 成交量比例 > {criteria.volume_min}")
    
    try:
        # 获取股票列表
        all_stocks = selector._get_stocks_from_xttrader()
        if not all_stocks:
            print("❌ 无法获取股票列表")
            return
        
        print(f"📊 获取到 {len(all_stocks)} 只股票")
        
        # 计算日期范围
        end_date = datetime.now()
        # 确保获取足够的数据，特别是当需要排除新股时
        extra_days = 60 if criteria.exclude_new_stock else 30
        start_date = end_date - timedelta(days=criteria.lookback_days + extra_days)
        
        print(f"📅 时间范围: {start_date.strftime('%Y%m%d')} 到 {end_date.strftime('%Y%m%d')}")
        
        selected_stocks = []
        
        # 只处理前10只股票进行详细调试
        for i, stock in enumerate(all_stocks[:10]):
            print(f"\n--- 处理股票 {i+1}: {stock['code']} ---")
            
            try:
                stock_code = stock['code']
                stock_name = stock.get('name', stock_code)
                
                print(f"   股票名称: {stock_name}")
                
                # 1. 检查ST股票
                if criteria.exclude_st and ('ST' in stock_name or '*ST' in stock_name):
                    print(f"   ❌ 跳过ST股票")
                    continue
                else:
                    print(f"   ✅ 非ST股票")
                
                # 2. 获取股票数据
                print(f"   📊 获取股票数据...")
                df = selector._get_stock_data_from_xttrader(
                    stock_code=stock_code,
                    start_date=start_date.strftime('%Y%m%d'),
                    end_date=end_date.strftime('%Y%m%d')
                )
                
                if df is None:
                    print(f"   ❌ 数据获取失败")
                    continue
                
                print(f"   ✅ 获取到 {len(df)} 天数据")
                
                # 3. 检查最少交易天数
                if len(df) < criteria.min_trading_days:
                    print(f"   ❌ 数据不足: {len(df)} < {criteria.min_trading_days}")
                    continue
                else:
                    print(f"   ✅ 数据充足: {len(df)} >= {criteria.min_trading_days}")
                
                # 4. 检查新股
                if criteria.exclude_new_stock and len(df) < 60:
                    print(f"   ❌ 新股: {len(df)} < 60天")
                    continue
                else:
                    print(f"   ✅ 非新股: {len(df)} >= 60天")
                
                # 5. 计算技术指标
                print(f"   📈 计算技术指标...")
                indicators = selector.calculate_technical_indicators(df)
                if not indicators:
                    print(f"   ❌ 技术指标计算失败")
                    continue
                
                volume_ratio = indicators.get('volume_ratio', 0)
                rsi = indicators.get('rsi', 0)
                print(f"   ✅ 技术指标计算成功")
                print(f"      成交量比: {volume_ratio:.4f}")
                print(f"      RSI: {rsi:.2f}")
                
                # 6. 计算Alpha101因子
                print(f"   🧮 计算Alpha因子...")
                alpha_factors = selector.calculate_alpha101_factors(df)
                print(f"   ✅ Alpha因子计算完成: {len(alpha_factors)} 个")
                
                # 7. 检查选股条件
                print(f"   🎯 检查选股条件...")
                passed, score = selector.check_selection_criteria(indicators, alpha_factors, criteria)
                
                print(f"   📊 条件检查结果:")
                print(f"      通过: {passed}")
                print(f"      评分: {score:.2f}")
                
                # 8. 最终判断
                if passed and score > 0:
                    print(f"   ✅ 股票通过选股条件！")
                    
                    stock_score = StockScore(
                        stock_code=stock_code,
                        stock_name=stock_name,
                        score=score,
                        indicators=indicators,
                        alpha_factors=alpha_factors,
                        selection_date=datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    )
                    selected_stocks.append(stock_score)
                    print(f"   🎉 已添加到选中列表")
                else:
                    print(f"   ❌ 股票未通过选股条件")
                    print(f"      原因: passed={passed}, score={score}")
                
            except Exception as e:
                print(f"   ❌ 处理失败: {e}")
                import traceback
                traceback.print_exc()
                continue
        
        print(f"\n📊 最终结果:")
        print(f"   处理股票数: 10")
        print(f"   选中股票数: {len(selected_stocks)}")
        
        if selected_stocks:
            print(f"\n🎯 选中的股票:")
            for i, stock in enumerate(selected_stocks):
                print(f"  {i+1}. {stock.stock_code} - {stock.stock_name}")
                print(f"     评分: {stock.score:.2f}")
                print(f"     成交量比: {stock.indicators.get('volume_ratio', 'N/A'):.4f}")
                print(f"     RSI: {stock.indicators.get('rsi', 'N/A'):.2f}")
        else:
            print(f"❌ 没有选中任何股票")
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_step_by_step()
