#!/usr/bin/env python3
"""
测试详细的回测结果
"""

import requests
import time
import json

def test_detailed_backtest():
    """测试详细的回测结果"""
    
    print("启动详细回测测试...")
    response = requests.post('http://localhost:8001/api/backtest/start', json={
        'strategy_name': 'detailed_test',
        'strategy_config': {},
        'start_date': '2024-01-01',
        'end_date': '2024-12-31',
        'initial_capital': 1000000,
        'commission': 0.0003
    })
    
    if response.status_code != 200:
        print(f"启动失败: {response.status_code} - {response.text}")
        return
    
    result = response.json()
    print(f"启动成功: {result}")
    task_id = result['data']['task_id']
    
    # 等待完成
    print("等待回测完成...")
    for i in range(15):
        time.sleep(1)
        status_response = requests.get('http://localhost:8001/api/backtest/status')
        if status_response.status_code == 200:
            status_data = status_response.json()
            print(f"状态 {i+1}: {status_data['data']['status']} - {status_data['data']['message']}")
            
            if status_data['data']['status'] in ['completed', 'failed']:
                break
    
    # 获取详细结果
    print("\n获取详细回测结果...")
    result_response = requests.get(f'http://localhost:8001/api/backtest/results/{task_id}')
    print(f"结果状态码: {result_response.status_code}")
    
    if result_response.status_code == 200:
        result_data = result_response.json()
        data = result_data['data']
        
        print(f"\n=== 回测基本信息 ===")
        print(f"策略名称: {data['strategy_name']}")
        print(f"回测时间: {data['start_date']} 到 {data['end_date']}")
        print(f"初始资金: ¥{data['initial_capital']:,.2f}")
        print(f"最终资金: ¥{data['final_capital']:,.2f}")
        
        print(f"\n=== 收益指标 ===")
        print(f"总收益率: {data['total_return']:.2%}")
        print(f"年化收益率: {data['annual_return']:.2%}")
        print(f"最大回撤: {data['max_drawdown']:.2%}")
        print(f"夏普比率: {data['sharpe_ratio']:.2f}")
        
        print(f"\n=== 交易统计 ===")
        print(f"总交易次数: {data['total_trades']}")
        print(f"盈利交易: {data['profit_trades']}")
        print(f"亏损交易: {data['loss_trades']}")
        print(f"胜率: {data['win_rate']:.2%}")
        print(f"平均盈利: ¥{data['avg_profit']:.2f}")
        print(f"平均亏损: ¥{data['avg_loss']:.2f}")
        print(f"盈亏比: {data['profit_factor']:.2f}")
        
        print(f"\n=== 详细交易记录 ===")
        trades = data['trades']
        for trade in trades:
            action_symbol = "📈" if trade['action'] == 'buy' else "📉"
            print(f"{action_symbol} 交易#{trade['id']}")
            print(f"   时间: {trade['date']} {trade['time']}")
            print(f"   股票: {trade['stock_code']} ({trade['stock_name']})")
            print(f"   操作: {trade['action_name']}")
            print(f"   价格: ¥{trade['price']}")
            print(f"   数量: {trade['quantity']:,}股")
            print(f"   金额: ¥{trade['amount']:,.2f}")
            print(f"   手续费: ¥{trade['commission']:.2f}")
            
            if trade['action'] == 'sell':
                print(f"   净收入: ¥{trade['net_amount']:,.2f}")
                print(f"   盈亏: ¥{trade['profit_loss']:,.2f} ({trade['profit_loss_pct']:.2f}%)")
                print(f"   持有天数: {trade['hold_days']}天")
            else:
                print(f"   总成本: ¥{trade['total_cost']:,.2f}")
            
            print(f"   资金变化: {trade['cash_change']:+,.2f}")
            print(f"   持仓变化: {trade['position_change']}")
            print(f"   原因: {trade['reason']}")
            print()
        
        print(f"=== 每日收益样本 (前5天) ===")
        daily_returns = data['daily_returns'][:5]
        for dr in daily_returns:
            print(f"{dr['date']}: 组合价值=¥{dr['value']:,.2f}, 日收益={dr['return']:.4%}, 现金=¥{dr['cash']:,.2f}")
        
        if len(data['daily_returns']) > 5:
            print(f"... (共{len(data['daily_returns'])}天数据)")
            
    else:
        print(f"获取结果失败: {result_response.text}")

if __name__ == "__main__":
    test_detailed_backtest()
