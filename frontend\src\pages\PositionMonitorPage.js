import React, { useState, useEffect } from 'react';
import { Card as ShadCard, <PERSON><PERSON><PERSON><PERSON> as <PERSON>had<PERSON><PERSON>Content, <PERSON><PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>eader, CardTitle as ShadCardTitle } from '../components/UI/card.jsx';
import { But<PERSON> as ShadButton } from '../components/UI/button.jsx';
import { Switch as ShadSwitch } from '../components/UI/switch.jsx';
import { Input as ShadInput } from '../components/UI/input.jsx';

import { Play, Square, RefreshCw, Settings, TrendingDown, Clock } from 'lucide-react';
import { toast } from 'sonner';

const API_BASE = '/api';

const PositionMonitorPage = () => {
  const [status, setStatus] = useState({
    is_running: false,
    signals_count: 0,
    last_check: null
  });
  
  const [config, setConfig] = useState({
    strategies: {
      loss_stop: true,
      trailing_stop: true,
      atr_stop: true,
      portfolio_stop: true
    },
    thresholds: {
      loss_stop_percent: 5.0,
      trailing_stop_percent: 5.0,
      atr_multiplier: 1.5,
      atr_period: 14,
      portfolio_loss_percent: 3.0
    },
    settings: {
      monitor_interval: 60,
      auto_sell: false,
      max_signals: 1000
    }
  });
  
  const [signals, setSignals] = useState([]);
  const [tradeLogs, setTradeLogs] = useState([]);
  const [pendingOrders, setPendingOrders] = useState([]);
  const [loading, setLoading] = useState(false);

  // 加载状态
  const loadStatus = async () => {
    try {
      const response = await fetch(`${API_BASE}/position-monitor/status`);
      const data = await response.json();
      setStatus(data);
    } catch (error) {
      console.error('加载状态失败:', error);
    }
  };

  // 加载配置
  const loadConfig = async () => {
    try {
      console.log('正在加载配置...');
      const response = await fetch(`${API_BASE}/position-monitor/config`);
      const data = await response.json();
      console.log('收到配置数据:', data);
      setConfig(data);
    } catch (error) {
      console.error('加载配置失败:', error);
      toast.error('加载配置失败: ' + error.message);
    }
  };

  // 加载信号
  const loadSignals = async () => {
    try {
      console.log('正在加载信号...');
      const response = await fetch(`${API_BASE}/position-monitor/signals?limit=20`);
      console.log('信号响应状态:', response.status);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      console.log('收到信号数据:', data);
      console.log('信号数量:', data.length);

      // 确保按时间正序排列（最新的在最下面）
      const sortedSignals = data.sort((a, b) => {
        const timeA = new Date(a.timestamp || 0);
        const timeB = new Date(b.timestamp || 0);
        return timeA - timeB; // 正序排列
      });

      setSignals(sortedSignals);
    } catch (error) {
      console.error('加载信号失败:', error);
      // 显示错误提示
      toast.error('加载止损信号失败: ' + error.message);
    }
  };

  // 加载交易日志
  const loadTradeLogs = async () => {
    try {
      console.log('正在加载交易日志...');
      const response = await fetch(`${API_BASE}/position-monitor/trade-logs?limit=20`);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      console.log('收到交易日志:', data);

      // 确保按时间倒序排列（最新的在前面）
      const sortedTradeLogs = data.sort((a, b) => {
        const timeA = new Date(a.timestamp || 0);
        const timeB = new Date(b.timestamp || 0);
        return timeB - timeA; // 倒序排列
      });

      setTradeLogs(sortedTradeLogs);
    } catch (error) {
      console.error('加载交易日志失败:', error);
      // 如果API不存在，使用空数组
      setTradeLogs([]);
    }
  };

  // 加载待处理订单
  const loadPendingOrders = async () => {
    try {
      console.log('正在加载待处理订单...');
      const response = await fetch(`${API_BASE}/position-monitor/pending-orders`);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      console.log('收到待处理订单:', data);
      setPendingOrders(data.pending_orders || []);
    } catch (error) {
      console.error('加载待处理订单失败:', error);
      setPendingOrders([]);
    }
  };

  // 取消订单
  const cancelOrder = async (orderId) => {
    try {
      setLoading(true);
      console.log('正在取消订单:', orderId);

      const response = await fetch(`${API_BASE}/position-monitor/cancel-order/${orderId}`, {
        method: 'POST'
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      console.log('取消订单结果:', result);

      if (result.success) {
        toast.success(`订单 ${orderId} 取消成功`);
        // 刷新待处理订单和交易日志
        await loadPendingOrders();
        await loadTradeLogs();
      } else {
        toast.error(`取消订单失败: ${result.message}`);
      }
    } catch (error) {
      console.error('取消订单失败:', error);
      toast.error('取消订单失败: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  // 启动监控
  const startMonitoring = async () => {
    setLoading(true);
    try {
      const response = await fetch(`${API_BASE}/position-monitor/start`, {
        method: 'POST'
      });
      const result = await response.json();
      toast.success(result.message);
      loadStatus();
    } catch (error) {
      toast.error('启动失败: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  // 停止监控
  const stopMonitoring = async () => {
    setLoading(true);
    try {
      const response = await fetch(`${API_BASE}/position-monitor/stop`, {
        method: 'POST'
      });
      const result = await response.json();
      toast.success(result.message);
      loadStatus();
    } catch (error) {
      toast.error('停止失败: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  // 手动检查
  const manualCheck = async () => {
    setLoading(true);
    try {
      const response = await fetch(`${API_BASE}/position-monitor/check`, {
        method: 'POST'
      });
      const result = await response.json();
      toast.success(result.message);
      loadSignals();
    } catch (error) {
      toast.error('检查失败: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  // 保存配置
  const saveConfig = async () => {
    setLoading(true);
    try {
      const response = await fetch(`${API_BASE}/position-monitor/config`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(config)
      });
      const result = await response.json();
      toast.success(result.message);
    } catch (error) {
      toast.error('保存配置失败: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  // 更新配置
  const updateConfig = (path, value) => {
    const keys = path.split('.');
    const newConfig = { ...config };
    let current = newConfig;
    
    for (let i = 0; i < keys.length - 1; i++) {
      current = current[keys[i]];
    }
    current[keys[keys.length - 1]] = value;
    
    setConfig(newConfig);
  };

  // 获取信号类型名称
  const getSignalTypeName = (type) => {
    const names = {
      'loss_stop': '亏损止损',
      'trailing_stop': '移动止损',
      'atr_stop': 'ATR止损',
      'portfolio_stop': '组合止损'
    };
    return names[type] || type;
  };

  // 页面加载时初始化
  useEffect(() => {
    loadStatus();
    loadConfig();
    loadSignals();
    loadTradeLogs();
    loadPendingOrders();

    // 定时刷新
    const statusInterval = setInterval(loadStatus, 5000);
    const signalsInterval = setInterval(loadSignals, 10000);
    const tradeLogsInterval = setInterval(loadTradeLogs, 15000);
    const pendingOrdersInterval = setInterval(loadPendingOrders, 8000);

    return () => {
      clearInterval(statusInterval);
      clearInterval(signalsInterval);
      clearInterval(tradeLogsInterval);
      clearInterval(pendingOrdersInterval);
    };
  }, []);

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">持仓监控</h1>
        <span className={`px-3 py-1 rounded-full text-sm font-medium ${
          status.is_running ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
        }`}>
          {status.is_running ? "运行中" : "已停止"}
        </span>
      </div>

      {/* 控制面板 */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <ShadCard>
          <ShadCardHeader>
            <ShadCardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              监控控制
            </ShadCardTitle>
          </ShadCardHeader>
          <ShadCardContent className="space-y-4">
            <div className="flex gap-2">
              <ShadButton
                onClick={startMonitoring}
                disabled={status.is_running || loading}
                className="flex items-center gap-2"
              >
                <Play className="h-4 w-4" />
                启动监控
              </ShadButton>
              <ShadButton
                onClick={stopMonitoring}
                disabled={!status.is_running || loading}
                variant="destructive"
                className="flex items-center gap-2 bg-red-600 hover:bg-red-700"
              >
                <Square className="h-4 w-4" />
                停止监控
              </ShadButton>
              <ShadButton
                onClick={manualCheck}
                disabled={loading}
                variant="outline"
                className="flex items-center gap-2"
              >
                <RefreshCw className="h-4 w-4" />
                手动检查
              </ShadButton>
            </div>
            <div className="text-sm text-gray-600">
              <p>信号总数: {status.signals_count}</p>
              <p>最后检查: {status.last_check ? new Date(status.last_check).toLocaleString() : '未开始'}</p>
            </div>
          </ShadCardContent>
        </ShadCard>

        <ShadCard>
          <ShadCardHeader>
            <ShadCardTitle>策略配置</ShadCardTitle>
          </ShadCardHeader>
          <ShadCardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="flex items-center space-x-2">
                <ShadSwitch
                  checked={config.strategies?.loss_stop || false}
                  onChange={(checked) => updateConfig('strategies.loss_stop', checked)}
                />
                <label className="text-sm font-medium">亏损止损</label>
              </div>
              <div className="flex items-center space-x-2">
                <ShadSwitch
                  checked={config.strategies?.trailing_stop || false}
                  onChange={(checked) => updateConfig('strategies.trailing_stop', checked)}
                />
                <label className="text-sm font-medium">移动止损</label>
              </div>
              <div className="flex items-center space-x-2">
                <ShadSwitch
                  checked={config.strategies?.atr_stop || false}
                  onChange={(checked) => updateConfig('strategies.atr_stop', checked)}
                />
                <label className="text-sm font-medium">ATR止损</label>
              </div>
              <div className="flex items-center space-x-2">
                <ShadSwitch
                  checked={config.strategies?.portfolio_stop || false}
                  onChange={(checked) => updateConfig('strategies.portfolio_stop', checked)}
                />
                <label className="text-sm font-medium">组合止损</label>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-1">亏损止损阈值 (%)</label>
                <ShadInput
                  type="number"
                  value={config.thresholds?.loss_stop_percent || 5}
                  onChange={(e) => updateConfig('thresholds.loss_stop_percent', parseFloat(e.target.value))}
                  step="0.1"
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">移动止损阈值 (%)</label>
                <ShadInput
                  type="number"
                  value={config.thresholds?.trailing_stop_percent || 5}
                  onChange={(e) => updateConfig('thresholds.trailing_stop_percent', parseFloat(e.target.value))}
                  step="0.1"
                />
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <ShadSwitch
                checked={config.settings?.auto_sell || false}
                onChange={(checked) => updateConfig('settings.auto_sell', checked)}
              />
              <label className="text-sm font-medium text-red-600">自动卖出 (谨慎开启)</label>
            </div>

            <ShadButton onClick={saveConfig} disabled={loading} className="w-full">
              保存配置
            </ShadButton>
          </ShadCardContent>
        </ShadCard>
      </div>

      {/* 待处理订单 */}
      <ShadCard className="mb-6">
        <ShadCardHeader>
          <div className="flex items-center justify-between">
            <ShadCardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              待处理订单 ({pendingOrders.length})
            </ShadCardTitle>
            <ShadButton
              onClick={loadPendingOrders}
              disabled={loading}
              variant="outline"
              size="sm"
              className="flex items-center gap-1"
            >
              <RefreshCw className="h-3 w-3" />
              刷新
            </ShadButton>
          </div>
        </ShadCardHeader>
        <ShadCardContent>
          {pendingOrders.length === 0 ? (
            <p className="text-gray-500 text-center py-8">暂无待处理订单</p>
          ) : (
            <div className="space-y-3">
              {pendingOrders.map((order, index) => (
                <div key={index} className="border border-yellow-200 rounded-lg p-3 bg-yellow-50">
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <div className="font-medium text-yellow-800">
                        卖出 {order.stock_code} - 订单号: {order.order_id}
                      </div>
                      <div className="text-sm text-gray-700 mt-1">
                        数量: {order.quantity} | 价格: ¥{order.price}
                      </div>
                      <div className="text-sm text-gray-600 mt-1">
                        触发: {order.signal_type} | 提交时间: {new Date(order.submit_time).toLocaleString()}
                      </div>
                    </div>
                    <div className="flex items-center gap-2 ml-2">
                      <span className="px-2 py-1 bg-yellow-600 text-white text-xs rounded-full">
                        {order.status}
                      </span>
                      <ShadButton
                        onClick={() => cancelOrder(order.order_id)}
                        disabled={loading}
                        variant="destructive"
                        size="sm"
                        className="text-xs px-2 py-1"
                      >
                        取消
                      </ShadButton>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </ShadCardContent>
      </ShadCard>

      {/* 止损信号和交易日志 - 左右并排显示 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 止损信号 */}
        <ShadCard>
        <ShadCardHeader>
          <div className="flex items-center justify-between">
            <ShadCardTitle className="flex items-center gap-2">
              <TrendingDown className="h-5 w-5" />
              止损信号
            </ShadCardTitle>
            <ShadButton
              onClick={loadSignals}
              disabled={loading}
              variant="outline"
              size="sm"
              className="flex items-center gap-1"
            >
              <RefreshCw className="h-3 w-3" />
              刷新
            </ShadButton>
          </div>
        </ShadCardHeader>
        <ShadCardContent>
          <div className="mb-2 text-sm text-gray-500">
            信号数量: {signals.length} | 最后更新: {new Date().toLocaleTimeString()}
          </div>
          {signals.length === 0 ? (
            <p className="text-gray-500 text-center py-8">暂无止损信号</p>
          ) : (
            <div className="space-y-3 max-h-96 overflow-y-auto">
              {signals.map((signal, index) => {
                console.log('渲染信号:', signal); // 调试信息

                // 确保信号数据完整
                const stockCode = signal?.stock_code || '未知股票';
                const signalType = signal?.signal_type || 'unknown';
                const message = signal?.message || '无消息';
                const timestamp = signal?.timestamp || new Date().toISOString();

                return (
                  <div key={index} className="border border-red-200 rounded-lg p-3 bg-red-50">
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <div className="font-medium text-red-800">
                          {stockCode} - {getSignalTypeName(signalType)}
                        </div>
                        <div className="text-sm text-gray-700 mt-1">
                          {message}
                        </div>
                        <div className="text-xs text-gray-500 mt-1">
                          {new Date(timestamp).toLocaleString()}
                        </div>
                      </div>
                      <span className="px-2 py-1 bg-red-600 text-white text-xs rounded-full ml-2">
                        {getSignalTypeName(signalType)}
                      </span>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </ShadCardContent>
        </ShadCard>

        {/* 交易日志 */}
        <ShadCard>
        <ShadCardHeader>
          <div className="flex items-center justify-between">
            <ShadCardTitle className="flex items-center gap-2">
              <TrendingDown className="h-5 w-5" />
              交易日志
            </ShadCardTitle>
            <ShadButton
              onClick={loadTradeLogs}
              disabled={loading}
              variant="outline"
              size="sm"
              className="flex items-center gap-1"
            >
              <RefreshCw className="h-3 w-3" />
              刷新
            </ShadButton>
          </div>
        </ShadCardHeader>
        <ShadCardContent>
          <div className="mb-2 text-sm text-gray-500">
            交易记录: {tradeLogs.length} | 最后更新: {new Date().toLocaleTimeString()}
          </div>
          {tradeLogs.length === 0 ? (
            <p className="text-gray-500 text-center py-8">暂无交易记录</p>
          ) : (
            <div className="space-y-3 max-h-96 overflow-y-auto">
              {tradeLogs.map((trade, index) => (
                <div key={index} className="border border-blue-200 rounded-lg p-3 bg-blue-50">
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <div className="font-medium text-blue-800">
                        {trade.action || '交易'} {trade.stock_code || '未知股票'}
                      </div>
                      <div className="text-sm text-gray-700 mt-1">
                        数量: {trade.quantity || 0} | 价格: ¥{trade.price || 0}
                      </div>
                      <div className="text-sm mt-1">
                        状态: <span className={trade.success ? 'text-green-600' : 'text-red-600'}>
                          {trade.success ? '成功' : '失败'}
                        </span>
                        {trade.order_id && ` | 订单: ${trade.order_id}`}
                      </div>
                      {trade.trigger_reason && (
                        <div className="text-sm text-gray-600 mt-1">
                          触发: {trade.trigger_reason}
                        </div>
                      )}
                      <div className="text-xs text-gray-500 mt-1">
                        {trade.timestamp ? new Date(trade.timestamp).toLocaleString() : '未知'}
                      </div>
                    </div>
                    <span className={`px-2 py-1 text-xs rounded-full ml-2 ${
                      trade.success
                        ? 'bg-green-600 text-white'
                        : 'bg-red-600 text-white'
                    }`}>
                      {trade.success ? '成功' : '失败'}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          )}
        </ShadCardContent>
        </ShadCard>
      </div>
    </div>
  );
};

export default PositionMonitorPage;
