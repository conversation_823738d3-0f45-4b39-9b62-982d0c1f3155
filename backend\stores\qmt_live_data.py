#!/usr/bin/env python3
"""
QMT实时数据源
专门用于实时交易，持续推送数据
"""

import threading
import time
import queue
from datetime import datetime, timedelta
from typing import Optional, Dict, Any

import backtrader as bt
import pandas as pd

from backend.core.logger import get_logger

logger = get_logger(__name__)


class QMTLiveData(bt.feeds.DataBase):
    """
    QMT实时数据源
    
    特点：
    1. 持续运行，不会结束
    2. 实时推送数据
    3. 支持纸面交易和实盘交易
    4. 提供真实的市场数据流
    """
    
    params = (
        ('dataname', ''),           # 股票代码
        ('timeframe', bt.TimeFrame.Minutes),  # 时间框架
        ('compression', 1),         # 压缩比例
        ('paper_trading', True),    # 交易模式
        ('update_interval', 5),     # 数据更新间隔（秒）- 更频繁的更新
    )
    
    def __init__(self, store=None, **kwargs):
        """初始化实时数据源"""
        self.store = store
        # 优先从dataname获取股票代码，如果没有则从stock_code获取
        self.stock_code = kwargs.get('dataname', '') or kwargs.get('stock_code', '')
        self.paper_trading = kwargs.get('paper_trading', True)
        self.update_interval = kwargs.get('update_interval', 5)  # 默认5秒更新

        # 调试信息
        logger.debug(f"🔍 QMTLiveData初始化参数: {kwargs}")
        logger.debug(f"🔍 解析的股票代码: '{self.stock_code}'")
        
        # 数据队列
        self.data_queue = queue.Queue()
        
        # 运行状态
        self._running = False
        self._update_thread = None
        
        # 当前数据
        self._current_price = 100.0  # 初始价格
        self._last_update = None
        
        logger.info(f"📡 初始化实时数据源: {self.stock_code}")
        logger.info(f"   交易模式: {'纸面交易' if self.paper_trading else '实盘交易'}")
        logger.info(f"   更新间隔: {self.update_interval}秒")
        
        # 调用父类初始化
        super().__init__()
    
    def start(self):
        """启动实时数据流"""
        logger.info(f"🚀 启动实时数据流: {self.stock_code}")

        # 初始化_laststatus属性（backtrader兼容性）
        if not hasattr(self, '_laststatus'):
            self._laststatus = self.DISCONNECTED

        self._running = True
        self._update_thread = threading.Thread(target=self._data_generator, daemon=True)
        self._update_thread.start()

        # 通知策略数据已连接
        self.put_notification(self.CONNECTED)
    
    def stop(self):
        """停止实时数据流"""
        logger.info(f"🛑 停止实时数据流: {self.stock_code}")
        
        self._running = False
        if self._update_thread and self._update_thread.is_alive():
            self._update_thread.join(timeout=1)
    
    def _data_generator(self):
        """数据生成器线程 - 先加载历史数据，然后接收实时数据"""
        logger.info(f"📊 开始数据生成: {self.stock_code}")

        try:
            # 第一步：加载历史数据作为基础
            self._load_historical_data()

            # 第二步：开始实时数据更新循环
            logger.info(f"🔄 开始实时数据更新: {self.stock_code}")
            last_bar_time = None

            while self._running:
                try:
                    bar_data = self._generate_bar()
                    if bar_data:
                        # 检查是否是新的K线（避免重复数据）
                        current_bar_time = bar_data['datetime']
                        if last_bar_time is None or current_bar_time != last_bar_time:
                            self.data_queue.put(bar_data)
                            last_bar_time = current_bar_time
                            logger.debug(f"📈 新K线: {self.stock_code} @ {bar_data['close']:.2f}")

                    # 等待下一次更新（实时数据通常1分钟更新一次）
                    time.sleep(self.update_interval)

                except Exception as e:
                    logger.error(f"❌ 实时数据更新异常: {e}")
                    if self._running:  # 只有在运行状态才继续
                        time.sleep(5)  # 出错后等待5秒再重试
                    else:
                        break

        except Exception as e:
            logger.error(f"❌ 数据生成器初始化失败: {e}")

        logger.info(f"🛑 数据生成线程结束: {self.stock_code}")

    def _load_historical_data(self):
        """加载历史数据作为基础"""
        logger.info(f"📈 加载历史数据: {self.stock_code}")

        try:
            if not self.store:
                raise RuntimeError("QMT Store未初始化")

            data_manager = self.store.get_data_manager()
            if not data_manager:
                raise RuntimeError("数据管理器未初始化")

            # 获取最近60天的历史数据（确保有足够数据用于指标计算）
            from datetime import datetime, timedelta
            end_date = datetime.now().strftime('%Y-%m-%d')
            start_date = (datetime.now() - timedelta(days=60)).strftime('%Y-%m-%d')

            logger.info(f"📊 获取历史数据: {self.stock_code} ({start_date} 到 {end_date})")

            # 获取日线历史数据
            df = data_manager.get_stock_data(
                stock_code=self.stock_code,
                period='1d',  # 日线数据
                start_date=start_date,
                end_date=end_date
            )

            if df is None or len(df) == 0:
                raise ValueError(f"未获取到{self.stock_code}的历史数据")

            logger.info(f"✅ 获取到历史数据: {self.stock_code} {len(df)}条记录")

            # 将历史数据转换为K线格式并加入队列
            for i, (timestamp, row) in enumerate(df.iterrows()):
                if not self._running:
                    break

                bar_data = {
                    'datetime': timestamp.to_pydatetime() if hasattr(timestamp, 'to_pydatetime') else timestamp,
                    'open': float(row['open']),
                    'high': float(row['high']),
                    'low': float(row['low']),
                    'close': float(row['close']),
                    'volume': int(row['volume']),
                    'openinterest': 0
                }

                self.data_queue.put(bar_data)

                # 每10条记录输出一次进度
                if i % 10 == 0:
                    logger.debug(f"📈 历史数据 {i+1}/{len(df)}: {self.stock_code} @ {bar_data['close']:.2f}")

            logger.info(f"✅ 历史数据加载完成: {self.stock_code}")

        except Exception as e:
            logger.error(f"❌ 加载历史数据失败: {e}")
            raise
    

    
    def _load(self):
        """加载数据（backtrader调用）"""
        if not self._running:
            return False  # 如果已停止，返回False结束

        try:
            # 尝试从队列获取数据，使用较长的超时时间
            bar_data = self.data_queue.get(timeout=1.0)

            # 设置数据到backtrader的lines
            self.lines.datetime[0] = bt.date2num(bar_data['datetime'])
            self.lines.open[0] = bar_data['open']
            self.lines.high[0] = bar_data['high']
            self.lines.low[0] = bar_data['low']
            self.lines.close[0] = bar_data['close']
            self.lines.volume[0] = bar_data['volume']
            self.lines.openinterest[0] = bar_data['openinterest']

            return True  # 成功加载数据

        except queue.Empty:
            # 没有新数据，但继续等待（不结束策略）
            # 对于实时数据源，我们需要持续运行
            if self._running:
                return None  # 返回None表示暂时没有数据，但不结束
            else:
                return False  # 如果已停止，返回False结束
        except Exception as e:
            logger.error(f"❌ 加载数据失败: {e}")
            return False
    
    def islive(self):
        """标识这是实时数据源"""
        return True
    
    def haslivedata(self):
        """检查是否有实时数据"""
        return not self.data_queue.empty()
    
    def get_current_price(self) -> float:
        """获取当前价格"""
        return self._current_price
    
    def get_last_update(self) -> Optional[datetime]:
        """获取最后更新时间"""
        return self._last_update


class QMTRealTimeData(QMTLiveData):
    """
    QMT真实实时数据源
    连接到真实的QMT数据接口
    """
    
    def __init__(self, store=None, **kwargs):
        """初始化真实实时数据源"""
        super().__init__(store=store, **kwargs)
        
        # 真实数据连接
        self.xt_data = None
        self._init_xt_connection()
    
    def _init_xt_connection(self):
        """初始化xtquant连接"""
        try:
            # 无论是否纸面交易，都连接真实数据源
            logger.info(f"🔌 连接xtquant实时数据: {self.stock_code}")

            # 导入并初始化xtquant
            from xtquant import xtdata
            self.xt_data = xtdata

            # 获取数据管理器以确保连接正常
            if self.store:
                data_manager = self.store.get_data_manager()
                if data_manager:
                    logger.info(f"✅ xtquant连接成功: {self.stock_code}")
                else:
                    raise RuntimeError("数据管理器未初始化")
            else:
                raise RuntimeError("QMT Store未初始化")

        except Exception as e:
            logger.error(f"❌ xtquant连接失败: {e}")
            raise RuntimeError(f"无法连接到QMT数据源: {e}")
    
    def _generate_bar(self) -> Optional[Dict[str, Any]]:
        """生成K线数据（只使用真实数据）"""
        return self._get_real_bar()
    
    def _get_real_bar(self) -> Optional[Dict[str, Any]]:
        """获取真实K线数据"""
        try:
            if not self.xt_data:
                raise RuntimeError("xtquant未初始化")

            # 获取最新的1分钟K线数据
            now = datetime.now()

            # 获取最近的K线数据（获取最新2条，取最新的一条）
            history_data = self.xt_data.get_market_data_ex(
                [], [self.stock_code],
                period='1m',  # 1分钟K线
                count=2,      # 获取最近2条
                dividend_type="front_ratio"
            )

            if not history_data or self.stock_code not in history_data:
                raise ValueError(f"未获取到{self.stock_code}的实时数据")

            df = history_data[self.stock_code]
            if df is None or len(df) == 0:
                raise ValueError(f"{self.stock_code}数据为空")

            # 取最新的一条数据
            latest_row = df.iloc[-1]
            latest_time = df.index[-1]

            # 构造K线数据
            bar_data = {
                'datetime': latest_time if hasattr(latest_time, 'to_pydatetime') else now,
                'open': float(latest_row['open']),
                'high': float(latest_row['high']),
                'low': float(latest_row['low']),
                'close': float(latest_row['close']),
                'volume': int(latest_row['volume']),
                'openinterest': 0
            }

            # 验证数据合理性
            if (bar_data['high'] < bar_data['low'] or
                bar_data['open'] <= 0 or bar_data['close'] <= 0 or
                bar_data['high'] <= 0 or bar_data['low'] <= 0):
                raise ValueError(f"获取的K线数据不合理: {bar_data}")

            logger.debug(f"📈 获取真实K线: {self.stock_code} @ {bar_data['close']:.2f}")
            return bar_data

        except Exception as e:
            logger.error(f"❌ 获取真实数据失败: {e}")
            raise RuntimeError(f"无法获取{self.stock_code}的实时数据: {e}")


def create_live_data(stock_code: str, paper_trading: bool = True, **kwargs) -> QMTLiveData:
    """
    创建实时数据源

    Args:
        stock_code: 股票代码
        paper_trading: 是否纸面交易
        **kwargs: 其他参数

    Returns:
        实时数据源实例
    """
    # 确保不重复传递paper_trading参数
    kwargs.pop('paper_trading', None)

    # 确保股票代码正确传递
    kwargs['dataname'] = stock_code
    kwargs['stock_code'] = stock_code  # 备用字段

    logger.debug(f"🔍 create_live_data: stock_code='{stock_code}', kwargs={kwargs}")

    if paper_trading:
        # 纸面交易使用模拟数据
        return QMTLiveData(paper_trading=True, **kwargs)
    else:
        # 实盘交易使用真实数据
        return QMTRealTimeData(paper_trading=False, **kwargs)
