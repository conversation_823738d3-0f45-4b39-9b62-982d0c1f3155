#!/usr/bin/env python3
"""
策略适配器 - 让同一个策略逻辑既能用于回测，也能用于实盘
"""

import asyncio
import pandas as pd
from typing import Dict, List, Any, Optional
from datetime import datetime
from abc import ABC, abstractmethod

from backend.strategies.signal_generators import SignalGenerator, Signal, SignalType
from backend.core.logger import get_logger

logger = get_logger(__name__)

class StrategyAdapter(ABC):
    """策略适配器基类"""
    
    def __init__(self, strategy_id: str, signal_generator: SignalGenerator, config: Dict[str, Any]):
        self.strategy_id = strategy_id
        self.signal_generator = signal_generator
        self.config = config
        self.is_running = False
        
    @abstractmethod
    async def execute_signal(self, signal: Signal) -> bool:
        """执行交易信号"""
        pass
    
    @abstractmethod
    async def get_market_data(self) -> Dict[str, pd.DataFrame]:
        """获取市场数据"""
        pass
    
    async def run_strategy(self):
        """运行策略"""
        if self.is_running:
            return
        
        self.is_running = True
        logger.info(f"🚀 策略适配器 {self.strategy_id} 开始运行")
        
        try:
            while self.is_running:
                # 获取市场数据
                market_data = await self.get_market_data()
                
                # 生成信号
                signals = self.signal_generator.generate_signals(market_data)
                
                # 执行信号
                for signal in signals:
                    success = await self.execute_signal(signal)
                    if success:
                        logger.info(f"✅ 信号执行成功: {signal.to_dict()}")
                    else:
                        logger.warning(f"⚠️ 信号执行失败: {signal.to_dict()}")
                
                # 等待下一次检查
                await asyncio.sleep(self.config.get('check_interval', 60))
                
        except Exception as e:
            logger.error(f"❌ 策略适配器 {self.strategy_id} 运行错误: {e}")
        finally:
            self.is_running = False
            logger.info(f"🛑 策略适配器 {self.strategy_id} 已停止")
    
    def stop(self):
        """停止策略"""
        self.is_running = False

class BacktestAdapter(StrategyAdapter):
    """回测适配器 - 将信号生成器适配到backtrader"""
    
    def __init__(self, strategy_id: str, signal_generator: SignalGenerator, config: Dict[str, Any]):
        super().__init__(strategy_id, signal_generator, config)
        self.backtest_data = config.get('backtest_data', {})
        self.current_index = 0
    
    async def get_market_data(self) -> Dict[str, pd.DataFrame]:
        """获取回测数据"""
        # 返回当前时间点的数据切片
        market_data = {}
        for stock_code, df in self.backtest_data.items():
            if self.current_index < len(df):
                market_data[stock_code] = df.iloc[:self.current_index + 1]
        
        self.current_index += 1
        return market_data
    
    async def execute_signal(self, signal: Signal) -> bool:
        """模拟执行信号（回测）"""
        logger.info(f"📊 回测信号: {signal.signal_type.value} {signal.stock_code} "
                   f"{signal.quantity}股 @{signal.price:.2f}")
        return True

class LiveAdapter(StrategyAdapter):
    """实盘适配器 - 将信号生成器适配到实盘交易"""
    
    def __init__(self, strategy_id: str, signal_generator: SignalGenerator, config: Dict[str, Any]):
        super().__init__(strategy_id, signal_generator, config)
        self.data_manager = None  # 将在初始化时设置
        self.trader = None        # 将在初始化时设置
        self.positions = {}       # 当前持仓
    
    async def get_market_data(self) -> Dict[str, pd.DataFrame]:
        """获取实时市场数据"""
        if not self.data_manager:
            return {}
        
        # 获取关注股票列表
        stock_codes = self.config.get('stock_codes', [])
        market_data = {}
        
        for stock_code in stock_codes:
            try:
                # 获取最近的K线数据
                df = await self.data_manager.get_kline_data(
                    stock_code, 
                    period='1min', 
                    count=200  # 获取足够的数据用于指标计算
                )
                if not df.empty:
                    market_data[stock_code] = df
            except Exception as e:
                logger.warning(f"⚠️ 获取 {stock_code} 数据失败: {e}")
        
        return market_data
    
    async def execute_signal(self, signal: Signal) -> bool:
        """执行实盘交易信号"""
        if not self.trader:
            logger.error("❌ 交易器未初始化")
            return False
        
        try:
            if signal.signal_type == SignalType.BUY:
                # 检查资金是否充足
                available_cash = await self.trader.get_available_cash()
                required_cash = signal.price * signal.quantity
                
                if available_cash < required_cash:
                    logger.warning(f"⚠️ 资金不足: 需要{required_cash:.2f}, 可用{available_cash:.2f}")
                    return False
                
                # 执行买入
                order_id = await self.trader.buy(
                    stock_code=signal.stock_code,
                    quantity=signal.quantity,
                    price=signal.price
                )
                
                if order_id:
                    logger.info(f"📈 买入订单提交: {signal.stock_code} {signal.quantity}股 "
                               f"@{signal.price:.2f}, 订单号: {order_id}")
                    return True
                
            elif signal.signal_type == SignalType.SELL:
                # 检查持仓
                position = self.positions.get(signal.stock_code, 0)
                if position <= 0:
                    logger.warning(f"⚠️ 无持仓可卖: {signal.stock_code}")
                    return False
                
                # 确定卖出数量
                sell_quantity = min(position, signal.quantity) if signal.quantity > 0 else position
                
                # 执行卖出
                order_id = await self.trader.sell(
                    stock_code=signal.stock_code,
                    quantity=sell_quantity,
                    price=signal.price
                )
                
                if order_id:
                    logger.info(f"📉 卖出订单提交: {signal.stock_code} {sell_quantity}股 "
                               f"@{signal.price:.2f}, 订单号: {order_id}")
                    return True
            
        except Exception as e:
            logger.error(f"❌ 执行信号失败: {e}")
        
        return False
    
    async def update_positions(self):
        """更新持仓信息"""
        if not self.trader:
            return
        
        try:
            positions = await self.trader.get_positions()
            self.positions = {pos['stock_code']: pos['quantity'] for pos in positions}
        except Exception as e:
            logger.error(f"❌ 更新持仓失败: {e}")

# 策略工厂
class StrategyFactory:
    """策略工厂 - 根据配置创建策略适配器"""
    
    @staticmethod
    def create_strategy(strategy_type: str, mode: str, config: Dict[str, Any]) -> StrategyAdapter:
        """
        创建策略适配器
        
        Args:
            strategy_type: 策略类型 (multi_signal, moving_average, etc.)
            mode: 运行模式 (backtest, live)
            config: 策略配置
        """
        from backend.strategies.signal_generators import MultiSignalGenerator, MovingAverageGenerator
        
        # 创建信号生成器
        if strategy_type == 'multi_signal':
            signal_generator = MultiSignalGenerator(config)
        elif strategy_type == 'moving_average':
            signal_generator = MovingAverageGenerator(config)
        else:
            raise ValueError(f"不支持的策略类型: {strategy_type}")
        
        # 创建适配器
        strategy_id = config.get('strategy_id', f"{strategy_type}_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
        
        if mode == 'backtest':
            return BacktestAdapter(strategy_id, signal_generator, config)
        elif mode == 'live':
            return LiveAdapter(strategy_id, signal_generator, config)
        else:
            raise ValueError(f"不支持的运行模式: {mode}")

# 使用示例
async def example_usage():
    """使用示例"""
    
    # 多信号策略配置
    config = {
        'strategy_id': 'multi_signal_001',
        'buy_signal_ma_cross': True,
        'ma_short_period': 5,
        'ma_long_period': 20,
        'stock_codes': ['000001.SZ', '000002.SZ'],
        'check_interval': 60  # 60秒检查一次
    }
    
    # 创建实盘策略
    live_strategy = StrategyFactory.create_strategy('multi_signal', 'live', config)
    
    # 运行策略
    await live_strategy.run_strategy()

if __name__ == "__main__":
    asyncio.run(example_usage())
