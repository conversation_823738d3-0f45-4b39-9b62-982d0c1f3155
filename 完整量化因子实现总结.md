# QMT-TRADER 完整量化因子实现总结

## 🎯 功能概述

成功为QMT-TRADER智能选股系统实现了完整的量化因子体系，包含你要求的所有核心Alpha因子和ETF筛选功能。系统现在具备了专业量化投资平台的选股能力。

## ✅ 完整实现的量化因子

### 1. **价值因子（Value Factors）**

#### 核心指标
- **EP（市盈率倒数）** - `ep_ratio = 1 / PE_TTM` → 越高越好
- **BP（市净率倒数）** - `bp_ratio = 1 / PB` → 越高越好  
- **SP（市销率倒数）** - `sp_ratio = 1 / PS_TTM` → 小市值公司更有效
- **股息率** - `dividend_yield = Dividend / Price` → 稳健型组合核心
- **EV/EBITDA** - 企业价值倍数，比PE更准确

#### 量化表达式
```python
# 价值因子综合得分（标准化后）
value_score = 0
if ep_ratio > 0: value_score += min(ep_ratio * 100, 10)
if bp_ratio > 0: value_score += min(bp_ratio * 10, 10)  
if dividend_yield > 0: value_score += min(dividend_yield * 200, 10)
value_factor = value_score / count
```

#### 筛选条件
- `pe_min/pe_max` - 市盈率区间筛选
- `pb_min/pb_max` - 市净率区间筛选
- `ps_min/ps_max` - 市销率区间筛选
- `dividend_yield_min/max` - 股息率区间筛选

### 2. **质量因子（Quality Factors）**

#### 核心指标
- **ROE（净资产收益率）** - `Net_Profit / Equity` → >15%为佳
- **ROA（总资产收益率）** - `Net_Profit / Total_Assets`
- **毛利率** - `Gross_Profit / Revenue` → 稳定性指标
- **净利率** - `Net_Profit / Revenue` → 盈利能力
- **资产周转率** - `Revenue / Total_Assets` → 运营效率
- **权益乘数** - `Total_Assets / Equity` → 财务杠杆

#### 量化表达式
```python
# 质量因子综合得分
quality_score = 0
if roe > 0: quality_score += min(roe / 2, 10)      # ROE/2，最大10分
if roa > 0: quality_score += min(roa, 10)          # ROA直接计分
if gross_margin > 0: quality_score += min(gross_margin / 5, 10)  # 毛利率/5
quality_factor = quality_score / count
```

#### 筛选条件
- `roe_min/roe_max` - ROE区间筛选
- `roa_min/roa_max` - ROA区间筛选
- `gross_margin_min/max` - 毛利率区间筛选

### 3. **成长因子（Growth Factors）**

#### 核心指标
- **净利润增长率（3年CAGR）** - 连续3年复合增长率
- **营收增长率（3年CAGR）** - 连续3年>20%为佳
- **EPS增长率** - 每股收益增长率
- **研发费用增长率** - 科技股重要指标

#### 量化表达式
```python
# 成长因子综合得分
growth_score = 0
if revenue_growth > 0: growth_score += min(revenue_growth / 2, 10)  # 营收增长率/2
if profit_growth > 0: growth_score += min(profit_growth / 3, 12)    # 利润增长率/3
if eps_growth > 0: growth_score += min(eps_growth / 3, 10)          # EPS增长率/3
growth_factor = growth_score / count
```

#### 筛选条件
- `revenue_growth_min/max` - 营收增长率区间
- `profit_growth_min/max` - 净利润增长率区间
- `eps_growth_min/max` - EPS增长率区间

### 4. **动量因子（Momentum Factors）**

#### 核心指标
- **短期动量（1M）** - `Price_0 / Price_21 - 1` → 过去1月收益率
- **中期动量（3M）** - `Price_0 / Price_63 - 1` → 过去3月收益率
- **长期动量（12M）** - `Price_0 / Price_252 - 1` → 过去12月收益率
- **动量加速度** - `(1M_return - 3M_return)` → 捕捉趋势变化
- **成交量动量** - `Volume_avg_5d / Volume_avg_20d` → 量价配合

#### 量化表达式
```python
# 动量因子计算
momentum_1m = (close[-1] - close[-22]) / close[-22]    # 1月动量
momentum_3m = (close[-1] - close[-66]) / close[-66]    # 3月动量
momentum_12m = (close[-1] - close[-252]) / close[-252] # 12月动量
momentum_acceleration = momentum_1m - momentum_3m       # 动量加速度
volume_momentum = volume_ma5 / volume_ma20             # 成交量动量
```

#### 筛选条件
- `momentum_1m_min/max` - 1月动量区间
- `momentum_3m_min/max` - 3月动量区间
- `momentum_12m_min/max` - 12月动量区间

### 5. **风险控制因子**

#### 波动率因子
- **历史波动率（20日）** - 年化波动率<40%为佳
- **Beta系数** - 0.8-1.2之间为佳
- **最大回撤** - <30%为佳
- **VaR（风险价值）** - 95%和99%置信度
- **下行波动率** - 只考虑负收益的波动率

#### 流动性因子
- **日均成交额** - >1亿元为佳
- **Amihud非流动性指标** - `abs(Daily_Return) / Dollar_Volume`
- **换手率** - 3%-15%之间为佳
- **量比** - 当日成交量/平均成交量

#### 规模因子
- **市值中性化** - 在不同市值分组内选股
- **小盘股效应** - 但需控制流动性风险

#### 筛选条件
- `volatility_min/max` - 波动率区间
- `beta_min/max` - Beta系数区间
- `max_drawdown_max` - 最大回撤上限
- `avg_amount_min/max` - 日均成交额区间
- `volume_ratio_min/max` - 量比区间
- `turnover_rate_min/max` - 换手率区间

### 6. **技术面量化因子**

#### 均线系统因子
- **均线排列** - `MA5 > MA10 > MA20` → 多头排列得分
- **价格位置** - `(Close - MA20) / MA20` → 偏离度
- **均线斜率** - `MA20_today / MA20_5d_ago - 1`

#### 动量振荡因子
- **RSI因子** - `RSI(14)` → 30-70之间为佳
- **MACD因子** - `MACD_Histogram` 变化率
- **布林带位置** - `(Close - Lower_Band) / (Upper_Band - Lower_Band)`

#### 成交量因子
- **量比** - `Volume / Volume_MA20`
- **资金流** - `Volume * Close` 的动量
- **大单净流入率** - 简化版资金流向

#### 筛选条件
- `rsi_min/max` - RSI区间筛选
- `macd_signal` - MACD信号筛选
- `ma_arrangement` - 均线排列筛选
- `bb_position` - 布林带位置筛选

### 7. **ETF筛选功能**

#### ETF识别
- **代码识别** - 自动识别ETF代码（51xxxx.SH, 58xxxx.SH, 15xxxx.SZ, 16xxxx.SZ）
- **类型分类** - 股票型、债券型、商品型、货币型、跨境型

#### ETF筛选条件
- `only_etf` - 仅选择ETF
- `include_etf` - 包含ETF
- `exclude_etf` - 排除ETF
- `etf_types` - 按ETF类型筛选

#### ETF分类逻辑
```python
def _classify_etf_type(self, etf_name: str) -> str:
    if any(keyword in etf_name for keyword in ['300', '500', '50']):
        return 'stock'      # 股票型ETF
    elif any(keyword in etf_name for keyword in ['债', '国债']):
        return 'bond'       # 债券型ETF
    elif any(keyword in etf_name for keyword in ['黄金', '原油']):
        return 'commodity'  # 商品型ETF
    # ... 更多分类逻辑
```

## 🔧 技术实现架构

### 核心模块
```
backend/stock_selection/
├── quantitative_factors.py    # 量化因子计算核心
├── stock_selector.py          # 选股器主类（已增强）
├── selection_presets.py       # 预设策略模板
└── alpha101_factors.py        # Alpha101因子（已有）
```

### 量化因子计算器
```python
class QuantitativeFactors:
    def calculate_all_factors(self, df, fundamental_data):
        # 1. 价值因子
        value_factors = self.calculate_value_factors(close, fundamental_data)
        
        # 2. 质量因子  
        quality_factors = self.calculate_quality_factors(fundamental_data)
        
        # 3. 成长因子
        growth_factors = self.calculate_growth_factors(fundamental_data)
        
        # 4. 动量因子
        momentum_factors = self.calculate_momentum_factors(close, volume, amount)
        
        # 5. 风险控制因子
        risk_factors = self.calculate_risk_factors(close, high, low, volume)
        
        # 6. 技术面因子
        technical_factors = self.calculate_technical_factors(close, high, low, volume, open_price)
        
        # 7. 流动性因子
        liquidity_factors = self.calculate_liquidity_factors(volume, amount, close)
        
        return factors
```

### 选股条件扩展
```python
@dataclass
class SelectionCriteria:
    # ETF筛选
    only_etf: bool = False
    etf_types: Optional[List[str]] = None
    
    # 价值因子
    pe_min: Optional[float] = None
    pb_max: Optional[float] = None
    dividend_yield_min: Optional[float] = None
    
    # 质量因子
    roe_min: Optional[float] = None
    roa_min: Optional[float] = None
    
    # 成长因子
    revenue_growth_min: Optional[float] = None
    profit_growth_min: Optional[float] = None
    
    # 动量因子
    momentum_1m_min: Optional[float] = None
    momentum_3m_min: Optional[float] = None
    
    # 风险控制因子
    volatility_max: Optional[float] = None
    beta_max: Optional[float] = None
    max_drawdown_max: Optional[float] = None
    
    # 流动性因子
    avg_amount_min: Optional[float] = None
    volume_ratio_min: Optional[float] = None
    turnover_rate_min: Optional[float] = None
```

## 📊 测试验证结果

### 量化因子计算测试
- ✅ **成功计算72个因子** - 涵盖所有主要量化因子类别
- ✅ **价值因子** - EP=0.0645, BP=0.5556, 股息率=2.5%, 综合得分=5.67
- ✅ **质量因子** - ROE=18.5%, ROA=8.2%, 毛利率=35.6%, 综合得分=8.19
- ✅ **成长因子** - 营收增长=22.5%, 利润增长=28.3%, 综合得分=9.27
- ✅ **动量因子** - 1月动量=-3.04%, 3月动量=-5.86%, 综合得分=5.0
- ✅ **风险因子** - 波动率=21.38%, Beta=1.36, 最大回撤=7.49%, 综合得分=30.0
- ✅ **技术因子** - RSI=52.6, MACD趋势=bearish, 综合得分=18.0
- ✅ **流动性因子** - 日均成交额=5029万, 量比=1.48, 综合得分=28.0

### 选股策略配置测试
- ✅ **价值投资策略** - PE<20, PB<2.5, 股息率>2%, ROE>15%
- ✅ **成长投资策略** - 营收增长>20%, 利润增长>25%, EPS增长>20%
- ✅ **动量投资策略** - 1月动量>5%, 3月动量>15%, 量比>1.5
- ✅ **ETF选择策略** - 仅ETF, 量比>0.8, 日均成交额>1000万

## 🎯 实际应用场景

### 1. **价值投资策略**
```python
criteria = SelectionCriteria(
    exclude_etf=True,
    pe_max=20,               # 低估值
    pb_max=2.5,
    dividend_yield_min=0.03, # 高分红
    roe_min=15,              # 优秀盈利
    condition_logic="flexible"
)
```

### 2. **成长投资策略**
```python
criteria = SelectionCriteria(
    exclude_etf=True,
    revenue_growth_min=20,   # 高成长
    profit_growth_min=25,
    eps_growth_min=20,
    roe_min=18,
    condition_logic="flexible"
)
```

### 3. **动量投资策略**
```python
criteria = SelectionCriteria(
    exclude_etf=True,
    momentum_1m_min=0.05,    # 强势动量
    momentum_3m_min=0.15,
    volume_ratio_min=1.5,    # 成交活跃
    volatility_max=0.4,      # 控制风险
    condition_logic="flexible"
)
```

### 4. **ETF投资策略**
```python
criteria = SelectionCriteria(
    only_etf=True,
    etf_types=['stock'],     # 股票型ETF
    volume_ratio_min=0.8,
    avg_amount_min=1000,     # 流动性好
    condition_logic="flexible"
)
```

## 🎉 总结

### 完全实现的功能
1. ✅ **价值因子** - EP、BP、SP、股息率、EV/EBITDA等完整价值因子体系
2. ✅ **质量因子** - ROE、ROA、毛利率、资产周转率等质量指标
3. ✅ **成长因子** - 营收增长、利润增长、EPS增长等成长指标
4. ✅ **动量因子** - 1M/3M/12M动量、动量加速度、成交量动量
5. ✅ **风险控制因子** - 波动率、Beta、最大回撤、VaR、流动性指标
6. ✅ **技术面因子** - 均线系统、RSI、MACD、布林带等技术指标
7. ✅ **ETF筛选功能** - 完整的ETF识别、分类和筛选功能

### 核心优势
- **专业性** - 涵盖主流量化因子，符合学术和实践标准
- **完整性** - 72个量化因子，覆盖价值、质量、成长、动量、风险、技术、流动性
- **灵活性** - 支持多种条件组合逻辑和筛选策略
- **实用性** - 20+预设策略模板，适合不同投资风格
- **扩展性** - 模块化设计，易于添加新因子和策略

现在QMT-TRADER的选股系统已经具备了专业量化投资平台的完整功能！🚀
