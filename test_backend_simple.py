#!/usr/bin/env python3
"""
简化版后端服务器，用于测试异步选股功能
"""

from fastapi import FastAPI, HTTPException, BackgroundTasks
from pydantic import BaseModel
from typing import List, Dict, Any, Optional
from datetime import datetime
import json
import uuid
import asyncio
import time
import random

app = FastAPI(title="QMT-TRADER Test API", version="1.0.0")

# 任务状态管理
_selection_tasks = {}

class SelectionCriteriaRequest(BaseModel):
    """选股条件请求"""
    volume_min: Optional[float] = None
    condition_logic: str = "flexible"

class SelectionRequest(BaseModel):
    """选股请求"""
    criteria: SelectionCriteriaRequest
    custom_name: str = "default"
    max_results: int = 100

class SelectionTaskStatus:
    """选股任务状态"""
    def __init__(self, task_id: str, custom_name: str):
        self.task_id = task_id
        self.custom_name = custom_name
        self.status = "pending"  # pending, running, completed, failed
        self.progress = 0  # 0-100
        self.current_stock = ""
        self.processed_count = 0
        self.total_count = 0
        self.selected_count = 0
        self.message = ""
        self.result = None
        self.error = None
        self.start_time = datetime.now()
        self.end_time = None

def update_task_progress(task_id: str, **kwargs):
    """更新任务进度"""
    if task_id in _selection_tasks:
        task = _selection_tasks[task_id]
        for key, value in kwargs.items():
            if hasattr(task, key):
                setattr(task, key, value)

async def run_mock_selection_async(task_id: str, criteria: SelectionCriteriaRequest, custom_name: str):
    """模拟异步选股任务"""
    try:
        # 更新任务状态为运行中
        update_task_progress(task_id, status="running", message="开始选股...")
        
        # 模拟股票列表
        mock_stocks = [
            f"00000{i:01d}" for i in range(1, 101)  # 100只模拟股票
        ]
        
        total_count = len(mock_stocks)
        selected_stocks = []
        
        update_task_progress(task_id, total_count=total_count)
        
        # 模拟处理每只股票
        for i, stock_code in enumerate(mock_stocks):
            processed_count = i + 1
            
            # 模拟处理时间
            await asyncio.sleep(0.1)  # 每只股票处理0.1秒
            
            # 随机选中一些股票
            if random.random() < 0.1:  # 10%的概率选中
                selected_stocks.append({
                    'stock_code': stock_code,
                    'stock_name': f'股票{stock_code}',
                    'score': round(random.uniform(60, 95), 2),
                    'indicators': {'volume_ratio': round(random.uniform(0.5, 3.0), 2)},
                    'alpha_factors': {'alpha001': round(random.uniform(-1, 1), 4)},
                    'selection_date': datetime.now().strftime('%Y-%m-%d')
                })
            
            # 更新进度
            progress = int((processed_count / total_count) * 100)
            update_task_progress(
                task_id,
                progress=progress,
                processed_count=processed_count,
                current_stock=stock_code,
                selected_count=len(selected_stocks),
                message=f"正在处理: {stock_code} ({processed_count}/{total_count})"
            )
        
        # 任务完成
        update_task_progress(
            task_id,
            status="completed",
            progress=100,
            result=selected_stocks,
            message=f"选股完成，共选中 {len(selected_stocks)} 只股票",
            end_time=datetime.now()
        )
        
    except Exception as e:
        # 任务失败
        update_task_progress(
            task_id,
            status="failed",
            error=str(e),
            message=f"选股失败: {str(e)}",
            end_time=datetime.now()
        )

@app.post("/api/stock-selection/select")
async def select_stocks(request: SelectionRequest, background_tasks: BackgroundTasks):
    """启动异步选股任务"""
    try:
        # 生成任务ID
        task_id = str(uuid.uuid4())
        
        # 创建任务状态
        task_status = SelectionTaskStatus(task_id, request.custom_name)
        _selection_tasks[task_id] = task_status
        
        # 启动后台任务
        background_tasks.add_task(run_mock_selection_async, task_id, request.criteria, request.custom_name)
        
        # 立即返回任务ID
        return {
            'success': True,
            'data': {
                'task_id': task_id,
                'message': '选股任务已启动，请使用task_id查询进度',
                'custom_name': request.custom_name
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"启动选股任务失败: {str(e)}")

@app.get("/api/stock-selection/task/{task_id}")
async def get_task_status(task_id: str):
    """查询选股任务状态"""
    if task_id not in _selection_tasks:
        raise HTTPException(status_code=404, detail="任务不存在")
    
    task = _selection_tasks[task_id]
    
    response_data = {
        'task_id': task.task_id,
        'custom_name': task.custom_name,
        'status': task.status,
        'progress': task.progress,
        'message': task.message,
        'processed_count': task.processed_count,
        'total_count': task.total_count,
        'selected_count': task.selected_count,
        'current_stock': task.current_stock,
        'start_time': task.start_time.isoformat() if task.start_time else None,
        'end_time': task.end_time.isoformat() if task.end_time else None
    }
    
    # 如果任务完成，包含结果
    if task.status == "completed" and task.result:
        response_data['result'] = {
            'total_selected': len(task.result),
            'selected_stocks': task.result,
            'filename': f"{task.custom_name}_{task.start_time.strftime('%Y%m%d_%H%M%S')}.json"
        }
    
    # 如果任务失败，包含错误信息
    if task.status == "failed" and task.error:
        response_data['error'] = task.error
    
    return {
        'success': True,
        'data': response_data
    }

@app.get("/api/stock-selection/presets")
async def get_selection_presets():
    """获取预设选股条件"""
    presets = {
        'test_preset': {
            'name': '测试预设',
            'description': '用于测试的预设条件',
            'criteria': {
                'volume_min': 1.0,
                'condition_logic': 'flexible'
            }
        }
    }
    
    return {
        'success': True,
        'data': {
            'total_presets': len(presets),
            'presets': presets
        }
    }

@app.get("/")
async def root():
    """根路径"""
    return {"message": "QMT-TRADER Test API", "version": "1.0.0"}

if __name__ == "__main__":
    import uvicorn
    print("🚀 启动测试后端服务器...")
    print("   地址: http://localhost:8000")
    print("   文档: http://localhost:8000/docs")
    uvicorn.run(app, host="0.0.0.0", port=8001)
