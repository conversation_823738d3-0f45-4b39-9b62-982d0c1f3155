#!/usr/bin/env python3
"""
测试QMT数据获取功能
"""

import sys
import os
import logging
from datetime import datetime, timedelta

# 添加项目路径
sys.path.append(os.path.dirname(__file__))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

def test_qmt_connection():
    """测试QMT连接"""
    logger.info("=== 测试QMT连接 ===")
    
    try:
        from backend.data.data_manager import DataManager
        data_manager = DataManager()
        logger.info("✅ DataManager初始化成功")
        return data_manager
    except Exception as e:
        logger.error(f"❌ DataManager初始化失败: {e}")
        return None

def test_stock_list(data_manager):
    """测试股票列表获取"""
    logger.info("\n=== 测试股票列表获取 ===")
    
    try:
        # 获取前10只股票
        result = data_manager.get_stock_list(page=1, page_size=10)
        stocks = result['data']['data']
        
        logger.info(f"✅ 成功获取股票列表: {len(stocks)}只股票")
        logger.info(f"总股票数: {result['data']['total']}")
        
        # 显示前5只股票
        for i, stock in enumerate(stocks[:5]):
            logger.info(f"  {i+1}. {stock['code']} - {stock['name']}")
        
        return stocks
    except Exception as e:
        logger.error(f"❌ 获取股票列表失败: {e}")
        return []

def test_stock_data(data_manager, stocks):
    """测试股票历史数据获取"""
    logger.info("\n=== 测试股票历史数据获取 ===")
    
    if not stocks:
        logger.error("❌ 没有可用的股票列表")
        return
    
    # 设置测试日期范围（最近3个月）
    end_date = datetime.now().strftime('%Y-%m-%d')
    start_date = (datetime.now() - timedelta(days=90)).strftime('%Y-%m-%d')
    
    logger.info(f"测试日期范围: {start_date} 到 {end_date}")
    
    # 测试前3只股票
    test_stocks = stocks[:3]
    successful_count = 0
    
    for stock in test_stocks:
        stock_code = stock['code']
        stock_name = stock['name']
        
        logger.info(f"\n--- 测试 {stock_name}({stock_code}) ---")
        
        try:
            df = data_manager.get_stock_data(
                stock_code=stock_code,
                start_date=start_date,
                end_date=end_date
            )
            
            if len(df) > 0:
                logger.info(f"✅ 成功获取{stock_code}数据: {len(df)}条记录")
                logger.info(f"   数据范围: {df.index[0]} 到 {df.index[-1]}")
                logger.info(f"   列名: {list(df.columns)}")
                logger.info(f"   最新收盘价: {df['close'].iloc[-1]:.2f}")
                successful_count += 1
                
                # 显示前3条数据
                logger.info("   前3条数据:")
                for i in range(min(3, len(df))):
                    row = df.iloc[i]
                    logger.info(f"     {df.index[i]}: O={row['open']:.2f}, H={row['high']:.2f}, L={row['low']:.2f}, C={row['close']:.2f}, V={row['volume']}")
            else:
                logger.warning(f"⚠️ {stock_code}数据为空")
                
        except Exception as e:
            logger.error(f"❌ 获取{stock_code}数据失败: {e}")
    
    logger.info(f"\n📊 测试结果: {successful_count}/{len(test_stocks)} 只股票数据获取成功")
    return successful_count > 0

def test_backtest_data_format(data_manager, stocks):
    """测试回测数据格式"""
    logger.info("\n=== 测试回测数据格式 ===")
    
    if not stocks:
        logger.error("❌ 没有可用的股票列表")
        return False
    
    # 使用第一只股票测试
    stock = stocks[0]
    stock_code = stock['code']
    
    # 设置较短的测试期间
    end_date = datetime.now().strftime('%Y-%m-%d')
    start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
    
    try:
        df = data_manager.get_stock_data(
            stock_code=stock_code,
            start_date=start_date,
            end_date=end_date
        )
        
        logger.info(f"测试股票: {stock['name']}({stock_code})")
        logger.info(f"数据形状: {df.shape}")
        logger.info(f"数据类型: {df.dtypes.to_dict()}")
        
        # 检查必要的列
        required_columns = ['open', 'high', 'low', 'close', 'volume']
        missing_columns = [col for col in required_columns if col not in df.columns]
        
        if missing_columns:
            logger.error(f"❌ 缺少必要列: {missing_columns}")
            return False
        else:
            logger.info("✅ 数据格式符合回测要求")
            
        # 检查数据质量
        null_counts = df.isnull().sum()
        if null_counts.sum() > 0:
            logger.warning(f"⚠️ 发现空值: {null_counts.to_dict()}")
        else:
            logger.info("✅ 数据质量良好，无空值")
            
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试回测数据格式失败: {e}")
        return False

def main():
    """主测试函数"""
    logger.info("🔍 开始QMT数据获取测试")
    
    # 测试1: QMT连接
    data_manager = test_qmt_connection()
    if not data_manager:
        logger.error("❌ QMT连接失败，无法继续测试")
        return
    
    # 测试2: 股票列表
    stocks = test_stock_list(data_manager)
    if not stocks:
        logger.error("❌ 股票列表获取失败，无法继续测试")
        return
    
    # 测试3: 股票数据
    data_success = test_stock_data(data_manager, stocks)
    if not data_success:
        logger.error("❌ 股票数据获取失败")
        return
    
    # 测试4: 回测数据格式
    format_success = test_backtest_data_format(data_manager, stocks)
    if not format_success:
        logger.error("❌ 回测数据格式不符合要求")
        return
    
    logger.info("\n🎉 所有QMT数据测试通过！")
    logger.info("✅ QMT连接正常")
    logger.info("✅ 股票列表获取正常")
    logger.info("✅ 历史数据获取正常")
    logger.info("✅ 数据格式符合回测要求")
    logger.info("\n现在可以使用真实QMT数据进行回测了！")

if __name__ == "__main__":
    main()
