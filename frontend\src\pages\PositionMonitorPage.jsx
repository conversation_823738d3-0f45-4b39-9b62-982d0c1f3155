import React, { useState, useEffect } from 'react';
import { Card as ShadCard, Card<PERSON>eader as <PERSON><PERSON><PERSON>ard<PERSON>eader, Card<PERSON>itle as <PERSON>had<PERSON><PERSON><PERSON>itle, CardContent as ShadCardContent } from '../components/UI/card.jsx';
import { But<PERSON> as ShadButton } from '../components/UI/button.jsx';
import { Alert as ShadAlert } from '../components/UI/alert.jsx';
import EnhancedPositionMonitor from '../components/EnhancedPositionMonitor.jsx';
import { 
  Shield, 
  Settings, 
  Info,
  AlertTriangle,
  CheckCircle
} from 'lucide-react';
import { toast } from 'sonner';

const PositionMonitorPage = () => {
  const [showSettings, setShowSettings] = useState(false);
  const [monitorConfig, setMonitorConfig] = useState({
    loss_stop_enabled: true,
    loss_stop_percent: 5.0,
    trailing_stop_enabled: true,
    trailing_stop_percent: 5.0,
    atr_stop_enabled: false,
    atr_multiplier: 1.5,
    portfolio_stop_enabled: true,
    portfolio_loss_percent: 3.0,
    auto_sell: false,
    monitor_interval: 60
  });

  // 加载配置
  const loadConfig = async () => {
    try {
      const response = await fetch('/api/position-monitor/config');
      const data = await response.json();
      
      if (data.success) {
        setMonitorConfig(data.config);
      }
    } catch (error) {
      console.error('加载配置失败:', error);
    }
  };

  // 保存配置
  const saveConfig = async () => {
    try {
      const response = await fetch('/api/position-monitor/config', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(monitorConfig)
      });
      
      const data = await response.json();
      
      if (data.success) {
        toast.success('配置保存成功');
        setShowSettings(false);
      } else {
        toast.error(data.message || '保存配置失败');
      }
    } catch (error) {
      console.error('保存配置失败:', error);
      toast.error('保存配置失败: ' + error.message);
    }
  };

  // 重置配置
  const resetConfig = () => {
    setMonitorConfig({
      loss_stop_enabled: true,
      loss_stop_percent: 5.0,
      trailing_stop_enabled: true,
      trailing_stop_percent: 5.0,
      atr_stop_enabled: false,
      atr_multiplier: 1.5,
      portfolio_stop_enabled: true,
      portfolio_loss_percent: 3.0,
      auto_sell: false,
      monitor_interval: 60
    });
  };

  useEffect(() => {
    loadConfig();
  }, []);

  return (
    <div className="space-y-6 p-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-2">
            <Shield className="w-8 h-8 text-blue-600" />
            持仓监控
          </h1>
          <p className="text-gray-600 mt-2">
            实时监控持仓风险，自动触发止损警告和订单处理
          </p>
        </div>
        
        <div className="flex gap-2">
          <ShadButton
            variant="outline"
            onClick={() => setShowSettings(!showSettings)}
          >
            <Settings className="w-4 h-4 mr-2" />
            监控设置
          </ShadButton>
        </div>
      </div>

      {/* 功能说明 */}
      <ShadAlert
        title="功能说明"
        description="持仓监控系统会实时检查您的持仓情况，当触发止损条件时会发出声音警告并可选择自动执行卖出操作。请谨慎使用自动卖出功能。"
        variant="info"
        icon={<Info className="w-4 h-4" />}
      />

      {/* 监控设置面板 */}
      {showSettings && (
        <ShadCard>
          <ShadCardHeader>
            <ShadCardTitle className="flex items-center gap-2">
              <Settings className="w-5 h-5" />
              监控配置
            </ShadCardTitle>
          </ShadCardHeader>
          <ShadCardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* 亏损止损 */}
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    id="loss_stop_enabled"
                    checked={monitorConfig.loss_stop_enabled}
                    onChange={(e) => setMonitorConfig(prev => ({
                      ...prev,
                      loss_stop_enabled: e.target.checked
                    }))}
                    className="rounded"
                  />
                  <label htmlFor="loss_stop_enabled" className="font-medium">
                    亏损止损
                  </label>
                </div>
                <div className="ml-6">
                  <label className="block text-sm text-gray-600 mb-1">
                    止损比例 (%)
                  </label>
                  <input
                    type="number"
                    value={monitorConfig.loss_stop_percent}
                    onChange={(e) => setMonitorConfig(prev => ({
                      ...prev,
                      loss_stop_percent: parseFloat(e.target.value) || 0
                    }))}
                    disabled={!monitorConfig.loss_stop_enabled}
                    className="w-full px-3 py-2 border rounded-md disabled:bg-gray-100"
                    min="0"
                    max="50"
                    step="0.1"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    当亏损超过此比例时触发止损
                  </p>
                </div>
              </div>

              {/* 移动止损 */}
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    id="trailing_stop_enabled"
                    checked={monitorConfig.trailing_stop_enabled}
                    onChange={(e) => setMonitorConfig(prev => ({
                      ...prev,
                      trailing_stop_enabled: e.target.checked
                    }))}
                    className="rounded"
                  />
                  <label htmlFor="trailing_stop_enabled" className="font-medium">
                    移动止损
                  </label>
                </div>
                <div className="ml-6">
                  <label className="block text-sm text-gray-600 mb-1">
                    回撤比例 (%)
                  </label>
                  <input
                    type="number"
                    value={monitorConfig.trailing_stop_percent}
                    onChange={(e) => setMonitorConfig(prev => ({
                      ...prev,
                      trailing_stop_percent: parseFloat(e.target.value) || 0
                    }))}
                    disabled={!monitorConfig.trailing_stop_enabled}
                    className="w-full px-3 py-2 border rounded-md disabled:bg-gray-100"
                    min="0"
                    max="50"
                    step="0.1"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    从最高点回撤超过此比例时触发止损
                  </p>
                </div>
              </div>

              {/* ATR止损 */}
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    id="atr_stop_enabled"
                    checked={monitorConfig.atr_stop_enabled}
                    onChange={(e) => setMonitorConfig(prev => ({
                      ...prev,
                      atr_stop_enabled: e.target.checked
                    }))}
                    className="rounded"
                  />
                  <label htmlFor="atr_stop_enabled" className="font-medium">
                    ATR止损
                  </label>
                </div>
                <div className="ml-6">
                  <label className="block text-sm text-gray-600 mb-1">
                    ATR倍数
                  </label>
                  <input
                    type="number"
                    value={monitorConfig.atr_multiplier}
                    onChange={(e) => setMonitorConfig(prev => ({
                      ...prev,
                      atr_multiplier: parseFloat(e.target.value) || 0
                    }))}
                    disabled={!monitorConfig.atr_stop_enabled}
                    className="w-full px-3 py-2 border rounded-md disabled:bg-gray-100"
                    min="0.1"
                    max="10"
                    step="0.1"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    基于平均真实波幅的动态止损
                  </p>
                </div>
              </div>

              {/* 组合止损 */}
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    id="portfolio_stop_enabled"
                    checked={monitorConfig.portfolio_stop_enabled}
                    onChange={(e) => setMonitorConfig(prev => ({
                      ...prev,
                      portfolio_stop_enabled: e.target.checked
                    }))}
                    className="rounded"
                  />
                  <label htmlFor="portfolio_stop_enabled" className="font-medium">
                    组合止损
                  </label>
                </div>
                <div className="ml-6">
                  <label className="block text-sm text-gray-600 mb-1">
                    资产比例 (%)
                  </label>
                  <input
                    type="number"
                    value={monitorConfig.portfolio_loss_percent}
                    onChange={(e) => setMonitorConfig(prev => ({
                      ...prev,
                      portfolio_loss_percent: parseFloat(e.target.value) || 0
                    }))}
                    disabled={!monitorConfig.portfolio_stop_enabled}
                    className="w-full px-3 py-2 border rounded-md disabled:bg-gray-100"
                    min="0"
                    max="20"
                    step="0.1"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    单只股票亏损占总资产比例超过此值时触发
                  </p>
                </div>
              </div>

              {/* 监控间隔 */}
              <div className="space-y-3">
                <label className="font-medium">监控间隔</label>
                <div>
                  <label className="block text-sm text-gray-600 mb-1">
                    检查间隔 (秒)
                  </label>
                  <input
                    type="number"
                    value={monitorConfig.monitor_interval}
                    onChange={(e) => setMonitorConfig(prev => ({
                      ...prev,
                      monitor_interval: parseInt(e.target.value) || 60
                    }))}
                    className="w-full px-3 py-2 border rounded-md"
                    min="10"
                    max="300"
                    step="10"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    建议设置为60秒，过于频繁可能影响性能
                  </p>
                </div>
              </div>

              {/* 自动卖出 */}
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    id="auto_sell"
                    checked={monitorConfig.auto_sell}
                    onChange={(e) => setMonitorConfig(prev => ({
                      ...prev,
                      auto_sell: e.target.checked
                    }))}
                    className="rounded"
                  />
                  <label htmlFor="auto_sell" className="font-medium text-red-600">
                    自动卖出 (谨慎使用)
                  </label>
                </div>
                <div className="ml-6">
                  <ShadAlert
                    title="风险提示"
                    description="启用自动卖出后，系统会在触发止损条件时自动提交卖出订单。请确保您完全理解此功能的风险。"
                    variant="warning"
                    icon={<AlertTriangle className="w-4 h-4" />}
                  />
                </div>
              </div>
            </div>

            {/* 操作按钮 */}
            <div className="flex justify-end gap-2 mt-6 pt-6 border-t">
              <ShadButton
                variant="outline"
                onClick={resetConfig}
              >
                重置默认
              </ShadButton>
              <ShadButton
                variant="outline"
                onClick={() => setShowSettings(false)}
              >
                取消
              </ShadButton>
              <ShadButton
                onClick={saveConfig}
              >
                <CheckCircle className="w-4 h-4 mr-2" />
                保存配置
              </ShadButton>
            </div>
          </ShadCardContent>
        </ShadCard>
      )}

      {/* 主监控面板 */}
      <EnhancedPositionMonitor />
    </div>
  );
};

export default PositionMonitorPage;
