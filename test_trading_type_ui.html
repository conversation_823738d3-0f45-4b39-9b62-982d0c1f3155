<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试交易类型显示</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 1000px; margin: 0 auto; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        button { padding: 10px 15px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .result { margin-top: 10px; padding: 10px; background: #f8f9fa; border-radius: 3px; }
        .error { background: #f8d7da; color: #721c24; }
        .success { background: #d4edda; color: #155724; }
        table { width: 100%; border-collapse: collapse; margin-top: 10px; }
        th, td { padding: 8px 12px; border: 1px solid #ddd; text-align: left; }
        th { background: #f8f9fa; font-weight: bold; }
        .tag { padding: 2px 8px; border-radius: 3px; font-size: 12px; font-weight: bold; }
        .tag-blue { background: #e3f2fd; color: #1976d2; }
        .tag-red { background: #ffebee; color: #d32f2f; }
        .tag-green { background: #e8f5e8; color: #2e7d32; }
    </style>
</head>
<body>
    <div class="container">
        <h1>测试交易类型显示</h1>
        
        <div class="section">
            <h3>1. 启动测试策略</h3>
            <button onclick="startPaperStrategy()">启动纸面交易策略</button>
            <button onclick="startRealStrategy()">启动实盘交易策略</button>
            <button onclick="startDefaultStrategy()">启动默认策略</button>
            <div id="startResult" class="result"></div>
        </div>
        
        <div class="section">
            <h3>2. 策略列表（包含交易类型）</h3>
            <button onclick="loadStrategies()">刷新策略列表</button>
            <div id="strategiesResult" class="result">
                <table id="strategiesTable" style="display: none;">
                    <thead>
                        <tr>
                            <th>策略名称</th>
                            <th>策略类型</th>
                            <th>交易类型</th>
                            <th>状态</th>
                            <th>初始资金</th>
                            <th>持仓数</th>
                            <th>盈亏</th>
                            <th>启动时间</th>
                        </tr>
                    </thead>
                    <tbody id="strategiesTableBody">
                    </tbody>
                </table>
            </div>
        </div>
        
        <div class="section">
            <h3>3. 清理测试策略</h3>
            <button onclick="cleanupStrategies()">清理所有测试策略</button>
            <div id="cleanupResult" class="result"></div>
        </div>
    </div>

    <script>
        let testStrategyIds = [];
        
        async function startPaperStrategy() {
            const resultDiv = document.getElementById('startResult');
            resultDiv.textContent = '启动纸面交易策略...';
            
            try {
                const strategyData = {
                    name: 'UI测试纸面交易',
                    strategy_type: 'bollinger_bands',
                    config: {
                        initial_capital: 50000,
                        paper_trading: true
                    }
                };
                
                const response = await fetch('/api/live/strategies', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(strategyData),
                });
                
                const result = await response.json();
                
                if (response.ok && result.success) {
                    testStrategyIds.push(result.strategy_id);
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ 纸面交易策略启动成功！ID: ${result.strategy_id}`;
                    loadStrategies(); // 自动刷新列表
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ 启动失败：${result.message || '未知错误'}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 请求失败：${error.message}`;
            }
        }
        
        async function startRealStrategy() {
            const resultDiv = document.getElementById('startResult');
            resultDiv.textContent = '启动实盘交易策略...';
            
            try {
                const strategyData = {
                    name: 'UI测试实盘交易',
                    strategy_type: 'bollinger_bands',
                    config: {
                        initial_capital: 30000,
                        paper_trading: false
                    }
                };
                
                const response = await fetch('/api/live/strategies', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(strategyData),
                });
                
                const result = await response.json();
                
                if (response.ok && result.success) {
                    testStrategyIds.push(result.strategy_id);
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ 实盘交易策略启动成功！ID: ${result.strategy_id}`;
                    loadStrategies(); // 自动刷新列表
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ 启动失败：${result.message || '未知错误'}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 请求失败：${error.message}`;
            }
        }
        
        async function startDefaultStrategy() {
            const resultDiv = document.getElementById('startResult');
            resultDiv.textContent = '启动默认策略...';
            
            try {
                const strategyData = {
                    name: 'UI测试默认策略',
                    strategy_type: 'bollinger_bands',
                    config: {
                        initial_capital: 40000
                        // 不指定 paper_trading，应该默认为 true
                    }
                };
                
                const response = await fetch('/api/live/strategies', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(strategyData),
                });
                
                const result = await response.json();
                
                if (response.ok && result.success) {
                    testStrategyIds.push(result.strategy_id);
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ 默认策略启动成功！ID: ${result.strategy_id}`;
                    loadStrategies(); // 自动刷新列表
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ 启动失败：${result.message || '未知错误'}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 请求失败：${error.message}`;
            }
        }
        
        async function loadStrategies() {
            const resultDiv = document.getElementById('strategiesResult');
            const table = document.getElementById('strategiesTable');
            const tbody = document.getElementById('strategiesTableBody');
            
            resultDiv.textContent = '加载策略列表...';
            
            try {
                const response = await fetch('/api/live/strategies');
                const strategies = await response.json();
                
                if (response.ok) {
                    tbody.innerHTML = '';
                    
                    strategies.forEach(strategy => {
                        const row = tbody.insertRow();
                        
                        // 策略名称
                        row.insertCell(0).textContent = strategy.name;
                        
                        // 策略类型
                        row.insertCell(1).textContent = strategy.strategy_type;
                        
                        // 交易类型
                        const tradingTypeCell = row.insertCell(2);
                        const isPaperTrading = strategy.config?.paper_trading !== false;
                        const tradingTypeTag = document.createElement('span');
                        tradingTypeTag.className = `tag ${isPaperTrading ? 'tag-blue' : 'tag-red'}`;
                        tradingTypeTag.textContent = isPaperTrading ? '纸面交易' : '实盘交易';
                        tradingTypeCell.appendChild(tradingTypeTag);
                        
                        // 状态
                        const statusCell = row.insertCell(3);
                        const statusTag = document.createElement('span');
                        statusTag.className = `tag ${strategy.status === 'running' ? 'tag-green' : 'tag-red'}`;
                        statusTag.textContent = strategy.status === 'running' ? '运行中' : strategy.status;
                        statusCell.appendChild(statusTag);
                        
                        // 初始资金
                        const capital = strategy.config?.initial_capital || 0;
                        row.insertCell(4).textContent = `¥${capital.toLocaleString()}`;
                        
                        // 持仓数
                        row.insertCell(5).textContent = strategy.positions || 0;
                        
                        // 盈亏
                        const pnlCell = row.insertCell(6);
                        const pnl = strategy.pnl || 0;
                        pnlCell.textContent = `¥${pnl.toLocaleString()}`;
                        pnlCell.style.color = pnl >= 0 ? '#2e7d32' : '#d32f2f';
                        pnlCell.style.fontWeight = 'bold';
                        
                        // 启动时间
                        const startTime = strategy.start_time ? new Date(strategy.start_time).toLocaleString() : '-';
                        row.insertCell(7).textContent = startTime;
                    });
                    
                    table.style.display = 'table';
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ 加载成功！共 ${strategies.length} 个策略`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ 加载失败：${strategies.detail || '未知错误'}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 请求失败：${error.message}`;
            }
        }
        
        async function cleanupStrategies() {
            const resultDiv = document.getElementById('cleanupResult');
            resultDiv.textContent = '清理测试策略...';
            
            let cleanedCount = 0;
            
            for (const strategyId of testStrategyIds) {
                try {
                    const response = await fetch(`/api/live/strategies/${strategyId}`, {
                        method: 'DELETE',
                    });
                    
                    if (response.ok) {
                        cleanedCount++;
                    }
                } catch (error) {
                    console.error(`清理策略失败: ${strategyId}`, error);
                }
            }
            
            testStrategyIds = [];
            
            resultDiv.className = 'result success';
            resultDiv.textContent = `✅ 清理完成！已停止 ${cleanedCount} 个测试策略`;
            
            // 刷新策略列表
            loadStrategies();
        }
        
        // 页面加载时自动加载策略列表
        window.onload = function() {
            loadStrategies();
        };
    </script>
</body>
</html>
