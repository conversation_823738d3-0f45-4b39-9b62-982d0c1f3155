#!/usr/bin/env python3
"""
统一的日志系统
支持按模块分类记录到不同文件
"""

import logging
import os
import sys
from datetime import datetime
from logging.handlers import RotatingFileHandler
from typing import Optional, Dict
import threading

class LoggerManager:
    """日志管理器"""
    
    _instance = None
    _lock = threading.Lock()
    _loggers: Dict[str, logging.Logger] = {}
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
                    cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        if self._initialized:
            return
        
        self.log_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'logs')
        self.ensure_log_directory()
        self._initialized = True
    
    def ensure_log_directory(self):
        """确保日志目录存在"""
        if not os.path.exists(self.log_dir):
            os.makedirs(self.log_dir)
        
        # 创建各模块的子目录
        modules = ['api', 'data', 'backtest', 'strategies', 'trading', 'core', 'services']
        for module in modules:
            module_dir = os.path.join(self.log_dir, module)
            if not os.path.exists(module_dir):
                os.makedirs(module_dir)
    
    def get_logger(self, name: str, module: str = 'core', level: int = None) -> logging.Logger:
        """
        获取指定模块的日志器
        
        Args:
            name: 日志器名称
            module: 模块名称 (api, data, backtest, strategies, trading, core, services)
            level: 日志级别
        
        Returns:
            配置好的日志器
        """
        logger_key = f"{module}.{name}"
        
        if logger_key in self._loggers:
            return self._loggers[logger_key]
        
        # 确定日志级别
        if level is None:
            try:
                from .log_config import get_log_level
                level = get_log_level(logger_key)
            except ImportError:
                level = logging.INFO

        logger = logging.getLogger(logger_key)
        logger.setLevel(level)
        
        # 避免重复添加处理器
        if logger.handlers:
            logger.handlers.clear()
        
        # 创建格式器 - 简化格式，更易读
        formatter = logging.Formatter(
            '%(asctime)s [%(levelname)s] %(name)s: %(message)s',
            datefmt='%H:%M:%S'
        )
        
        # 控制台处理器
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(level)
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
        
        # 文件处理器 - 模块级别
        module_log_file = os.path.join(self.log_dir, module, f"{module}.log")
        file_handler = RotatingFileHandler(
            module_log_file,
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5,
            encoding='utf-8'
        )
        file_handler.setLevel(level)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
        
        # 文件处理器 - 具体日志器级别
        if name != module:
            specific_log_file = os.path.join(self.log_dir, module, f"{name}.log")
            specific_handler = RotatingFileHandler(
                specific_log_file,
                maxBytes=5*1024*1024,  # 5MB
                backupCount=3,
                encoding='utf-8'
            )
            specific_handler.setLevel(level)
            specific_handler.setFormatter(formatter)
            logger.addHandler(specific_handler)
        
        # 错误日志单独记录
        error_log_file = os.path.join(self.log_dir, 'errors.log')
        error_handler = RotatingFileHandler(
            error_log_file,
            maxBytes=10*1024*1024,  # 10MB
            backupCount=10,
            encoding='utf-8'
        )
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(formatter)
        logger.addHandler(error_handler)
        
        # 防止日志传播到根日志器
        logger.propagate = False
        
        self._loggers[logger_key] = logger
        return logger
    
    def get_api_logger(self, name: str = 'api') -> logging.Logger:
        """获取API模块日志器"""
        return self.get_logger(name, 'api')
    
    def get_data_logger(self, name: str = 'data') -> logging.Logger:
        """获取数据模块日志器"""
        return self.get_logger(name, 'data')
    
    def get_backtest_logger(self, name: str = 'backtest') -> logging.Logger:
        """获取回测模块日志器"""
        return self.get_logger(name, 'backtest')
    
    def get_strategy_logger(self, name: str = 'strategy') -> logging.Logger:
        """获取策略模块日志器"""
        return self.get_logger(name, 'strategies')
    
    def get_trading_logger(self, name: str = 'trading') -> logging.Logger:
        """获取交易模块日志器"""
        return self.get_logger(name, 'trading')
    
    def get_service_logger(self, name: str = 'service') -> logging.Logger:
        """获取服务模块日志器"""
        return self.get_logger(name, 'services')
    
    def get_core_logger(self, name: str = 'core') -> logging.Logger:
        """获取核心模块日志器"""
        return self.get_logger(name, 'core')
    
    def set_level(self, level: int):
        """设置所有日志器的级别"""
        for logger in self._loggers.values():
            logger.setLevel(level)
            for handler in logger.handlers:
                handler.setLevel(level)
    
    def cleanup_old_logs(self, days: int = 30):
        """清理旧日志文件"""
        import glob
        import time
        
        cutoff_time = time.time() - (days * 24 * 60 * 60)
        
        for root, dirs, files in os.walk(self.log_dir):
            for file in files:
                if file.endswith('.log'):
                    file_path = os.path.join(root, file)
                    if os.path.getmtime(file_path) < cutoff_time:
                        try:
                            os.remove(file_path)
                            print(f"删除旧日志文件: {file_path}")
                        except Exception as e:
                            print(f"删除日志文件失败 {file_path}: {e}")

# 全局日志管理器实例
logger_manager = LoggerManager()

# 便捷函数
def get_logger(name: str, module: str = 'core') -> logging.Logger:
    """获取日志器的便捷函数"""
    return logger_manager.get_logger(name, module)

def get_api_logger(name: str = 'api') -> logging.Logger:
    """获取API日志器"""
    return logger_manager.get_api_logger(name)

def get_data_logger(name: str = 'data') -> logging.Logger:
    """获取数据日志器"""
    return logger_manager.get_data_logger(name)

def get_backtest_logger(name: str = 'backtest') -> logging.Logger:
    """获取回测日志器"""
    return logger_manager.get_backtest_logger(name)

def get_strategy_logger(name: str = 'strategy') -> logging.Logger:
    """获取策略日志器"""
    return logger_manager.get_strategy_logger(name)

def get_trading_logger(name: str = 'trading') -> logging.Logger:
    """获取交易日志器"""
    return logger_manager.get_trading_logger(name)

def get_service_logger(name: str = 'service') -> logging.Logger:
    """获取服务日志器"""
    return logger_manager.get_service_logger(name)

def get_core_logger(name: str = 'core') -> logging.Logger:
    """获取核心日志器"""
    return logger_manager.get_core_logger(name)
