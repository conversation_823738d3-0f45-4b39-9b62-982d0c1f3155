#!/usr/bin/env python3
"""
测试使用真实xttrader数据的选股功能
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from backend.stock_selection.stock_selector import StockSelector, SelectionCriteria
from backend.core.logger import get_logger

logger = get_logger(__name__)

def test_real_data_selection():
    """测试真实数据选股"""
    print("🎯 测试使用真实xttrader数据的选股功能")
    print("=" * 60)
    
    try:
        # 创建选股器
        selector = StockSelector()
        print("✅ 选股器初始化成功")
        
        # 测试获取股票列表
        print("\n📋 测试获取股票列表...")
        stocks = selector._get_stocks_from_xttrader()
        print(f"✅ 获取到 {len(stocks)} 只股票")
        
        if len(stocks) > 0:
            print(f"📊 前5只股票:")
            for i, stock in enumerate(stocks[:5]):
                print(f"   {i+1}. {stock['code']} - {stock['name']}")
        
        # 测试获取单只股票数据
        if stocks:
            test_stock = stocks[0]
            stock_code = test_stock['code']
            print(f"\n📈 测试获取股票数据: {stock_code}")
            
            from datetime import datetime, timedelta
            end_date = datetime.now()
            start_date = end_date - timedelta(days=100)
            
            df = selector._get_stock_data_from_xttrader(
                stock_code=stock_code,
                start_date=start_date.strftime('%Y%m%d'),
                end_date=end_date.strftime('%Y%m%d')
            )
            
            if df is not None and not df.empty:
                print(f"✅ 获取到 {len(df)} 条数据记录")
                print(f"📅 数据范围: {df.index[0]} 到 {df.index[-1]}")
                print(f"📊 数据列: {list(df.columns)}")
                print(f"💰 最新价格: {df['close'].iloc[-1]:.2f}")
                print(f"📊 最新成交量: {df['volume'].iloc[-1]:,.0f}")
                
                # 测试技术指标计算
                print(f"\n🔧 测试技术指标计算...")
                indicators = selector.calculate_technical_indicators(df)
                
                if indicators:
                    print(f"✅ 计算出 {len(indicators)} 个技术指标")
                    print(f"📊 关键指标:")
                    print(f"   RSI: {indicators.get('rsi', 'N/A'):.2f}")
                    print(f"   成交量比: {indicators.get('volume_ratio', 'N/A'):.2f}")
                    print(f"   ATR比率: {indicators.get('atr_ratio', 'N/A'):.4f}")
                    print(f"   MA趋势: {indicators.get('ma_trend', 'N/A')}")
                    
                    # 测试选股条件
                    print(f"\n🎯 测试选股条件...")
                    criteria = SelectionCriteria(
                        volume_min=0.5,
                        condition_logic="flexible"
                    )
                    
                    alpha_factors = selector.calculate_alpha101_factors(df)
                    passed, score = selector.check_selection_criteria(indicators, alpha_factors, criteria)
                    
                    print(f"📋 条件: 成交量比 > 0.5")
                    print(f"🎯 结果: 通过={passed}, 评分={score:.2f}")
                    
                    if not passed:
                        print(f"💡 建议: 当前成交量比为 {indicators.get('volume_ratio', 0):.2f}")
                        print(f"   可以尝试更宽松的条件，如 volume_min=0.1")
                
                else:
                    print("❌ 技术指标计算失败")
            else:
                print("❌ 股票数据获取失败")
        
        # 测试完整选股流程
        print(f"\n🚀 测试完整选股流程...")
        criteria = SelectionCriteria(
            volume_min=0.1,  # 使用更宽松的条件
            rsi_min=20,
            rsi_max=80,
            condition_logic="flexible"
        )
        
        print(f"📋 选股条件:")
        print(f"   成交量比 > 0.1")
        print(f"   RSI 在 20-80 之间")
        print(f"   条件逻辑: 灵活模式")
        
        results = selector.select_stocks(criteria, "真实数据测试")
        
        print(f"\n📊 选股结果:")
        print(f"✅ 共选中 {len(results)} 只股票")
        
        if results:
            print(f"\n🏆 前5只股票:")
            for i, stock in enumerate(results[:5]):
                print(f"   {i+1}. {stock.stock_code} - {stock.stock_name}")
                print(f"      评分: {stock.score:.2f}")
                print(f"      RSI: {stock.indicators.get('rsi', 'N/A'):.2f}")
                print(f"      成交量比: {stock.indicators.get('volume_ratio', 'N/A'):.2f}")
                print()
        else:
            print("❌ 没有选中任何股票")
            print("💡 建议:")
            print("   1. 检查选股条件是否过于严格")
            print("   2. 尝试更宽松的条件")
            print("   3. 检查数据质量")
        
    except ImportError as e:
        print(f"❌ xttrader模块未安装: {e}")
        print("💡 请确保已安装xtquant模块并连接到QMT")
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "=" * 60)
    print("✅ 真实数据选股测试完成")

if __name__ == "__main__":
    test_real_data_selection()
