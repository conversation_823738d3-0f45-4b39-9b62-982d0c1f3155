#!/usr/bin/env python3
"""
QMT Store - 核心适配器
连接QMT系统，提供统一的数据和交易接口
"""

import threading
from typing import Dict, Any, Optional
from datetime import datetime

import backtrader as bt
from backend.core.logger import get_logger

logger = get_logger(__name__)

class QMTStore:
    """
    QMT Store - 核心适配器类
    
    这是Store模式的核心，负责：
    1. 连接QMT系统
    2. 提供数据源工厂方法
    3. 提供broker工厂方法
    4. 管理连接状态
    """
    
    # 单例模式，确保全局只有一个Store实例
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls, **kwargs):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self, **kwargs):
        # 避免重复初始化
        if hasattr(self, '_initialized'):
            return
        
        logger.info("🚀 初始化QMT Store")
        
        # 存储初始化参数
        self.params = kwargs
        
        # 连接状态
        self.connected = False
        
        # QMT组件引用
        self.data_manager = None
        self.qmt_trader = None
        
        # 初始化QMT连接
        self._init_qmt_connection()
        
        # 标记已初始化
        self._initialized = True
        
        logger.info("✅ QMT Store初始化完成")
    
    def _init_qmt_connection(self):
        """初始化QMT连接"""
        try:
            # 导入现有的data_manager
            try:
                from backend.data.data_manager import data_manager
                self.data_manager = data_manager
                logger.info("✅ 连接到QMT数据管理器")
            except Exception as e:
                logger.error(f"❌ 导入数据管理器失败: {e}")
                self.data_manager = None

            # 导入现有的qmt_trader
            try:
                from backend.trading.qmt_trader import QMTTrader
                self.qmt_trader = QMTTrader()
                logger.info("✅ 连接到QMT交易器")
            except Exception as e:
                logger.error(f"❌ 导入QMT交易器失败: {e}")
                self.qmt_trader = None

            # 检查连接状态
            if self.qmt_trader and self.qmt_trader.is_connected:
                self.connected = True
                logger.info("✅ QMT系统连接成功")
            else:
                logger.warning("⚠️ QMT交易器未连接，仅支持数据获取")
                self.connected = False

        except Exception as e:
            logger.error(f"❌ QMT连接失败: {e}")
            self.connected = False
    
    def getdata(self, dataname, **kwargs):
        """
        获取数据源实例

        Args:
            dataname: 股票代码，如 '000001.SZ'
            **kwargs: 其他参数

        Returns:
            QMTData实例
        """
        logger.info(f"📊 创建数据源: {dataname}")
        logger.info(f"📋 传入参数: {kwargs}")

        # 检查数据源模式
        historical_mode = kwargs.get('historical', True)
        live_mode = kwargs.get('live', False)

        # 如果是纯实时模式，使用实时数据源
        if live_mode and not historical_mode:
            logger.info(f"🔄 创建实时数据源: {dataname}")
            from .qmt_live_data import create_live_data

            paper_trading = kwargs.pop('paper_trading', True)  # 使用pop避免重复
            return create_live_data(
                stock_code=dataname,
                paper_trading=paper_trading,
                store=self,
                **kwargs
            )

        # 否则使用传统数据源
        from .qmt_data import QMTData

        if historical_mode and isinstance(dataname, str):
            # 预先加载数据
            logger.info(f"📈 预加载历史数据: {dataname}")

            try:
                # 确定日期范围
                from datetime import datetime, timedelta

                if kwargs.get('fromdate'):
                    start_date = kwargs['fromdate'].strftime('%Y-%m-%d')
                else:
                    start_date = (datetime.now() - timedelta(days=365)).strftime('%Y-%m-%d')

                if kwargs.get('todate'):
                    end_date = kwargs['todate'].strftime('%Y-%m-%d')
                else:
                    end_date = datetime.now().strftime('%Y-%m-%d')

                logger.info(f"   日期范围: {start_date} 到 {end_date}")

                # 从QMT获取数据 - 不使用任何虚假数据
                data_manager = self.get_data_manager()
                if data_manager is None:
                    logger.error("❌ 数据管理器未初始化，无法获取真实数据")
                    raise RuntimeError(f"数据管理器未初始化，无法获取{dataname}的数据")

                # 只使用真实的QMT数据
                df = data_manager.get_stock_data(
                    stock_code=dataname,
                    start_date=start_date,
                    end_date=end_date
                )

                if df is not None and len(df) > 0:
                    # 格式化数据
                    df = self._format_dataframe(df)
                    kwargs['dataname'] = df
                    logger.info(f"✅ 成功预加载数据: {len(df)}条记录")
                else:
                    # 创建空DataFrame
                    import pandas as pd
                    kwargs['dataname'] = pd.DataFrame()
                    logger.warning(f"⚠️ 未获取到数据: {dataname}")

            except Exception as e:
                logger.error(f"❌ 预加载数据失败: {e}")
                import pandas as pd
                kwargs['dataname'] = pd.DataFrame()

        # 保存原始股票代码
        kwargs['stock_code'] = dataname

        logger.info(f"📋 最终参数: dataname类型={type(kwargs['dataname'])}")

        # 过滤掉QMTData不支持的参数
        qmt_data_kwargs = kwargs.copy()
        unsupported_params = ['paper_trading', 'live', 'historical']
        for param in unsupported_params:
            qmt_data_kwargs.pop(param, None)

        # 创建数据源实例，传入store引用
        return QMTData(store=self, **qmt_data_kwargs)

    def _format_dataframe(self, df):
        """格式化DataFrame为backtrader标准格式"""
        try:
            import pandas as pd

            # 确保必要的列存在
            required_columns = ['open', 'high', 'low', 'close', 'volume']
            for col in required_columns:
                if col not in df.columns:
                    if col == 'volume':
                        df[col] = 0  # 成交量默认为0
                    else:
                        df[col] = df.get('close', 0)  # 其他价格列默认使用收盘价

            # 确保数据类型正确
            for col in ['open', 'high', 'low', 'close']:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            df['volume'] = pd.to_numeric(df['volume'], errors='coerce').fillna(0)

            # 处理缺失值
            df = df.fillna(method='ffill').fillna(method='bfill')

            # 确保索引是日期类型
            if not isinstance(df.index, pd.DatetimeIndex):
                df.index = pd.to_datetime(df.index)

            # 按日期排序
            df = df.sort_index()

            logger.debug(f"   格式化后数据形状: {df.shape}")
            return df

        except Exception as e:
            logger.error(f"❌ 数据格式化失败: {e}")
            import pandas as pd
            return pd.DataFrame()
    
    def getbroker(self, **kwargs):
        """
        获取broker实例
        
        Returns:
            QMTBroker实例
        """
        logger.info("💼 创建QMT Broker")
        
        if not self.connected:
            logger.warning("⚠️ QMT未连接，broker功能可能受限")
        
        # 导入QMTBroker类
        from .qmt_broker import QMTBroker
        
        # 创建broker实例，传入store引用
        return QMTBroker(store=self, **kwargs)
    
    def is_connected(self):
        """检查连接状态"""
        return self.connected
    
    def get_data_manager(self):
        """获取数据管理器"""
        return self.data_manager
    
    def get_trader(self):
        """获取交易器"""
        return self.qmt_trader
    
    def start(self):
        """启动Store（预留接口）"""
        logger.info("🔄 启动QMT Store")
        pass
    
    def stop(self):
        """停止Store（预留接口）"""
        logger.info("🛑 停止QMT Store")
        pass
    
    def notify(self, msg):
        """通知消息（预留接口）"""
        logger.info(f"📢 QMT Store通知: {msg}")
        pass
