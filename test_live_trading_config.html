<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LiveTradingConfig 测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        pre {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>LiveTradingConfig 组件测试</h1>
    
    <div class="test-section">
        <h2>1. API 响应测试</h2>
        <button onclick="testAPI()">测试策略API</button>
        <div id="api-result"></div>
    </div>
    
    <div class="test-section">
        <h2>2. 参数格式转换测试</h2>
        <button onclick="testParameterConversion()">测试参数转换</button>
        <div id="param-result"></div>
    </div>
    
    <div class="test-section">
        <h2>3. 前端组件状态</h2>
        <p>请在浏览器中访问 <a href="http://localhost:3000/multi-strategy" target="_blank">多策略交易页面</a></p>
        <p>然后点击"添加策略"按钮测试是否还有错误</p>
    </div>

    <script>
        async function testAPI() {
            const resultDiv = document.getElementById('api-result');
            resultDiv.innerHTML = '<p>测试中...</p>';
            
            try {
                const response = await fetch('http://localhost:8000/api/backtest/strategies');
                const data = await response.json();
                
                if (data.success && Array.isArray(data.data)) {
                    resultDiv.innerHTML = `
                        <p class="success">✅ API 响应正常</p>
                        <p>策略数量: ${data.data.length}</p>
                        <pre>${JSON.stringify(data.data[0], null, 2)}</pre>
                    `;
                } else {
                    resultDiv.innerHTML = `<p class="error">❌ API 响应格式异常</p><pre>${JSON.stringify(data, null, 2)}</pre>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<p class="error">❌ API 请求失败: ${error.message}</p>`;
            }
        }
        
        function testParameterConversion() {
            const resultDiv = document.getElementById('param-result');
            
            // 模拟API返回的参数格式
            const apiParams = {
                "period": {
                    "type": "int",
                    "value": 20,
                    "min": 5,
                    "max": 50,
                    "description": "布林线计算周期"
                },
                "std_dev": {
                    "type": "float",
                    "value": 2.0,
                    "min": 0.5,
                    "max": 5.0,
                    "description": "标准差倍数"
                },
                "buy_signal_ma_cross": {
                    "type": "boolean",
                    "value": true,
                    "description": "启用均线金叉买入信号"
                }
            };
            
            // 转换为前端期望的格式
            const convertedParams = Object.entries(apiParams).map(([name, config]) => ({
                name,
                type: config.type || 'text',
                default: config.value,
                description: config.description || '',
                min: config.min,
                max: config.max,
                options: config.options
            }));
            
            resultDiv.innerHTML = `
                <p class="success">✅ 参数转换成功</p>
                <h4>原始格式:</h4>
                <pre>${JSON.stringify(apiParams, null, 2)}</pre>
                <h4>转换后格式:</h4>
                <pre>${JSON.stringify(convertedParams, null, 2)}</pre>
            `;
        }
        
        // 页面加载时自动测试
        window.onload = function() {
            testAPI();
            testParameterConversion();
        };
    </script>
</body>
</html>
