#!/usr/bin/env python3
"""
启动开发服务器脚本
"""

import sys
import os
import uvicorn

# 添加项目根目录到路径
sys.path.append(os.path.dirname(__file__))

def main():
    print("🚀 启动QMT-TRADER开发服务器...")
    print("📋 功能:")
    print("  - 持仓监控")
    print("  - 智能选股")
    print("  - 策略回测")
    print("  - 实盘交易")
    print("  - 数据管理")
    print("📍 访问地址: http://localhost:8001")
    print("🌐 前端地址: http://localhost:3001")
    print("=" * 50)
    
    try:
        from backend.api.dev_main import app
        uvicorn.run(
            app, 
            host="0.0.0.0", 
            port=8001,
            log_level="info",
            reload=False  # 禁用自动重载避免问题
        )
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        print("请检查:")
        print("1. Python环境是否正确")
        print("2. 依赖包是否安装完整")
        print("3. 端口8001是否被占用")

if __name__ == "__main__":
    main()
