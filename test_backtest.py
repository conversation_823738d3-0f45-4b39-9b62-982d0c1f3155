#!/usr/bin/env python3
"""
简单的回测测试脚本
"""

import requests
import time
import json

def test_backtest():
    """测试回测功能"""
    
    # 启动回测
    print("启动回测...")
    response = requests.post('http://localhost:8001/api/backtest/start', json={
        'strategy_name': 'simple_test',
        'strategy_config': {}
    })
    
    if response.status_code != 200:
        print(f"启动失败: {response.status_code} - {response.text}")
        return
    
    result = response.json()
    print(f"启动成功: {result}")
    task_id = result['data']['task_id']
    
    # 等待完成
    print("等待回测完成...")
    for i in range(10):
        time.sleep(1)
        status_response = requests.get('http://localhost:8001/api/backtest/status')
        if status_response.status_code == 200:
            status_data = status_response.json()
            print(f"状态 {i+1}: {status_data['data']}")
            
            if status_data['data']['status'] in ['completed', 'failed']:
                break
    
    # 获取结果
    print("获取回测结果...")
    result_response = requests.get(f'http://localhost:8001/api/backtest/results/{task_id}')
    print(f"结果状态码: {result_response.status_code}")
    
    if result_response.status_code == 200:
        result_data = result_response.json()
        print(f"结果数据: {json.dumps(result_data, indent=2, ensure_ascii=False)}")
    else:
        print(f"获取结果失败: {result_response.text}")

if __name__ == "__main__":
    test_backtest()
