# QMT-TRADER 股票池功能使用说明

## 功能概述

股票池功能允许用户将选股结果或自定义股票列表作为策略回测和运行的股票池，提供更精准和个性化的投资策略测试。

## 主要特性

### 🎯 **股票池来源**
1. **选股结果转换** - 将智能选股的结果转换为股票池
2. **自定义股票池** - 手动创建包含特定股票的股票池
3. **灵活筛选** - 支持按评分、数量等条件筛选股票

### 📊 **回测集成**
1. **无缝集成** - 回测引擎自动支持股票池
2. **智能选择** - 优先使用最新的股票池
3. **多种模式** - 支持默认、自定义股票池、单只股票三种模式

### 🔧 **管理功能**
1. **可视化管理** - 前端页面管理所有股票池
2. **导入导出** - 支持JSON和CSV格式导出
3. **验证功能** - 自动验证股票代码格式

## 使用流程

### 1. 创建股票池

#### 方式一：从选股结果创建
1. 进入"智能选股"页面，执行选股策略
2. 进入"股票池管理"页面
3. 点击"创建股票池" → "从选股结果创建"
4. 选择选股结果文件
5. 设置筛选条件：
   - **最大股票数量**：限制股票池大小
   - **最低评分**：只包含评分高于阈值的股票
6. 填写股票池信息并创建

#### 方式二：创建自定义股票池
1. 进入"股票池管理"页面
2. 点击"创建股票池" → "自定义股票池"
3. 输入股票代码（支持多种格式）：
   ```
   000001.SZ
   600000.SH
   000002.SZ, 600036.SH
   
   支持逗号、空格、换行分隔
   ```
4. 填写股票池信息并创建

### 2. 在回测中使用股票池

#### 策略回测页面设置
1. 进入"策略回测"页面
2. 在"回测参数"部分找到"股票池选择"
3. 选择股票池类型：
   - **默认股票池**：系统自动选择最新股票池
   - **自定义股票池**：选择已创建的股票池
   - **单只股票**：选择单只股票进行回测
4. 配置其他回测参数并启动回测

#### 自动优先级
回测引擎会按以下优先级选择股票池：
1. 用户指定的股票池
2. 最新创建的自定义股票池
3. 系统默认股票池（前50只A股）

## 功能详解

### 股票池管理页面

#### 📋 **股票池列表**
- 显示所有已创建的股票池
- 包含来源、股票数量、创建时间等信息
- 支持查看详情、导出、删除操作

#### 🔍 **股票池详情**
- 显示股票池基本信息
- 列出所有包含的股票代码
- 显示创建来源和元数据

#### 📤 **导出功能**
- **JSON格式**：完整的股票池信息
- **CSV格式**：纯股票代码列表，便于Excel处理

### 选股结果转换

#### 🎯 **智能筛选**
```python
# 支持的筛选条件
- 最大股票数量：限制股票池大小（如前50只）
- 最低评分：只包含高质量股票（如评分>5.0）
- 按评分排序：确保选择最优股票
```

#### 📊 **元数据保留**
- 保留原始选股条件信息
- 记录筛选参数
- 显示评分分布

### API接口

#### 获取股票池列表
```http
GET /api/stock-pools/
```

#### 创建股票池
```http
POST /api/stock-pools/create-from-selection
POST /api/stock-pools/create-custom
```

#### 获取股票池股票列表
```http
GET /api/stock-pools/{pool_name}/stocks
```

## 实际应用场景

### 🏦 **行业投资策略**
```
创建银行股票池：
000001.SZ (平安银行)
600000.SH (浦发银行)
600036.SH (招商银行)
...

用于测试银行行业的投资策略
```

### 🍷 **主题投资策略**
```
创建白酒股票池：
600519.SH (贵州茅台)
000858.SZ (五粮液)
000568.SZ (泸州老窖)
...

用于测试白酒主题的投资策略
```

### 📈 **选股策略验证**
```
1. 使用技术指标选股
2. 将选股结果转换为股票池
3. 在股票池上测试不同的交易策略
4. 验证选股效果和策略表现
```

## 最佳实践

### 🎯 **股票池设计**
1. **适当规模**：建议20-100只股票，平衡多样性和管理复杂度
2. **定期更新**：根据市场变化定期更新股票池
3. **分类管理**：按行业、主题、风格等分类创建股票池

### 📊 **回测优化**
1. **对比测试**：使用不同股票池测试同一策略
2. **基准对比**：与全市场回测结果对比
3. **风险控制**：关注股票池的行业集中度

### 🔧 **管理维护**
1. **命名规范**：使用清晰的命名规则
2. **文档记录**：在描述中记录创建原因和用途
3. **定期清理**：删除不再使用的股票池

## 技术实现

### 后端架构
```
stock_pool_manager.py     # 股票池管理核心
stock_pool_api.py         # API接口
backtest_engine.py        # 回测引擎集成
```

### 数据存储
```
data/stock_pools/         # 股票池文件存储
├── pool1.json           # 股票池配置文件
├── pool2.json
└── ...
```

### 前端集成
```
StockPoolPage.jsx         # 股票池管理页面
StrategyBacktestPage.js   # 回测页面集成
```

## 故障排除

### 常见问题

#### Q: 股票池创建失败
A: 检查股票代码格式，确保使用正确的交易所后缀（.SH/.SZ）

#### Q: 回测时找不到股票池
A: 确保股票池文件存在，检查股票池名称是否正确

#### Q: 选股结果为空
A: 检查选股条件是否过于严格，适当放宽筛选条件

### 日志调试
```python
# 启用调试日志
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 更新日志

### v1.0.0 (2025-09-05)
- ✅ 实现股票池管理核心功能
- ✅ 集成选股结果转换
- ✅ 支持自定义股票池创建
- ✅ 回测引擎无缝集成
- ✅ 前端管理界面
- ✅ API接口完整实现

---

**注意**：使用股票池功能前，请确保已完成选股功能的配置，并且QMT连接正常。
