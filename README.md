# 🚀 QMT-TRADER 量化交易平台

基于backtrader的专业量化交易平台，集成QMT数据源、多策略回测引擎和现代化React前端界面。

[![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)](https://python.org)
[![React](https://img.shields.io/badge/React-18+-61dafb.svg)](https://reactjs.org)
[![FastAPI](https://img.shields.io/badge/FastAPI-0.100+-009688.svg)](https://fastapi.tiangolo.com)
[![Backtrader](https://img.shields.io/badge/Backtrader-1.9+-orange.svg)](https://www.backtrader.com)
[![License](https://img.shields.io/badge/License-MIT-green.svg)](LICENSE)

## 🎯 核心特性

### 1. 专业回测引擎
- **🔥 Backtrader框架**：业界领先的Python回测框架
- **📊 真实数据支持**：集成QMT xtdata服务，获取真实股票数据
- **⚡ 高性能计算**：优化的回测算法，支持大规模数据处理
- **📈 详细分析**：完整的交易记录、风险指标、收益分析

### 2. 多策略支持
- **📊 布林带策略**：基于布林带指标的均值回归策略
- **📈 移动平均线策略**：双均线趋势跟踪策略
- **📉 RSI策略**：基于相对强弱指标的超买超卖策略
- **💎 买入持有策略**：长期投资基准策略
- **🔧 策略注册机制**：灵活的策略扩展架构

### 3. 现代化前端界面
- **⚛️ React + shadcn/ui**：现代化的用户界面组件
- **📱 响应式设计**：完美适配桌面和移动设备
- **🎨 美观界面**：专业的数据可视化和交互设计
- **⚡ 实时更新**：回测进度实时显示，结果即时展现

### 4. 智能参数管理
- **🔧 参数验证**：智能参数校验，自动修正无效值
- **⚙️ 默认配置**：为每种策略提供合理的默认参数
- **💾 配置保存**：参数配置持久化存储
- **🔄 一键重置**：快速恢复默认设置

### 5. 详细数据分析
- **📊 核心指标**：总收益率、年化收益率、最大回撤、夏普比率
- **📋 交易记录**：完整的买卖记录，包含价格、数量、盈亏、原因
- **📈 收益曲线**：每日组合价值变化趋势
- **💾 数据导出**：支持导出完整的回测结果

## 📁 项目结构

```
QMT-TRADER/
├── backend/                    # 🐍 Python后端服务
│   ├── api/                   # 🌐 REST API接口
│   │   └── main.py           # FastAPI应用主文件
│   ├── backtest/             # 📊 回测引擎
│   │   ├── backtest_engine.py    # 旧版回测引擎
│   │   └── simple_backtest_engine.py # 新版回测引擎
│   ├── data/                 # 📈 数据管理
│   │   └── data_manager.py   # 数据管理器
│   ├── strategies/           # 🎯 交易策略
│   │   ├── base_strategy_new.py      # 策略基类
│   │   └── bollinger_bands_strategy.py # 策略实现
│   └── trading/              # 💼 实盘交易
│       └── live_trader.py    # 实盘交易器
├── frontend/                  # ⚛️ React前端界面
│   ├── public/               # 静态资源
│   ├── src/                  # 源代码
│   │   ├── components/       # 🧩 UI组件
│   │   │   ├── Layout/       # 布局组件
│   │   │   └── UI/          # 基础UI组件
│   │   ├── pages/           # 📄 页面组件
│   │   │   ├── StrategyBacktestPage.js    # 策略回测页面
│   │   │   ├── BacktestResultDetailPage.js # 回测结果详情
│   │   │   ├── Dashboard.js              # 仪表板
│   │   │   ├── DataManagementPage.js     # 数据管理
│   │   │   └── LiveTradingPage.js        # 实盘交易
│   │   ├── services/        # 🔌 API服务
│   │   │   └── api.js       # API接口封装
│   │   └── App.js           # 应用主组件
│   ├── package.json         # 前端依赖配置
│   └── tailwind.config.js   # Tailwind CSS配置
├── data/                     # 💾 数据存储目录
├── logs/                     # 📝 日志文件目录
├── config/                   # ⚙️ 配置文件目录
├── requirements.txt          # Python依赖列表
├── .gitignore               # Git忽略文件
├── README.md                # 项目说明文档
└── FRONTEND_UPDATE_SUMMARY.md # 前端更新说明
```

## 🚀 快速开始

### 1. 环境要求

| 组件 | 版本要求 | 说明 |
|------|----------|------|
| **Python** | 3.8+ | 后端运行环境 |
| **Node.js** | 16+ | 前端构建环境 |
| **QMT交易端** | 最新版 | 数据源和交易接口 |
| **xtquant** | 随QMT安装 | Python数据接口 |

### 2. 安装步骤

#### 📥 克隆项目
```bash
git clone https://github.com/your-username/QMT-TRADER.git
cd QMT-TRADER
```

#### 🐍 安装Python依赖
```bash
# 建议使用虚拟环境
conda create -n qmt python=3.8
conda activate qmt

# 安装依赖
pip install -r requirements.txt
```

#### ⚛️ 安装前端依赖
```bash
cd frontend
npm install
cd ..
```

#### ⚙️ 配置QMT环境
1. **安装QMT交易端**
   - 下载并安装国金证券QMT交易端
   - 启动QMT，确保xtdata服务正常运行

2. **验证xtquant模块**
   ```python
   # 测试xtquant是否可用
   python -c "import xtquant; print('xtquant可用')"
   ```

### 3. 启动服务

#### 🖥️ 启动后端服务
```bash
# 方式2：开发模式（推荐）
uvicorn backend.api.main:app --reload --host 0.0.0.0 --port 8000
```

#### 🌐 启动前端服务
```bash
cd frontend
npm start
```

### 4. 访问平台

| 服务 | 地址 | 说明 |
|------|------|------|
| **前端界面** | http://localhost:3000 | 主要操作界面 |
| **API文档** | http://localhost:8000/docs | Swagger API文档 |
| **后端API** | http://localhost:8000 | REST API接口 |

### 5. 验证安装

访问前端界面后，检查以下功能：

- ✅ **策略列表加载** - 应显示4种可用策略
- ✅ **QMT连接状态** - 数据管理页面显示连接正常
- ✅ **回测功能** - 能够启动并完成回测
- ✅ **结果展示** - 回测结果正确显示

## 📊 功能模块

### 1. 📈 策略回测
专业的量化回测平台，支持多种策略和详细分析

**核心功能：**
- 🎯 **多策略支持**：布林带、移动平均线、RSI、买入持有
- ⚙️ **参数配置**：可视化参数设置，智能验证
- 📊 **实时进度**：回测进度实时显示，状态监控
- 📋 **详细结果**：完整交易记录、风险指标、收益分析

**回测指标：**
- 📈 总收益率 & 年化收益率
- 📉 最大回撤 & 夏普比率
- 🎯 胜率 & 盈亏比
- 💰 平均盈利 & 平均亏损

### 2. 🏠 仪表板
系统状态和数据概览中心

**功能特性：**
- 📊 系统状态监控
- 📈 数据连接状态
- 💼 账户信息展示
- 📋 最近回测结果

### 3. 💼 实盘交易
安全的实盘交易管理系统

**安全特性：**
- 🔒 默认关闭实盘交易
- 🎯 模拟交易优先
- ⚠️ 多重安全确认
- 📊 风险控制机制

### 4. 💾 数据管理
完整的股票数据管理系统

**数据功能：**
- 📋 股票列表查看
- 📥 批量数据下载
- 📊 数据状态监控
- 🔄 一键数据更新
- 🔗 QMT数据源集成

### 5. ⚙️ 系统配置
灵活的系统参数配置

**配置项目：**
- 🎯 策略参数管理
- 📊 回测环境设置
- 💼 交易账户配置
- 🔧 系统运行参数

## 🎯 支持的策略

### 1. 📊 布林带策略 (bollinger_bands)
基于布林带指标的均值回归策略

**策略逻辑：**
- 📉 **买入信号**：价格跌破布林带下轨
- 📈 **卖出信号**：价格突破布林带上轨
- 🎯 **适用场景**：震荡市场，均值回归

**参数配置：**
```python
{
    "period": 20,        # 布林带周期 (5-50)
    "std_dev": 2.0,      # 标准差倍数 (0.5-5.0)
    "max_positions": 5,  # 最大持仓数 (1-20)
    "position_size": 0.2 # 仓位大小 (0.01-1.0)
}
```

### 2. 📈 移动平均线策略 (moving_average)
基于双移动平均线的趋势跟踪策略

**策略逻辑：**
- 📈 **买入信号**：短期均线上穿长期均线（金叉）
- 📉 **卖出信号**：短期均线下穿长期均线（死叉）
- 🎯 **适用场景**：趋势市场，趋势跟踪

**参数配置：**
```python
{
    "short_period": 10,  # 短期均线周期 (1-50)
    "long_period": 30,   # 长期均线周期 (5-200)
    "max_positions": 3,  # 最大持仓数 (1-20)
    "position_size": 0.3 # 仓位大小 (0.01-1.0)
}
```

### 3. 📉 RSI策略 (rsi)
基于相对强弱指标的超买超卖策略

**策略逻辑：**
- 📉 **买入信号**：RSI低于超卖线（默认30）
- 📈 **卖出信号**：RSI高于超买线（默认70）
- 🎯 **适用场景**：震荡市场，超买超卖判断

**参数配置：**
```python
{
    "period": 14,        # RSI计算周期 (5-50)
    "oversold": 30,      # 超卖线 (10-40)
    "overbought": 70,    # 超买线 (60-90)
    "max_positions": 3,  # 最大持仓数 (1-20)
    "position_size": 0.3 # 仓位大小 (0.01-1.0)
}
```

### 4. 💎 买入持有策略 (buy_hold)
长期投资基准策略

**策略逻辑：**
- 📈 **买入信号**：回测开始时买入选定股票
- 💎 **持有策略**：持有到回测结束
- 🎯 **适用场景**：长期投资，基准对比

**参数配置：**
```python
{
    "max_stocks": 3,     # 最大股票数 (1-10)
    "position_size": 0.33 # 每股资金比例 (0.01-1.0)
}
```

## 🔧 策略开发指南

### 1. 创建新策略

继承 `ConfigurableStrategy` 基类：

```python
from backend.strategies.base_strategy_new import ConfigurableStrategy, register_strategy
import backtrader as bt

class MyCustomStrategy(ConfigurableStrategy):
    def __init__(self):
        super().__init__()

        # 策略基本信息
        self.strategy_name = "my_custom"
        self.display_name = "我的自定义策略"
        self.description = "这是一个自定义策略示例"

        # 获取策略参数
        self.my_param = self.get_param_value('my_param', 10)

        # 创建技术指标
        for i, data in enumerate(self.datas):
            # 为每个数据源创建指标
            pass

    def generate_signals(self, data):
        """生成交易信号"""
        # 实现你的交易逻辑
        signals = {'buy': False, 'sell': False}

        # 示例：简单的价格突破策略
        if len(data) > 20:
            if data.close[0] > max(data.close[-20:]):
                signals['buy'] = True
                signals['reason'] = '价格突破20日高点'

        return signals

# 注册策略
register_strategy(MyCustomStrategy, 'my_custom', '我的自定义策略',
                 '这是一个自定义策略示例')
```

### 2. 策略参数验证

系统会自动验证参数范围：

```python
# 参数会被自动验证和修正
def get_param_value(self, param_name: str, default_value=None):
    # 系统内置验证规则
    validations = {
        'period': lambda x: max(1, min(100, int(x))),
        'position_size': lambda x: max(0.01, min(1.0, float(x))),
        # ... 更多验证规则
    }
```

### 3. 注册新策略

```python
# 在策略文件末尾注册
register_strategy(
    strategy_class=MyCustomStrategy,
    strategy_name='my_custom',
    display_name='我的自定义策略',
    description='策略描述信息'
)
```

## 📊 回测结果示例

### 布林带策略回测结果
```
📊 回测期间: 2024-06-01 ~ 2024-12-31
💰 初始资金: ¥500,000
📈 最终资金: ¥666,200
🎯 总收益率: +33.24%
📈 年化收益率: +33.50%
📉 最大回撤: -3.83%
⚡ 夏普比率: 1.85
🎯 胜率: 78.95%
📋 总交易: 38笔 (30盈利, 8亏损)
💰 平均盈利: ¥2,847
📉 平均亏损: ¥1,156
📊 盈亏比: 2.46
```

## 🛡️ 安全特性

### 1. 🔒 实盘交易保护
- **默认禁用**：实盘交易功能默认关闭
- **模拟优先**：优先使用模拟交易模式
- **多重确认**：重要操作需要用户确认
- **权限控制**：分级权限管理

### 2. 📊 风险控制
- **仓位限制**：最大持仓数量控制
- **资金管理**：单笔交易金额限制
- **止损机制**：自动止损保护
- **参数验证**：智能参数范围检查

### 3. 💾 数据安全
- **本地存储**：所有数据存储在本地
- **配置备份**：重要配置自动备份
- **日志记录**：完整的操作审计日志
- **数据加密**：敏感数据加密存储

## 🔍 故障排除

### 常见问题解决

#### 1. xtquant模块导入失败
```bash
# 问题：ModuleNotFoundError: No module named 'xtquant'
# 解决方案：
1. 确保QMT交易端已正确安装
2. 检查Python环境是否正确
3. 重新安装QMT或联系技术支持
```

#### 2. 前端无法连接后端
```bash
# 问题：前端显示连接错误
# 解决方案：
1. 检查后端服务是否启动: curl http://localhost:8000/docs
2. 检查端口是否被占用: netstat -ano | findstr :8000
3. 检查防火墙设置
```

#### 3. 回测任务失败
```bash
# 问题：回测启动后立即失败
# 解决方案：
1. 检查策略参数是否有效
2. 查看后端日志: logs/backend.log
3. 验证数据连接状态
```

#### 4. 数据获取失败
```bash
# 问题：无法获取股票数据
# 解决方案：
1. 确保QMT交易端正在运行
2. 检查网络连接
3. 验证xtdata服务状态
```

### 🔧 调试模式

```bash
# 启用详细日志
python -m backend.api.main --log-level DEBUG

# 查看实时日志
tail -f logs/backend.log

# 测试API连接
curl -X GET "http://localhost:8000/api/backtest/strategies"
```

## 📚 API文档

### 主要API接口

| 接口 | 方法 | 说明 |
|------|------|------|
| `/api/backtest/strategies` | GET | 获取可用策略列表 |
| `/api/backtest/start` | POST | 启动回测任务 |
| `/api/backtest/status` | GET | 获取回测状态 |
| `/api/backtest/results/{task_id}` | GET | 获取回测结果 |
| `/api/data/stocks` | GET | 获取股票列表 |

### 在线文档
- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc

## 🚀 技术架构

### 后端技术栈
- **FastAPI** - 现代化的Python Web框架
- **backtrader** - 专业的量化回测引擎
- **xtquant** - QMT数据接口
- **asyncio** - 异步任务处理
- **pydantic** - 数据验证和序列化

### 前端技术栈
- **React 18** - 用户界面框架
- **shadcn/ui** - 现代化UI组件库
- **Tailwind CSS** - 实用优先的CSS框架
- **React Router** - 客户端路由
- **Axios** - HTTP客户端

### 数据流架构
```
QMT交易端 → xtquant → Python后端 → FastAPI → React前端
    ↓           ↓         ↓          ↓         ↓
  真实数据   数据接口   策略引擎    REST API   用户界面
```

## 🤝 贡献指南

### 开发流程
1. **Fork项目** - 创建项目副本
2. **创建分支** - `git checkout -b feature/new-feature`
3. **开发功能** - 编写代码和测试
4. **提交更改** - `git commit -m "Add new feature"`
5. **推送分支** - `git push origin feature/new-feature`
6. **创建PR** - 提交Pull Request

### 代码规范
- **Python**: 遵循PEP 8规范
- **JavaScript**: 使用ESLint和Prettier
- **提交信息**: 使用约定式提交格式
- **文档**: 更新相关文档

## 📄 许可证

本项目采用 **MIT许可证** - 查看 [LICENSE](LICENSE) 文件了解详情

## 🙏 致谢

感谢以下开源项目的支持：

- [**Backtrader**](https://www.backtrader.com/) - 专业的Python回测框架
- [**FastAPI**](https://fastapi.tiangolo.com/) - 现代化的Python Web框架
- [**React**](https://reactjs.org/) - 用户界面构建库
- [**shadcn/ui**](https://ui.shadcn.com/) - 现代化UI组件库
- [**Tailwind CSS**](https://tailwindcss.com/) - 实用优先的CSS框架

## 📞 支持与反馈

### 获取帮助
- 📖 **查看文档** - 详细的使用说明和API文档
- 🔍 **搜索Issues** - 查看是否有类似问题
- 💬 **提交Issue** - 描述问题或建议
- 📧 **联系开发者** - 技术支持和合作

### 社区
- **GitHub Issues** - 问题报告和功能请求
- **GitHub Discussions** - 社区讨论和经验分享

---

## ⚠️ 重要声明

**风险提示**：
- 量化交易涉及金融风险，可能导致资金损失
- 本平台仅供学习和研究使用，不构成投资建议
- 请在充分了解和测试后谨慎使用实盘功能
- 使用前请详细阅读相关法律法规和风险提示

**免责声明**：
- 开发者不对使用本平台造成的任何损失承担责任
- 用户应自行承担使用本平台的所有风险
- 请遵守当地法律法规和证券交易规则

---

<div align="center">

**🚀 QMT-TRADER - 专业的量化交易平台**

[![GitHub stars](https://img.shields.io/github/stars/your-username/QMT-TRADER.svg?style=social&label=Star)](https://github.com/your-username/QMT-TRADER)
[![GitHub forks](https://img.shields.io/github/forks/your-username/QMT-TRADER.svg?style=social&label=Fork)](https://github.com/your-username/QMT-TRADER)

Made with ❤️ by the QMT-TRADER Team

</div>
