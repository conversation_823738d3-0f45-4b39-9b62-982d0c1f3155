import React, { useState, useEffect } from 'react';
import { Card as ShadCard, CardHeader as <PERSON>had<PERSON>ard<PERSON>eader, CardTitle as <PERSON>had<PERSON><PERSON><PERSON><PERSON><PERSON>, CardContent as ShadCardContent } from '../components/UI/card.jsx';
import { But<PERSON> as ShadButton } from '../components/UI/button.jsx';
import { dataAPI, apiUtils } from '../services/api';

const TestDataPage = () => {
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState(null);
  const [error, setError] = useState(null);

  const testAPI = async () => {
    setLoading(true);
    setError(null);
    setResult(null);

    try {
      console.log('开始测试API调用...');
      
      // 测试不同的参数组合
      const tests = [
        { name: '默认参数', params: {} },
        { name: '明确参数', params: { page: 1, page_size: 10 } },
        { name: '字符串参数', params: { page: '1', page_size: '10' } },
        { name: '数字参数', params: { page: 1, page_size: 10 } }
      ];

      const results = [];
      
      for (const test of tests) {
        try {
          console.log(`测试: ${test.name}`, test.params);
          const response = await dataAPI.getStockList(test.params);

          console.log(`${test.name} 响应:`, response);

          if (apiUtils.isSuccess(response)) {
            // 修复：直接使用response，因为axios拦截器已经返回了response.data
            results.push({
              test: test.name,
              success: true,
              count: response.data?.length || 0,
              total: response.total || 0,
              page: response.page || 0,
              pageSize: response.page_size || 0,
              rawResponse: JSON.stringify(response, null, 2).substring(0, 200) + '...'
            });
          } else {
            results.push({
              test: test.name,
              success: false,
              error: '响应不成功',
              rawResponse: JSON.stringify(response, null, 2).substring(0, 200) + '...'
            });
          }
        } catch (err) {
          console.error(`测试 ${test.name} 失败:`, err);
          results.push({
            test: test.name,
            success: false,
            error: err.message
          });
        }
      }
      
      setResult(results);
      
    } catch (err) {
      console.error('测试失败:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    testAPI();
  }, []);

  return (
    <div className="space-y-6">
      <ShadCard>
        <ShadCardHeader>
          <ShadCardTitle>🔍 数据API测试</ShadCardTitle>
        </ShadCardHeader>
        <ShadCardContent>
          <div className="space-y-4">
            <ShadButton onClick={testAPI} disabled={loading}>
              {loading ? '测试中...' : '重新测试'}
            </ShadButton>

            {error && (
              <div className="p-4 bg-red-50 border border-red-200 rounded">
                <h4 className="font-bold text-red-800">错误:</h4>
                <p className="text-red-600">{error}</p>
              </div>
            )}

            {result && (
              <div className="space-y-3">
                <h4 className="font-bold">测试结果:</h4>
                {result.map((test, index) => (
                  <div 
                    key={index}
                    className={`p-3 border rounded ${
                      test.success 
                        ? 'bg-green-50 border-green-200' 
                        : 'bg-red-50 border-red-200'
                    }`}
                  >
                    <div className="flex justify-between items-center">
                      <span className="font-medium">{test.test}</span>
                      <span className={`px-2 py-1 rounded text-xs ${
                        test.success 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {test.success ? '成功' : '失败'}
                      </span>
                    </div>
                    
                    {test.success ? (
                      <div className="mt-2 text-sm text-gray-600">
                        <p>获取数据: {test.count} 条</p>
                        <p>总数: {test.total} 条</p>
                        <p>页码: {test.page}</p>
                        <p>每页: {test.pageSize} 条</p>
                        <details className="mt-2">
                          <summary className="cursor-pointer text-blue-600">查看原始响应</summary>
                          <pre className="mt-1 text-xs bg-gray-100 p-2 rounded overflow-auto">
                            {test.rawResponse}
                          </pre>
                        </details>
                      </div>
                    ) : (
                      <div className="mt-2 text-sm text-red-600">
                        <p>错误: {test.error}</p>
                        <details className="mt-2">
                          <summary className="cursor-pointer text-blue-600">查看原始响应</summary>
                          <pre className="mt-1 text-xs bg-gray-100 p-2 rounded overflow-auto">
                            {test.rawResponse}
                          </pre>
                        </details>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>
        </ShadCardContent>
      </ShadCard>
    </div>
  );
};

export default TestDataPage;
