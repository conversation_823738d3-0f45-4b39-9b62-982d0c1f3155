<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据API测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .result {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success {
            border-left: 4px solid #28a745;
        }
        .error {
            border-left: 4px solid #dc3545;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        input {
            padding: 5px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 数据API测试工具</h1>
        <p>用于测试和调试数据管理API的问题</p>

        <div class="test-section">
            <h3>📊 测试1: 直接API调用</h3>
            <button onclick="testDirectAPI()">测试直接调用</button>
            <div id="direct-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>🔧 测试2: 参数类型测试</h3>
            <div>
                <label>页码: </label>
                <input type="number" id="test-page" value="1" min="1">
                <label>每页数量: </label>
                <input type="number" id="test-size" value="100" min="1" max="500">
                <button onclick="testWithParams()">测试参数</button>
            </div>
            <div id="params-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>⚠️ 测试3: 问题参数测试</h3>
            <button onclick="testProblematicParams()">测试问题参数</button>
            <div id="problematic-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>🌐 测试4: 网络状态检查</h3>
            <button onclick="checkNetworkStatus()">检查网络状态</button>
            <div id="network-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>📋 测试5: 后端健康检查</h3>
            <button onclick="checkBackendHealth()">检查后端状态</button>
            <div id="health-result" class="result"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8001';

        function log(elementId, message, isError = false) {
            const element = document.getElementById(elementId);
            const timestamp = new Date().toLocaleTimeString();
            element.textContent = `[${timestamp}] ${message}`;
            element.className = `result ${isError ? 'error' : 'success'}`;
        }

        async function testDirectAPI() {
            log('direct-result', '正在测试直接API调用...');
            
            try {
                const url = `${API_BASE}/api/data/stocks?page=1&page_size=100`;
                console.log('请求URL:', url);
                
                const response = await fetch(url);
                console.log('响应状态:', response.status);
                console.log('响应头:', response.headers);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                console.log('响应数据:', data);
                
                log('direct-result', `✅ 成功！获取到 ${data.data?.data?.length || 0} 条股票数据\n状态: ${response.status}\n数据: ${JSON.stringify(data, null, 2).substring(0, 200)}...`);
            } catch (error) {
                console.error('直接API调用错误:', error);
                log('direct-result', `❌ 失败: ${error.message}`, true);
            }
        }

        async function testWithParams() {
            const page = document.getElementById('test-page').value;
            const pageSize = document.getElementById('test-size').value;
            
            log('params-result', `正在测试参数: page=${page}, page_size=${pageSize}...`);
            
            try {
                // 测试不同的参数构造方式
                const tests = [
                    {
                        name: '直接字符串拼接',
                        url: `${API_BASE}/api/data/stocks?page=${page}&page_size=${pageSize}`
                    },
                    {
                        name: 'URLSearchParams构造',
                        url: (() => {
                            const params = new URLSearchParams();
                            params.append('page', page);
                            params.append('page_size', pageSize);
                            return `${API_BASE}/api/data/stocks?${params.toString()}`;
                        })()
                    },
                    {
                        name: '数字类型转换',
                        url: (() => {
                            const params = new URLSearchParams();
                            params.append('page', Number(page));
                            params.append('page_size', Number(pageSize));
                            return `${API_BASE}/api/data/stocks?${params.toString()}`;
                        })()
                    }
                ];

                let results = '';
                for (const test of tests) {
                    try {
                        console.log(`测试 ${test.name}:`, test.url);
                        const response = await fetch(test.url);
                        const data = await response.json();
                        results += `✅ ${test.name}: 成功 (${response.status})\n`;
                    } catch (error) {
                        results += `❌ ${test.name}: 失败 - ${error.message}\n`;
                    }
                }
                
                log('params-result', results);
            } catch (error) {
                log('params-result', `❌ 测试失败: ${error.message}`, true);
            }
        }

        async function testProblematicParams() {
            log('problematic-result', '正在测试问题参数...');
            
            const problematicCases = [
                { name: '空参数', params: '' },
                { name: '对象参数', params: 'page=[object Object]&page_size=100' },
                { name: '数组参数', params: 'page=[1]&page_size=[100]' },
                { name: '字符串参数', params: 'page=string&page_size=string' },
                { name: '负数参数', params: 'page=-1&page_size=-100' },
                { name: '浮点数参数', params: 'page=1.5&page_size=100.7' },
                { name: '超大参数', params: 'page=999999&page_size=999999' }
            ];

            let results = '';
            for (const testCase of problematicCases) {
                try {
                    const url = `${API_BASE}/api/data/stocks?${testCase.params}`;
                    console.log(`测试 ${testCase.name}:`, url);
                    
                    const response = await fetch(url);
                    const data = await response.json();
                    
                    if (response.ok) {
                        results += `⚠️ ${testCase.name}: 意外成功 (${response.status})\n`;
                    } else {
                        results += `✅ ${testCase.name}: 正确拒绝 (${response.status})\n`;
                    }
                } catch (error) {
                    results += `✅ ${testCase.name}: 正确失败 - ${error.message}\n`;
                }
            }
            
            log('problematic-result', results);
        }

        async function checkNetworkStatus() {
            log('network-result', '正在检查网络状态...');
            
            try {
                // 检查基本连通性
                const startTime = Date.now();
                const response = await fetch(`${API_BASE}/docs`);
                const endTime = Date.now();
                const latency = endTime - startTime;
                
                let status = `✅ 网络连通正常\n`;
                status += `延迟: ${latency}ms\n`;
                status += `后端状态: ${response.status}\n`;
                status += `响应时间: ${new Date().toISOString()}\n`;
                
                log('network-result', status);
            } catch (error) {
                log('network-result', `❌ 网络连接失败: ${error.message}`, true);
            }
        }

        async function checkBackendHealth() {
            log('health-result', '正在检查后端健康状态...');
            
            try {
                const checks = [
                    { name: 'API文档', url: `${API_BASE}/docs` },
                    { name: '根路径', url: `${API_BASE}/` },
                    { name: '股票API', url: `${API_BASE}/api/data/stocks?page=1&page_size=1` },
                    { name: '策略API', url: `${API_BASE}/api/backtest/strategies` }
                ];

                let results = '';
                for (const check of checks) {
                    try {
                        const response = await fetch(check.url);
                        if (response.ok) {
                            results += `✅ ${check.name}: 正常 (${response.status})\n`;
                        } else {
                            results += `⚠️ ${check.name}: 异常 (${response.status})\n`;
                        }
                    } catch (error) {
                        results += `❌ ${check.name}: 失败 - ${error.message}\n`;
                    }
                }
                
                log('health-result', results);
            } catch (error) {
                log('health-result', `❌ 健康检查失败: ${error.message}`, true);
            }
        }

        // 页面加载时自动运行基本检查
        window.addEventListener('load', () => {
            console.log('数据API测试工具已加载');
            checkNetworkStatus();
        });
    </script>
</body>
</html>
