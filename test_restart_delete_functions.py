#!/usr/bin/env python3
"""
测试重启和删除功能
"""

import sys
import os
import requests
import json
import time

# 添加项目路径
sys.path.append(os.path.dirname(__file__))

def test_restart_delete_functions():
    """测试重启和删除功能"""
    try:
        print("🧪 测试重启和删除功能...")
        
        # 1. 启动一个策略
        test_data = {
            "strategy_name": "bollinger_bands",
            "initial_capital": 100000,
            "commission": 0.001,
            "stock_codes": ["000001.SZ"],
            "paper_trading": True,
            "strategy_params": {
                "period": 20,
                "std_dev": 2.0
            }
        }
        
        print("📤 步骤1: 启动测试策略...")
        response = requests.post(
            'http://localhost:8000/api/live/start',
            json=test_data,
            headers={'Content-Type': 'application/json'}
        )
        
        if response.status_code != 200:
            print(f"❌ 启动策略失败: {response.status_code}")
            return
        
        data = response.json()
        if not data.get('success'):
            print(f"❌ 启动策略失败: {data}")
            return
        
        task_id = data.get('task_id')
        print(f"✅ 策略启动成功: {task_id[:8]}...")
        
        # 等待策略启动
        time.sleep(1)
        
        # 2. 验证策略状态
        print("\n📋 步骤2: 验证策略状态...")
        response = requests.get('http://localhost:8000/api/live/results')
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                results = data.get('data', [])
                strategy = next((r for r in results if r['task_id'] == task_id), None)
                
                if strategy:
                    print(f"✅ 策略状态: {strategy['status']}")
                    print(f"   交易模式: {'纸上交易' if strategy.get('paper_trading') else '实盘交易'}")
                else:
                    print("❌ 未找到策略")
                    return
            else:
                print(f"❌ 获取策略失败: {data}")
                return
        else:
            print(f"❌ 获取策略API错误: {response.status_code}")
            return
        
        # 3. 停止策略
        print(f"\n🛑 步骤3: 停止策略 {task_id[:8]}...")
        response = requests.post(f'http://localhost:8000/api/live/stop/{task_id}')
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("✅ 策略停止成功")
            else:
                print(f"❌ 策略停止失败: {data}")
                return
        else:
            print(f"❌ 停止策略API错误: {response.status_code}")
            return
        
        # 等待状态更新
        time.sleep(1)
        
        # 4. 验证停止状态
        print("\n📋 步骤4: 验证停止状态...")
        response = requests.get('http://localhost:8000/api/live/results')
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                results = data.get('data', [])
                strategy = next((r for r in results if r['task_id'] == task_id), None)
                
                if strategy and strategy['status'] == 'stopped':
                    print("✅ 策略已停止")
                else:
                    print(f"❌ 策略状态异常: {strategy['status'] if strategy else '未找到'}")
                    return
            else:
                print(f"❌ 获取策略失败: {data}")
                return
        
        # 5. 测试重启功能
        print(f"\n🔄 步骤5: 重启策略 {task_id[:8]}...")
        response = requests.post(f'http://localhost:8000/api/live/restart/{task_id}')
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("✅ 策略重启成功")
            else:
                print(f"❌ 策略重启失败: {data}")
                return
        else:
            print(f"❌ 重启策略API错误: {response.status_code}")
            return
        
        # 等待状态更新
        time.sleep(1)
        
        # 6. 验证重启状态
        print("\n📋 步骤6: 验证重启状态...")
        response = requests.get('http://localhost:8000/api/live/results')
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                results = data.get('data', [])
                strategy = next((r for r in results if r['task_id'] == task_id), None)
                
                if strategy and strategy['status'] == 'running':
                    print("✅ 策略已重启")
                else:
                    print(f"❌ 重启状态异常: {strategy['status'] if strategy else '未找到'}")
                    return
            else:
                print(f"❌ 获取策略失败: {data}")
                return
        
        # 7. 再次停止策略（为删除做准备）
        print(f"\n🛑 步骤7: 再次停止策略 {task_id[:8]}...")
        response = requests.post(f'http://localhost:8000/api/live/stop/{task_id}')
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("✅ 策略再次停止成功")
            else:
                print(f"❌ 策略停止失败: {data}")
                return
        
        # 等待状态更新
        time.sleep(1)
        
        # 8. 测试删除功能
        print(f"\n🗑️ 步骤8: 删除策略 {task_id[:8]}...")
        response = requests.delete(f'http://localhost:8000/api/live/delete/{task_id}')
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("✅ 策略删除成功")
            else:
                print(f"❌ 策略删除失败: {data}")
                return
        else:
            print(f"❌ 删除策略API错误: {response.status_code}")
            return
        
        # 9. 验证删除结果
        print("\n📋 步骤9: 验证删除结果...")
        response = requests.get('http://localhost:8000/api/live/results')
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                results = data.get('data', [])
                strategy = next((r for r in results if r['task_id'] == task_id), None)
                
                if strategy is None:
                    print("✅ 策略已成功删除")
                else:
                    print(f"❌ 策略仍然存在: {strategy}")
                    return
            else:
                print(f"❌ 获取策略失败: {data}")
                return
        
        print("\n🎉 所有功能测试通过！")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def test_frontend_buttons():
    """测试前端按钮显示"""
    print("\n🌐 前端按钮显示测试...")
    
    print("✅ 新增功能:")
    print("  1. 重启按钮: 🟢 绿色播放图标 (只在停止状态显示)")
    print("  2. 删除按钮: 🗑️ 垃圾桶图标 (只在停止状态显示)")
    print("  3. 停止按钮: 🔴 红色停止图标 (只在运行状态显示)")
    print("  4. 查看按钮: 🔵 蓝色眼睛图标 (始终显示)")
    
    print("\n📊 按钮状态逻辑:")
    print("  运行中策略: [👁️查看] [🛑停止]")
    print("  已停止策略: [👁️查看] [▶️重启] [🗑️删除]")
    
    print("\n🔗 测试步骤:")
    print("  1. 访问: http://localhost:3000/multi-strategy")
    print("  2. 启动一个策略")
    print("  3. 观察运行中策略的按钮: 查看 + 停止")
    print("  4. 点击停止按钮")
    print("  5. 观察停止后的按钮: 查看 + 重启 + 删除")
    print("  6. 测试重启功能")
    print("  7. 测试删除功能")

if __name__ == "__main__":
    print("🧪 重启和删除功能完整测试")
    print("=" * 60)
    
    # 测试后端API功能
    test_restart_delete_functions()
    
    # 前端按钮说明
    test_frontend_buttons()
    
    print("\n" + "=" * 60)
    print("🎯 测试总结:")
    print("✅ 后端API: 重启和删除功能正常")
    print("✅ 前端按钮: 根据状态动态显示")
    print("✅ 用户体验: 直观的操作流程")
    print("✅ 安全机制: 只能删除已停止的策略")
