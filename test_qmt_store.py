#!/usr/bin/env python3
"""
测试QMT Store模式实现
验证Store、Data、Broker的基本功能
"""

import sys
import os
from datetime import datetime, timedelta

# 添加项目路径
sys.path.append(os.path.dirname(__file__))

import backtrader as bt
from backend.core.logger import get_logger

logger = get_logger(__name__)

def test_qmt_store_creation():
    """测试QMT Store创建"""
    logger.info("=== 测试QMT Store创建 ===")
    
    try:
        from backend.stores import QMTStore
        
        # 创建Store
        store = QMTStore()
        logger.info("✅ QMT Store创建成功")
        
        # 检查连接状态
        if store.is_connected():
            logger.info("✅ QMT系统连接正常")
        else:
            logger.warning("⚠️ QMT系统未连接（可能是正常的，如果QMT未运行）")
        
        return store
        
    except Exception as e:
        logger.error(f"❌ QMT Store创建失败: {e}")
        return None

def test_qmt_data_creation(store):
    """测试QMT Data创建"""
    logger.info("\n=== 测试QMT Data创建 ===")
    
    if not store:
        logger.error("❌ Store未创建，跳过测试")
        return None
    
    try:
        # 创建数据源
        data = store.getdata(
            dataname='000001.SZ',
            historical=True,
            fromdate=datetime.now() - timedelta(days=30),
            todate=datetime.now()
        )
        
        logger.info("✅ QMT Data创建成功")
        logger.info(f"   股票代码: {data.dataname}")
        logger.info(f"   数据模式: {'实时' if data.live_mode else '历史'}")
        
        return data
        
    except Exception as e:
        logger.error(f"❌ QMT Data创建失败: {e}")
        return None

def test_qmt_broker_creation(store):
    """测试QMT Broker创建"""
    logger.info("\n=== 测试QMT Broker创建 ===")
    
    if not store:
        logger.error("❌ Store未创建，跳过测试")
        return None
    
    try:
        # 创建Broker
        broker = store.getbroker(cash=100000.0)
        
        logger.info("✅ QMT Broker创建成功")
        logger.info(f"   初始资金: {broker.get_cash():,.2f}")
        logger.info(f"   总资产: {broker.get_value():,.2f}")
        
        return broker
        
    except Exception as e:
        logger.error(f"❌ QMT Broker创建失败: {e}")
        return None

def test_simple_strategy_integration():
    """测试简单策略集成"""
    logger.info("\n=== 测试策略集成 ===")
    
    try:
        from backend.stores import QMTStore
        
        # 创建一个简单的测试策略
        class TestStrategy(bt.Strategy):
            def __init__(self):
                logger.info("📊 测试策略初始化")
            
            def next(self):
                # 简单的测试逻辑
                if len(self.data) == 1:  # 第一天
                    logger.info(f"📈 第一天数据: 收盘价={self.data.close[0]:.2f}")
                elif len(self.data) == 10:  # 第十天
                    logger.info(f"📊 第十天数据: 收盘价={self.data.close[0]:.2f}")
                    # 测试买入
                    self.buy(size=100)
                    logger.info("📝 提交买入订单: 100股")
            
            def notify_order(self, order):
                if order.status == order.Completed:
                    logger.info(f"✅ 订单完成: {'买入' if order.isbuy() else '卖出'} "
                               f"{order.executed.size}股 @{order.executed.price:.2f}")
        
        # 创建Cerebro
        cerebro = bt.Cerebro()
        
        # 创建QMT Store
        qmt_store = QMTStore()
        
        # 设置broker
        cerebro.broker = qmt_store.getbroker(cash=100000.0)
        
        # 添加数据
        data = qmt_store.getdata(
            dataname='000001.SZ',
            historical=True,
            fromdate=datetime.now() - timedelta(days=30),
            todate=datetime.now()
        )
        cerebro.adddata(data)
        
        # 添加策略
        cerebro.addstrategy(TestStrategy)
        
        # 设置手续费
        cerebro.broker.setcommission(commission=0.001)
        
        logger.info("🚀 开始运行测试策略")
        
        # 运行策略
        results = cerebro.run()
        
        # 显示结果
        final_value = cerebro.broker.getvalue()
        logger.info("✅ 策略运行完成")
        logger.info(f"   最终资产: {final_value:,.2f}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 策略集成测试失败: {e}")
        return False

def test_existing_strategy_compatibility():
    """测试现有策略兼容性"""
    logger.info("\n=== 测试现有策略兼容性 ===")
    
    try:
        # 尝试导入现有策略
        from backend.strategies.bollinger_bands_strategy import BollingerBandsStrategy
        from backend.stores import QMTStore
        
        logger.info("✅ 成功导入现有策略: BollingerBandsStrategy")
        
        # 创建Cerebro
        cerebro = bt.Cerebro()
        
        # 创建QMT Store
        qmt_store = QMTStore()
        
        # 设置broker
        cerebro.broker = qmt_store.getbroker(cash=100000.0)
        
        # 添加数据
        data = qmt_store.getdata(
            dataname='000001.SZ',
            historical=True,
            fromdate=datetime.now() - timedelta(days=60),
            todate=datetime.now()
        )
        cerebro.adddata(data)
        
        # 添加现有策略
        cerebro.addstrategy(BollingerBandsStrategy)
        
        # 设置手续费
        cerebro.broker.setcommission(commission=0.001)
        
        logger.info("🚀 开始运行现有策略")
        
        # 运行策略（限制运行时间，避免长时间运行）
        results = cerebro.run()
        
        # 显示结果
        final_value = cerebro.broker.getvalue()
        logger.info("✅ 现有策略运行完成")
        logger.info(f"   最终资产: {final_value:,.2f}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 现有策略兼容性测试失败: {e}")
        return False

def test_live_engine_integration():
    """测试实盘引擎集成"""
    logger.info("\n=== 测试实盘引擎集成 ===")
    
    try:
        from backend.live.simple_live_engine import live_engine
        
        # 测试配置
        config = {
            'strategy_name': 'BollingerBandsStrategy',
            'initial_capital': 100000.0,
            'commission': 0.001,
            'stock_codes': ['000001.SZ'],
            'paper_trading': True,
            # 策略参数
            'bb_period': 20,
            'bb_std': 2.0
        }
        
        logger.info("📝 测试配置创建成功")
        logger.info(f"   策略: {config['strategy_name']}")
        logger.info(f"   股票: {config['stock_codes']}")
        logger.info(f"   资金: {config['initial_capital']:,.2f}")
        
        # 注意：这里不实际启动，只测试配置
        logger.info("✅ 实盘引擎集成测试通过（未实际启动）")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 实盘引擎集成测试失败: {e}")
        return False

def main():
    """主测试函数"""
    logger.info("🔍 开始QMT Store模式测试")
    
    # 测试计数
    total_tests = 0
    passed_tests = 0
    
    # 测试1: Store创建
    total_tests += 1
    store = test_qmt_store_creation()
    if store:
        passed_tests += 1
    
    # 测试2: Data创建
    total_tests += 1
    data = test_qmt_data_creation(store)
    if data:
        passed_tests += 1
    
    # 测试3: Broker创建
    total_tests += 1
    broker = test_qmt_broker_creation(store)
    if broker:
        passed_tests += 1
    
    # 测试4: 简单策略集成
    total_tests += 1
    if test_simple_strategy_integration():
        passed_tests += 1
    
    # 测试5: 现有策略兼容性
    total_tests += 1
    if test_existing_strategy_compatibility():
        passed_tests += 1
    
    # 测试6: 实盘引擎集成
    total_tests += 1
    if test_live_engine_integration():
        passed_tests += 1
    
    # 显示测试结果
    logger.info(f"\n🎯 测试完成")
    logger.info(f"   总测试数: {total_tests}")
    logger.info(f"   通过测试: {passed_tests}")
    logger.info(f"   失败测试: {total_tests - passed_tests}")
    logger.info(f"   通过率: {passed_tests / total_tests * 100:.1f}%")
    
    if passed_tests == total_tests:
        logger.info("🎉 所有测试通过！QMT Store模式实现正确")
    else:
        logger.warning("⚠️ 部分测试失败，请检查实现")

if __name__ == "__main__":
    main()
