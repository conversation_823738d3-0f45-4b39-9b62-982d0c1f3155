#!/usr/bin/env python3
"""
直接测试QMTData
"""

import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(__file__))

def test_qmt_data_direct():
    """直接测试QMTData"""
    try:
        print("🔍 直接测试QMTData...")
        
        # 强制重新导入
        if 'backend.stores.qmt_data' in sys.modules:
            del sys.modules['backend.stores.qmt_data']
        if 'backend.stores.qmt_store' in sys.modules:
            del sys.modules['backend.stores.qmt_store']
        
        from backend.stores.qmt_store import QMTStore
        from backend.stores.qmt_data import QMTData
        import backtrader as bt
        
        print(f"✅ 导入成功")
        print(f"   QMTData类型: {QMTData}")
        print(f"   QMTData父类: {QMTData.__bases__}")
        
        # 检查是否正确继承
        if issubclass(QMTData, bt.feeds.PandasData):
            print("✅ 正确继承自PandasData")
        else:
            print("❌ 没有正确继承PandasData")
            return False
        
        # 创建QMTStore
        store = QMTStore()
        print("✅ QMTStore创建成功")
        
        # 直接测试QMTData创建
        test_kwargs = {
            'dataname': '000001.SZ',
            'historical': True,
            'live': False
        }
        
        print(f"📤 测试参数: {test_kwargs}")
        
        try:
            # 应该通过QMTStore.getdata创建，而不是直接创建QMTData
            data = store.getdata(**test_kwargs)
            print("✅ QMTData创建成功")
            print(f"   股票代码: {getattr(data, 'stock_code', 'NOT_SET')}")
            return True
        except Exception as e:
            print(f"❌ QMTData创建失败: {e}")
            import traceback
            print(f"详细错误: {traceback.format_exc()}")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False

def test_qmt_store_getdata():
    """测试QMTStore.getdata"""
    try:
        print("\n🔍 测试QMTStore.getdata...")
        
        from backend.stores.qmt_store import QMTStore
        
        store = QMTStore()
        
        test_kwargs = {
            'historical': True,
            'live': False
        }
        
        print(f"📤 调用getdata('000001.SZ', {test_kwargs})")
        
        try:
            data = store.getdata(dataname='000001.SZ', **test_kwargs)
            print("✅ getdata调用成功")
            print(f"   数据类型: {type(data)}")
            print(f"   股票代码: {getattr(data, 'stock_code', 'NOT_SET')}")
            return True
        except Exception as e:
            print(f"❌ getdata调用失败: {e}")
            import traceback
            print(f"详细错误: {traceback.format_exc()}")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False

def test_simple_live_engine_flow():
    """测试SimpleLiveEngine流程"""
    try:
        print("\n🔍 测试SimpleLiveEngine流程...")
        
        from backend.live.simple_live_engine import SimpleLiveEngine
        
        engine = SimpleLiveEngine()
        
        config = {
            'strategy_name': 'bollinger_bands',
            'initial_capital': 100000,
            'commission': 0.001,
            'stock_codes': ['000001.SZ'],
            'paper_trading': True
        }
        
        print(f"📤 测试配置: {config}")
        
        try:
            # 这里不实际启动，只测试到数据源创建
            print("📋 模拟SimpleLiveEngine中的数据源创建...")
            
            from backend.stores.qmt_store import QMTStore
            from datetime import datetime, timedelta
            
            qmt_store = QMTStore()
            stock_codes = config.get('stock_codes', [])
            
            print(f"📋 股票代码: {stock_codes}")
            
            for stock_code in stock_codes:
                print(f"📤 创建数据源: {stock_code}")
                
                data = qmt_store.getdata(
                    dataname=stock_code,
                    historical=True,
                    live=True,
                    fromdate=datetime.now() - timedelta(days=30),
                    todate=datetime.now()
                )
                
                print(f"✅ 数据源创建成功: {stock_code}")
                print(f"   类型: {type(data)}")
                print(f"   股票代码: {getattr(data, 'stock_code', 'NOT_SET')}")
            
            return True
            
        except Exception as e:
            print(f"❌ SimpleLiveEngine流程失败: {e}")
            import traceback
            print(f"详细错误: {traceback.format_exc()}")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    print("🧪 QMTData直接测试")
    print("=" * 60)
    
    # 测试QMTData直接创建
    direct_ok = test_qmt_data_direct()
    
    if direct_ok:
        # 测试QMTStore.getdata
        getdata_ok = test_qmt_store_getdata()
        
        if getdata_ok:
            # 测试SimpleLiveEngine流程
            engine_ok = test_simple_live_engine_flow()
            
            if engine_ok:
                print("\n🎉 所有测试通过！")
                print("✅ QMTData工作正常")
                print("✅ QMTStore.getdata工作正常")
                print("✅ SimpleLiveEngine流程正常")
                print("\n💡 问题可能在其他地方，建议检查：")
                print("   1. 后端服务器是否真的重启了")
                print("   2. 是否有其他地方导入了旧版本的模块")
                print("   3. 是否有多个Python进程在运行")
            else:
                print("\n❌ SimpleLiveEngine流程有问题")
        else:
            print("\n❌ QMTStore.getdata有问题")
    else:
        print("\n❌ QMTData直接创建有问题")
    
    print("\n" + "=" * 60)
    print("🎯 测试完成")
