import * as React from 'react'
import { cn } from '../../lib/utils'

export function Switch({ 
  checked = false, 
  onChange, 
  disabled = false, 
  size = 'default',
  checkedChildren,
  unCheckedChildren,
  className = '',
  ...props 
}) {
  const sizeClasses = {
    small: 'h-5 w-9',
    default: 'h-6 w-11',
    large: 'h-7 w-13'
  }

  const thumbSizeClasses = {
    small: 'h-4 w-4',
    default: 'h-5 w-5', 
    large: 'h-6 w-6'
  }

  const translateClasses = {
    small: checked ? 'translate-x-4' : 'translate-x-0',
    default: checked ? 'translate-x-5' : 'translate-x-0',
    large: checked ? 'translate-x-6' : 'translate-x-0'
  }

  return (
    <button
      type="button"
      role="switch"
      aria-checked={checked}
      disabled={disabled}
      onClick={() => !disabled && onChange && onChange(!checked)}
      className={cn(
        'relative inline-flex shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',
        checked ? 'bg-blue-600' : 'bg-gray-200',
        sizeClasses[size],
        className
      )}
      {...props}
    >
      <span className="sr-only">Toggle switch</span>
      <span
        className={cn(
          'pointer-events-none inline-block rounded-full bg-white shadow transform ring-0 transition duration-200 ease-in-out',
          thumbSizeClasses[size],
          translateClasses[size]
        )}
      >
        {/* 可选的文字标签 */}
        {(checkedChildren || unCheckedChildren) && (
          <span className="absolute inset-0 flex items-center justify-center text-xs font-medium">
            {checked ? checkedChildren : unCheckedChildren}
          </span>
        )}
      </span>
    </button>
  )
}
