#!/usr/bin/env python3
"""
调试选股问题的脚本
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from backend.stock_selection.stock_selector import StockSelector, SelectionCriteria
from backend.core.logger import get_logger

logger = get_logger(__name__)

def test_basic_selection():
    """测试基本选股功能"""
    print("🔍 开始调试选股功能...")
    
    # 创建选股器
    selector = StockSelector()
    
    # 创建非常简单的条件
    criteria = SelectionCriteria(
        volume_min=0.5,  # 成交量比例大于0.5
        condition_logic="flexible"
    )
    
    print(f"📋 选股条件: 成交量比例 > {criteria.volume_min}")
    print(f"🎯 条件逻辑: {criteria.condition_logic}")
    
    # 执行选股
    try:
        print("\n🚀 开始执行选股...")
        results = selector.select_stocks(criteria, "debug_test")
        
        print(f"\n✅ 选股完成!")
        print(f"📈 选中股票数量: {len(results)}")
        
        if results:
            print("\n📊 选中的股票:")
            for i, stock in enumerate(results[:5]):
                print(f"  {i+1}. {stock.stock_code} - {stock.stock_name}")
                print(f"     评分: {stock.score:.2f}")
                print(f"     成交量比: {stock.indicators.get('volume_ratio', 'N/A')}")
                print(f"     RSI: {stock.indicators.get('rsi', 'N/A')}")
                print()
        else:
            print("❌ 没有选中任何股票")
            print("\n🔍 可能的原因:")
            print("  1. 所有股票都不满足条件")
            print("  2. 数据获取失败")
            print("  3. 技术指标计算失败")
            print("  4. 条件匹配逻辑有问题")
            
    except Exception as e:
        print(f"❌ 选股执行失败: {e}")
        import traceback
        traceback.print_exc()

def test_single_stock():
    """测试单只股票的处理"""
    print("\n🔍 测试单只股票处理...")

    selector = StockSelector()

    # 使用模拟数据测试
    try:
        from backend.stock_selection.mock_data import generate_mock_stock_list, get_mock_stock_data
        from datetime import datetime, timedelta

        # 获取模拟股票列表
        stocks = generate_mock_stock_list(5)
        test_stock = stocks[0]
        stock_code = test_stock['code']
        print(f"📊 测试股票: {stock_code} - {test_stock['name']}")

        # 获取模拟股票数据
        end_date = datetime.now()
        start_date = end_date - timedelta(days=100)

        df = get_mock_stock_data(
            stock_code=stock_code,
            start_date=start_date.strftime('%Y-%m-%d'),
            end_date=end_date.strftime('%Y-%m-%d')
        )

        if df is not None and not df.empty:
            print(f"✅ 模拟数据获取成功: {len(df)} 条记录")
            print(f"📅 数据范围: {df.index[0]} 到 {df.index[-1]}")
            print(f"📊 数据列: {list(df.columns)}")
            print(f"📈 价格范围: {df['close'].min():.2f} - {df['close'].max():.2f}")
            print(f"📊 成交量范围: {df['volume'].min():.0f} - {df['volume'].max():.0f}")

            # 计算技术指标
            indicators = selector.calculate_technical_indicators(df)
            if indicators:
                print(f"✅ 技术指标计算成功: {len(indicators)} 个指标")
                print(f"📊 关键指标:")
                print(f"   RSI: {indicators.get('rsi', 'N/A'):.2f}")
                print(f"   成交量比: {indicators.get('volume_ratio', 'N/A'):.2f}")
                print(f"   ATR比率: {indicators.get('atr_ratio', 'N/A'):.4f}")
                print(f"   MA趋势: {indicators.get('ma_trend', 'N/A')}")
                print(f"   MACD趋势: {indicators.get('macd_trend', 'N/A')}")

                # 测试条件检查
                criteria = SelectionCriteria(volume_min=0.5, condition_logic="flexible")
                alpha_factors = selector.calculate_alpha101_factors(df)

                passed, score = selector.check_selection_criteria(indicators, alpha_factors, criteria)
                print(f"🎯 条件检查结果: 通过={passed}, 评分={score:.2f}")

                # 测试更宽松的条件
                criteria2 = SelectionCriteria(volume_min=0.1, condition_logic="flexible")
                passed2, score2 = selector.check_selection_criteria(indicators, alpha_factors, criteria2)
                print(f"🎯 宽松条件检查: 通过={passed2}, 评分={score2:.2f}")

            else:
                print("❌ 技术指标计算失败")
        else:
            print("❌ 模拟数据获取失败")

    except Exception as e:
        print(f"❌ 单股票测试失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("🎯 QMT-TRADER 选股功能调试")
    print("=" * 50)
    
    # 测试基本选股
    test_basic_selection()
    
    # 测试单只股票
    test_single_stock()
    
    print("\n" + "=" * 50)
    print("✅ 调试完成")

if __name__ == "__main__":
    main()
