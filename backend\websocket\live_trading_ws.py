#!/usr/bin/env python3
"""
实盘交易WebSocket服务
提供实时数据推送功能
"""

import asyncio
import json
from typing import Dict, List, Set, Any
from datetime import datetime
from fastapi import WebSocket, WebSocketDisconnect

from backend.core.logger import get_logger
from backend.live.simple_live_engine import live_engine
from backend.risk.risk_manager import risk_manager

logger = get_logger(__name__)

class LiveTradingWebSocketManager:
    """实盘交易WebSocket管理器"""
    
    def __init__(self):
        # 连接管理
        self.active_connections: Dict[str, WebSocket] = {}  # {client_id: websocket}
        self.client_subscriptions: Dict[str, Set[str]] = {}  # {client_id: {task_ids}}
        
        # 数据缓存
        self.last_data: Dict[str, Any] = {}
        
        # 推送任务
        self.push_task = None
        self.is_running = False
        
        logger.info("📡 实盘交易WebSocket管理器初始化完成")
    
    async def connect(self, websocket: WebSocket, client_id: str):
        """建立WebSocket连接"""
        try:
            await websocket.accept()
            self.active_connections[client_id] = websocket
            self.client_subscriptions[client_id] = set()
            
            logger.info(f"✅ WebSocket客户端连接: {client_id}")
            
            # 发送连接成功消息
            await self.send_to_client(client_id, {
                'type': 'connection',
                'status': 'connected',
                'client_id': client_id,
                'timestamp': datetime.now().isoformat()
            })
            
            # 启动推送任务（如果还没启动）
            if not self.is_running:
                await self.start_push_task()
            
        except Exception as e:
            logger.error(f"❌ WebSocket连接失败: {e}")
    
    async def disconnect(self, client_id: str):
        """断开WebSocket连接"""
        try:
            if client_id in self.active_connections:
                del self.active_connections[client_id]
            
            if client_id in self.client_subscriptions:
                del self.client_subscriptions[client_id]
            
            logger.info(f"🔌 WebSocket客户端断开: {client_id}")
            
            # 如果没有活跃连接，停止推送任务
            if not self.active_connections and self.is_running:
                await self.stop_push_task()
                
        except Exception as e:
            logger.error(f"❌ WebSocket断开失败: {e}")
    
    async def subscribe(self, client_id: str, task_ids: List[str]):
        """订阅策略任务"""
        try:
            if client_id not in self.client_subscriptions:
                self.client_subscriptions[client_id] = set()
            
            self.client_subscriptions[client_id].update(task_ids)
            
            logger.info(f"📋 客户端 {client_id} 订阅任务: {task_ids}")
            
            # 立即发送当前数据
            await self.send_current_data(client_id, task_ids)
            
        except Exception as e:
            logger.error(f"❌ 订阅失败: {e}")
    
    async def unsubscribe(self, client_id: str, task_ids: List[str]):
        """取消订阅策略任务"""
        try:
            if client_id in self.client_subscriptions:
                self.client_subscriptions[client_id].difference_update(task_ids)
            
            logger.info(f"📋 客户端 {client_id} 取消订阅任务: {task_ids}")
            
        except Exception as e:
            logger.error(f"❌ 取消订阅失败: {e}")
    
    async def send_to_client(self, client_id: str, data: Dict[str, Any]):
        """发送数据给指定客户端"""
        try:
            if client_id in self.active_connections:
                websocket = self.active_connections[client_id]
                await websocket.send_text(json.dumps(data, ensure_ascii=False))
                
        except WebSocketDisconnect:
            await self.disconnect(client_id)
        except Exception as e:
            logger.error(f"❌ 发送数据失败: {e}")
    
    async def broadcast(self, data: Dict[str, Any], task_id: str = None):
        """广播数据给所有相关客户端"""
        try:
            for client_id, subscriptions in self.client_subscriptions.items():
                # 如果指定了task_id，只发送给订阅了该任务的客户端
                if task_id and task_id not in subscriptions:
                    continue
                
                await self.send_to_client(client_id, data)
                
        except Exception as e:
            logger.error(f"❌ 广播数据失败: {e}")
    
    async def send_current_data(self, client_id: str, task_ids: List[str]):
        """发送当前数据给客户端"""
        try:
            # 获取所有实盘交易结果
            results = live_engine.get_all_live_trading_results()
            
            for result in results:
                if result.task_id in task_ids:
                    # 发送策略状态
                    await self.send_to_client(client_id, {
                        'type': 'strategy_status',
                        'task_id': result.task_id,
                        'data': {
                            'task_id': result.task_id,
                            'strategy_name': result.strategy_name,
                            'status': result.status,
                            'current_capital': result.current_capital,
                            'total_return': result.total_return,
                            'positions': result.positions,
                            'orders': result.orders,
                            'current_time': result.current_time
                        },
                        'timestamp': datetime.now().isoformat()
                    })
                    
                    # 发送风险指标
                    risk_metrics = risk_manager.get_risk_metrics(result.task_id)
                    if risk_metrics:
                        await self.send_to_client(client_id, {
                            'type': 'risk_metrics',
                            'task_id': result.task_id,
                            'data': risk_metrics,
                            'timestamp': datetime.now().isoformat()
                        })
            
        except Exception as e:
            logger.error(f"❌ 发送当前数据失败: {e}")
    
    async def start_push_task(self):
        """启动数据推送任务"""
        if self.is_running:
            return
        
        self.is_running = True
        self.push_task = asyncio.create_task(self._push_loop())
        logger.info("🚀 WebSocket推送任务启动")
    
    async def stop_push_task(self):
        """停止数据推送任务"""
        if not self.is_running:
            return
        
        self.is_running = False
        if self.push_task:
            self.push_task.cancel()
            try:
                await self.push_task
            except asyncio.CancelledError:
                pass
        
        logger.info("🛑 WebSocket推送任务停止")
    
    async def _push_loop(self):
        """数据推送循环"""
        try:
            while self.is_running:
                await self._push_updates()
                await asyncio.sleep(2)  # 每2秒推送一次
                
        except asyncio.CancelledError:
            logger.info("📡 WebSocket推送循环被取消")
        except Exception as e:
            logger.error(f"❌ WebSocket推送循环错误: {e}")
    
    async def _push_updates(self):
        """推送更新数据"""
        try:
            if not self.active_connections:
                return
            
            # 获取所有实盘交易结果
            results = live_engine.get_all_live_trading_results()
            
            for result in results:
                task_id = result.task_id
                
                # 检查数据是否有变化
                current_data = {
                    'current_capital': result.current_capital,
                    'total_return': result.total_return,
                    'status': result.status,
                    'positions_count': len(result.positions),
                    'orders_count': len(result.orders)
                }
                
                last_data = self.last_data.get(task_id, {})
                if current_data != last_data:
                    # 数据有变化，推送更新
                    await self.broadcast({
                        'type': 'strategy_update',
                        'task_id': task_id,
                        'data': {
                            'task_id': result.task_id,
                            'strategy_name': result.strategy_name,
                            'status': result.status,
                            'current_capital': result.current_capital,
                            'total_return': result.total_return,
                            'positions': result.positions,
                            'orders': result.orders,
                            'current_time': result.current_time
                        },
                        'timestamp': datetime.now().isoformat()
                    }, task_id)
                    
                    # 更新缓存
                    self.last_data[task_id] = current_data
            
            # 推送风险告警
            await self._push_risk_alerts()
            
        except Exception as e:
            logger.error(f"❌ 推送更新失败: {e}")
    
    async def _push_risk_alerts(self):
        """推送风险告警"""
        try:
            # 获取最近的风险告警
            alerts = risk_manager.get_risk_alerts()
            
            # 只推送最近5分钟的告警
            recent_alerts = []
            now = datetime.now()
            for alert_dict in alerts:
                alert_time = datetime.fromisoformat(alert_dict['timestamp'])
                if (now - alert_time).total_seconds() < 300:  # 5分钟
                    recent_alerts.append(alert_dict)
            
            if recent_alerts:
                await self.broadcast({
                    'type': 'risk_alerts',
                    'data': recent_alerts,
                    'timestamp': datetime.now().isoformat()
                })
                
        except Exception as e:
            logger.error(f"❌ 推送风险告警失败: {e}")
    
    async def handle_message(self, client_id: str, message: str):
        """处理客户端消息"""
        try:
            data = json.loads(message)
            msg_type = data.get('type')
            
            if msg_type == 'subscribe':
                task_ids = data.get('task_ids', [])
                await self.subscribe(client_id, task_ids)
                
            elif msg_type == 'unsubscribe':
                task_ids = data.get('task_ids', [])
                await self.unsubscribe(client_id, task_ids)
                
            elif msg_type == 'ping':
                await self.send_to_client(client_id, {
                    'type': 'pong',
                    'timestamp': datetime.now().isoformat()
                })
                
            else:
                logger.warning(f"⚠️ 未知消息类型: {msg_type}")
                
        except Exception as e:
            logger.error(f"❌ 处理客户端消息失败: {e}")

# 全局WebSocket管理器实例
ws_manager = LiveTradingWebSocketManager()
