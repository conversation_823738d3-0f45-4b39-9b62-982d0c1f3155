#!/usr/bin/env python3
"""
调试实盘引擎问题
"""

import asyncio
import sys
import traceback

async def debug_live_engine():
    """调试实盘引擎"""
    print("🔍 开始调试实盘引擎")
    
    try:
        # 1. 导入实盘引擎
        print("\n1. 导入实盘引擎...")
        from backend.live.simple_live_engine import live_engine
        print("✅ 实盘引擎导入成功")
        
        # 2. 检查当前运行的策略
        print("\n2. 检查当前运行的策略...")
        running_strategies = live_engine.get_running_strategies()
        print(f"当前运行策略数量: {len(running_strategies)}")
        
        results = live_engine.get_all_live_trading_results()
        print(f"历史结果数量: {len(results)}")
        
        # 3. 测试策略类获取
        print("\n3. 测试策略类获取...")
        from backend.strategies.base_strategy_new import get_strategy_class

        # 确保策略被注册
        from backend.strategies import bollinger_bands_strategy

        strategy_class = get_strategy_class('buy_hold')
        if strategy_class:
            print(f"✅ 找到buy_hold策略类: {strategy_class.__name__}")
        else:
            print("❌ 未找到buy_hold策略类")
            # 列出所有可用策略
            from backend.strategies.base_strategy_new import list_strategies
            strategies = list_strategies()
            print(f"可用策略: {[s['name'] for s in strategies]}")
            return False
        
        # 4. 测试QMT Store创建
        print("\n4. 测试QMT Store创建...")
        from backend.stores.qmt_store import QMTStore
        qmt_store = QMTStore()
        print("✅ QMT Store创建成功")
        
        # 5. 测试数据源创建
        print("\n5. 测试数据源创建...")
        test_stock = '000001.SZ'
        try:
            data = qmt_store.getdata(
                dataname=test_stock,
                historical=True,  # 改为True，确保有历史数据
                live=True,
                paper_trading=True
            )
            print(f"✅ 数据源创建成功: {test_stock}")
            print(f"   数据源类型: {type(data)}")
            print(f"   股票代码: {getattr(data, '_name', 'NOT_SET')}")
        except Exception as e:
            print(f"❌ 数据源创建失败: {e}")
            traceback.print_exc()
            return False
        
        # 6. 测试简化的实盘交易启动
        print("\n6. 测试简化的实盘交易启动...")
        config = {
            'strategy_name': 'buy_hold',
            'initial_capital': 100000,
            'commission': 0.001,
            'stock_codes': ['000001.SZ'],
            'paper_trading': True,
            'strategy_params': {
                'max_stocks': 1,
                'position_size': 1.0
            }
        }
        
        print(f"配置: {config}")
        
        try:
            task_id = await live_engine.start_live_trading(config)
            print(f"✅ 实盘交易启动成功: {task_id}")
            
            # 7. 等待一段时间，观察策略状态
            print("\n7. 观察策略状态...")
            for i in range(10):
                await asyncio.sleep(2)
                
                results = live_engine.get_all_live_trading_results()
                for result in results:
                    if result.task_id == task_id:
                        print(f"[{i+1}/10] 状态:{result.status} 资金:{result.current_capital:.2f} 持仓:{len(result.positions)} 订单:{len(result.orders)}")
                        
                        if result.status == 'error':
                            print(f"❌ 策略出现错误")
                            return False
                        
                        if len(result.positions) > 0 or len(result.orders) > 0:
                            print(f"✅ 策略开始交易！")
                            print(f"   持仓: {result.positions}")
                            print(f"   订单: {result.orders}")
                            break
                        break
                else:
                    print(f"[{i+1}/10] 未找到策略结果")
            
            # 8. 停止策略
            print(f"\n8. 停止策略...")
            success = live_engine.stop_live_trading(task_id)
            if success:
                print(f"✅ 策略停止成功")
            else:
                print(f"⚠️ 策略停止失败")
            
            return True
            
        except Exception as e:
            print(f"❌ 实盘交易启动失败: {e}")
            traceback.print_exc()
            return False
        
    except Exception as e:
        print(f"❌ 调试过程出错: {e}")
        traceback.print_exc()
        return False

async def debug_strategy_directly():
    """直接调试策略"""
    print("\n🔍 直接调试策略")
    
    try:
        # 1. 导入策略类
        from backend.strategies.bollinger_bands_strategy import BuyHoldStrategy
        import backtrader as bt
        
        # 2. 创建cerebro
        cerebro = bt.Cerebro()
        
        # 3. 设置初始资金
        cerebro.broker.setcash(100000)
        
        # 4. 创建数据源
        from backend.stores.qmt_store import QMTStore
        qmt_store = QMTStore()
        
        data = qmt_store.getdata(
            dataname='000001.SZ',
            historical=True,
            live=False,  # 先测试历史数据模式
            paper_trading=True
        )
        
        cerebro.adddata(data, name='000001.SZ')
        
        # 5. 添加策略
        cerebro.addstrategy(BuyHoldStrategy, config={'parameters': {'max_stocks': 1, 'position_size': 1.0}})
        
        # 6. 运行策略
        print("运行策略...")
        results = cerebro.run()
        
        print(f"✅ 策略运行完成")
        print(f"最终资金: {cerebro.broker.getvalue():.2f}")
        
        return True
        
    except Exception as e:
        print(f"❌ 直接策略调试失败: {e}")
        traceback.print_exc()
        return False

async def main():
    """主函数"""
    print("🚀 开始调试buy_hold策略问题")
    
    # 测试1: 调试实盘引擎
    print("="*60)
    print("测试1: 调试实盘引擎")
    print("="*60)
    
    success1 = await debug_live_engine()
    
    # 测试2: 直接调试策略
    print("\n" + "="*60)
    print("测试2: 直接调试策略")
    print("="*60)
    
    success2 = await debug_strategy_directly()
    
    # 总结
    print("\n" + "="*60)
    print("调试结果总结")
    print("="*60)
    
    print(f"实盘引擎测试: {'✅ 成功' if success1 else '❌ 失败'}")
    print(f"直接策略测试: {'✅ 成功' if success2 else '❌ 失败'}")
    
    if success1 and success2:
        print("\n🎉 所有测试通过！buy_hold策略应该能正常工作")
    elif success2:
        print("\n💡 策略本身正常，问题在实盘引擎集成")
    elif success1:
        print("\n💡 实盘引擎正常，问题在策略逻辑")
    else:
        print("\n⚠️ 需要进一步调试，两个测试都失败")

if __name__ == "__main__":
    asyncio.run(main())
