#!/usr/bin/env python3
"""
调试股票代码传递问题
"""

import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(__file__))

def test_dataname_passing():
    """测试股票代码传递"""
    try:
        print("🔍 测试股票代码传递...")
        
        from backend.stores.qmt_store import QMTStore
        
        # 创建QMT Store
        store = QMTStore()
        print("✅ QMTStore创建成功")
        
        # 测试getdata调用
        test_stock_code = "000001.SZ"
        print(f"\n📤 调用getdata(dataname='{test_stock_code}')")
        
        try:
            data = store.getdata(
                dataname=test_stock_code,
                historical=True,
                live=False
            )
            print("✅ QMTData创建成功")
            print(f"   股票代码: {getattr(data, 'dataname', 'NOT_SET')}")
            print(f"   stock_code: {getattr(data, 'stock_code', 'NOT_SET')}")
            
        except Exception as e:
            print(f"❌ QMTData创建失败: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False

def test_direct_creation():
    """测试直接创建QMTData"""
    try:
        print("\n🔍 测试直接创建QMTData...")
        
        from backend.stores.qmt_data import QMTData
        from backend.stores.qmt_store import QMTStore
        
        store = QMTStore()
        test_stock_code = "000001.SZ"
        
        print(f"📤 直接创建QMTData(dataname='{test_stock_code}')")
        
        try:
            data = QMTData(
                store=store,
                dataname=test_stock_code,
                historical=True,
                live=False
            )
            print("✅ 直接创建成功")
            print(f"   股票代码: {getattr(data, 'dataname', 'NOT_SET')}")
            print(f"   stock_code: {getattr(data, 'stock_code', 'NOT_SET')}")
            
        except Exception as e:
            print(f"❌ 直接创建失败: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False

def test_backtrader_params():
    """测试backtrader参数机制"""
    try:
        print("\n🔍 测试backtrader参数机制...")
        
        import backtrader as bt
        import pandas as pd
        
        # 创建一个简单的DataFrame
        df = pd.DataFrame({
            'open': [100, 101, 102],
            'high': [105, 106, 107],
            'low': [95, 96, 97],
            'close': [103, 104, 105],
            'volume': [1000, 1100, 1200]
        }, index=pd.date_range('2023-01-01', periods=3))
        
        print("📤 创建标准PandasData")
        
        try:
            data = bt.feeds.PandasData(
                dataname=df,
                name="TEST_STOCK"
            )
            print("✅ 标准PandasData创建成功")
            print(f"   p.dataname类型: {type(data.p.dataname)}")
            print(f"   p.dataname长度: {len(data.p.dataname) if hasattr(data.p.dataname, '__len__') else 'N/A'}")
            
        except Exception as e:
            print(f"❌ 标准PandasData创建失败: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    print("🧪 股票代码传递调试")
    print("=" * 50)
    
    # 测试通过QMTStore传递
    store_ok = test_dataname_passing()
    
    # 测试直接创建
    direct_ok = test_direct_creation()
    
    # 测试backtrader参数机制
    bt_ok = test_backtrader_params()
    
    print("\n" + "=" * 50)
    print("🎯 调试结果:")
    print(f"  QMTStore传递: {'✅' if store_ok else '❌'}")
    print(f"  直接创建: {'✅' if direct_ok else '❌'}")
    print(f"  backtrader参数: {'✅' if bt_ok else '❌'}")
    
    if not store_ok:
        print("\n💡 建议:")
        print("  1. 检查QMTStore.getdata参数传递")
        print("  2. 检查QMTData.__init__参数处理")
        print("  3. 检查backtrader PandasData初始化顺序")
