#!/usr/bin/env python3
"""
测试纸面交易和实盘交易的显示
"""

import requests
import json

BASE_URL = "http://localhost:8000"

def test_trading_type_display():
    """测试交易类型显示"""
    print("=== 测试纸面交易和实盘交易显示 ===")
    
    # 1. 启动纸面交易策略
    print("\n1. 启动纸面交易策略")
    paper_strategy = {
        'name': '纸面交易策略',
        'strategy_type': 'bollinger_bands',
        'config': {
            'initial_capital': 50000,
            'paper_trading': True  # 明确设置为纸面交易
        }
    }
    
    response = requests.post(f"{BASE_URL}/api/live/strategies", json=paper_strategy)
    if response.status_code == 200:
        result = response.json()
        print(f"✅ 纸面交易策略启动成功: {result['strategy_id']}")
        paper_strategy_id = result['strategy_id']
    else:
        print(f"❌ 纸面交易策略启动失败: {response.text}")
        return False
    
    # 2. 启动实盘交易策略
    print("\n2. 启动实盘交易策略")
    real_strategy = {
        'name': '实盘交易策略',
        'strategy_type': 'bollinger_bands',
        'config': {
            'initial_capital': 30000,
            'paper_trading': False  # 明确设置为实盘交易
        }
    }
    
    response = requests.post(f"{BASE_URL}/api/live/strategies", json=real_strategy)
    if response.status_code == 200:
        result = response.json()
        print(f"✅ 实盘交易策略启动成功: {result['strategy_id']}")
        real_strategy_id = result['strategy_id']
    else:
        print(f"❌ 实盘交易策略启动失败: {response.text}")
        return False
    
    # 3. 启动默认策略（应该是纸面交易）
    print("\n3. 启动默认策略（不指定paper_trading）")
    default_strategy = {
        'name': '默认策略',
        'strategy_type': 'bollinger_bands',
        'config': {
            'initial_capital': 40000
            # 不指定 paper_trading，应该默认为 True
        }
    }
    
    response = requests.post(f"{BASE_URL}/api/live/strategies", json=default_strategy)
    if response.status_code == 200:
        result = response.json()
        print(f"✅ 默认策略启动成功: {result['strategy_id']}")
        default_strategy_id = result['strategy_id']
    else:
        print(f"❌ 默认策略启动失败: {response.text}")
        return False
    
    # 4. 检查策略列表和交易类型
    print("\n4. 检查策略列表和交易类型")
    response = requests.get(f"{BASE_URL}/api/live/strategies")
    if response.status_code == 200:
        strategies = response.json()
        print(f"当前策略数量: {len(strategies)}")
        
        for strategy in strategies:
            name = strategy['name']
            paper_trading = strategy.get('config', {}).get('paper_trading', True)
            trading_type = '纸面交易' if paper_trading else '实盘交易'
            initial_capital = strategy.get('config', {}).get('initial_capital', 0)
            
            print(f"- {name}:")
            print(f"  交易类型: {trading_type}")
            print(f"  初始资金: ¥{initial_capital:,}")
            print(f"  paper_trading值: {paper_trading}")
            
            # 验证预期结果
            if name == '纸面交易策略':
                if paper_trading:
                    print(f"  ✅ 纸面交易策略显示正确")
                else:
                    print(f"  ❌ 纸面交易策略显示错误，应该是纸面交易")
            elif name == '实盘交易策略':
                if not paper_trading:
                    print(f"  ✅ 实盘交易策略显示正确")
                else:
                    print(f"  ❌ 实盘交易策略显示错误，应该是实盘交易")
            elif name == '默认策略':
                if paper_trading:
                    print(f"  ✅ 默认策略显示正确（默认为纸面交易）")
                else:
                    print(f"  ❌ 默认策略显示错误，应该默认为纸面交易")
    else:
        print(f"❌ 获取策略列表失败: {response.text}")
        return False
    
    # 5. 检查持久化文件中的数据
    print("\n5. 检查持久化文件中的交易类型")
    import os
    tasks_file = "data/multi_strategy_tasks.json"
    if os.path.exists(tasks_file):
        with open(tasks_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        running_strategies = data.get('running_strategies', {})
        print(f"持久化文件中的策略数量: {len(running_strategies)}")
        
        for strategy_id, strategy_data in running_strategies.items():
            name = strategy_data.get('name', 'N/A')
            paper_trading = strategy_data.get('config', {}).get('paper_trading', True)
            trading_type = '纸面交易' if paper_trading else '实盘交易'
            
            print(f"- {name} ({strategy_id}): {trading_type}")
    else:
        print("❌ 持久化文件不存在")
    
    # 6. 清理测试策略
    print("\n6. 清理测试策略")
    test_strategy_ids = [paper_strategy_id, real_strategy_id, default_strategy_id]
    for strategy_id in test_strategy_ids:
        response = requests.delete(f"{BASE_URL}/api/live/strategies/{strategy_id}")
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 策略已停止: {strategy_id}")
        else:
            print(f"❌ 停止策略失败: {strategy_id}")
    
    return True

def test_frontend_data_format():
    """测试前端数据格式"""
    print("\n=== 测试前端数据格式 ===")
    
    # 模拟前端配置数据格式
    frontend_configs = [
        {
            'name': '前端纸面交易测试',
            'strategy_name': 'bollinger_bands',
            'initial_capital': 60000,
            'paper_trading': True,
            'strategy_params': {
                'bb_period': 20,
                'bb_std': 2.0
            }
        },
        {
            'name': '前端实盘交易测试',
            'strategy_name': 'bollinger_bands', 
            'initial_capital': 80000,
            'paper_trading': False,
            'strategy_params': {
                'bb_period': 15,
                'bb_std': 1.5
            }
        },
        {
            'name': '前端默认测试',
            'strategy_name': 'bollinger_bands',
            'initial_capital': 70000,
            # 不包含 paper_trading 字段
            'strategy_params': {
                'bb_period': 25,
                'bb_std': 2.5
            }
        }
    ]
    
    strategy_ids = []
    
    for config in frontend_configs:
        print(f"\n启动策略: {config['name']}")
        
        # 模拟前端的数据转换逻辑
        strategy_data = {
            'name': config.get('strategy_name', '未命名策略'),
            'strategy_type': config.get('strategy_name', 'bollinger_bands'),
            'config': {
                'initial_capital': config.get('initial_capital', 50000),
                'max_positions': 5,
                'risk_limit': 0.02,
                'stock_codes': config.get('stock_codes', []),
                'paper_trading': config.get('paper_trading') if 'paper_trading' in config else True,  # 修复后的逻辑
                **config.get('strategy_params', {})
            }
        }
        
        print(f"转换后的数据: {json.dumps(strategy_data, indent=2, ensure_ascii=False)}")
        
        response = requests.post(f"{BASE_URL}/api/live/strategies", json=strategy_data)
        if response.status_code == 200:
            result = response.json()
            strategy_ids.append(result['strategy_id'])
            print(f"✅ 启动成功: {result['strategy_id']}")
        else:
            print(f"❌ 启动失败: {response.text}")
    
    # 检查结果
    print(f"\n检查启动结果:")
    response = requests.get(f"{BASE_URL}/api/live/strategies")
    if response.status_code == 200:
        strategies = response.json()
        for strategy in strategies:
            if strategy['id'] in strategy_ids:
                paper_trading = strategy.get('config', {}).get('paper_trading', True)
                trading_type = '纸面交易' if paper_trading else '实盘交易'
                print(f"- {strategy['name']}: {trading_type}")
    
    # 清理
    print(f"\n清理测试策略:")
    for strategy_id in strategy_ids:
        requests.delete(f"{BASE_URL}/api/live/strategies/{strategy_id}")
        print(f"✅ 已停止: {strategy_id}")

def main():
    """主测试函数"""
    print("🚀 开始测试交易类型显示功能")
    
    try:
        # 测试基本功能
        success = test_trading_type_display()
        
        if success:
            # 测试前端数据格式
            test_frontend_data_format()
            
            print("\n🎉 交易类型显示功能测试完成！")
            print("\n✅ 修复内容:")
            print("  - 添加了交易类型显示列")
            print("  - 修复了前端paper_trading参数传递逻辑")
            print("  - 纸面交易显示为蓝色标签")
            print("  - 实盘交易显示为红色标签")
            print("  - 默认为纸面交易")
            
            print("\n💡 现在前端会正确显示:")
            print("  - 纸面交易: 蓝色标签 '纸面交易'")
            print("  - 实盘交易: 红色标签 '实盘交易'")
        else:
            print("\n❌ 测试失败，请检查后端服务")
            
    except Exception as e:
        print(f"\n❌ 测试过程中出现异常: {e}")

if __name__ == "__main__":
    main()
