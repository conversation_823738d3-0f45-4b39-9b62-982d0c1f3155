#!/usr/bin/env python3
"""
选股模块API接口
"""

from fastapi import APIRouter, HTTPException, Query, BackgroundTasks
from pydantic import BaseModel
from typing import List, Dict, Any, Optional
from datetime import datetime
import json
import os
import uuid
import asyncio
from pathlib import Path

from backend.stock_selection.stock_selector import StockSelector, SelectionCriteria, StockScore
from backend.stock_selection.selection_presets import selection_presets
from backend.core.logger import get_logger

logger = get_logger(__name__)

router = APIRouter(prefix="/api/stock-selection", tags=["股票选择"])

# 创建选股器实例（延迟初始化）
_stock_selector = None

# 任务状态管理
_selection_tasks = {}

def get_stock_selector():
    """获取选股器实例"""
    global _stock_selector
    if _stock_selector is None:
        _stock_selector = StockSelector()
    return _stock_selector

class SelectionTaskStatus:
    """选股任务状态"""
    def __init__(self, task_id: str, custom_name: str):
        self.task_id = task_id
        self.custom_name = custom_name
        self.status = "pending"  # pending, running, completed, failed
        self.progress = 0  # 0-100
        self.current_stock = ""
        self.processed_count = 0
        self.total_count = 0
        self.selected_count = 0
        self.message = ""
        self.result = None
        self.error = None
        self.start_time = datetime.now()
        self.end_time = None

def update_task_progress(task_id: str, **kwargs):
    """更新任务进度"""
    if task_id in _selection_tasks:
        task = _selection_tasks[task_id]
        for key, value in kwargs.items():
            if hasattr(task, key):
                setattr(task, key, value)

async def run_stock_selection_async(task_id: str, criteria: SelectionCriteria, custom_name: str):
    """异步执行选股任务"""
    try:
        # 更新任务状态为运行中
        update_task_progress(task_id, status="running", message="开始选股...")

        # 获取选股器
        selector = get_stock_selector()

        # 创建一个带进度回调的选股方法
        def progress_callback(processed: int, total: int, current_stock: str, selected: int):
            progress = int((processed / total) * 100) if total > 0 else 0
            update_task_progress(
                task_id,
                progress=progress,
                processed_count=processed,
                total_count=total,
                current_stock=current_stock,
                selected_count=selected,
                message=f"正在处理: {current_stock} ({processed}/{total})"
            )

        # 执行选股（需要修改选股器支持进度回调）
        selected_stocks = selector.select_stocks_with_progress(criteria, custom_name, progress_callback)

        # 更新任务状态为完成
        update_task_progress(
            task_id,
            status="completed",
            progress=100,
            result=selected_stocks,
            message=f"选股完成，共选中 {len(selected_stocks)} 只股票",
            end_time=datetime.now()
        )

    except Exception as e:
        # 更新任务状态为失败
        update_task_progress(
            task_id,
            status="failed",
            error=str(e),
            message=f"选股失败: {str(e)}",
            end_time=datetime.now()
        )
        logger.error(f"异步选股任务失败: {e}", exc_info=True)

class SelectionCriteriaRequest(BaseModel):
    """选股条件请求"""
    # 股票类型筛选
    include_etf: bool = False  # 包含ETF
    only_etf: bool = False  # 仅ETF
    exclude_etf: bool = False  # 排除ETF
    etf_types: Optional[List[str]] = None  # ETF类型：['stock', 'bond', 'commodity', 'money', 'cross_border']

    # 技术指标条件
    atr_min: Optional[float] = None
    atr_max: Optional[float] = None
    atr_period: int = 14

    # 布林带条件
    bb_position: Optional[str] = None  # 'upper', 'lower', 'middle', 'outside', 'inside'
    bb_period: int = 20
    bb_std: float = 2.0

    # RSI条件
    rsi_min: Optional[float] = None
    rsi_max: Optional[float] = None
    rsi_period: int = 14

    # MACD条件
    macd_signal: Optional[str] = None  # 'bullish', 'bearish'
    macd_fast: int = 12
    macd_slow: int = 26
    macd_signal_period: int = 9

    # 移动平均线条件
    ma_trend: Optional[str] = None  # 'above_ma', 'below_ma', 'cross_up', 'cross_down'
    ma_period: int = 20
    ma_arrangement: Optional[str] = None  # 'bullish', 'bearish' - 均线排列

    # 成交量条件
    volume_min: Optional[float] = None
    volume_max: Optional[float] = None
    volume_avg_period: int = 20
    volume_ratio_min: Optional[float] = None  # 量比最小值
    volume_ratio_max: Optional[float] = None  # 量比最大值
    turnover_rate_min: Optional[float] = None  # 换手率最小值（%）
    turnover_rate_max: Optional[float] = None  # 换手率最大值（%）

    # 价格条件
    price_min: Optional[float] = None
    price_max: Optional[float] = None
    price_change_min: Optional[float] = None
    price_change_max: Optional[float] = None

    # 动量因子条件
    momentum_1m_min: Optional[float] = None  # 1月动量最小值
    momentum_1m_max: Optional[float] = None  # 1月动量最大值
    momentum_3m_min: Optional[float] = None  # 3月动量最小值
    momentum_3m_max: Optional[float] = None  # 3月动量最大值
    momentum_12m_min: Optional[float] = None  # 12月动量最小值
    momentum_12m_max: Optional[float] = None  # 12月动量最大值

    # 市值条件
    market_cap_min: Optional[float] = None
    market_cap_max: Optional[float] = None

    # 价值因子条件
    pe_min: Optional[float] = None  # 市盈率最小值
    pe_max: Optional[float] = None  # 市盈率最大值
    pb_min: Optional[float] = None  # 市净率最小值
    pb_max: Optional[float] = None  # 市净率最大值
    ps_min: Optional[float] = None  # 市销率最小值
    ps_max: Optional[float] = None  # 市销率最大值
    dividend_yield_min: Optional[float] = None  # 股息率最小值
    dividend_yield_max: Optional[float] = None  # 股息率最大值

    # 质量因子条件
    roe_min: Optional[float] = None  # ROE最小值（%）
    roe_max: Optional[float] = None  # ROE最大值（%）
    roa_min: Optional[float] = None  # ROA最小值（%）
    roa_max: Optional[float] = None  # ROA最大值（%）
    gross_margin_min: Optional[float] = None  # 毛利率最小值（%）
    gross_margin_max: Optional[float] = None  # 毛利率最大值（%）

    # 成长因子条件
    revenue_growth_min: Optional[float] = None  # 营收增长率最小值（%）
    revenue_growth_max: Optional[float] = None  # 营收增长率最大值（%）
    profit_growth_min: Optional[float] = None  # 净利润增长率最小值（%）
    profit_growth_max: Optional[float] = None  # 净利润增长率最大值（%）
    eps_growth_min: Optional[float] = None  # EPS增长率最小值（%）
    eps_growth_max: Optional[float] = None  # EPS增长率最大值（%）

    # 风险控制因子
    volatility_min: Optional[float] = None  # 波动率最小值
    volatility_max: Optional[float] = None  # 波动率最大值
    beta_min: Optional[float] = None  # Beta最小值
    beta_max: Optional[float] = None  # Beta最大值
    max_drawdown_max: Optional[float] = None  # 最大回撤上限

    # 流动性因子
    avg_amount_min: Optional[float] = None  # 日均成交额最小值（万元）
    avg_amount_max: Optional[float] = None  # 日均成交额最大值（万元）

    # Alpha101因子条件
    alpha_factors: Optional[List[str]] = None
    alpha_min_values: Optional[Dict[str, float]] = None
    alpha_max_values: Optional[Dict[str, float]] = None

    # 时间范围
    lookback_days: int = 30

    # 其他条件
    exclude_st: bool = True
    exclude_new_stock: bool = True
    min_trading_days: int = 20

class SelectionRequest(BaseModel):
    """选股请求"""
    criteria: SelectionCriteriaRequest
    custom_name: str = "default"
    max_results: int = 100

@router.post("/select")
async def select_stocks(request: SelectionRequest, background_tasks: BackgroundTasks):
    """启动异步选股任务"""
    try:
        # 转换请求为选股条件
        try:
            # 兼容不同版本的Pydantic
            criteria_dict = request.criteria.model_dump() if hasattr(request.criteria, 'model_dump') else request.criteria.dict()
            criteria = SelectionCriteria(**criteria_dict)
        except AttributeError:
            criteria = SelectionCriteria(**request.criteria.__dict__)

        # 生成任务ID
        task_id = str(uuid.uuid4())

        # 创建任务状态
        task_status = SelectionTaskStatus(task_id, request.custom_name)
        _selection_tasks[task_id] = task_status

        # 启动后台任务
        background_tasks.add_task(run_stock_selection_async, task_id, criteria, request.custom_name)

        # 立即返回任务ID
        return {
            'success': True,
            'data': {
                'task_id': task_id,
                'message': '选股任务已启动，请使用task_id查询进度',
                'custom_name': request.custom_name
            }
        }
        
    except Exception as e:
        logger.error(f"启动选股任务失败: {e}")
        raise HTTPException(status_code=500, detail=f"启动选股任务失败: {str(e)}")

@router.get("/task/{task_id}")
async def get_task_status(task_id: str):
    """查询选股任务状态"""
    if task_id not in _selection_tasks:
        raise HTTPException(status_code=404, detail="任务不存在")

    task = _selection_tasks[task_id]

    response_data = {
        'task_id': task.task_id,
        'custom_name': task.custom_name,
        'status': task.status,
        'progress': task.progress,
        'message': task.message,
        'processed_count': task.processed_count,
        'total_count': task.total_count,
        'selected_count': task.selected_count,
        'current_stock': task.current_stock,
        'start_time': task.start_time.isoformat() if task.start_time else None,
        'end_time': task.end_time.isoformat() if task.end_time else None
    }

    # 如果任务完成，包含结果
    if task.status == "completed" and task.result:
        # 转换结果格式
        results = []
        for stock in task.result:
            results.append({
                'stock_code': stock.stock_code,
                'stock_name': stock.stock_name,
                'score': round(stock.score, 2),
                'indicators': {k: round(v, 4) if isinstance(v, (int, float)) else v
                             for k, v in stock.indicators.items()},
                'alpha_factors': {k: round(v, 4) for k, v in stock.alpha_factors.items()},
                'selection_date': stock.selection_date
            })

        response_data['result'] = {
            'total_selected': len(results),
            'selected_stocks': results,
            'filename': f"{task.custom_name}_{task.start_time.strftime('%Y%m%d_%H%M%S')}.json"
        }

    # 如果任务失败，包含错误信息
    if task.status == "failed" and task.error:
        response_data['error'] = task.error

    return {
        'success': True,
        'data': response_data
    }

@router.get("/files")
async def list_selection_files():
    """获取选股结果文件列表"""
    try:
        selector = get_stock_selector()
        files = selector.list_selection_files()
        
        return {
            'success': True,
            'data': {
                'total_files': len(files),
                'files': files
            }
        }
        
    except Exception as e:
        logger.error(f"获取选股文件列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取文件列表失败: {str(e)}")

@router.get("/files/{filename}")
async def get_selection_results(filename: str):
    """获取指定选股结果"""
    try:
        selector = get_stock_selector()
        selected_stocks = selector.load_selection_results(filename)
        
        if selected_stocks is None:
            raise HTTPException(status_code=404, detail="选股结果文件不存在")
        
        # 转换为响应格式
        results = []
        for stock in selected_stocks:
            results.append({
                'stock_code': stock.stock_code,
                'stock_name': stock.stock_name,
                'score': round(stock.score, 2),
                'indicators': {k: round(v, 4) if isinstance(v, (int, float)) else v 
                             for k, v in stock.indicators.items()},
                'alpha_factors': {k: round(v, 4) for k, v in stock.alpha_factors.items()},
                'selection_date': stock.selection_date
            })
        
        return {
            'success': True,
            'data': {
                'filename': filename,
                'total_selected': len(results),
                'stocks': results
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取选股结果失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取选股结果失败: {str(e)}")

@router.get("/files/{filename}/codes")
async def get_stock_codes(filename: str):
    """获取选股结果中的股票代码列表"""
    try:
        selector = get_stock_selector()
        stock_codes = selector.get_stock_codes_from_file(filename)
        
        if not stock_codes:
            raise HTTPException(status_code=404, detail="选股结果文件不存在或无股票数据")
        
        return {
            'success': True,
            'data': {
                'filename': filename,
                'total_stocks': len(stock_codes),
                'stock_codes': stock_codes
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取股票代码列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取股票代码列表失败: {str(e)}")

@router.get("/presets")
async def get_selection_presets():
    """获取预设选股条件"""
    presets = {
        'momentum_stocks': {
            'name': '动量股票',
            'description': '寻找具有强劲上涨动量的股票',
            'criteria': {
                'rsi_min': 50,
                'rsi_max': 80,
                'ma_trend': 'bullish',
                'volume_min': 1.5,
                'price_change_min': 0.02,
                'alpha_factors': ['alpha001', 'alpha003'],
                'exclude_st': True,
                'exclude_new_stock': True
            }
        },
        'value_stocks': {
            'name': '价值股票',
            'description': '寻找被低估的价值股票',
            'criteria': {
                'rsi_min': 20,
                'rsi_max': 50,
                'bb_position': 'lower',
                'price_change_max': 0.05,
                'alpha_factors': ['alpha004', 'alpha005'],
                'exclude_st': True,
                'exclude_new_stock': True
            }
        },
        'breakout_stocks': {
            'name': '突破股票',
            'description': '寻找技术突破的股票',
            'criteria': {
                'bb_position': 'outside',
                'volume_min': 2.0,
                'ma_trend': 'strong_bullish',
                'atr_min': 0.02,
                'alpha_factors': ['alpha001', 'alpha002'],
                'exclude_st': True,
                'exclude_new_stock': True
            }
        },
        'oversold_stocks': {
            'name': '超卖反弹',
            'description': '寻找超卖后可能反弹的股票',
            'criteria': {
                'rsi_max': 30,
                'bb_position': 'lower',
                'price_change_max': -0.03,
                'volume_min': 1.2,
                'alpha_factors': ['alpha004'],
                'exclude_st': True,
                'exclude_new_stock': True
            }
        },
        'high_volatility': {
            'name': '高波动股票',
            'description': '寻找高波动率的活跃股票',
            'criteria': {
                'atr_min': 0.03,
                'volume_min': 1.5,
                'alpha_factors': ['alpha001', 'alpha002', 'alpha003'],
                'exclude_st': True,
                'exclude_new_stock': True
            }
        }
    }
    
    return {
        'success': True,
        'data': {
            'total_presets': len(presets),
            'presets': presets
        }
    }

@router.get("/indicators/{stock_code}")
async def get_stock_indicators(stock_code: str, days: int = Query(30, ge=10, le=100)):
    """获取单只股票的技术指标"""
    try:
        from datetime import timedelta
        
        from backend.data.data_manager import data_manager

        # 获取股票数据
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days + 30)

        df = data_manager.get_stock_data(
            stock_code=stock_code,
            start_date=start_date.strftime('%Y-%m-%d'),
            end_date=end_date.strftime('%Y-%m-%d')
        )
        
        if df is None or len(df) < 10:
            raise HTTPException(status_code=404, detail="股票数据不足")
        
        # 计算技术指标
        selector = get_stock_selector()
        indicators = selector.calculate_technical_indicators(df)
        alpha_factors = selector.calculate_alpha101_factors(df)
        
        # 格式化返回数据
        formatted_indicators = {k: round(v, 4) if isinstance(v, (int, float)) else v 
                              for k, v in indicators.items()}
        formatted_alpha = {k: round(v, 4) for k, v in alpha_factors.items()}
        
        return {
            'success': True,
            'data': {
                'stock_code': stock_code,
                'calculation_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'data_days': len(df),
                'indicators': formatted_indicators,
                'alpha_factors': formatted_alpha
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取股票指标失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取股票指标失败: {str(e)}")

@router.delete("/files/{filename}")
async def delete_selection_file(filename: str):
    """删除选股结果文件"""
    try:
        selector = get_stock_selector()
        filepath = selector.results_dir / filename
        
        if not filepath.exists():
            raise HTTPException(status_code=404, detail="文件不存在")
        
        filepath.unlink()
        
        return {
            'success': True,
            'message': f'文件 {filename} 删除成功'
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除文件失败: {e}")
        raise HTTPException(status_code=500, detail=f"删除文件失败: {str(e)}")

@router.get("/presets/enhanced")
async def get_enhanced_selection_presets():
    """获取增强版选股预设策略"""
    try:
        all_presets = selection_presets.get_all_presets()

        # 转换为API响应格式
        presets_data = []
        for preset_name, preset_info in all_presets.items():
            # 将SelectionCriteria对象转换为字典
            criteria = preset_info['criteria']
            criteria_dict = {}

            # 遍历所有属性
            for attr_name in dir(criteria):
                if not attr_name.startswith('_'):
                    attr_value = getattr(criteria, attr_name)
                    if attr_value is not None:
                        criteria_dict[attr_name] = attr_value

            preset_data = {
                'name': preset_name,
                'display_name': preset_info['display_name'],
                'description': preset_info['description'],
                'category': preset_info['category'],
                'risk_level': preset_info['risk_level'],
                'criteria': criteria_dict
            }
            presets_data.append(preset_data)

        return {
            'success': True,
            'data': presets_data,
            'message': f'获取到 {len(presets_data)} 个增强选股预设'
        }

    except Exception as e:
        logger.error(f"获取增强选股预设失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取增强预设失败: {str(e)}")

@router.get("/presets/categories")
async def get_preset_categories():
    """获取预设策略类别"""
    try:
        categories = selection_presets.get_categories()
        return {
            'success': True,
            'data': categories,
            'message': f'获取到 {len(categories)} 个策略类别'
        }
    except Exception as e:
        logger.error(f"获取策略类别失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取类别失败: {str(e)}")

@router.get("/etf-types")
async def get_etf_types():
    """获取ETF类型列表"""
    try:
        etf_types = [
            {"value": "stock", "label": "股票型ETF", "description": "跟踪股票指数的ETF"},
            {"value": "bond", "label": "债券型ETF", "description": "投资债券市场的ETF"},
            {"value": "commodity", "label": "商品型ETF", "description": "投资商品市场的ETF"},
            {"value": "money", "label": "货币型ETF", "description": "货币市场基金ETF"},
            {"value": "cross_border", "label": "跨境ETF", "description": "投资海外市场的ETF"}
        ]

        return {
            'success': True,
            'data': etf_types,
            'message': f'获取到 {len(etf_types)} 种ETF类型'
        }

    except Exception as e:
        logger.error(f"获取ETF类型失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取ETF类型失败: {str(e)}")
