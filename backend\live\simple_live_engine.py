#!/usr/bin/env python3
"""
简单实盘交易引擎
基于现有回测引擎架构，最大化复用现有代码
"""

import asyncio
import uuid
from datetime import datetime, timedelta
from dataclasses import dataclass
from typing import Dict, List, Any, Optional

import backtrader as bt

from backend.core.logger import get_logger
from backend.strategies.base_strategy_new import get_strategy_class
from backend.stores import QMTStore
from backend.storage.live_trading_storage import live_trading_storage
from backend.storage.strategy_state_manager import strategy_state_manager

logger = get_logger(__name__)

@dataclass
class LiveTradingResult:
    """实盘交易结果"""
    task_id: str
    strategy_name: str
    start_time: str
    current_time: str
    initial_capital: float
    current_capital: float
    total_return: float
    positions: List[Dict[str, Any]]
    orders: List[Dict[str, Any]]
    status: str  # 'running', 'paused', 'error' (实时交易状态，不包含'completed')
    created_at: str
    paper_trading: bool = True  # 交易模式：True=纸上交易，False=实盘交易

class SimpleLiveEngine:
    """
    简单实盘交易引擎
    
    复用现有回测引擎的架构：
    1. 相同的策略注册系统
    2. 相同的参数处理逻辑
    3. 相同的配置管理
    4. 只替换数据源和broker
    """
    
    def __init__(self):
        self.running_strategies = {}  # {task_id: cerebro}
        self.results = {}  # {task_id: LiveTradingResult}
        
        logger.info("🚀 初始化简单实盘交易引擎")
    
    async def start_live_trading(self, config: Dict[str, Any]) -> str:
        """
        启动实盘交易
        
        Args:
            config: 交易配置，格式与回测配置相同
        
        Returns:
            任务ID
        """
        task_id = str(uuid.uuid4())
        logger.info(f"🔄 启动实盘交易任务: {task_id}")
        
        try:
            # 1. 参数处理（完全复用回测引擎的逻辑）
            strategy_name = config.get('strategy_name')
            if not strategy_name:
                raise ValueError("策略名称不能为空")
            
            # 分离系统参数和策略参数（复用回测逻辑）
            system_params = {
                'strategy_name', 'initial_capital', 'commission',
                'stock_codes', 'live_mode', 'paper_trading'
            }
            strategy_params = {k: v for k, v in config.items() if k not in system_params}

            # 构建策略配置（适配ConfigurableStrategy）
            strategy_config = {
                'config': {
                    'parameters': strategy_params
                }
            }
            
            # 2. 获取策略类（完全复用）
            strategy_class = get_strategy_class(strategy_name)
            if not strategy_class:
                raise ValueError(f"未找到策略: {strategy_name}")
            
            logger.info(f"✅ 找到策略类: {strategy_class.__name__}")
            
            # 3. 创建Cerebro实例（与回测相同）
            cerebro = bt.Cerebro()
            
            # 4. 设置初始资金
            initial_capital = config.get('initial_capital', 100000.0)
            
            # 5. 创建QMT Store（关键差异）
            qmt_store = QMTStore()
            
            # 6. 设置broker（使用QMT broker而不是backtrader默认broker）
            cerebro.broker = qmt_store.getbroker(cash=initial_capital)

            # 7. 设置交易模式
            paper_trading = config.get('paper_trading', True)
            logger.info(f"📋 交易模式: {'纸面交易' if paper_trading else '实盘交易'}")

            # 8. 添加数据源（使用QMT数据而不是历史数据）
            # 支持新的股票池配置格式
            stock_codes = []
            stock_universe = config.get('stock_universe')

            if stock_universe and isinstance(stock_universe, dict):
                # 新格式：使用股票池配置
                stock_codes = stock_universe.get('stock_codes', [])
                universe_type = stock_universe.get('type', 'custom')
                universe_name = stock_universe.get('universe_name', '')

                logger.info(f"📋 使用股票池配置: {universe_type}")
                if universe_name:
                    logger.info(f"📋 股票池名称: {universe_name}")
            else:
                # 兼容旧格式：直接使用stock_codes
                stock_codes = config.get('stock_codes', [])
                logger.info(f"📋 使用传统股票代码配置")

            logger.info(f"📋 最终股票代码: {stock_codes}")

            if not stock_codes:
                # 如果没有指定股票，使用默认股票池
                from backend.core.stock_universe_manager import stock_universe_manager
                default_universe = stock_universe_manager.get_recommended_universe("default")
                stock_codes = stock_universe_manager.get_universe_stocks(default_universe)

                if not stock_codes:
                    # 最后的fallback
                    stock_codes = ['000001.SZ', '000002.SZ']

                logger.info(f"📋 使用推荐股票池: {stock_codes}")
            
            for stock_code in stock_codes:
                # 创建实时数据源（历史数据+实时数据模式）
                data = qmt_store.getdata(
                    dataname=stock_code,
                    historical=False,  # 不在这里预加载历史数据，由Live Data自己处理
                    live=True,         # 使用实时数据源（内部会先加载历史数据）
                    timeframe=bt.TimeFrame.Days,   # 日线数据（历史数据用日线，实时可以是分钟）
                    compression=1,     # 1日K线
                    paper_trading=paper_trading  # 传递交易模式
                )
                cerebro.adddata(data, name=stock_code)
                logger.info(f"✅ 添加实时数据源: {stock_code} (历史+实时模式)")
            
            # 8. 添加策略（完全复用回测逻辑）
            cerebro.addstrategy(strategy_class, **strategy_config)
            logger.info(f"✅ 添加策略: {strategy_name}")
            
            # 9. 设置手续费（复用回测逻辑）
            commission = config.get('commission', 0.001)
            cerebro.broker.setcommission(commission=commission)

            # 10. 配置broker为实时模式
            if hasattr(cerebro.broker, 'set_live_mode'):
                cerebro.broker.set_live_mode(True, paper_trading=paper_trading)
                logger.info(f"✅ Broker设置为实时模式: {'纸面交易' if paper_trading else '实盘交易'}")

            # 11. 创建结果记录
            result = LiveTradingResult(
                task_id=task_id,
                strategy_name=strategy_name,
                start_time=datetime.now().isoformat(),
                current_time=datetime.now().isoformat(),
                initial_capital=initial_capital,
                current_capital=initial_capital,
                total_return=0.0,
                positions=[],
                orders=[],
                status='running',
                created_at=datetime.now().isoformat(),
                paper_trading=paper_trading
            )
            
            # 11. 存储任务
            self.running_strategies[task_id] = cerebro
            self.results[task_id] = result

            # 12. 保存策略状态到持久化存储
            strategy_data = {
                'task_id': task_id,
                'strategy_name': strategy_name,
                'status': 'running',
                'start_time': result.start_time,
                'initial_capital': initial_capital,
                'current_capital': initial_capital,
                'total_return': 0.0,
                'paper_trading': paper_trading,
                'stock_codes': config.get('stock_codes', []),
                'strategy_params': config.get('strategy_params', {}),
                'positions': [],
                'orders': [],
                'created_at': result.created_at
            }
            await strategy_state_manager.save_strategy_state(task_id, strategy_data)

            # 13. 异步运行策略
            asyncio.create_task(self._run_strategy_async(task_id, cerebro))
            
            logger.info(f"✅ 实盘交易任务启动成功: {task_id}")
            return task_id
            
        except Exception as e:
            logger.error(f"❌ 启动实盘交易失败: {e}")
            raise
    
    async def _run_strategy_async(self, task_id: str, cerebro: bt.Cerebro):
        """异步运行策略"""
        try:
            logger.info(f"🔄 开始运行策略: {task_id}")

            # 数据预热：等待数据源生成足够的初始数据
            logger.info(f"⏳ 等待数据预热: {task_id}")
            await asyncio.sleep(3)  # 等待3秒让数据源生成初始数据

            # 在单独的线程中运行cerebro（实时模式，持续运行）
            loop = asyncio.get_event_loop()

            # 实时模式需要特殊处理
            def run_live_strategy():
                try:
                    # 启动实时策略，这会持续运行直到被停止
                    # 设置实时模式参数
                    results = cerebro.run(
                        live=True,
                        preload=False,  # 不预加载数据
                        oldbuysell=False,  # 使用新的买卖信号处理
                        runonce=False,  # 不使用runonce模式，适合实时数据
                        exactbars=1  # 只保留最少的历史数据
                    )

                    # 策略正常结束（被停止）
                    logger.info(f"📊 策略正常结束: {task_id}")

                except Exception as e:
                    logger.error(f"❌ 实时策略运行异常: {e}")
                    # 更新策略状态为错误
                    if task_id in self.results:
                        self.results[task_id].status = 'error'
                    raise

            # 在线程池中运行（不等待完成，让它在后台运行）
            import concurrent.futures
            executor = concurrent.futures.ThreadPoolExecutor(max_workers=1)
            future = executor.submit(run_live_strategy)

            # 存储future以便后续停止
            if not hasattr(self, 'strategy_futures'):
                self.strategy_futures = {}
            self.strategy_futures[task_id] = future

            # 不等待完成，让策略在后台持续运行
            logger.info(f"✅ 策略已在后台启动: {task_id}")

            # 监控策略运行状态
            asyncio.create_task(self._monitor_strategy(task_id, future))
            
        except Exception as e:
            import traceback
            error_msg = str(e) if str(e) else f"{type(e).__name__}: {repr(e)}"
            logger.error(f"❌ 策略运行失败: {task_id}, 错误: {error_msg}")
            logger.error(f"详细错误: {traceback.format_exc()}")

            # 分析错误类型并提供建议
            self._analyze_error(e, task_id)

            if task_id in self.results:
                self.results[task_id].status = 'error'

    async def _monitor_strategy(self, task_id: str, future):
        """监控策略运行状态"""
        try:
            # 等待策略完成或被停止
            await asyncio.get_event_loop().run_in_executor(None, future.result)

            # 策略正常完成
            if task_id in self.results:
                self.results[task_id].status = 'completed'
                self.results[task_id].current_time = datetime.now().isoformat()

                # 保存策略历史
                await self._save_strategy_history(task_id, self.results[task_id])

            logger.info(f"✅ 策略运行完成: {task_id}")

        except asyncio.CancelledError:
            logger.info(f"📋 策略监控被取消: {task_id}")
            if task_id in self.results:
                self.results[task_id].status = 'stopped'

        except Exception as e:
            import traceback
            error_msg = str(e) if str(e) else f"{type(e).__name__}: {repr(e)}"
            logger.error(f"❌ 策略监控异常: {task_id}, 错误: {error_msg}")
            logger.error(f"详细错误: {traceback.format_exc()}")

            # 分析错误类型
            self._analyze_error(e, task_id)

            if task_id in self.results:
                self.results[task_id].status = 'error'

            # 如果是deque错误，尝试重启策略
            if 'deque index out of range' in error_msg:
                logger.warning(f"⚠️ 检测到deque错误，策略 {task_id} 可能需要重启")
                # 可以在这里添加自动重启逻辑

    def _analyze_error(self, error, task_id):
        """分析错误并提供建议"""
        error_type = type(error).__name__
        error_msg = str(error)

        suggestions = []

        if "NotImplementedError" in error_type:
            if "getcash" in error_msg or "getvalue" in error_msg or "getposition" in error_msg:
                suggestions.append("🔧 建议：QMTBroker缺少必要方法，需要重启后端服务器")
            elif "buy" in error_msg or "sell" in error_msg:
                suggestions.append("🔧 建议：QMTBroker缺少交易方法，需要重启后端服务器")

        elif "AttributeError" in error_type:
            if "get_notification" in error_msg:
                suggestions.append("🔧 建议：QMTBroker缺少get_notification方法，需要重启后端服务器")
            elif "startingcash" in error_msg:
                suggestions.append("🔧 建议：QMTBroker缺少startingcash属性，需要重启后端服务器")
            elif "_create_order" in error_msg:
                suggestions.append("🔧 建议：QMTBroker缺少_create_order方法，需要重启后端服务器")

        elif "股票代码" in error_msg:
            suggestions.append("🔧 建议：检查股票代码传递逻辑")
            suggestions.append("🔧 建议：确认前端发送的数据格式")

        elif "deque index out of range" in error_msg:
            suggestions.append("🔧 建议：策略尝试访问不存在的数据，这通常是因为：")
            suggestions.append("   - 数据源还没有生成足够的K线数据")
            suggestions.append("   - 指标计算需要更多历史数据")
            suggestions.append("   - 建议增加初始数据生成数量或检查数据访问逻辑")

        elif "IndexError" in error_type:
            suggestions.append("🔧 建议：数组索引超出范围，检查数据访问逻辑")
            suggestions.append("🔧 建议：确保在访问数据前检查数据长度")

        elif "数据" in error_msg:
            suggestions.append("🔧 建议：检查数据源配置")
            suggestions.append("🔧 建议：确认QMT连接状态")

        if suggestions:
            logger.info(f"💡 错误分析 ({task_id}):")
            for suggestion in suggestions:
                logger.info(f"   {suggestion}")
        else:
            logger.info(f"💡 未知错误类型: {error_type}")
            logger.info(f"   建议：检查策略代码或联系开发者")

    async def stop_live_trading(self, task_id: str) -> bool:
        """
        停止实盘交易
        
        Args:
            task_id: 任务ID
        
        Returns:
            是否成功停止
        """
        try:
            if task_id not in self.running_strategies:
                logger.warning(f"⚠️ 任务不存在: {task_id}")
                return False

            logger.info(f"🛑 开始停止策略: {task_id}")

            # 获取策略信息
            strategy_info = self.running_strategies[task_id]

            # 停止后台运行的future
            if hasattr(self, 'strategy_futures') and task_id in self.strategy_futures:
                future = self.strategy_futures[task_id]
                if not future.done():
                    future.cancel()
                    logger.info(f"✅ 策略线程已取消: {task_id}")
                del self.strategy_futures[task_id]

            # 停止数据源
            if hasattr(strategy_info, 'datas'):
                for data in strategy_info.datas:
                    if hasattr(data, 'stop'):
                        data.stop()
                        logger.info(f"✅ 数据源已停止: {getattr(data, 'stock_code', 'unknown')}")

            # 停止broker
            if hasattr(strategy_info, 'broker') and hasattr(strategy_info.broker, 'stop'):
                strategy_info.broker.stop()
                logger.info(f"✅ Broker已停止")

            # 标记策略为停止状态
            if hasattr(strategy_info, '_stop'):
                strategy_info._stop = True

            # 更新状态为暂停（而不是删除）
            if task_id in self.results:
                self.results[task_id].status = 'paused'  # 改为暂停状态
                self.results[task_id].current_time = datetime.now().isoformat()

            # 更新持久化状态
            await strategy_state_manager.update_strategy_status(task_id, 'paused')

            # 注意：不删除running_strategies，保留策略信息以便恢复

            logger.info(f"✅ 实盘交易停止成功: {task_id}")
            return True

        except Exception as e:
            logger.error(f"❌ 停止实盘交易失败: {e}")
            return False

    async def pause_live_trading(self, task_id: str) -> bool:
        """暂停实盘交易"""
        try:
            if task_id not in self.running_strategies:
                logger.warning(f"⚠️ 任务不存在: {task_id}")
                return False

            logger.info(f"⏸️ 暂停策略: {task_id}")

            # 更新内存状态
            if task_id in self.results:
                self.results[task_id].status = 'paused'
                self.results[task_id].current_time = datetime.now().isoformat()

            # 更新持久化状态
            await strategy_state_manager.update_strategy_status(task_id, 'paused')

            logger.info(f"✅ 策略暂停成功: {task_id}")
            return True

        except Exception as e:
            logger.error(f"❌ 暂停策略失败: {e}")
            return False

    async def resume_live_trading(self, task_id: str) -> bool:
        """恢复实盘交易"""
        try:
            if task_id not in self.running_strategies:
                logger.warning(f"⚠️ 任务不存在: {task_id}")
                return False

            logger.info(f"▶️ 恢复策略: {task_id}")

            # 更新内存状态
            if task_id in self.results:
                self.results[task_id].status = 'running'
                self.results[task_id].current_time = datetime.now().isoformat()

            # 更新持久化状态
            await strategy_state_manager.update_strategy_status(task_id, 'running')

            logger.info(f"✅ 策略恢复成功: {task_id}")
            return True

        except Exception as e:
            logger.error(f"❌ 恢复策略失败: {e}")
            return False

    async def delete_live_trading(self, task_id: str) -> bool:
        """删除实盘交易策略"""
        try:
            logger.info(f"🗑️ 删除策略: {task_id}")

            # 从内存中删除
            if task_id in self.running_strategies:
                del self.running_strategies[task_id]
                logger.info(f"✅ 从运行列表中删除: {task_id}")

            if task_id in self.results:
                del self.results[task_id]
                logger.info(f"✅ 从结果列表中删除: {task_id}")

            # 从持久化存储中删除
            success = await strategy_state_manager.delete_strategy_state(task_id)
            if success:
                logger.info(f"✅ 从持久化存储中删除: {task_id}")

            logger.info(f"✅ 策略删除成功: {task_id}")
            return True

        except Exception as e:
            logger.error(f"❌ 删除策略失败: {e}")
            return False

    async def _save_strategy_history(self, task_id: str, strategy_result: Any):
        """保存策略历史"""
        try:
            await live_trading_storage.save_strategy_history(task_id, strategy_result)
        except Exception as e:
            logger.error(f"❌ 保存策略历史失败: {e}")
    
    def restart_live_trading(self, task_id: str) -> bool:
        """
        重启实盘交易

        Args:
            task_id: 任务ID

        Returns:
            是否成功重启
        """
        try:
            if task_id not in self.results:
                logger.warning(f"⚠️ 任务不存在: {task_id}")
                return False

            result = self.results[task_id]

            # 只有停止的策略才能重启
            if result.status != 'stopped':
                logger.warning(f"⚠️ 策略状态不允许重启: {task_id} (当前状态: {result.status})")
                return False

            # 重启策略
            logger.info(f"🔄 重启实盘交易任务: {task_id}")

            # 更新状态
            result.status = 'running'
            result.current_time = datetime.now().isoformat()

            # 重新添加到运行策略列表（这里需要重新创建策略实例）
            # 暂时只更新状态，实际的重启逻辑需要根据具体需求实现

            return True

        except Exception as e:
            logger.error(f"❌ 重启实盘交易失败: {e}")
            return False

    def delete_live_trading(self, task_id: str) -> bool:
        """
        删除实盘交易记录

        Args:
            task_id: 任务ID

        Returns:
            是否成功删除
        """
        try:
            if task_id not in self.results:
                logger.warning(f"⚠️ 任务不存在: {task_id}")
                return False

            result = self.results[task_id]

            # 只有停止的策略才能删除
            if result.status == 'running':
                logger.warning(f"⚠️ 运行中的策略无法删除: {task_id}")
                return False

            # 删除记录
            logger.info(f"🗑️ 删除实盘交易记录: {task_id}")
            del self.results[task_id]

            # 如果在运行策略列表中，也删除
            if task_id in self.running_strategies:
                del self.running_strategies[task_id]

            return True

        except Exception as e:
            logger.error(f"❌ 删除实盘交易失败: {e}")
            return False

    def get_live_trading_result(self, task_id: str) -> Optional[LiveTradingResult]:
        """获取实盘交易结果"""
        return self.results.get(task_id)

    def get_all_live_trading_results(self) -> List[LiveTradingResult]:
        """获取所有实盘交易结果"""
        return list(self.results.values())
    
    def get_running_strategies(self) -> List[str]:
        """获取正在运行的策略列表"""
        return list(self.running_strategies.keys())

# 全局实例
live_engine = SimpleLiveEngine()
