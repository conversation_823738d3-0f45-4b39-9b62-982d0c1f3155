#!/usr/bin/env python3
"""
日志配置文件
设置不同模块的日志级别
"""

import logging

# 日志级别配置
LOG_LEVELS = {
    # 核心模块
    'core': logging.INFO,
    'api': logging.INFO,
    
    # 数据模块 - 减少详细输出
    'data': logging.WARNING,  # 只显示警告和错误
    'data.data_manager': logging.WARNING,
    
    # 选股模块
    'stock_selection': logging.INFO,
    'stock_selection.stock_selector': logging.INFO,
    
    # 策略模块
    'strategies': logging.INFO,
    
    # 交易模块
    'trading': logging.INFO,
    
    # 回测模块
    'backtest': logging.INFO,
    
    # WebSocket模块
    'websocket': logging.INFO,
    
    # 默认级别
    'default': logging.INFO
}

def get_log_level(module_name: str) -> int:
    """
    根据模块名获取日志级别
    
    Args:
        module_name: 模块名称
        
    Returns:
        日志级别
    """
    # 尝试精确匹配
    if module_name in LOG_LEVELS:
        return LOG_LEVELS[module_name]
    
    # 尝试前缀匹配
    for key, level in LOG_LEVELS.items():
        if module_name.startswith(key):
            return level
    
    # 返回默认级别
    return LOG_LEVELS['default']

# 选股过程中的特殊配置
STOCK_SELECTION_CONFIG = {
    # 进度报告间隔（每处理多少只股票报告一次）
    'progress_interval': 100,
    
    # 是否显示详细的股票处理信息
    'show_stock_details': False,
    
    # 是否显示技术指标计算详情
    'show_indicator_details': False,
    
    # 是否显示数据获取详情
    'show_data_details': False
}
