import * as React from 'react'
import { cn } from '../../lib/utils'

export const Input = React.forwardRef(({ 
  className, 
  type = 'text',
  size = 'default',
  error = false,
  prefix,
  suffix,
  ...props 
}, ref) => {
  const sizeClasses = {
    small: 'h-8 px-3 text-sm',
    default: 'h-9 px-3 text-sm',
    large: 'h-10 px-4 text-base'
  }

  const baseClasses = cn(
    'flex w-full rounded-md border border-gray-200 bg-white ring-offset-white file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',
    error && 'border-red-500 focus-visible:ring-red-500',
    sizeClasses[size],
    className
  )

  if (prefix || suffix) {
    return (
      <div className="relative flex items-center">
        {prefix && (
          <div className="absolute left-3 z-10 text-gray-500">
            {prefix}
          </div>
        )}
        <input
          type={type}
          className={cn(
            baseClasses,
            prefix && 'pl-10',
            suffix && 'pr-10'
          )}
          ref={ref}
          {...props}
        />
        {suffix && (
          <div className="absolute right-3 z-10 text-gray-500">
            {suffix}
          </div>
        )}
      </div>
    )
  }

  return (
    <input
      type={type}
      className={baseClasses}
      ref={ref}
      {...props}
    />
  )
})

Input.displayName = 'Input'

export const Textarea = React.forwardRef(({ 
  className, 
  error = false,
  ...props 
}, ref) => {
  return (
    <textarea
      className={cn(
        'flex min-h-[80px] w-full rounded-md border border-gray-200 bg-white px-3 py-2 text-sm ring-offset-white placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',
        error && 'border-red-500 focus-visible:ring-red-500',
        className
      )}
      ref={ref}
      {...props}
    />
  )
})

Textarea.displayName = 'Textarea'
