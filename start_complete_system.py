#!/usr/bin/env python3
"""
完整系统启动脚本
同时启动后端和前端服务器
"""

import subprocess
import time
import os
import sys
import signal
import requests
from threading import Thread

class SystemStarter:
    def __init__(self):
        self.backend_process = None
        self.frontend_process = None
        self.backend_port = 8002
        self.frontend_port = 3000
        
    def check_port(self, port):
        """检查端口是否被占用"""
        try:
            result = subprocess.run(
                ['netstat', '-ano'], 
                capture_output=True, 
                text=True, 
                timeout=10
            )
            return f':{port}' in result.stdout
        except:
            return False
    
    def kill_port_process(self, port):
        """杀掉占用指定端口的进程"""
        try:
            result = subprocess.run(
                ['netstat', '-ano'], 
                capture_output=True, 
                text=True, 
                timeout=10
            )
            
            for line in result.stdout.split('\n'):
                if f':{port}' in line and 'LISTENING' in line:
                    parts = line.split()
                    if len(parts) >= 5:
                        pid = parts[-1]
                        try:
                            subprocess.run(['taskkill', '/F', '/PID', pid], 
                                         capture_output=True, timeout=5)
                            print(f"✅ 已终止端口{port}的进程 (PID: {pid})")
                            return True
                        except:
                            pass
            return False
        except:
            return False
    
    def start_backend(self):
        """启动后端服务器"""
        print(f"🔧 启动后端服务器 (端口{self.backend_port})...")
        
        # 检查端口是否被占用
        if self.check_port(self.backend_port):
            print(f"⚠️ 端口{self.backend_port}被占用，尝试清理...")
            self.kill_port_process(self.backend_port)
            time.sleep(2)
        
        try:
            # 启动后端
            backend_cmd = [
                sys.executable, '-c',
                f"""
import uvicorn
import sys
import os
sys.path.append('{os.getcwd()}')
from backend.api.dev_main import app
print('🚀 后端服务器启动中...')
uvicorn.run(app, host='0.0.0.0', port={self.backend_port}, log_level='info')
"""
            ]
            
            self.backend_process = subprocess.Popen(
                backend_cmd,
                cwd=os.getcwd(),
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1
            )
            
            # 等待后端启动
            print("⏳ 等待后端服务器启动...")
            for i in range(30):  # 最多等待30秒
                try:
                    response = requests.get(f'http://localhost:{self.backend_port}/api/data/status', timeout=2)
                    if response.status_code == 200:
                        print("✅ 后端服务器启动成功")
                        return True
                except:
                    pass
                time.sleep(1)
                print(f"   等待中... ({i+1}/30)")
            
            print("❌ 后端服务器启动超时")
            return False
            
        except Exception as e:
            print(f"❌ 后端启动失败: {e}")
            return False
    
    def start_frontend(self):
        """启动前端服务器"""
        print(f"🌐 启动前端服务器 (端口{self.frontend_port})...")
        
        # 检查端口是否被占用
        if self.check_port(self.frontend_port):
            print(f"⚠️ 端口{self.frontend_port}被占用，尝试清理...")
            self.kill_port_process(self.frontend_port)
            time.sleep(2)
        
        try:
            frontend_dir = os.path.join(os.getcwd(), 'frontend')
            
            if not os.path.exists(frontend_dir):
                print("❌ 前端目录不存在")
                return False
            
            # 设置环境变量
            env = os.environ.copy()
            env['BROWSER'] = 'none'  # 不自动打开浏览器
            
            # 启动前端
            self.frontend_process = subprocess.Popen(
                ['npm', 'start'],
                cwd=frontend_dir,
                env=env,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1
            )
            
            # 等待前端启动
            print("⏳ 等待前端服务器启动...")
            for i in range(60):  # 最多等待60秒
                try:
                    response = requests.get(f'http://localhost:{self.frontend_port}', timeout=2)
                    if response.status_code == 200:
                        print("✅ 前端服务器启动成功")
                        return True
                except:
                    pass
                time.sleep(1)
                if i % 10 == 9:  # 每10秒显示一次进度
                    print(f"   等待中... ({i+1}/60)")
            
            print("❌ 前端服务器启动超时")
            return False
            
        except Exception as e:
            print(f"❌ 前端启动失败: {e}")
            return False
    
    def test_system(self):
        """测试系统功能"""
        print("\n🧪 测试系统功能...")
        
        try:
            # 测试后端API
            response = requests.get(f'http://localhost:{self.backend_port}/api/stock-selection/presets', timeout=5)
            if response.status_code == 200:
                data = response.json()
                presets_count = len(data.get('data', {}).get('presets', {}))
                print(f"✅ 选股API正常 (预设策略: {presets_count}个)")
            else:
                print("❌ 选股API异常")
            
            # 测试前端代理
            response = requests.get(f'http://localhost:{self.frontend_port}/api/data/status', timeout=5)
            if response.status_code == 200:
                print("✅ 前端代理正常")
            else:
                print("❌ 前端代理异常")
                
        except Exception as e:
            print(f"❌ 系统测试失败: {e}")
    
    def cleanup(self):
        """清理进程"""
        print("\n🛑 正在停止服务器...")
        
        if self.backend_process:
            try:
                self.backend_process.terminate()
                self.backend_process.wait(timeout=5)
                print("✅ 后端服务器已停止")
            except:
                try:
                    self.backend_process.kill()
                except:
                    pass
        
        if self.frontend_process:
            try:
                self.frontend_process.terminate()
                self.frontend_process.wait(timeout=5)
                print("✅ 前端服务器已停止")
            except:
                try:
                    self.frontend_process.kill()
                except:
                    pass
    
    def run(self):
        """运行完整系统"""
        print("🚀 QMT-TRADER完整系统启动")
        print("=" * 50)
        
        try:
            # 启动后端
            if not self.start_backend():
                print("❌ 后端启动失败，退出")
                return
            
            # 启动前端
            if not self.start_frontend():
                print("❌ 前端启动失败，但后端仍在运行")
                print(f"📍 后端地址: http://localhost:{self.backend_port}")
                return
            
            # 测试系统
            self.test_system()
            
            # 显示访问信息
            print("\n" + "=" * 50)
            print("✅ 系统启动完成！")
            print(f"🌐 前端地址: http://localhost:{self.frontend_port}")
            print(f"🔧 后端地址: http://localhost:{self.backend_port}")
            print("\n🎯 可用功能:")
            print(f"   - 智能选股: http://localhost:{self.frontend_port}/stock-selection")
            print(f"   - 持仓监控: http://localhost:{self.frontend_port}/position-monitor")
            print(f"   - 策略回测: http://localhost:{self.frontend_port}/backtest")
            print(f"   - 实盘交易: http://localhost:{self.frontend_port}/multi-strategy")
            print(f"   - 数据管理: http://localhost:{self.frontend_port}/data-management")
            print("\n按 Ctrl+C 停止系统")
            
            # 保持运行
            try:
                while True:
                    time.sleep(1)
            except KeyboardInterrupt:
                print("\n收到停止信号...")
                
        except Exception as e:
            print(f"❌ 系统启动失败: {e}")
        finally:
            self.cleanup()

def main():
    starter = SystemStarter()
    
    # 设置信号处理
    def signal_handler(signum, frame):
        starter.cleanup()
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    starter.run()

if __name__ == "__main__":
    main()
