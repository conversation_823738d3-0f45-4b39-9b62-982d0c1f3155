# -*- coding: utf-8 -*-
"""
股票池管理API路由
"""

from fastapi import APIRouter, HTTPException, Query
from typing import List, Dict, Any, Optional
from pydantic import BaseModel
from backend.stock_pool.stock_pool_manager import stock_pool_manager
from backend.core.logger import get_logger

logger = get_logger(__name__)

router = APIRouter(prefix="/api/stock-pool", tags=["股票池管理"])

# 请求模型
class CreatePoolRequest(BaseModel):
    pool_name: str
    description: str = ""
    category: str = "custom"

class UpdatePoolRequest(BaseModel):
    pool_name: Optional[str] = None
    description: Optional[str] = None

class AddStockRequest(BaseModel):
    stock_code: str
    stock_name: str
    added_reason: str = ""
    tags: List[str] = []
    notes: str = ""

class UpdateStockRequest(BaseModel):
    added_reason: Optional[str] = None
    tags: Optional[List[str]] = None
    notes: Optional[str] = None

class AddSelectionResultsRequest(BaseModel):
    selection_results: List[Dict[str, Any]]
    pool_name: Optional[str] = None
    create_new_pool: bool = True

@router.get("/pools")
async def list_pools():
    """获取所有股票池列表"""
    try:
        pools = stock_pool_manager.list_pools()
        return {
            "success": True,
            "data": pools,
            "message": f"获取到{len(pools)}个股票池"
        }
    except Exception as e:
        logger.error(f"获取股票池列表失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/pools")
async def create_pool(request: CreatePoolRequest):
    """创建股票池"""
    try:
        pool_id = stock_pool_manager.create_pool(
            pool_name=request.pool_name,
            description=request.description,
            category=request.category
        )
        
        return {
            "success": True,
            "data": {"pool_id": pool_id},
            "message": f"股票池'{request.pool_name}'创建成功"
        }
    except Exception as e:
        logger.error(f"创建股票池失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/pools/{pool_id}")
async def get_pool(pool_id: str):
    """获取股票池详情"""
    try:
        pool = stock_pool_manager.get_pool(pool_id)
        if not pool:
            raise HTTPException(status_code=404, detail="股票池不存在")
        
        # 转换为字典格式
        pool_dict = {
            "pool_id": pool.pool_id,
            "pool_name": pool.pool_name,
            "description": pool.description,
            "category": pool.category,
            "created_date": pool.created_date,
            "updated_date": pool.updated_date,
            "metadata": pool.metadata,
            "stocks": [
                {
                    "stock_code": stock.stock_code,
                    "stock_name": stock.stock_name,
                    "added_date": stock.added_date,
                    "added_reason": stock.added_reason,
                    "tags": stock.tags,
                    "notes": stock.notes
                }
                for stock in pool.stocks
            ]
        }
        
        return {
            "success": True,
            "data": pool_dict,
            "message": "获取股票池详情成功"
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取股票池详情失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.put("/pools/{pool_id}")
async def update_pool(pool_id: str, request: UpdatePoolRequest):
    """更新股票池基本信息"""
    try:
        success = stock_pool_manager.update_pool(
            pool_id=pool_id,
            pool_name=request.pool_name,
            description=request.description
        )
        
        if not success:
            raise HTTPException(status_code=404, detail="股票池不存在")
        
        return {
            "success": True,
            "message": "股票池信息更新成功"
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新股票池失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/pools/{pool_id}")
async def delete_pool(pool_id: str):
    """删除股票池"""
    try:
        success = stock_pool_manager.delete_pool(pool_id)
        if not success:
            raise HTTPException(status_code=404, detail="股票池不存在")
        
        return {
            "success": True,
            "message": "股票池删除成功"
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除股票池失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/pools/{pool_id}/stocks")
async def add_stock(pool_id: str, request: AddStockRequest):
    """向股票池添加股票"""
    try:
        success = stock_pool_manager.add_stock(
            pool_id=pool_id,
            stock_code=request.stock_code,
            stock_name=request.stock_name,
            added_reason=request.added_reason,
            tags=request.tags,
            notes=request.notes
        )
        
        if not success:
            raise HTTPException(status_code=400, detail="添加股票失败，可能股票池不存在或股票已存在")
        
        return {
            "success": True,
            "message": f"股票{request.stock_code}添加成功"
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"添加股票失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/pools/{pool_id}/stocks/{stock_code}")
async def remove_stock(pool_id: str, stock_code: str):
    """从股票池移除股票"""
    try:
        success = stock_pool_manager.remove_stock(pool_id, stock_code)
        if not success:
            raise HTTPException(status_code=404, detail="股票池不存在或股票不在池中")
        
        return {
            "success": True,
            "message": f"股票{stock_code}移除成功"
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"移除股票失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.put("/pools/{pool_id}/stocks/{stock_code}")
async def update_stock(pool_id: str, stock_code: str, request: UpdateStockRequest):
    """更新股票池中的股票信息"""
    try:
        # 构建更新参数
        update_params = {}
        if request.added_reason is not None:
            update_params["added_reason"] = request.added_reason
        if request.tags is not None:
            update_params["tags"] = request.tags
        if request.notes is not None:
            update_params["notes"] = request.notes
        
        success = stock_pool_manager.update_stock(pool_id, stock_code, **update_params)
        if not success:
            raise HTTPException(status_code=404, detail="股票池不存在或股票不在池中")
        
        return {
            "success": True,
            "message": f"股票{stock_code}信息更新成功"
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新股票信息失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/pools/add-selection-results")
async def add_selection_results(request: AddSelectionResultsRequest):
    """将智能选股结果加入股票池"""
    try:
        result = stock_pool_manager.add_selection_results(
            selection_results=request.selection_results,
            pool_name=request.pool_name,
            create_new_pool=request.create_new_pool
        )
        
        if not result["success"]:
            raise HTTPException(status_code=400, detail=result["message"])
        
        return {
            "success": True,
            "data": result,
            "message": result["message"]
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"添加选股结果失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/pools/{pool_id}/statistics")
async def get_pool_statistics(pool_id: str):
    """获取股票池统计信息"""
    try:
        stats = stock_pool_manager.get_pool_statistics(pool_id)
        if not stats["success"]:
            raise HTTPException(status_code=404, detail=stats["message"])
        
        return {
            "success": True,
            "data": stats["data"],
            "message": "获取统计信息成功"
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取统计信息失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/search")
async def search_stocks(
    query: str = Query(..., description="搜索关键词"),
    pool_id: Optional[str] = Query(None, description="指定股票池ID，为空则搜索所有池")
):
    """在股票池中搜索股票"""
    try:
        results = stock_pool_manager.search_stocks(query, pool_id)
        return {
            "success": True,
            "data": results,
            "message": f"找到{len(results)}个匹配结果"
        }
    except Exception as e:
        logger.error(f"搜索股票失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# 选股结果相关API
@router.get("/selection-results")
async def get_selection_results():
    """获取所有选股结果列表"""
    try:
        import os
        import json
        from datetime import datetime

        selection_dir = "data/stock_selection"
        if not os.path.exists(selection_dir):
            return {
                "success": True,
                "data": [],
                "message": "暂无选股结果"
            }

        results = []
        for filename in os.listdir(selection_dir):
            if filename.endswith('.json') and not filename.startswith('latest-'):
                filepath = os.path.join(selection_dir, filename)
                try:
                    with open(filepath, 'r', encoding='utf-8') as f:
                        data = json.load(f)

                    # 提取基本信息
                    selection_info = data.get('selection_info', {})
                    stocks = data.get('stocks', [])

                    result_info = {
                        "filename": filename,
                        "name": selection_info.get('custom_name', filename.replace('.json', '')),
                        "date": selection_info.get('date', ''),
                        "time": selection_info.get('time', ''),
                        "total_selected": len(stocks),
                        "selection_criteria": selection_info.get('selection_criteria', ''),
                        "file_size": os.path.getsize(filepath),
                        "created_time": datetime.fromtimestamp(os.path.getctime(filepath)).isoformat()
                    }
                    results.append(result_info)

                except Exception as e:
                    logger.warning(f"读取选股结果文件失败 {filename}: {e}")
                    continue

        # 按创建时间倒序排列
        results.sort(key=lambda x: x['created_time'], reverse=True)

        return {
            "success": True,
            "data": results,
            "message": f"获取到{len(results)}个选股结果"
        }

    except Exception as e:
        logger.error(f"获取选股结果列表失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/selection-results/{filename}")
async def get_selection_result_detail(filename: str):
    """获取选股结果详情"""
    try:
        import os
        import json

        # 安全检查文件名
        if '..' in filename or '/' in filename or '\\' in filename:
            raise HTTPException(status_code=400, detail="无效的文件名")

        filepath = os.path.join("data/stock_selection", filename)
        if not os.path.exists(filepath):
            raise HTTPException(status_code=404, detail="选股结果文件不存在")

        with open(filepath, 'r', encoding='utf-8') as f:
            data = json.load(f)

        return {
            "success": True,
            "data": data,
            "message": "获取选股结果详情成功"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取选股结果详情失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# 从选股结果创建股票池的请求模型
class CreatePoolFromSelectionRequest(BaseModel):
    filename: str
    pool_name: Optional[str] = None
    description: Optional[str] = None
    max_stocks: Optional[int] = None  # 最多选择多少只股票
    min_score: Optional[float] = None  # 最低评分要求


# 手动导入选股结果到股票池的请求模型
class ManualImportRequest(BaseModel):
    filename: str
    selected_stocks: List[str]  # 手动选中的股票代码列表
    target_pool_id: Optional[str] = None  # 目标股票池ID，如果为空则创建新池
    pool_name: Optional[str] = None  # 新建池时的名称
    description: Optional[str] = None  # 新建池时的描述
    import_mode: str = "add"  # add: 添加到现有池, replace: 替换池内容, new: 创建新池


# 手动导入选股结果到股票池的请求模型
class ManualImportRequest(BaseModel):
    filename: str
    selected_stocks: List[str]  # 手动选中的股票代码列表
    target_pool_id: Optional[str] = None  # 目标股票池ID，如果为空则创建新池
    pool_name: Optional[str] = None  # 新建池时的名称
    description: Optional[str] = None  # 新建池时的描述
    import_mode: str = "add"  # add: 添加到现有池, replace: 替换池内容, new: 创建新池


# 手动导入选股结果到现有股票池的请求模型
class ImportStocksToPoolRequest(BaseModel):
    pool_id: str
    filename: str
    selected_stocks: List[str]  # 选中的股票代码列表
    import_mode: str = "add"  # add: 添加到现有股票池, replace: 替换股票池内容


@router.post("/pools/from-selection")
async def create_pool_from_selection(request: CreatePoolFromSelectionRequest):
    """从选股结果创建股票池"""
    try:
        import os
        import json
        from datetime import datetime

        # 安全检查文件名
        if '..' in request.filename or '/' in request.filename or '\\' in request.filename:
            raise HTTPException(status_code=400, detail="无效的文件名")

        filepath = os.path.join("data/stock_selection", request.filename)
        if not os.path.exists(filepath):
            raise HTTPException(status_code=404, detail="选股结果文件不存在")

        # 读取选股结果
        with open(filepath, 'r', encoding='utf-8') as f:
            selection_data = json.load(f)

        selection_info = selection_data.get('selection_info', {})
        stocks = selection_data.get('stocks', [])

        # 生成股票池名称和描述
        if not request.pool_name:
            base_name = selection_info.get('custom_name', request.filename.replace('.json', ''))
            pool_name = f"选股结果_{base_name}"
        else:
            pool_name = request.pool_name

        if not request.description:
            description = f"从选股结果导入，原始结果：{len(stocks)}只股票，选股时间：{selection_info.get('date', '')} {selection_info.get('time', '')}"
        else:
            description = request.description

        # 创建股票池
        pool_id = stock_pool_manager.create_pool(
            pool_name=pool_name,
            description=description,
            category="selection"
        )

        # 过滤和排序股票
        filtered_stocks = stocks

        # 按评分过滤
        if request.min_score is not None:
            filtered_stocks = [s for s in filtered_stocks if s.get('score', 0) >= request.min_score]

        # 按评分排序（降序）
        filtered_stocks.sort(key=lambda x: x.get('score', 0), reverse=True)

        # 限制数量
        if request.max_stocks is not None:
            filtered_stocks = filtered_stocks[:request.max_stocks]

        # 添加股票到股票池
        added_count = 0
        skipped_count = 0

        for stock in filtered_stocks:
            stock_code = stock.get('stock_code', '')
            stock_name = stock.get('stock_name', '')
            score = stock.get('score', 0)

            if not stock_code or not stock_name:
                skipped_count += 1
                continue

            # 生成添加原因
            indicators = stock.get('indicators', {})
            key_indicators = []
            if 'rsi' in indicators:
                key_indicators.append(f"RSI: {indicators['rsi']:.1f}")
            if 'macd_trend' in indicators:
                key_indicators.append(f"MACD: {indicators['macd_trend']}")
            if 'ma_trend' in indicators:
                key_indicators.append(f"MA趋势: {indicators['ma_trend']}")

            added_reason = f"智能选股 - 评分: {score:.1f}"
            if key_indicators:
                added_reason += f", {', '.join(key_indicators[:3])}"

            # 生成标签
            tags = ["智能选股"]
            if score >= 80:
                tags.append("高分")
            elif score >= 60:
                tags.append("中等")
            else:
                tags.append("低分")

            # 添加趋势标签
            if indicators.get('ma_trend') == 'strong_bullish':
                tags.append("强势上涨")
            elif indicators.get('ma_trend') == 'bullish':
                tags.append("上涨趋势")

            success = stock_pool_manager.add_stock(
                pool_id=pool_id,
                stock_code=stock_code,
                stock_name=stock_name,
                added_reason=added_reason,
                tags=tags,
                notes=f"选股时间: {selection_info.get('date', '')} {selection_info.get('time', '')}"
            )

            if success:
                added_count += 1
            else:
                skipped_count += 1

        return {
            "success": True,
            "data": {
                "pool_id": pool_id,
                "pool_name": pool_name,
                "total_stocks": len(stocks),
                "filtered_stocks": len(filtered_stocks),
                "added_count": added_count,
                "skipped_count": skipped_count
            },
            "message": f"成功从选股结果创建股票池，添加了{added_count}只股票"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"从选股结果创建股票池失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/pools/manual-import")
async def manual_import_stocks(request: ManualImportRequest):
    """手动导入选中的股票到股票池"""
    try:
        import os
        import json

        # 安全检查文件名
        if '..' in request.filename or '/' in request.filename or '\\' in request.filename:
            raise HTTPException(status_code=400, detail="无效的文件名")

        filepath = os.path.join("data/stock_selection", request.filename)
        if not os.path.exists(filepath):
            raise HTTPException(status_code=404, detail="选股结果文件不存在")

        # 读取选股结果
        with open(filepath, 'r', encoding='utf-8') as f:
            selection_data = json.load(f)

        selection_info = selection_data.get('selection_info', {})
        all_stocks = selection_data.get('stocks', [])

        # 创建股票代码到股票信息的映射
        stock_map = {stock['stock_code']: stock for stock in all_stocks}

        # 验证选中的股票是否存在
        valid_stocks = []
        invalid_stocks = []

        for stock_code in request.selected_stocks:
            if stock_code in stock_map:
                valid_stocks.append(stock_map[stock_code])
            else:
                invalid_stocks.append(stock_code)

        if not valid_stocks:
            raise HTTPException(status_code=400, detail="没有找到有效的股票")

        # 根据导入模式处理
        if request.import_mode == "new" or not request.target_pool_id:
            # 创建新股票池
            if not request.pool_name:
                base_name = selection_info.get('custom_name', request.filename.replace('.json', ''))
                pool_name = f"手动选择_{base_name}"
            else:
                pool_name = request.pool_name

            if not request.description:
                description = f"从选股结果手动导入，原始结果：{len(all_stocks)}只股票，手动选择：{len(valid_stocks)}只股票"
            else:
                description = request.description

            pool_id = stock_pool_manager.create_pool(
                pool_name=pool_name,
                description=description,
                category="selection"
            )

        else:
            # 使用现有股票池
            pool_id = request.target_pool_id

            # 验证股票池是否存在
            pool_info = stock_pool_manager.get_pool(pool_id)
            if not pool_info:
                raise HTTPException(status_code=404, detail="目标股票池不存在")

            # 如果是替换模式，先清空股票池
            if request.import_mode == "replace":
                # 获取现有股票并删除
                existing_stocks = pool_info.get('stocks', [])
                for stock in existing_stocks:
                    stock_pool_manager.remove_stock(pool_id, stock['stock_code'])

        # 添加选中的股票
        added_count = 0
        skipped_count = 0
        error_stocks = []

        for stock in valid_stocks:
            stock_code = stock.get('stock_code', '')
            stock_name = stock.get('stock_name', '')
            score = stock.get('score', 0)

            if not stock_code or not stock_name:
                skipped_count += 1
                continue

            # 生成添加原因
            indicators = stock.get('indicators', {})
            key_indicators = []
            if 'rsi' in indicators:
                key_indicators.append(f"RSI: {indicators['rsi']:.1f}")
            if 'macd_trend' in indicators:
                key_indicators.append(f"MACD: {indicators['macd_trend']}")
            if 'ma_trend' in indicators:
                key_indicators.append(f"MA趋势: {indicators['ma_trend']}")

            added_reason = f"手动选择 - 评分: {score:.1f}"
            if key_indicators:
                added_reason += f", {', '.join(key_indicators[:3])}"

            # 生成标签
            tags = ["手动选择", "智能选股"]
            if score >= 80:
                tags.append("高分")
            elif score >= 60:
                tags.append("中等")
            else:
                tags.append("低分")

            # 添加趋势标签
            if indicators.get('ma_trend') == 'strong_bullish':
                tags.append("强势上涨")
            elif indicators.get('ma_trend') == 'bullish':
                tags.append("上涨趋势")

            success = stock_pool_manager.add_stock(
                pool_id=pool_id,
                stock_code=stock_code,
                stock_name=stock_name,
                added_reason=added_reason,
                tags=tags,
                notes=f"手动从选股结果导入，选股时间: {selection_info.get('date', '')} {selection_info.get('time', '')}"
            )

            if success:
                added_count += 1
            else:
                skipped_count += 1
                error_stocks.append(stock_code)

        # 准备返回结果
        result_data = {
            "pool_id": pool_id,
            "total_selected": len(request.selected_stocks),
            "valid_stocks": len(valid_stocks),
            "invalid_stocks": len(invalid_stocks),
            "added_count": added_count,
            "skipped_count": skipped_count,
            "import_mode": request.import_mode
        }

        if invalid_stocks:
            result_data["invalid_stock_codes"] = invalid_stocks

        if error_stocks:
            result_data["error_stock_codes"] = error_stocks

        message = f"手动导入完成，成功添加{added_count}只股票"
        if skipped_count > 0:
            message += f"，跳过{skipped_count}只股票"
        if invalid_stocks:
            message += f"，{len(invalid_stocks)}只股票代码无效"

        return {
            "success": True,
            "data": result_data,
            "message": message
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"手动导入股票失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))
