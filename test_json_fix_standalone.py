#!/usr/bin/env python3
"""
独立的JSON序列化修复测试
"""

import json
import math
import numpy as np
from dataclasses import dataclass, asdict, is_dataclass
from typing import List, Dict, Any

def safe_json_convert(obj):
    """安全的JSON转换，处理NaN和无穷大值"""
    
    if is_dataclass(obj):
        obj = asdict(obj)
    
    if isinstance(obj, dict):
        return {k: safe_json_convert(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [safe_json_convert(item) for item in obj]
    elif isinstance(obj, (float, np.floating)):
        if math.isnan(obj) or math.isinf(obj):
            return None
        return float(obj)
    elif isinstance(obj, (np.integer, np.int64, np.int32)):
        return int(obj)
    elif isinstance(obj, np.ndarray):
        return safe_json_convert(obj.tolist())
    else:
        return obj

@dataclass
class MockBacktestResult:
    """模拟回测结果数据类"""
    total_return: float
    sharpe_ratio: float
    max_drawdown: float
    win_rate: float
    trades: List[Dict[str, Any]]
    daily_returns: List[float]
    portfolio_values: List[float]

def create_problematic_data():
    """创建包含NaN和无穷大值的测试数据"""
    
    trades = [
        {
            'date': '2024-01-01',
            'symbol': '000001.SZ',
            'action': 'buy',
            'price': 10.5,
            'quantity': 100,
            'return': 0.05
        },
        {
            'date': '2024-01-02',
            'symbol': '000001.SZ',
            'action': 'sell',
            'price': 11.0,
            'quantity': 100,
            'return': float('nan')  # 问题值：NaN
        },
        {
            'date': '2024-01-03',
            'symbol': '600000.SH',
            'action': 'buy',
            'price': 15.0,
            'quantity': 200,
            'return': float('inf')  # 问题值：无穷大
        }
    ]
    
    daily_returns = [0.01, 0.02, float('nan'), -0.01, float('inf'), 0.03]
    portfolio_values = [100000, 101000, 103020, float('nan'), 101990, 105030]
    
    result = MockBacktestResult(
        total_return=0.2262,  # 正常值
        sharpe_ratio=float('nan'),  # 问题值：NaN
        max_drawdown=float('-inf'),  # 问题值：负无穷
        win_rate=0.6667,  # 正常值
        trades=trades,
        daily_returns=daily_returns,
        portfolio_values=portfolio_values
    )
    
    return result

def test_original_serialization():
    """测试原始序列化方法"""
    print("🔍 测试原始序列化方法...")
    
    data = create_problematic_data()
    
    try:
        # 尝试使用原始方法序列化
        dict_data = asdict(data)
        json_str = json.dumps(dict_data)
        print("❌ 原始方法应该失败，但没有失败")
        return False
    except ValueError as e:
        if "Out of range float values are not JSON compliant" in str(e):
            print("✅ 原始方法正确失败：JSON序列化错误")
            return True
        else:
            print(f"❌ 原始方法失败，但错误不同: {e}")
            return False
    except Exception as e:
        print(f"❌ 原始方法出现其他错误: {e}")
        return False

def test_safe_serialization():
    """测试安全序列化方法"""
    print("\n🔧 测试安全序列化方法...")
    
    data = create_problematic_data()
    
    try:
        # 使用安全方法序列化
        safe_data = safe_json_convert(data)
        json_str = json.dumps(safe_data)
        
        print("✅ 安全方法成功序列化")
        
        # 验证结果
        parsed_data = json.loads(json_str)
        
        print("📊 序列化结果验证:")
        print(f"   total_return: {parsed_data['total_return']}")
        print(f"   sharpe_ratio: {parsed_data['sharpe_ratio']} (原值为NaN)")
        print(f"   max_drawdown: {parsed_data['max_drawdown']} (原值为-inf)")
        print(f"   win_rate: {parsed_data['win_rate']}")
        
        # 检查交易数据
        trades = parsed_data['trades']
        print(f"   交易数量: {len(trades)}")
        for i, trade in enumerate(trades):
            print(f"     交易{i+1} return: {trade['return']}")
        
        # 检查日收益率
        daily_returns = parsed_data['daily_returns']
        print(f"   日收益率: {daily_returns}")
        
        # 检查组合价值
        portfolio_values = parsed_data['portfolio_values']
        print(f"   组合价值: {portfolio_values}")
        
        return True
        
    except Exception as e:
        print(f"❌ 安全方法失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_numpy_values():
    """测试numpy值的处理"""
    print("\n🔢 测试numpy值处理...")
    
    try:
        # 创建包含numpy值的数据
        numpy_data = {
            'np_float64': np.float64(3.14159),
            'np_int64': np.int64(42),
            'np_nan': np.nan,
            'np_inf': np.inf,
            'np_array': np.array([1.0, 2.0, np.nan, 4.0]),
            'nested': {
                'np_values': [np.float32(1.5), np.int32(10)]
            }
        }
        
        # 使用安全方法处理
        safe_data = safe_json_convert(numpy_data)
        json_str = json.dumps(safe_data)
        
        print("✅ numpy值处理成功")
        
        # 验证结果
        parsed_data = json.loads(json_str)
        print("📊 numpy值处理结果:")
        for key, value in parsed_data.items():
            print(f"   {key}: {value} (类型: {type(value)})")
        
        return True
        
    except Exception as e:
        print(f"❌ numpy值处理失败: {e}")
        return False

def test_edge_cases():
    """测试边界情况"""
    print("\n🎯 测试边界情况...")
    
    try:
        # 测试各种边界情况
        edge_cases = {
            'normal_float': 3.14159,
            'zero': 0.0,
            'negative': -2.5,
            'very_small': 1e-10,
            'very_large': 1e10,
            'nan': float('nan'),
            'positive_inf': float('inf'),
            'negative_inf': float('-inf'),
            'none_value': None,
            'empty_list': [],
            'empty_dict': {},
            'mixed_list': [1, 2.5, float('nan'), 'text', None]
        }
        
        # 使用安全方法处理
        safe_data = safe_json_convert(edge_cases)
        json_str = json.dumps(safe_data)
        
        print("✅ 边界情况处理成功")
        
        # 验证结果
        parsed_data = json.loads(json_str)
        print("📊 边界情况处理结果:")
        for key, value in parsed_data.items():
            print(f"   {key}: {value}")
        
        return True
        
    except Exception as e:
        print(f"❌ 边界情况处理失败: {e}")
        return False

def main():
    """主函数"""
    print("🎯 JSON序列化修复测试")
    print("=" * 50)
    
    # 运行所有测试
    tests = [
        ("原始序列化方法", test_original_serialization),
        ("安全序列化方法", test_safe_serialization),
        ("numpy值处理", test_numpy_values),
        ("边界情况", test_edge_cases)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results.append((test_name, False))
    
    # 汇总结果
    print(f"\n" + "=" * 50)
    print("📊 测试结果汇总:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎉 总体结果: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("✅ 所有测试通过！JSON序列化修复成功！")
        print("\n💡 修复说明:")
        print("   - NaN值被转换为null")
        print("   - 无穷大值被转换为null")
        print("   - numpy类型被正确转换为Python原生类型")
        print("   - 嵌套结构被递归处理")
        print("   - 保持了原有数据结构")
    else:
        print("❌ 部分测试失败，需要进一步修复")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
