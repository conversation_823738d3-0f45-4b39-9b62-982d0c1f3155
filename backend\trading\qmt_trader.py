#!/usr/bin/env python3
"""
QMT真实交易接口
实现真正的买卖订单功能
"""

from typing import Optional, Dict, Any, List
from datetime import datetime

from backend.core.logger import get_trading_logger

logger = get_trading_logger('qmt_trader')

class QMTTrader:
    """QMT交易器 - 通用交易接口"""

    def __init__(self):
        self.xt_trader = None
        self.account_id = None
        self.is_connected = False
        self.xtconstant = None
        self.StockAccount = None
        self._init_trader()
    
    def _init_trader(self):
        """初始化QMT交易器"""
        logger.info("初始化QMT交易器")
        
        # 尝试导入QMT交易模块
        try:
            # 注意：这些导入需要在QMT环境中才能成功
            import xtquant.xttrader as xttrader
            from xtquant.xttype import StockAccount
            import xtquant.xtconstant as xtconstant

            self.xttrader = xttrader
            self.StockAccount = StockAccount
            self.xtconstant = xtconstant
            
            # 从配置中获取账户信息
            try:
                from backend.core.config_manager import config_manager
                account_configs = config_manager.get_all_live_configs()

                if 'live_trading' in account_configs:
                    live_config = account_configs['live_trading']
                    self.account_id = live_config.account_id
                    logger.info(f"✅ 从配置获取交易账户: {self.account_id}")
                    self.is_connected = True
                    logger.info("✅ QMT交易器初始化成功")
                else:
                    logger.warning("⚠️ 未找到交易账户配置")
                    self.is_connected = False
            except Exception as e:
                logger.error(f"❌ 获取交易账户配置失败: {e}")
                self.is_connected = False
                
        except ImportError as e:
            logger.warning(f"⚠️ QMT交易模块导入失败: {e}")
            logger.warning("请确保在QMT环境中运行")
        except Exception as e:
            logger.error(f"❌ QMT交易器初始化失败: {e}")
    
    def sell_stock(self, stock_code: str, quantity: int, price: Optional[float] = None) -> Dict[str, Any]:
        """
        卖出股票
        
        Args:
            stock_code: 股票代码
            quantity: 卖出数量
            price: 卖出价格（None为市价）
        
        Returns:
            交易结果字典
        """
        logger.info(f"🔥 执行卖出: {stock_code}, 数量: {quantity}, 价格: {price}")
        
        if not self.is_connected:
            return {
                'success': False,
                'message': 'QMT交易服务器未连接',
                'order_id': None
            }
        
        if not self.account_id:
            return {
                'success': False,
                'message': '未获取到交易账户',
                'order_id': None
            }
        
        try:
            # 创建账户对象
            account = self.StockAccount(self.account_id)
            
            # 确定订单类型和价格
            if price is None:
                # 市价卖出
                price_type = self.xtconstant.MARKET_PRICE if hasattr(self.xtconstant, 'MARKET_PRICE') else 1
                order_price = 0  # 市价单价格为0
                logger.info(f"📊 提交市价卖出订单")
            else:
                # 限价卖出
                price_type = self.xtconstant.FIX_PRICE if hasattr(self.xtconstant, 'FIX_PRICE') else 0
                order_price = price
                logger.info(f"📊 提交限价卖出订单，价格: {price}")

            # 提交卖出订单 - 需要通过xt_trader实例调用
            # 从data_manager获取已初始化的xt_trader实例
            from backend.data.data_manager import data_manager

            if hasattr(data_manager, 'xt_trader') and data_manager.xt_trader:
                # 使用data_manager中已初始化的xt_trader实例
                # 使用正确的常量值
                stock_sell = self.xtconstant.STOCK_SELL if hasattr(self.xtconstant, 'STOCK_SELL') else 1

                order_id = data_manager.xt_trader.order_stock(
                    account,                           # StockAccount 资金账号
                    stock_code,                        # str 证券代码
                    stock_sell,                        # int 委托类型 (卖出)
                    quantity,                          # int 委托数量
                    price_type,                        # int 报价类型 (限价/市价)
                    order_price,                       # float 委托价格
                    'auto_stop_loss',                  # str 策略名称
                    f'止损卖出-{stock_code}'            # str 委托备注
                )
            else:
                raise Exception("xt_trader实例不可用")
            
            # 根据官方文档：成功委托后的订单编号为大于0的正整数，如果为-1表示委托失败
            if order_id and order_id > 0:
                logger.info(f"✅ 卖出订单提交成功: 订单号 {order_id}")

                # 记录交易日志
                self._log_order(stock_code, 'SELL', quantity, order_price, order_id, True)

                return {
                    'success': True,
                    'message': f'卖出订单提交成功',
                    'order_id': order_id,
                    'stock_code': stock_code,
                    'quantity': quantity,
                    'price': order_price,
                    'timestamp': datetime.now().isoformat()
                }
            elif order_id == -1:
                logger.error(f"❌ 卖出订单委托失败: {stock_code}")
                self._log_order(stock_code, 'SELL', quantity, order_price, None, False, "委托失败")
                return {
                    'success': False,
                    'message': f'卖出订单委托失败',
                    'order_id': None
                }
            else:
                logger.error(f"❌ 卖出订单提交异常: 返回订单号 {order_id}")
                self._log_order(stock_code, 'SELL', quantity, order_price, None, False, f"异常订单号: {order_id}")
                return {
                    'success': False,
                    'message': f'卖出订单提交异常: 订单号 {order_id}',
                    'order_id': None
                }
                
        except Exception as e:
            logger.error(f"❌ 卖出订单异常: {e}")
            self._log_order(stock_code, 'SELL', quantity, price or 0, None, False, str(e))
            
            return {
                'success': False,
                'message': f'卖出订单异常: {e}',
                'order_id': None
            }
    
    def query_orders(self, cancelable_only: bool = False) -> List[Dict[str, Any]]:
        """查询当日所有委托"""
        if not self.is_connected:
            return []

        try:
            from backend.data.data_manager import data_manager
            account = self.StockAccount(self.account_id)

            orders = data_manager.xt_trader.query_stock_orders(account, cancelable_only)

            if not orders:
                return []

            order_list = []
            for order in orders:
                order_info = {
                    'order_id': getattr(order, 'order_id', None),
                    'stock_code': getattr(order, 'stock_code', ''),
                    'order_type': getattr(order, 'order_type', 0),  # 0=买入, 1=卖出
                    'order_volume': getattr(order, 'order_volume', 0),
                    'price': getattr(order, 'price', 0.0),
                    'filled_volume': getattr(order, 'filled_volume', 0),
                    'order_status': getattr(order, 'order_status', 0),  # 订单状态
                    'order_time': getattr(order, 'order_time', ''),
                    'strategy_name': getattr(order, 'strategy_name', ''),
                    'order_remark': getattr(order, 'order_remark', '')
                }
                order_list.append(order_info)

            return order_list

        except Exception as e:
            logger.error(f"查询委托失败: {e}")
            return []

    def query_trades(self) -> List[Dict[str, Any]]:
        """查询当日所有成交"""
        if not self.is_connected:
            return []

        try:
            from backend.data.data_manager import data_manager
            account = self.StockAccount(self.account_id)

            trades = data_manager.xt_trader.query_stock_trades(account)

            if not trades:
                return []

            trade_list = []
            for trade in trades:
                trade_info = {
                    'order_id': getattr(trade, 'order_id', None),
                    'stock_code': getattr(trade, 'stock_code', ''),
                    'order_type': getattr(trade, 'order_type', 0),
                    'traded_volume': getattr(trade, 'traded_volume', 0),
                    'traded_price': getattr(trade, 'traded_price', 0.0),
                    'traded_time': getattr(trade, 'traded_time', ''),
                    'strategy_name': getattr(trade, 'strategy_name', ''),
                    'order_remark': getattr(trade, 'order_remark', '')
                }
                trade_list.append(trade_info)

            return trade_list

        except Exception as e:
            logger.error(f"查询成交失败: {e}")
            return []

    def get_order_status(self, order_id: int) -> Dict[str, Any]:
        """查询特定订单状态"""
        orders = self.query_orders()

        for order in orders:
            if order['order_id'] == order_id:
                return {
                    'found': True,
                    'order_status': order['order_status'],
                    'filled_volume': order['filled_volume'],
                    'order_volume': order['order_volume'],
                    'is_filled': order['filled_volume'] >= order['order_volume'],
                    'is_partial': 0 < order['filled_volume'] < order['order_volume'],
                    'order_info': order
                }

        return {'found': False, 'message': '订单未找到'}

    def get_account_info(self) -> Optional[Dict[str, Any]]:
        """获取账户信息"""
        if not self.is_connected:
            logger.warning("⚠️ QMT未连接，无法获取账户信息")
            return None

        try:
            from backend.data.data_manager import data_manager
            account = self.StockAccount(self.account_id)

            # 获取账户资金信息
            if hasattr(data_manager, 'xt_trader') and data_manager.xt_trader:
                account_data = data_manager.xt_trader.query_stock_asset(account)

                if account_data:
                    return {
                        'account_id': self.account_id,
                        'total_asset': getattr(account_data, 'total_asset', 0.0),
                        'available_cash': getattr(account_data, 'cash', 0.0),
                        'market_value': getattr(account_data, 'market_value', 0.0),
                        'frozen_cash': getattr(account_data, 'frozen_cash', 0.0),
                        'profit_loss': getattr(account_data, 'profit_loss', 0.0),
                        'updated_at': datetime.now().isoformat()
                    }
                else:
                    logger.warning("⚠️ 未获取到账户资金数据")
                    return None
            else:
                logger.warning("⚠️ xt_trader实例不可用")
                return None

        except Exception as e:
            logger.error(f"❌ 获取账户信息失败: {e}")
            return None

    def get_positions(self) -> List[Dict[str, Any]]:
        """获取持仓信息"""
        if not self.is_connected:
            logger.warning("⚠️ QMT未连接，无法获取持仓信息")
            return []

        try:
            from backend.data.data_manager import data_manager
            account = self.StockAccount(self.account_id)

            # 获取持仓信息
            if hasattr(data_manager, 'xt_trader') and data_manager.xt_trader:
                positions_data = data_manager.xt_trader.query_stock_positions(account)

                if not positions_data:
                    return []

                positions = []
                for pos in positions_data:
                    position_info = {
                        'stock_code': getattr(pos, 'stock_code', ''),
                        'stock_name': getattr(pos, 'stock_name', ''),
                        'quantity': getattr(pos, 'volume', 0),
                        'available_quantity': getattr(pos, 'can_use_volume', 0),
                        'avg_price': getattr(pos, 'avg_price', 0.0),
                        'current_price': getattr(pos, 'price', 0.0),
                        'market_value': getattr(pos, 'market_value', 0.0),
                        'profit_loss': getattr(pos, 'profit_loss', 0.0),
                        'profit_loss_ratio': getattr(pos, 'profit_loss_ratio', 0.0)
                    }
                    positions.append(position_info)

                return positions
            else:
                logger.warning("⚠️ xt_trader实例不可用")
                return []

        except Exception as e:
            logger.error(f"❌ 获取持仓信息失败: {e}")
            return []

    def buy(self, stock_code: str, quantity: int, price: Optional[float] = None) -> Optional[int]:
        """
        买入股票

        Args:
            stock_code: 股票代码
            quantity: 买入数量
            price: 买入价格（None为市价）

        Returns:
            订单ID，失败返回None
        """
        logger.info(f"📈 执行买入: {stock_code}, 数量: {quantity}, 价格: {price}")

        if not self.is_connected:
            logger.error("❌ QMT交易服务器未连接")
            return None

        if not self.account_id:
            logger.error("❌ 未获取到交易账户")
            return None

        try:
            from backend.data.data_manager import data_manager
            account = self.StockAccount(self.account_id)

            # 确定价格类型和价格
            if price is None:
                # 市价单
                price_type = self.xtconstant.FIX_NONE if hasattr(self.xtconstant, 'FIX_NONE') else 0
                order_price = 0.0
            else:
                # 限价单
                price_type = self.xtconstant.FIX_PRICE if hasattr(self.xtconstant, 'FIX_PRICE') else 1
                order_price = price

            if hasattr(data_manager, 'xt_trader') and data_manager.xt_trader:
                # 使用正确的常量值
                stock_buy = self.xtconstant.STOCK_BUY if hasattr(self.xtconstant, 'STOCK_BUY') else 0

                order_id = data_manager.xt_trader.order_stock(
                    account,                           # StockAccount 资金账号
                    stock_code,                        # str 证券代码
                    stock_buy,                         # int 委托类型 (买入)
                    quantity,                          # int 委托数量
                    price_type,                        # int 报价类型 (限价/市价)
                    order_price,                       # float 委托价格
                    'strategy_trading',                # str 策略名称
                    f'策略买入-{stock_code}'            # str 委托备注
                )

                if order_id and order_id > 0:
                    logger.info(f"✅ 买入订单提交成功: 订单号 {order_id}")
                    self._log_order(stock_code, 'BUY', quantity, order_price, order_id, True)
                    return order_id
                else:
                    logger.error(f"❌ 买入订单提交失败: 返回订单号 {order_id}")
                    self._log_order(stock_code, 'BUY', quantity, order_price, None, False, f"异常订单号: {order_id}")
                    return None
            else:
                raise Exception("xt_trader实例不可用")

        except Exception as e:
            logger.error(f"❌ 买入订单异常: {e}")
            self._log_order(stock_code, 'BUY', quantity, price or 0, None, False, str(e))
            return None

    def sell(self, stock_code: str, quantity: int, price: Optional[float] = None) -> Optional[int]:
        """
        卖出股票

        Args:
            stock_code: 股票代码
            quantity: 卖出数量
            price: 卖出价格（None为市价）

        Returns:
            订单ID，失败返回None
        """
        # 直接调用现有的sell_stock方法
        result = self.sell_stock(stock_code, quantity, price)
        if result['success']:
            return result.get('order_id')
        else:
            return None
    
    def cancel_order(self, order_id: int) -> Dict[str, Any]:
        """撤销订单（通过订单ID）"""
        if not self.is_connected:
            return {'success': False, 'message': 'QMT未连接'}

        try:
            from backend.data.data_manager import data_manager
            account = self.StockAccount(self.account_id)

            result = data_manager.xt_trader.cancel_order_stock(account, order_id)
            logger.info(f"撤销订单 {order_id}: {result}")

            return {
                'success': result == 0,
                'message': '撤单成功' if result == 0 else f'撤单失败，错误代码: {result}',
                'order_id': order_id
            }
        except Exception as e:
            logger.error(f"撤销订单失败: {e}")
            return {'success': False, 'message': str(e), 'order_id': order_id}

    def cancel_order_by_sysid(self, market: int, order_sysid: str) -> Dict[str, Any]:
        """撤销订单（通过系统合同编号）"""
        if not self.is_connected:
            return {'success': False, 'message': 'QMT未连接'}

        try:
            from backend.data.data_manager import data_manager
            account = self.StockAccount(self.account_id)

            result = data_manager.xt_trader.cancel_order_stock_sysid(account, market, order_sysid)
            logger.info(f"撤销订单 {order_sysid}: {result}")

            return {
                'success': result == 0,
                'message': '撤单成功' if result == 0 else f'撤单失败，错误代码: {result}',
                'order_sysid': order_sysid
            }
        except Exception as e:
            logger.error(f"撤销订单失败: {e}")
            return {'success': False, 'message': str(e), 'order_sysid': order_sysid}
    
    def _log_order(self, stock_code: str, action: str, quantity: int, price: float, 
                   order_id: Optional[int], success: bool, error_msg: str = ""):
        """记录订单日志"""
        status = "成功" if success else "失败"
        log_msg = f"订单{status}: {action} {stock_code}, 数量: {quantity}, 价格: {price}"
        
        if order_id:
            log_msg += f", 订单号: {order_id}"
        
        if error_msg:
            log_msg += f", 错误: {error_msg}"
        
        if success:
            logger.info(log_msg)
        else:
            logger.error(log_msg)

# 全局交易器实例
qmt_trader = QMTTrader()
