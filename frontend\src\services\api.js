import axios from 'axios';

// 创建axios实例
const api = axios.create({
  baseURL: '/api',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    console.log('API Request:', config.method?.toUpperCase(), config.url);
    return config;
  },
  (error) => {
    console.error('API Request Error:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    console.log('API Response:', response.status, response.config.url);
    return response.data;
  },
  (error) => {
    console.error('API Response Error:', error.response?.status, error.response?.data);
    
    // 统一错误处理
    const message = error.response?.data?.detail || error.message || '请求失败';
    
    return Promise.reject({
      status: error.response?.status,
      message,
      data: error.response?.data
    });
  }
);

// 配置管理API
export const configAPI = {
  // 策略配置
  getStrategyConfigs: () => api.get('/config/strategies'),
  getStrategyConfig: (name) => api.get(`/config/strategies/${name}`),
  updateStrategyConfig: (name, config) => api.post(`/config/strategies/${name}`, { config_data: config }),
  saveStrategyConfig: (name, config) => api.post(`/config/strategies/${name}`, { config_data: config }),

  // 回测配置
  getBacktestConfigs: () => api.get('/config/backtest'),

  // 实盘配置
  getLiveConfigs: () => api.get('/config/live'),
};

// 数据管理API
export const dataAPI = {
  // 股票列表 - 支持分页
  getStockList: (params = {}) => {
    const { page = 1, page_size = 100, limit = null } = params;

    // 确保参数是数字类型
    const pageNum = Number(page) || 1;
    const pageSizeNum = Number(page_size) || 100;

    console.log('getStockList called with:', {
      original: params,
      processed: { page: pageNum, page_size: pageSizeNum, limit }
    });

    const queryParams = new URLSearchParams();
    queryParams.append('page', pageNum.toString());
    queryParams.append('page_size', pageSizeNum.toString());
    if (limit !== null) {
      queryParams.append('limit', limit.toString());
    }

    const url = `/data/stocks?${queryParams.toString()}`;
    console.log('Final API URL:', url);

    return api.get(url);
  },
  
  // 数据状态
  getDataStatus: () => api.get('/data/status'),
  
  // 下载数据
  downloadData: (stockCodes, startDate, endDate) => 
    api.post('/data/download', {
      stock_codes: stockCodes,
      start_date: startDate,
      end_date: endDate
    }),
  
  // 获取股票数据
  getStockData: (stockCode, startDate, endDate) =>
    api.get(`/data/${stockCode}`, {
      params: {
        start_date: startDate,
        end_date: endDate
      }
    }),

  // 股票池管理
  getStockUniverses: () => api.get('/data/universes'),
  getStockUniverse: (universeName) => api.get(`/data/universes/${universeName}`),
  createStockUniverse: (universeData) => api.post('/data/universes', universeData),
  updateStockUniverse: (universeName, universeData) => api.put(`/data/universes/${universeName}`, universeData),
  deleteStockUniverse: (universeName) => api.delete(`/data/universes/${universeName}`),
};

// 回测API
export const backtestAPI = {
  // 启动回测
  startBacktest: (config) => api.post('/backtest/start', config),

  // 获取回测状态
  getBacktestStatus: () => api.get('/backtest/status'),

  // 获取回测结果
  getBacktestResults: (taskId) => api.get(`/backtest/results/${taskId}`),

  // 停止回测
  stopBacktest: () => api.post('/backtest/stop'),

  // 获取可用策略列表
  getAvailableStrategies: () => api.get('/backtest/strategies'),
};

// 实盘交易API
export const liveAPI = {
  // 启动实盘交易（新的QMT Store模式）
  startLiveTrading: (request) =>
    api.post('/live/start', request),

  // 暂停实盘交易
  pauseLiveTrading: (taskId) =>
    api.post(`/live/pause/${taskId}`),

  // 恢复实盘交易
  resumeLiveTrading: (taskId) =>
    api.post(`/live/resume/${taskId}`),

  // 停止实盘交易（现在改为暂停）
  stopLiveTrading: (taskId) =>
    api.post(`/live/stop/${taskId}`),

  // 重启实盘交易
  restartLiveTrading: (taskId) =>
    api.post(`/live/restart/${taskId}`),

  // 删除实盘交易
  deleteLiveTrading: (taskId) =>
    api.delete(`/live/delete/${taskId}`),

  // 获取实盘交易结果
  getLiveTradingResult: (taskId) =>
    api.get(`/live/result/${taskId}`),

  // 获取所有实盘交易结果
  getAllLiveTradingResults: () =>
    api.get('/live/results'),

  // 获取正在运行的策略
  getRunningStrategies: () =>
    api.get('/live/running'),

  // 获取账户信息（保留兼容性）
  getAccountInfo: () => api.get('/live/account'),

  // 兼容旧版本的启动方法
  startLiveTradingLegacy: (strategyName, strategyConfig, liveConfig) =>
    api.post('/live/start', {
      strategy_name: strategyName,
      strategy_config: strategyConfig,
      live_config: liveConfig
    }),
};

// 通用API工具
export const apiUtils = {
  // 处理API错误
  handleError: (error, defaultMessage = '操作失败') => {
    console.error('API Error:', error);

    // 如果是字符串，直接返回
    if (typeof error === 'string') {
      return error;
    }

    // 如果有message属性，返回message
    if (error && error.message) {
      return error.message;
    }

    // 如果是HTTP错误响应
    if (error && error.response && error.response.data) {
      const data = error.response.data;
      if (typeof data === 'string') {
        return data;
      }
      if (data.detail) {
        return data.detail;
      }
      if (data.message) {
        return data.message;
      }
    }

    // 如果是对象，尝试序列化
    if (typeof error === 'object' && error !== null) {
      try {
        return JSON.stringify(error);
      } catch (e) {
        return defaultMessage;
      }
    }

    return defaultMessage;
  },
  
  // 检查响应是否成功
  isSuccess: (response) => {
    return response && response.success === true;
  },
  
  // 获取响应数据
  getData: (response) => {
    return response?.data || null;
  },
  
  // 获取响应消息
  getMessage: (response) => {
    return response?.message || '';
  }
};

// 股票池管理API
export const stockPoolAPI = {
  // 获取用户创建的股票池列表
  getStockPools: () => api.get('/stock-pool/pools'),

  // 获取股票池详情
  getStockPool: (poolId) => api.get(`/stock-pool/pools/${poolId}`),

  // 创建股票池
  createStockPool: (data) => api.post('/stock-pool/pools', data),

  // 更新股票池
  updateStockPool: (poolId, data) => api.put(`/stock-pool/pools/${poolId}`, data),

  // 删除股票池
  deleteStockPool: (poolId) => api.delete(`/stock-pool/pools/${poolId}`),

  // 向股票池添加股票
  addStock: (poolId, data) => api.post(`/stock-pool/pools/${poolId}/stocks`, data),

  // 从股票池移除股票
  removeStock: (poolId, stockCode) => api.delete(`/stock-pool/pools/${poolId}/stocks/${stockCode}`),

  // 更新股票池中的股票信息
  updateStock: (poolId, stockCode, data) => api.put(`/stock-pool/pools/${poolId}/stocks/${stockCode}`, data)
};

export default api;
