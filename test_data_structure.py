#!/usr/bin/env python3
"""
测试数据结构，验证股票列表返回格式
"""

import sys
import os
import logging

# 添加项目路径
sys.path.append(os.path.dirname(__file__))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

def test_stock_list_structure():
    """测试股票列表数据结构"""
    logger.info("=== 测试股票列表数据结构 ===")
    
    try:
        # 尝试导入data_manager
        from backend.data.data_manager import data_manager
        logger.info("✅ data_manager导入成功")
        
        # 获取股票列表
        logger.info("正在获取股票列表...")
        stock_list_response = data_manager.get_stock_list(page=1, page_size=5)
        
        # 打印完整的响应结构
        logger.info("📊 完整响应结构:")
        logger.info(f"类型: {type(stock_list_response)}")
        logger.info(f"键: {list(stock_list_response.keys()) if isinstance(stock_list_response, dict) else 'Not a dict'}")
        
        # 检查data字段
        if 'data' in stock_list_response:
            data = stock_list_response['data']
            logger.info(f"data字段类型: {type(data)}")
            logger.info(f"data长度: {len(data) if hasattr(data, '__len__') else 'No length'}")
            
            if isinstance(data, list) and len(data) > 0:
                logger.info("✅ data是列表格式")
                logger.info(f"第一个元素类型: {type(data[0])}")
                logger.info(f"第一个元素内容: {data[0]}")
                
                # 检查股票对象结构
                if isinstance(data[0], dict):
                    logger.info(f"股票对象键: {list(data[0].keys())}")
                    
                    # 测试访问方式
                    logger.info("\n--- 测试数据访问 ---")
                    all_stocks = stock_list_response['data']  # 正确的访问方式
                    logger.info(f"✅ 正确访问: all_stocks = stock_list_response['data']")
                    logger.info(f"获取到{len(all_stocks)}只股票")
                    
                    # 显示前3只股票
                    for i, stock in enumerate(all_stocks[:3]):
                        logger.info(f"  {i+1}. {stock}")
                        
                else:
                    logger.warning("⚠️ 股票对象不是字典格式")
            else:
                logger.warning("⚠️ data不是列表或为空")
        else:
            logger.error("❌ 响应中没有data字段")
            
        # 测试其他字段
        other_fields = ['total', 'page', 'page_size', 'total_pages']
        for field in other_fields:
            if field in stock_list_response:
                logger.info(f"{field}: {stock_list_response[field]}")
            else:
                logger.warning(f"⚠️ 缺少字段: {field}")
                
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False

def test_stock_data_access():
    """测试股票数据访问"""
    logger.info("\n=== 测试股票数据访问 ===")
    
    try:
        from backend.data.data_manager import data_manager
        
        # 获取股票列表
        stock_list_response = data_manager.get_stock_list(page=1, page_size=3)
        all_stocks = stock_list_response['data']
        
        if not all_stocks:
            logger.error("❌ 没有获取到股票数据")
            return False
            
        # 测试第一只股票的历史数据获取
        test_stock = all_stocks[0]
        stock_code = test_stock['code']
        stock_name = test_stock['name']
        
        logger.info(f"测试股票: {stock_name}({stock_code})")
        
        # 设置测试日期
        from datetime import datetime, timedelta
        end_date = datetime.now().strftime('%Y-%m-%d')
        start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
        
        logger.info(f"测试日期范围: {start_date} 到 {end_date}")
        
        # 获取历史数据
        df = data_manager.get_stock_data(
            stock_code=stock_code,
            start_date=start_date,
            end_date=end_date
        )
        
        logger.info(f"✅ 成功获取{stock_code}数据: {len(df)}条记录")
        logger.info(f"数据列: {list(df.columns)}")
        
        if len(df) > 0:
            logger.info(f"最新数据: {df.iloc[-1].to_dict()}")
            
        return True
        
    except Exception as e:
        logger.error(f"❌ 股票数据访问测试失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False

def main():
    """主测试函数"""
    logger.info("🔍 开始数据结构测试")
    
    # 测试1: 股票列表结构
    structure_ok = test_stock_list_structure()
    
    if structure_ok:
        # 测试2: 股票数据访问
        data_ok = test_stock_data_access()
        
        if data_ok:
            logger.info("\n🎉 所有测试通过！")
            logger.info("✅ 数据结构正确")
            logger.info("✅ 数据访问正常")
            logger.info("✅ 回测引擎应该可以正常工作")
        else:
            logger.error("\n❌ 股票数据访问测试失败")
    else:
        logger.error("\n❌ 数据结构测试失败")

if __name__ == "__main__":
    main()
