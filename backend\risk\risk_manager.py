#!/usr/bin/env python3
"""
风险管理模块
为实盘交易提供风险控制功能
"""

from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from datetime import datetime, timedelta
from enum import Enum

from backend.core.logger import get_logger

logger = get_logger(__name__)

class RiskLevel(Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

@dataclass
class RiskAlert:
    """风险告警"""
    level: RiskLevel
    message: str
    timestamp: datetime
    strategy_id: str
    metric_name: str
    current_value: float
    threshold_value: float
    
    def to_dict(self):
        return {
            'level': self.level.value,
            'message': self.message,
            'timestamp': self.timestamp.isoformat(),
            'strategy_id': self.strategy_id,
            'metric_name': self.metric_name,
            'current_value': self.current_value,
            'threshold_value': self.threshold_value
        }

@dataclass
class RiskMetrics:
    """风险指标"""
    strategy_id: str
    current_drawdown: float = 0.0
    max_drawdown: float = 0.0
    daily_pnl: float = 0.0
    total_pnl: float = 0.0
    position_count: int = 0
    position_concentration: float = 0.0  # 最大单股持仓比例
    leverage: float = 1.0
    var_1d: float = 0.0  # 1日风险价值
    sharpe_ratio: float = 0.0
    
    def to_dict(self):
        return {
            'strategy_id': self.strategy_id,
            'current_drawdown': self.current_drawdown,
            'max_drawdown': self.max_drawdown,
            'daily_pnl': self.daily_pnl,
            'total_pnl': self.total_pnl,
            'position_count': self.position_count,
            'position_concentration': self.position_concentration,
            'leverage': self.leverage,
            'var_1d': self.var_1d,
            'sharpe_ratio': self.sharpe_ratio
        }

class RiskManager:
    """
    风险管理器
    
    提供实盘交易的风险控制功能：
    1. 资金风险控制
    2. 持仓风险控制
    3. 交易频率控制
    4. 异常检测和告警
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        
        # 风险阈值配置
        self.max_drawdown_threshold = self.config.get('max_drawdown_threshold', 0.10)  # 最大回撤10%
        self.max_daily_loss_threshold = self.config.get('max_daily_loss_threshold', 0.05)  # 日最大亏损5%
        self.max_position_concentration = self.config.get('max_position_concentration', 0.20)  # 单股最大20%
        self.max_positions = self.config.get('max_positions', 10)  # 最大持仓数
        self.max_leverage = self.config.get('max_leverage', 1.0)  # 最大杠杆
        
        # 风险指标存储
        self.risk_metrics: Dict[str, RiskMetrics] = {}
        self.risk_alerts: List[RiskAlert] = []
        
        # 交易频率控制
        self.trade_counts: Dict[str, List[datetime]] = {}  # {strategy_id: [trade_times]}
        self.max_trades_per_hour = self.config.get('max_trades_per_hour', 10)
        
        logger.info("🛡️ 风险管理器初始化完成")
        logger.info(f"   最大回撤阈值: {self.max_drawdown_threshold*100:.1f}%")
        logger.info(f"   日最大亏损阈值: {self.max_daily_loss_threshold*100:.1f}%")
        logger.info(f"   最大持仓数: {self.max_positions}")
    
    def update_risk_metrics(self, strategy_id: str, portfolio_data: Dict[str, Any]) -> RiskMetrics:
        """更新风险指标"""
        try:
            # 计算风险指标
            metrics = RiskMetrics(strategy_id=strategy_id)
            
            # 基本财务指标
            initial_capital = portfolio_data.get('initial_capital', 100000)
            current_capital = portfolio_data.get('current_capital', initial_capital)
            positions = portfolio_data.get('positions', [])
            
            metrics.total_pnl = current_capital - initial_capital
            metrics.position_count = len(positions)
            
            # 计算回撤
            peak_capital = portfolio_data.get('peak_capital', initial_capital)
            if peak_capital > 0:
                metrics.current_drawdown = (peak_capital - current_capital) / peak_capital
                metrics.max_drawdown = max(metrics.current_drawdown, 
                                         self.risk_metrics.get(strategy_id, RiskMetrics(strategy_id)).max_drawdown)
            
            # 计算持仓集中度
            if positions and current_capital > 0:
                max_position_value = max([pos.get('market_value', 0) for pos in positions])
                metrics.position_concentration = max_position_value / current_capital
            
            # 存储指标
            self.risk_metrics[strategy_id] = metrics
            
            # 检查风险
            self._check_risk_thresholds(strategy_id, metrics)
            
            return metrics
            
        except Exception as e:
            logger.error(f"❌ 更新风险指标失败: {e}")
            return RiskMetrics(strategy_id=strategy_id)
    
    def _check_risk_thresholds(self, strategy_id: str, metrics: RiskMetrics):
        """检查风险阈值"""
        alerts = []
        
        # 检查最大回撤
        if metrics.current_drawdown > self.max_drawdown_threshold:
            alert = RiskAlert(
                level=RiskLevel.CRITICAL,
                message=f"策略回撤超过阈值: {metrics.current_drawdown*100:.2f}% > {self.max_drawdown_threshold*100:.1f}%",
                timestamp=datetime.now(),
                strategy_id=strategy_id,
                metric_name="current_drawdown",
                current_value=metrics.current_drawdown,
                threshold_value=self.max_drawdown_threshold
            )
            alerts.append(alert)
        
        # 检查持仓集中度
        if metrics.position_concentration > self.max_position_concentration:
            alert = RiskAlert(
                level=RiskLevel.HIGH,
                message=f"单股持仓比例过高: {metrics.position_concentration*100:.2f}% > {self.max_position_concentration*100:.1f}%",
                timestamp=datetime.now(),
                strategy_id=strategy_id,
                metric_name="position_concentration",
                current_value=metrics.position_concentration,
                threshold_value=self.max_position_concentration
            )
            alerts.append(alert)
        
        # 检查持仓数量
        if metrics.position_count > self.max_positions:
            alert = RiskAlert(
                level=RiskLevel.MEDIUM,
                message=f"持仓数量超过限制: {metrics.position_count} > {self.max_positions}",
                timestamp=datetime.now(),
                strategy_id=strategy_id,
                metric_name="position_count",
                current_value=metrics.position_count,
                threshold_value=self.max_positions
            )
            alerts.append(alert)
        
        # 添加告警
        for alert in alerts:
            self.risk_alerts.append(alert)
            logger.warning(f"⚠️ 风险告警: {alert.message}")
    
    def check_trade_frequency(self, strategy_id: str) -> bool:
        """检查交易频率是否超限"""
        try:
            now = datetime.now()
            one_hour_ago = now - timedelta(hours=1)
            
            # 获取策略的交易记录
            if strategy_id not in self.trade_counts:
                self.trade_counts[strategy_id] = []
            
            # 清理1小时前的记录
            self.trade_counts[strategy_id] = [
                trade_time for trade_time in self.trade_counts[strategy_id]
                if trade_time > one_hour_ago
            ]
            
            # 检查是否超过限制
            current_count = len(self.trade_counts[strategy_id])
            if current_count >= self.max_trades_per_hour:
                logger.warning(f"⚠️ 策略 {strategy_id} 交易频率超限: {current_count}/{self.max_trades_per_hour}")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 检查交易频率失败: {e}")
            return True  # 出错时允许交易
    
    def record_trade(self, strategy_id: str):
        """记录交易"""
        try:
            if strategy_id not in self.trade_counts:
                self.trade_counts[strategy_id] = []
            
            self.trade_counts[strategy_id].append(datetime.now())
            
        except Exception as e:
            logger.error(f"❌ 记录交易失败: {e}")
    
    def should_stop_strategy(self, strategy_id: str) -> bool:
        """判断是否应该停止策略"""
        try:
            metrics = self.risk_metrics.get(strategy_id)
            if not metrics:
                return False
            
            # 严重回撤时停止
            if metrics.current_drawdown > self.max_drawdown_threshold:
                logger.critical(f"🚨 策略 {strategy_id} 因严重回撤被强制停止")
                return True
            
            # 日亏损过大时停止
            if metrics.daily_pnl < -self.max_daily_loss_threshold * metrics.total_pnl:
                logger.critical(f"🚨 策略 {strategy_id} 因日亏损过大被强制停止")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"❌ 判断策略停止失败: {e}")
            return False
    
    def get_risk_metrics(self, strategy_id: str = None) -> Dict[str, Any]:
        """获取风险指标"""
        if strategy_id:
            metrics = self.risk_metrics.get(strategy_id)
            return metrics.to_dict() if metrics else {}
        else:
            return {sid: metrics.to_dict() for sid, metrics in self.risk_metrics.items()}
    
    def get_risk_alerts(self, strategy_id: str = None, level: RiskLevel = None) -> List[Dict[str, Any]]:
        """获取风险告警"""
        alerts = self.risk_alerts
        
        # 按策略过滤
        if strategy_id:
            alerts = [alert for alert in alerts if alert.strategy_id == strategy_id]
        
        # 按级别过滤
        if level:
            alerts = [alert for alert in alerts if alert.level == level]
        
        # 按时间倒序排列
        alerts.sort(key=lambda x: x.timestamp, reverse=True)
        
        return [alert.to_dict() for alert in alerts]
    
    def clear_old_alerts(self, hours: int = 24):
        """清理旧告警"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        self.risk_alerts = [
            alert for alert in self.risk_alerts
            if alert.timestamp > cutoff_time
        ]

# 全局风险管理器实例
risk_manager = RiskManager()
