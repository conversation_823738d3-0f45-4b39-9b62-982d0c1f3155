#!/usr/bin/env python3
"""
测试回测引擎修复效果
"""

import sys
import os
import asyncio
import logging
from datetime import datetime, timedelta

# 添加项目路径
sys.path.append(os.path.dirname(__file__))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

async def test_backtest_engine():
    """测试回测引擎数据获取"""
    logger.info("=== 测试回测引擎数据获取 ===")
    
    try:
        from backend.backtest.simple_backtest_engine import SimpleBacktestEngine
        
        # 创建回测引擎
        engine = SimpleBacktestEngine()
        logger.info("✅ 回测引擎创建成功")
        
        # 设置测试参数
        end_date = datetime.now().strftime('%Y-%m-%d')
        start_date = (datetime.now() - timedelta(days=90)).strftime('%Y-%m-%d')
        task_id = "test_task_001"
        
        # 初始化任务状态
        engine.running_tasks[task_id] = {
            'status': 'running',
            'progress': 0,
            'message': '开始测试',
            'error': None
        }
        
        logger.info(f"测试日期范围: {start_date} 到 {end_date}")
        
        # 测试数据获取
        logger.info("开始测试数据获取...")
        stock_data = await engine._get_stock_data(start_date, end_date, task_id)
        
        if stock_data:
            logger.info(f"✅ 数据获取成功: {len(stock_data)}只股票")
            
            # 显示获取到的股票信息
            for i, (stock_code, df) in enumerate(stock_data.items()):
                logger.info(f"  {i+1}. {stock_code}: {len(df)}条记录")
                if len(df) > 0:
                    logger.info(f"     数据范围: {df.index[0]} 到 {df.index[-1]}")
                    logger.info(f"     最新收盘价: {df['close'].iloc[-1]:.2f}")
            
            # 测试数据一致性
            logger.info("\n--- 测试数据一致性 ---")
            
            # 第二次获取相同数据
            stock_data2 = await engine._get_stock_data(start_date, end_date, task_id)
            
            if len(stock_data) == len(stock_data2):
                logger.info("✅ 两次获取的股票数量一致")
                
                # 检查每只股票的数据是否一致
                all_consistent = True
                for stock_code in stock_data.keys():
                    if stock_code in stock_data2:
                        df1 = stock_data[stock_code]
                        df2 = stock_data2[stock_code]
                        
                        if df1.equals(df2):
                            logger.info(f"✅ {stock_code} 数据完全一致")
                        else:
                            logger.error(f"❌ {stock_code} 数据不一致")
                            all_consistent = False
                    else:
                        logger.error(f"❌ {stock_code} 在第二次获取中缺失")
                        all_consistent = False
                
                if all_consistent:
                    logger.info("🎉 数据一致性测试通过！")
                    return True
                else:
                    logger.error("❌ 数据一致性测试失败")
                    return False
            else:
                logger.error(f"❌ 两次获取的股票数量不一致: {len(stock_data)} vs {len(stock_data2)}")
                return False
        else:
            logger.error("❌ 数据获取失败")
            return False
            
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False

async def test_multiple_runs():
    """测试多次运行的一致性"""
    logger.info("\n=== 测试多次运行一致性 ===")
    
    try:
        from backend.backtest.simple_backtest_engine import SimpleBacktestEngine
        
        # 设置测试参数
        end_date = datetime.now().strftime('%Y-%m-%d')
        start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
        
        results = []
        
        # 运行3次测试
        for run in range(3):
            logger.info(f"\n--- 第{run+1}次运行 ---")
            
            engine = SimpleBacktestEngine()
            task_id = f"test_task_{run+1:03d}"
            
            # 初始化任务状态
            engine.running_tasks[task_id] = {
                'status': 'running',
                'progress': 0,
                'message': f'第{run+1}次测试',
                'error': None
            }
            
            # 获取数据
            stock_data = await engine._get_stock_data(start_date, end_date, task_id)
            
            if stock_data:
                # 记录结果摘要
                result_summary = {}
                for stock_code, df in stock_data.items():
                    result_summary[stock_code] = {
                        'count': len(df),
                        'first_close': df['close'].iloc[0] if len(df) > 0 else None,
                        'last_close': df['close'].iloc[-1] if len(df) > 0 else None,
                        'mean_close': df['close'].mean() if len(df) > 0 else None
                    }
                
                results.append(result_summary)
                logger.info(f"✅ 第{run+1}次运行完成: {len(stock_data)}只股票")
            else:
                logger.error(f"❌ 第{run+1}次运行失败")
                return False
        
        # 比较所有运行结果
        logger.info("\n--- 比较运行结果 ---")
        
        if len(results) >= 2:
            base_result = results[0]
            all_consistent = True
            
            for i, result in enumerate(results[1:], 2):
                logger.info(f"比较第1次和第{i}次运行...")
                
                # 检查股票数量
                if len(base_result) != len(result):
                    logger.error(f"❌ 股票数量不一致: {len(base_result)} vs {len(result)}")
                    all_consistent = False
                    continue
                
                # 检查每只股票的数据
                for stock_code in base_result.keys():
                    if stock_code not in result:
                        logger.error(f"❌ {stock_code} 在第{i}次运行中缺失")
                        all_consistent = False
                        continue
                    
                    base_data = base_result[stock_code]
                    curr_data = result[stock_code]
                    
                    # 比较关键指标
                    if (base_data['count'] == curr_data['count'] and
                        abs((base_data['first_close'] or 0) - (curr_data['first_close'] or 0)) < 0.001 and
                        abs((base_data['last_close'] or 0) - (curr_data['last_close'] or 0)) < 0.001):
                        logger.info(f"✅ {stock_code} 数据一致")
                    else:
                        logger.error(f"❌ {stock_code} 数据不一致")
                        logger.error(f"   第1次: count={base_data['count']}, first={base_data['first_close']}, last={base_data['last_close']}")
                        logger.error(f"   第{i}次: count={curr_data['count']}, first={curr_data['first_close']}, last={curr_data['last_close']}")
                        all_consistent = False
            
            if all_consistent:
                logger.info("🎉 多次运行一致性测试通过！")
                return True
            else:
                logger.error("❌ 多次运行一致性测试失败")
                return False
        else:
            logger.error("❌ 运行结果不足，无法比较")
            return False
            
    except Exception as e:
        logger.error(f"❌ 多次运行测试失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False

async def main():
    """主测试函数"""
    logger.info("🔍 开始回测引擎修复验证")
    
    # 测试1: 基本数据获取
    basic_ok = await test_backtest_engine()
    
    if basic_ok:
        # 测试2: 多次运行一致性
        consistency_ok = await test_multiple_runs()
        
        if consistency_ok:
            logger.info("\n🎉 所有测试通过！")
            logger.info("✅ 数据获取正常")
            logger.info("✅ 数据结构正确")
            logger.info("✅ 结果一致性保证")
            logger.info("✅ 回测引擎修复成功")
        else:
            logger.error("\n❌ 一致性测试失败")
    else:
        logger.error("\n❌ 基本功能测试失败")

if __name__ == "__main__":
    asyncio.run(main())
