import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ger, Ta<PERSON>Content } from '../components/UI/tabs.jsx';
import { Alert as ShadAlert } from '../components/UI/alert.jsx';
import { Progress as ShadProgress } from '../components/UI/progress.jsx';
import { <PERSON><PERSON> as ShadButton } from '../components/UI/button.jsx';
import { Card as ShadCard, CardHeader as ShadCardHeader, CardTitle as ShadCardTitle, CardContent as ShadCardContent } from '../components/UI/card.jsx';
import { Input } from '../components/UI/input.jsx';

import { InputNumber } from '../components/UI/input-number.jsx';
import { Select, SelectItem } from '../components/UI/select.jsx';
import { Spinner } from '../components/UI/spinner.jsx';
import { DataTable } from '../components/UI/table.jsx';
import { Save, RefreshCw, Play, TrendingUp, TrendingDown, DollarSign, BarChart3 } from 'lucide-react';
import { toast as imported_toast } from 'sonner';
import { Link } from 'react-router-dom';
import { backtestAPI, dataAPI, apiUtils } from '../services/api';

const StrategyBacktestPage = () => {
  const [loading, setLoading] = useState(false);
  const [availableStrategies, setAvailableStrategies] = useState([]);
  const [stockList, setStockList] = useState([]);
  const [stockPools, setStockPools] = useState([]);
  const [error, setError] = useState(null);
  const [formValues, setFormValues] = useState({});
  const [backtestFormValues, setBacktestFormValues] = useState({
    initial_capital: 1000000,
    commission: 0.0003,
    start_date: (() => {
      const date = new Date();
      date.setMonth(date.getMonth() - 6);
      return date.toISOString().split('T')[0];
    })(),  // 6个月前
    end_date: new Date().toISOString().split('T')[0]  // 今天
  });
  const [activeTab, setActiveTab] = useState('config');
  const [selectedStrategy, setSelectedStrategy] = useState('');
  const [backtestStatus, setBacktestStatus] = useState(null);
  const [backtestResults, setBacktestResults] = useState(null);
  const [lastTaskId, setLastTaskId] = useState(null);

  // 分页状态
  const [tradesPage, setTradesPage] = useState(1);
  const [tradesPageSize, setTradesPageSize] = useState(20);
  const [dailyReturnsPage, setDailyReturnsPage] = useState(1);
  const [dailyReturnsPageSize, setDailyReturnsPageSize] = useState(20);

  // 分页组件
  const PaginationComponent = ({
    currentPage,
    pageSize,
    totalItems,
    onPageChange,
    onPageSizeChange,
    itemName = "项"
  }) => {
    const totalPages = Math.ceil(totalItems / pageSize);
    const startItem = (currentPage - 1) * pageSize + 1;
    const endItem = Math.min(currentPage * pageSize, totalItems);

    return (
      <div className="flex items-center justify-between mt-4 p-4 bg-gray-50 rounded-lg">
        <div className="flex items-center gap-4">
          <span className="text-sm text-gray-600">
            显示 {startItem}-{endItem} 项，共 {totalItems} {itemName}
          </span>
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-600">每页显示:</span>
            <select
              value={pageSize}
              onChange={(e) => onPageSizeChange(Number(e.target.value))}
              className="border rounded px-2 py-1 text-sm"
            >
              <option value={10}>10</option>
              <option value={20}>20</option>
              <option value={50}>50</option>
              <option value={100}>100</option>
            </select>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <ShadButton
            variant="outline"
            size="sm"
            onClick={() => onPageChange(currentPage - 1)}
            disabled={currentPage <= 1}
          >
            上一页
          </ShadButton>

          <div className="flex items-center gap-1">
            {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
              let pageNum;
              if (totalPages <= 5) {
                pageNum = i + 1;
              } else if (currentPage <= 3) {
                pageNum = i + 1;
              } else if (currentPage >= totalPages - 2) {
                pageNum = totalPages - 4 + i;
              } else {
                pageNum = currentPage - 2 + i;
              }

              return (
                <ShadButton
                  key={pageNum}
                  variant={currentPage === pageNum ? "default" : "outline"}
                  size="sm"
                  onClick={() => onPageChange(pageNum)}
                  className="w-8 h-8 p-0"
                >
                  {pageNum}
                </ShadButton>
              );
            })}
          </div>

          <ShadButton
            variant="outline"
            size="sm"
            onClick={() => onPageChange(currentPage + 1)}
            disabled={currentPage >= totalPages}
          >
            下一页
          </ShadButton>
        </div>
      </div>
    );
  };


  useEffect(() => {
    loadData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const loadData = async () => {
    setLoading(true);
    try {
      // 加载可用策略列表
      const strategyResponse = await backtestAPI.getAvailableStrategies();
      if (apiUtils.isSuccess(strategyResponse)) {
        const strategies = apiUtils.getData(strategyResponse);
        setAvailableStrategies(strategies);

        // 设置默认选中的策略
        if (strategies.length > 0 && !selectedStrategy) {
          const firstStrategy = strategies[0];
          setSelectedStrategy(firstStrategy.name);
          // 设置默认参数值
          setFormValues(getDefaultStrategyParams(firstStrategy.name));
        }
      }

      // 加载股票池列表
      try {
        const poolResponse = await fetch('/api/stock-pool/pools');
        const poolData = await poolResponse.json();
        if (poolData.success) {
          setStockPools(poolData.data);
        }
      } catch (poolError) {
        console.warn('获取股票池列表失败:', poolError);
      }

      // 加载股票列表（保持原有逻辑作为备用）
      const stockResponse = await dataAPI.getStockList({ page: 1, page_size: 100 });
      if (apiUtils.isSuccess(stockResponse)) {
        const stockData = apiUtils.getData(stockResponse);
        setStockList(stockData.data || []);
      } else {
        console.error('获取股票列表失败:', stockResponse);
      }
    } catch (error) {
      console.error('加载数据失败:', error);
      setError('加载数据失败: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  // 获取策略的默认参数
  const getDefaultStrategyParams = (strategyName) => {
    const defaultParams = {
      'bollinger_bands': {
        period: 20,
        std_dev: 2.0,
        max_positions: 5,
        position_size: 0.2
      },
      'moving_average': {
        short_period: 10,
        long_period: 30,
        max_positions: 3,
        position_size: 0.3
      },
      'buy_hold': {
        max_stocks: 3,
        position_size: 0.33
      },
      'rsi': {
        period: 14,
        oversold: 30,
        overbought: 70,
        max_positions: 3,
        position_size: 0.3
      }
    };
    return defaultParams[strategyName] || {};
  };

  const handleStrategyChange = (strategyName) => {
    setSelectedStrategy(strategyName);
    setFormValues(getDefaultStrategyParams(strategyName));
  };

  const saveStrategyConfig = async () => {
    try {
      // 新的回测系统不需要保存策略配置，参数直接在回测时传递
      imported_toast.success('策略参数已更新');
    } catch (err) {
      imported_toast.error('保存失败: ' + (err.message || '未知错误'));
    }
  };

  const startBacktest = async () => {
    try {
      const strategyValues = formValues;
      const backtestValues = backtestFormValues;

      // 日期已经是字符串格式，直接使用
      let processedBacktestValues = { ...backtestValues };

      // 处理股票池参数
      let stockPoolConfig = null;
      if (backtestValues.stock_pool_type === 'custom_pool' && backtestValues.stock_pool) {
        // 获取股票池的股票代码列表
        try {
          const poolResponse = await fetch(`/api/stock-pool/pools/${backtestValues.stock_pool}`);
          const poolData = await poolResponse.json();
          if (poolData.success && poolData.data.stocks) {
            // 从股票池详情中提取股票代码列表
            stockPoolConfig = poolData.data.stocks.map(stock => stock.stock_code);
          }
        } catch (poolError) {
          console.warn('获取股票池股票列表失败:', poolError);
        }
      } else if (backtestValues.stock_pool_type === 'single_stock' && backtestValues.stock_pool) {
        stockPoolConfig = [backtestValues.stock_pool];
      }

      // 新的回测配置格式
      const config = {
        strategy_name: selectedStrategy,
        strategy_config: strategyValues,
        start_date: processedBacktestValues.start_date,
        end_date: processedBacktestValues.end_date,
        initial_capital: processedBacktestValues.initial_capital,
        commission: processedBacktestValues.commission,
        stock_codes: stockPoolConfig  // 使用stock_codes字段，与后端保持一致
      };

      const response = await backtestAPI.startBacktest(config);
      if (apiUtils.isSuccess(response)) {
        const data = apiUtils.getData(response);
        imported_toast.success('回测已启动');
        setActiveTab('results');
        setBacktestResults(null); // 清空之前的结果
        setLastTaskId(data.task_id);
        // 开始轮询回测状态
        pollBacktestStatus(data.task_id);
      }
    } catch (err) {
      imported_toast.error('启动回测失败: ' + (err.message || '未知错误'));
    }
  };

  const pollBacktestStatus = async (taskId = null) => {
    try {
      const response = await backtestAPI.getBacktestStatus();
      if (apiUtils.isSuccess(response)) {
        const status = apiUtils.getData(response);
        setBacktestStatus(status);

        if (status.status === 'running') {
          setTimeout(() => pollBacktestStatus(taskId), 2000); // 2秒后再次查询
        } else if (status.status === 'completed' && taskId) {
          // 获取回测结果
          loadBacktestResults(taskId);
        }
      }
    } catch (err) {
      console.error('获取回测状态失败:', err);
    }
  };

  const loadBacktestResults = async (taskId) => {
    try {
      const response = await backtestAPI.getBacktestResults(taskId);
      if (apiUtils.isSuccess(response)) {
        setBacktestResults(apiUtils.getData(response));
      }
    } catch (err) {
      console.error('获取回测结果失败:', err);
      imported_toast.error('获取回测结果失败');
    }
  };

  // 渲染策略参数输入框 - 支持动态参数配置
  const renderStrategyParameters = () => {
    if (!selectedStrategy) {
      return null;
    }

    const strategy = availableStrategies.find(s => s.name === selectedStrategy);
    if (!strategy || !strategy.parameters) {
      return <div className="text-gray-500">该策略暂无可配置参数</div>;
    }

    const parameters = strategy.parameters;

    // 对多信号策略进行参数分组
    if (selectedStrategy === 'multi_signal_strategy') {
      return renderMultiSignalParameters(parameters);
    }

    return Object.entries(parameters).map(([paramName, paramConfig]) => {
      const value = formValues[paramName] !== undefined ? formValues[paramName] : paramConfig.value;

      const handleChange = (newValue) => {
        setFormValues(prev => ({ ...prev, [paramName]: newValue }));
      };

      // 根据参数类型渲染不同的输入组件
      if (paramConfig.type === 'boolean') {
        return (
          <div key={paramName} className="space-y-2">
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={value}
                onChange={(e) => handleChange(e.target.checked)}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span className="text-sm font-medium text-gray-700">
                {paramConfig.description || paramName}
              </span>
            </label>
          </div>
        );
      } else if (paramConfig.type === 'select') {
        return (
          <div key={paramName} className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              {paramConfig.description || paramName}
            </label>
            <select
              value={value}
              onChange={(e) => handleChange(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              {paramConfig.options?.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>
        );
      } else {
        // 数字类型参数
        const isFloat = paramConfig.type === 'float';
        return (
          <div key={paramName} className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              {paramConfig.description || paramName}
            </label>
            <InputNumber
              value={value}
              onChange={handleChange}
              min={paramConfig.min}
              max={paramConfig.max}
              step={isFloat ? 0.01 : 1}
              precision={isFloat ? 2 : 0}
              className="w-full"
            />
          </div>
        );
      }
    });
  };

  // 渲染多信号策略参数 - 分组显示
  const renderMultiSignalParameters = (parameters) => {
    const buySignals = {};
    const sellSignals = {};
    const otherParams = {};

    // 分组参数
    Object.entries(parameters).forEach(([paramName, paramConfig]) => {
      if (paramName.startsWith('buy_signal_')) {
        buySignals[paramName] = paramConfig;
      } else if (paramName.startsWith('sell_signal_')) {
        sellSignals[paramName] = paramConfig;
      } else {
        otherParams[paramName] = paramConfig;
      }
    });

    const renderParameterGroup = (title, params, bgColor = 'bg-gray-50') => {
      if (Object.keys(params).length === 0) return null;

      return (
        <div className={`p-4 rounded-lg ${bgColor} space-y-3`}>
          <h4 className="font-medium text-gray-900 border-b pb-2">{title}</h4>
          <div className="grid grid-cols-1 gap-3">
            {Object.entries(params).map(([paramName, paramConfig]) => {
              const value = formValues[paramName] !== undefined ? formValues[paramName] : paramConfig.value;

              const handleChange = (newValue) => {
                setFormValues(prev => ({ ...prev, [paramName]: newValue }));
              };

              if (paramConfig.type === 'boolean') {
                return (
                  <div key={paramName} className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={value}
                      onChange={(e) => handleChange(e.target.checked)}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="text-sm text-gray-700">
                      {paramConfig.description || paramName}
                    </span>
                  </div>
                );
              } else {
                const isFloat = paramConfig.type === 'float';
                return (
                  <div key={paramName} className="space-y-1">
                    <label className="block text-sm font-medium text-gray-700">
                      {paramConfig.description || paramName}
                    </label>
                    <InputNumber
                      value={value}
                      onChange={handleChange}
                      min={paramConfig.min}
                      max={paramConfig.max}
                      step={isFloat ? 0.01 : 1}
                      precision={isFloat ? 2 : 0}
                      className="w-full"
                      size="small"
                    />
                  </div>
                );
              }
            })}
          </div>
        </div>
      );
    };

    return (
      <div className="space-y-4">
        {renderParameterGroup('📈 买入信号', buySignals, 'bg-green-50')}
        {renderParameterGroup('📉 卖出信号', sellSignals, 'bg-red-50')}
        {renderParameterGroup('⚙️ 策略参数', otherParams, 'bg-blue-50')}
      </div>
    );
  };

  const renderStrategyConfig = () => {
    if (!selectedStrategy) {
      return <div>请选择策略</div>;
    }

    const strategy = availableStrategies.find(s => s.name === selectedStrategy);
    if (!strategy) {
      return <div>策略信息加载中...</div>;
    }

    return (
      <div style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
        {/* 策略信息卡片 */}
        <ShadCard>
          <ShadCardHeader>
            <ShadCardTitle className="flex items-center gap-2">
              <BarChart3 className="w-5 h-5" />
              策略信息
            </ShadCardTitle>
          </ShadCardHeader>
          <ShadCardContent>
            <div className="flex flex-col gap-3">
              <div><strong>名称:</strong> {strategy.display_name}</div>
              <div><strong>描述:</strong> {strategy.description}</div>
              <div><strong>类名:</strong> {strategy.class_name}</div>
              <div><strong>策略ID:</strong> {strategy.name}</div>
            </div>
          </ShadCardContent>
        </ShadCard>

        <ShadCard className="mt-4">
          <ShadCardHeader>
            <ShadCardTitle className="flex items-center gap-2">
              <DollarSign className="w-5 h-5" />
              参数配置
            </ShadCardTitle>
          </ShadCardHeader>
          <ShadCardContent>
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {renderStrategyParameters()}
              </div>
              <div className="pt-4">
                <ShadButton
                  onClick={saveStrategyConfig}
                  className="bg-blue-600 hover:bg-blue-700 text-white inline-flex items-center gap-2"
                >
                  <Save className="w-4 h-4" />
                  更新参数
                </ShadButton>
              </div>
            </div>
          </ShadCardContent>
        </ShadCard>
      </div>
    );
  };

  const renderBacktestConfig = () => (
    <ShadCard>
      <ShadCardHeader>
        <ShadCardTitle>回测配置</ShadCardTitle>
      </ShadCardHeader>
      <ShadCardContent>
        <div className="space-y-8">
          {/* 时间范围配置 - 独立区域避免布局冲突 */}
          <div className="space-y-4 pb-4 border-b border-gray-200">
            <h4 className="text-sm font-medium text-gray-900">回测时间范围</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700">开始日期</label>
                <Input
                  type="date"
                  value={backtestFormValues.start_date}
                  onChange={(e) => setBacktestFormValues(prev => ({ ...prev, start_date: e.target.value }))}
                  className="w-full"
                />
              </div>
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700">结束日期</label>
                <Input
                  type="date"
                  value={backtestFormValues.end_date}
                  onChange={(e) => setBacktestFormValues(prev => ({ ...prev, end_date: e.target.value }))}
                  className="w-full"
                />
              </div>
            </div>
          </div>

          {/* 其他配置 */}
          <div className="space-y-4">
            <h4 className="text-sm font-medium text-gray-900">回测参数</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">初始资金</label>
              <InputNumber
                value={backtestFormValues.initial_capital}
                onChange={(value) => setBacktestFormValues(prev => ({ ...prev, initial_capital: value }))}
                min={10000}
                max={100000000}
                formatter={value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                parser={value => value.replace(/¥\s?|(,*)/g, '')}
                className="w-full"
              />
            </div>

            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">手续费率</label>
              <InputNumber
                value={backtestFormValues.commission}
                onChange={(value) => setBacktestFormValues(prev => ({ ...prev, commission: value }))}
                min={0}
                max={0.01}
                step={0.0001}
                precision={4}
                formatter={value => `${(value * 100).toFixed(2)}%`}
                parser={value => value.replace('%', '') / 100}
                className="w-full"
              />
            </div>

            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">股票池选择</label>
              <Select
                value={backtestFormValues.stock_pool_type || 'default'}
                onValueChange={(value) => setBacktestFormValues(prev => ({
                  ...prev,
                  stock_pool_type: value,
                  stock_pool: value === 'default' ? null : prev.stock_pool
                }))}
                placeholder="选择股票池类型"
              >
                <SelectItem value="default">默认股票池（系统自动选择）</SelectItem>
                <SelectItem value="custom_pool">自定义股票池</SelectItem>
                <SelectItem value="single_stock">单只股票</SelectItem>
              </Select>
            </div>

            {/* 自定义股票池选择 */}
            {backtestFormValues.stock_pool_type === 'custom_pool' && (
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700">选择股票池</label>
                <Select
                  value={backtestFormValues.stock_pool}
                  onValueChange={(value) => setBacktestFormValues(prev => ({ ...prev, stock_pool: value }))}
                  placeholder="选择已创建的股票池"
                >
                  {stockPools.map(pool => (
                    <SelectItem key={pool.pool_id} value={pool.pool_id}>
                      {pool.pool_name} ({pool.stock_count}只股票)
                    </SelectItem>
                  ))}
                </Select>
                {stockPools.length === 0 && (
                  <p className="text-sm text-gray-500">
                    暂无可用股票池，请先在股票池管理页面创建股票池
                  </p>
                )}
              </div>
            )}

            {/* 单只股票选择 */}
            {backtestFormValues.stock_pool_type === 'single_stock' && (
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700">选择股票</label>
                <Select
                  value={backtestFormValues.stock_pool}
                  onValueChange={(value) => setBacktestFormValues(prev => ({ ...prev, stock_pool: value }))}
                  placeholder="选择单只股票进行回测"
                  className="w-full"
                >
                  {stockList.length > 0 ? (
                    stockList.map(stock => (
                      <SelectItem key={stock.code} value={stock.code}>
                        {stock.code} - {stock.name}
                      </SelectItem>
                    ))
                  ) : (
                    <SelectItem value="loading" disabled>
                      加载中...
                    </SelectItem>
                  )}
                </Select>
              </div>
            )}
          </div>
        </div>

        <div className="flex flex-wrap gap-2 pt-4">
            <ShadButton
              onClick={startBacktest}
              disabled={!selectedStrategy}
              className="bg-blue-600 hover:bg-blue-700 text-white inline-flex items-center gap-2"
            >
              <Play className="w-4 h-4" />
              开始回测
            </ShadButton>
            {lastTaskId && (
              <Link to={`/backtest/results/${lastTaskId}`} className="inline-flex items-center px-4 py-2 text-sm font-medium text-blue-600 hover:text-blue-800">
                查看详细结果
              </Link>
            )}
            <ShadButton onClick={loadData} className="inline-flex items-center gap-2">
              <RefreshCw className="w-4 h-4" />
              刷新数据
            </ShadButton>
          </div>
        </div>
      </ShadCardContent>
    </ShadCard>
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center py-20">
        <Spinner size="lg" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-gray-900">策略配置与回测</h1>
        <Select
          value={selectedStrategy}
          onValueChange={handleStrategyChange}
          placeholder="选择策略"
          className="w-48"
        >
          {availableStrategies.map((strategy) => (
            <SelectItem key={strategy.name} value={strategy.name}>
              {strategy.display_name}
            </SelectItem>
          ))}
        </Select>
      </div>

      {error && (
        <ShadAlert
          title="错误"
          description={error}
          variant="error"
          closable
          onClose={() => setError(null)}
        />
      )}

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="config">策略配置</TabsTrigger>
          <TabsTrigger value="backtest">回测配置</TabsTrigger>
          <TabsTrigger value="results">回测结果</TabsTrigger>
        </TabsList>

        <TabsContent value="config">
          {renderStrategyConfig()}
        </TabsContent>

        <TabsContent value="backtest">
          {renderBacktestConfig()}
        </TabsContent>

        <TabsContent value="results">
          <div className="space-y-6">
            <ShadCard>
              <ShadCardHeader>
                <ShadCardTitle>回测状态</ShadCardTitle>
              </ShadCardHeader>
              <ShadCardContent>
                {backtestStatus ? (
                  <div className="space-y-4">
                    <div className="flex items-center gap-4">
                      <span>状态:</span>
                      <span className={`font-bold ${
                        backtestStatus.status === 'running' ? 'text-blue-600' :
                        backtestStatus.status === 'completed' ? 'text-green-600' :
                        backtestStatus.status === 'failed' ? 'text-red-600' : 'text-gray-500'
                      }`}>
                        {backtestStatus.status}
                      </span>
                    </div>
                    <div>消息: {backtestStatus.message}</div>
                    {backtestStatus.status === 'running' && (
                      <ShadProgress value={backtestStatus.progress || 0} />
                    )}
                  </div>
                ) : (
                  <div>暂无回测任务</div>
                )}
              </ShadCardContent>
            </ShadCard>

            {backtestResults && (
              <div className="space-y-6">
                {/* 核心指标概览 */}
                <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
                  <ShadCard>
                    <ShadCardContent className="p-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-gray-600">总收益率</p>
                          <p className={`text-2xl font-bold ${backtestResults.total_return >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                            {(backtestResults.total_return * 100).toFixed(2)}%
                          </p>
                        </div>
                        <TrendingUp className={`h-8 w-8 ${backtestResults.total_return >= 0 ? 'text-green-600' : 'text-red-600'}`} />
                      </div>
                    </ShadCardContent>
                  </ShadCard>

                  <ShadCard>
                    <ShadCardContent className="p-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-gray-600">年化收益率</p>
                          <p className={`text-2xl font-bold ${backtestResults.annual_return >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                            {(backtestResults.annual_return * 100).toFixed(2)}%
                          </p>
                        </div>
                        <BarChart3 className="h-8 w-8 text-blue-600" />
                      </div>
                    </ShadCardContent>
                  </ShadCard>

                  <ShadCard>
                    <ShadCardContent className="p-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-gray-600">最大回撤</p>
                          <p className="text-2xl font-bold text-red-600">
                            {(backtestResults.max_drawdown * 100).toFixed(2)}%
                          </p>
                        </div>
                        <TrendingDown className="h-8 w-8 text-red-600" />
                      </div>
                    </ShadCardContent>
                  </ShadCard>

                  <ShadCard>
                    <ShadCardContent className="p-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-gray-600">夏普比率</p>
                          <p className="text-2xl font-bold text-purple-600">
                            {backtestResults.sharpe_ratio?.toFixed(2) || '0.00'}
                          </p>
                        </div>
                        <DollarSign className="h-8 w-8 text-purple-600" />
                      </div>
                    </ShadCardContent>
                  </ShadCard>
                </div>

                {/* 详细信息 */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <ShadCard>
                    <ShadCardHeader>
                      <ShadCardTitle>回测概览</ShadCardTitle>
                    </ShadCardHeader>
                    <ShadCardContent>
                      <div className="space-y-3">
                        <div className="flex justify-between">
                          <span>策略名称:</span>
                          <span className="font-bold">{backtestResults.strategy_name}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>回测期间:</span>
                          <span>{backtestResults.start_date} ~ {backtestResults.end_date}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>初始资金:</span>
                          <span>¥{backtestResults.initial_capital?.toLocaleString()}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>最终资金:</span>
                          <span className={backtestResults.final_capital >= backtestResults.initial_capital ? 'text-green-600 font-bold' : 'text-red-600 font-bold'}>
                            ¥{backtestResults.final_capital?.toLocaleString()}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span>绝对收益:</span>
                          <span className={backtestResults.final_capital >= backtestResults.initial_capital ? 'text-green-600 font-bold' : 'text-red-600 font-bold'}>
                            ¥{(backtestResults.final_capital - backtestResults.initial_capital)?.toLocaleString()}
                          </span>
                        </div>
                      </div>
                    </ShadCardContent>
                  </ShadCard>

                  <ShadCard>
                    <ShadCardHeader>
                      <ShadCardTitle>交易统计</ShadCardTitle>
                    </ShadCardHeader>
                    <ShadCardContent>
                      <div className="space-y-3">
                        <div className="flex justify-between">
                          <span>总交易次数:</span>
                          <span className="font-bold">{backtestResults.total_trades}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>盈利交易:</span>
                          <span className="font-bold text-green-600">{backtestResults.profit_trades}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>亏损交易:</span>
                          <span className="font-bold text-red-600">{backtestResults.loss_trades}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>胜率:</span>
                          <span className="font-bold">{(backtestResults.win_rate * 100).toFixed(2)}%</span>
                        </div>
                        <div className="flex justify-between">
                          <span>平均盈利:</span>
                          <span className="font-bold text-green-600">
                            ¥{backtestResults.avg_profit?.toFixed(2) || '0.00'}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span>平均亏损:</span>
                          <span className="font-bold text-red-600">
                            ¥{backtestResults.avg_loss?.toFixed(2) || '0.00'}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span>盈亏比:</span>
                          <span className="font-bold">{backtestResults.profit_factor?.toFixed(2) || '0.00'}</span>
                        </div>
                      </div>
                    </ShadCardContent>
                  </ShadCard>
                </div>

                {/* 交易记录 */}
                {backtestResults.trades && backtestResults.trades.length > 0 && (() => {
                  const startIndex = (tradesPage - 1) * tradesPageSize;
                  const endIndex = startIndex + tradesPageSize;
                  const paginatedTrades = backtestResults.trades.slice(startIndex, endIndex);

                  return (
                    <ShadCard>
                      <ShadCardHeader>
                        <ShadCardTitle>交易记录</ShadCardTitle>
                      </ShadCardHeader>
                      <ShadCardContent>
                        <DataTable
                          data={paginatedTrades}
                        columns={[
                          {
                            accessorKey: 'id',
                            header: '序号',
                            cell: info => info.getValue(),
                          },
                          {
                            accessorKey: 'date',
                            header: '日期',
                            cell: info => info.getValue(),
                          },
                          {
                            accessorKey: 'time',
                            header: '时间',
                            cell: info => info.getValue(),
                          },
                          {
                            accessorKey: 'stock_code',
                            header: '股票代码',
                            cell: info => info.getValue(),
                          },
                          {
                            accessorKey: 'stock_name',
                            header: '股票名称',
                            cell: info => info.getValue(),
                          },
                          {
                            accessorKey: 'action_name',
                            header: '操作',
                            cell: info => {
                              const action = info.row.original.action;
                              return (
                                <span className={`px-2 py-1 rounded text-xs font-medium ${
                                  action === 'buy' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                                }`}>
                                  {info.getValue()}
                                </span>
                              );
                            },
                          },
                          {
                            accessorKey: 'price',
                            header: '价格',
                            cell: info => `¥${info.getValue()?.toFixed(2) || '0.00'}`,
                          },
                          {
                            accessorKey: 'quantity',
                            header: '数量',
                            cell: info => `${info.getValue()?.toLocaleString() || '0'}股`,
                          },
                          {
                            accessorKey: 'amount',
                            header: '金额',
                            cell: info => `¥${info.getValue()?.toLocaleString() || '0'}`,
                          },
                          {
                            accessorKey: 'profit_loss',
                            header: '盈亏',
                            cell: info => {
                              const value = info.getValue();
                              if (value === null || value === undefined) return '-';
                              return (
                                <span className={value >= 0 ? 'text-green-600 font-medium' : 'text-red-600 font-medium'}>
                                  ¥{value?.toFixed(2)}
                                </span>
                              );
                            },
                          },
                          {
                            accessorKey: 'profit_loss_pct',
                            header: '盈亏比例',
                            cell: info => {
                              const value = info.getValue();
                              if (value === null || value === undefined) return '-';
                              return (
                                <span className={value >= 0 ? 'text-green-600 font-medium' : 'text-red-600 font-medium'}>
                                  {value?.toFixed(2)}%
                                </span>
                              );
                            },
                          },
                          {
                            accessorKey: 'reason',
                            header: '交易原因',
                            cell: info => (
                              <span className="text-xs text-gray-600 max-w-32 truncate" title={info.getValue()}>
                                {info.getValue()}
                              </span>
                            ),
                          },
                        ]}
                      />

                      <PaginationComponent
                        currentPage={tradesPage}
                        pageSize={tradesPageSize}
                        totalItems={backtestResults.trades.length}
                        onPageChange={setTradesPage}
                        onPageSizeChange={(newSize) => {
                          setTradesPageSize(newSize);
                          setTradesPage(1); // 重置到第一页
                        }}
                        itemName="笔交易"
                      />
                    </ShadCardContent>
                  </ShadCard>
                  );
                })()}

                {/* 每日收益曲线 */}
                {backtestResults.daily_returns && backtestResults.daily_returns.length > 0 && (() => {
                  const startIndex = (dailyReturnsPage - 1) * dailyReturnsPageSize;
                  const endIndex = startIndex + dailyReturnsPageSize;
                  const paginatedDailyReturns = backtestResults.daily_returns.slice(startIndex, endIndex);

                  return (
                    <ShadCard>
                      <ShadCardHeader>
                        <ShadCardTitle>每日收益概览</ShadCardTitle>
                      </ShadCardHeader>
                      <ShadCardContent>
                        <DataTable
                          data={paginatedDailyReturns}
                        columns={[
                          {
                            accessorKey: 'date',
                            header: '日期',
                            cell: info => info.getValue(),
                          },
                          {
                            accessorKey: 'value',
                            header: '组合价值',
                            cell: info => `¥${info.getValue()?.toLocaleString() || '0'}`,
                          },
                          {
                            accessorKey: 'return',
                            header: '日收益率',
                            cell: info => {
                              const value = info.getValue();
                              return (
                                <span className={value >= 0 ? 'text-green-600' : 'text-red-600'}>
                                  {(value * 100)?.toFixed(4) || '0.0000'}%
                                </span>
                              );
                            },
                          },
                          {
                            accessorKey: 'cash',
                            header: '现金',
                            cell: info => `¥${info.getValue()?.toLocaleString() || '0'}`,
                          },
                          {
                            accessorKey: 'positions_value',
                            header: '持仓市值',
                            cell: info => `¥${info.getValue()?.toLocaleString() || '0'}`,
                          },
                        ]}
                      />

                      <PaginationComponent
                        currentPage={dailyReturnsPage}
                        pageSize={dailyReturnsPageSize}
                        totalItems={backtestResults.daily_returns.length}
                        onPageChange={setDailyReturnsPage}
                        onPageSizeChange={(newSize) => {
                          setDailyReturnsPageSize(newSize);
                          setDailyReturnsPage(1); // 重置到第一页
                        }}
                        itemName="天数据"
                      />
                    </ShadCardContent>
                  </ShadCard>
                  );
                })()}
              </div>
            )}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default StrategyBacktestPage;
