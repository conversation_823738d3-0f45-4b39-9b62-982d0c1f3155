#!/usr/bin/env python3
"""
测试日志格式和调试信息
"""

import logging
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(__file__))

# 配置详细日志格式
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - [%(filename)s:%(lineno)d:%(funcName)s] - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

logger = logging.getLogger(__name__)

def test_logging_format():
    """测试日志格式"""
    logger.debug("这是一个DEBUG级别的日志")
    logger.info("这是一个INFO级别的日志")
    logger.warning("这是一个WARNING级别的日志")
    logger.error("这是一个ERROR级别的日志")

def test_format_error_simulation():
    """模拟格式化错误"""
    logger.info("=== 模拟格式化错误测试 ===")
    
    # 测试1: 正常格式化
    try:
        value = 123.456
        logger.info(f"正常格式化: {value:.2f}")
    except Exception as e:
        logger.error(f"正常格式化失败: {e}")
    
    # 测试2: None值格式化（会出错）
    try:
        none_value = None
        logger.info(f"None值格式化: {none_value:.2f}")
    except Exception as e:
        logger.error(f"None值格式化错误: {e}")
    
    # 测试3: 安全格式化
    try:
        none_value = None
        safe_value = none_value if none_value is not None else 0
        logger.info(f"安全格式化: {safe_value:.2f}")
    except Exception as e:
        logger.error(f"安全格式化失败: {e}")

def test_backtrader_simulation():
    """模拟backtrader对象的格式化"""
    logger.info("=== 模拟backtrader对象格式化测试 ===")
    
    class MockOrder:
        def __init__(self, size=None, price=None):
            self.executed = MockExecuted(size, price) if size is not None else None
            self.status = 'Completed'
        
        def isbuy(self):
            return True
    
    class MockExecuted:
        def __init__(self, size=None, price=None):
            self.size = size
            self.price = price
    
    # 测试正常订单
    order1 = MockOrder(100, 50.25)
    try:
        size = order1.executed.size if (order1.executed and order1.executed.size is not None) else 0
        price = order1.executed.price if (order1.executed and order1.executed.price is not None) else 0
        logger.info(f"正常订单: {size}股 @{price:.2f}")
    except Exception as e:
        logger.error(f"正常订单格式化错误: {e}")
    
    # 测试None订单
    order2 = MockOrder(None, None)
    try:
        size = order2.executed.size if (order2.executed and order2.executed.size is not None) else 0
        price = order2.executed.price if (order2.executed and order2.executed.price is not None) else 0
        logger.info(f"None订单: {size}股 @{price:.2f}")
    except Exception as e:
        logger.error(f"None订单格式化错误: {e}")

if __name__ == "__main__":
    logger.info("🔍 开始日志格式测试")
    
    test_logging_format()
    test_format_error_simulation()
    test_backtrader_simulation()
    
    logger.info("✅ 日志格式测试完成")
