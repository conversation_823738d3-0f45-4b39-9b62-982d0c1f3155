import React, { useEffect, useRef } from 'react';
import { Card as ShadCard, CardHeader as <PERSON>had<PERSON>ardHeader, CardTitle as Shad<PERSON>ardTitle, CardContent as ShadCardContent } from './UI/card.jsx';
import { Alert as ShadAlert } from './UI/alert.jsx';
import { Stat } from './UI/stat.jsx';
import { Tag } from './UI/tag.jsx';
import { DataTable } from './UI/table.jsx';
import { 
  Activity, 
  AlertTriangle, 
  TrendingUp, 
  TrendingDown, 
  DollarSign,
  Shield,
  Wifi,
  WifiOff
} from 'lucide-react';
import { useLiveTradingWebSocket } from '../hooks/useLiveTradingWebSocket.js';

const LiveTradingMonitor = ({ taskIds = [] }) => {
  const {
    isConnected,
    connectionError,
    // strategies,
    // riskMetrics,
    riskAlerts,
    subscribe,
    unsubscribe,
    getAllStrategies,
    criticalAlerts
  } = useLiveTradingWebSocket();

  // 订阅管理 - 使用ref来避免频繁重新订阅
  const previousTaskIds = useRef([]);

  useEffect(() => {
    if (!isConnected) return;

    // 比较任务ID是否发生变化
    const taskIdsChanged =
      taskIds.length !== previousTaskIds.current.length ||
      taskIds.some(id => !previousTaskIds.current.includes(id)) ||
      previousTaskIds.current.some(id => !taskIds.includes(id));

    if (taskIdsChanged && taskIds.length > 0) {
      // 取消订阅旧的任务
      if (previousTaskIds.current.length > 0) {
        unsubscribe(previousTaskIds.current);
      }

      // 订阅新的任务
      subscribe(taskIds);
      previousTaskIds.current = [...taskIds];

      return () => {
        unsubscribe(taskIds);
      };
    }
  }, [taskIds, isConnected, subscribe, unsubscribe]);

  // 策略表格列定义
  const strategyColumns = [
    {
      accessorKey: 'strategy_name',
      header: '策略名称',
      cell: ({ row }) => (
        <div className="font-medium">{row.getValue('strategy_name')}</div>
      ),
    },
    {
      accessorKey: 'status',
      header: '状态',
      cell: ({ row }) => {
        const status = row.getValue('status');
        const colors = {
          running: 'green',
          stopped: 'gray',
          error: 'red',
          completed: 'blue'
        };
        const labels = {
          running: '运行中',
          stopped: '已停止',
          error: '错误',
          completed: '已完成'
        };
        return <Tag color={colors[status] || 'gray'}>{labels[status] || status}</Tag>;
      },
    },
    {
      accessorKey: 'current_capital',
      header: '当前资金',
      cell: ({ row }) => {
        const value = row.getValue('current_capital');
        return `¥${value?.toLocaleString() || 0}`;
      },
    },
    {
      accessorKey: 'total_return',
      header: '总收益率',
      cell: ({ row }) => {
        const value = row.getValue('total_return') || 0;
        const isPositive = value >= 0;
        return (
          <span className={isPositive ? 'text-green-600 font-semibold' : 'text-red-600 font-semibold'}>
            {isPositive ? '+' : ''}{(value * 100).toFixed(2)}%
          </span>
        );
      },
    },
    {
      accessorKey: 'positions',
      header: '持仓数',
      cell: ({ row }) => {
        const positions = row.getValue('positions') || [];
        return positions.length;
      },
    },
    {
      accessorKey: 'lastUpdate',
      header: '最后更新',
      cell: ({ row }) => {
        const time = row.getValue('lastUpdate');
        return time ? new Date(time).toLocaleTimeString() : '-';
      },
    },
  ];

  // 风险告警表格列定义
  const alertColumns = [
    {
      accessorKey: 'level',
      header: '级别',
      cell: ({ row }) => {
        const level = row.getValue('level');
        const colors = {
          critical: 'red',
          high: 'orange',
          medium: 'yellow',
          low: 'blue'
        };
        const labels = {
          critical: '严重',
          high: '高',
          medium: '中',
          low: '低'
        };
        return <Tag color={colors[level] || 'gray'}>{labels[level] || level}</Tag>;
      },
    },
    {
      accessorKey: 'message',
      header: '告警信息',
      cell: ({ row }) => (
        <div className="max-w-xs truncate" title={row.getValue('message')}>
          {row.getValue('message')}
        </div>
      ),
    },
    {
      accessorKey: 'strategy_id',
      header: '策略ID',
      cell: ({ row }) => {
        const id = row.getValue('strategy_id');
        return (
          <div className="font-mono text-xs">
            {id ? id.slice(0, 8) + '...' : '-'}
          </div>
        );
      },
    },
    {
      accessorKey: 'timestamp',
      header: '时间',
      cell: ({ row }) => {
        const time = row.getValue('timestamp');
        return time ? new Date(time).toLocaleTimeString() : '-';
      },
    },
  ];

  // 计算汇总统计
  const allStrategies = getAllStrategies();
  const runningStrategies = allStrategies.filter(s => s.status === 'running');
  const totalCapital = allStrategies.reduce((sum, s) => sum + (s.current_capital || 0), 0);
  const totalReturn = allStrategies.length > 0 
    ? allStrategies.reduce((sum, s) => sum + (s.total_return || 0), 0) / allStrategies.length 
    : 0;

  return (
    <div className="space-y-6">
      {/* 连接状态 */}
      <ShadCard>
        <ShadCardContent className="pt-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {isConnected ? (
                <>
                  <Wifi className="w-5 h-5 text-green-600" />
                  <span className="text-green-600 font-medium">实时连接正常</span>
                </>
              ) : (
                <>
                  <WifiOff className="w-5 h-5 text-red-600" />
                  <span className="text-red-600 font-medium">连接断开</span>
                </>
              )}
            </div>
            <div className="text-sm text-gray-500">
              监控 {taskIds.length} 个策略
            </div>
          </div>
          
          {connectionError && (
            <ShadAlert
              title="连接错误"
              description={connectionError}
              variant="error"
              className="mt-4"
            />
          )}
        </ShadCardContent>
      </ShadCard>

      {/* 汇总统计 */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <Stat
          title="运行策略"
          value={runningStrategies.length}
          suffix="个"
          icon={<Activity className="w-4 h-4" />}
        />
        <Stat
          title="总资金"
          value={totalCapital.toLocaleString()}
          prefix="¥"
          icon={<DollarSign className="w-4 h-4" />}
        />
        <Stat
          title="平均收益率"
          value={`${totalReturn >= 0 ? '+' : ''}${(totalReturn * 100).toFixed(2)}`}
          suffix="%"
          icon={totalReturn >= 0 ? <TrendingUp className="w-4 h-4" /> : <TrendingDown className="w-4 h-4" />}
          className={totalReturn >= 0 ? 'text-green-600' : 'text-red-600'}
        />
        <Stat
          title="风险告警"
          value={criticalAlerts}
          suffix="个"
          icon={<Shield className="w-4 h-4" />}
          className={criticalAlerts > 0 ? 'text-red-600' : 'text-green-600'}
        />
      </div>

      {/* 策略状态 */}
      <ShadCard>
        <ShadCardHeader>
          <ShadCardTitle className="flex items-center gap-2">
            <Activity className="w-5 h-5" />
            策略实时状态
          </ShadCardTitle>
        </ShadCardHeader>
        <ShadCardContent>
          {allStrategies.length > 0 ? (
            <DataTable
              columns={strategyColumns}
              data={allStrategies}
            />
          ) : (
            <div className="text-center py-8 text-gray-500">
              <Activity className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>暂无策略数据</p>
              <p className="text-sm">等待实时数据推送...</p>
            </div>
          )}
        </ShadCardContent>
      </ShadCard>

      {/* 风险告警 */}
      {riskAlerts.length > 0 && (
        <ShadCard>
          <ShadCardHeader>
            <ShadCardTitle className="flex items-center gap-2">
              <AlertTriangle className="w-5 h-5 text-orange-500" />
              风险告警
              {criticalAlerts > 0 && (
                <Tag color="red" size="sm">{criticalAlerts} 严重</Tag>
              )}
            </ShadCardTitle>
          </ShadCardHeader>
          <ShadCardContent>
            <DataTable
              columns={alertColumns}
              data={riskAlerts.slice(0, 10)} // 只显示最近10条
            />
          </ShadCardContent>
        </ShadCard>
      )}
    </div>
  );
};

export default LiveTradingMonitor;
