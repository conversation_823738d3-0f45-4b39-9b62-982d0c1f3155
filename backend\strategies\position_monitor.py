#!/usr/bin/env python3
"""
持仓监控策略
"""

import pandas as pd
import numpy as np
from typing import Dict, List
from datetime import datetime

from backend.config.position_monitor_config import position_monitor_config
from backend.data.data_manager import data_manager
from backend.core.logger import get_strategy_logger

logger = get_strategy_logger('position_monitor')

class PositionMonitor:
    """持仓监控器"""

    def __init__(self):
        self.config = position_monitor_config
        self.high_prices: Dict[str, float] = {}
        self.signals_history: List[Dict] = []

    def calculate_atr(self, stock_code: str) -> float:
        """计算ATR"""
        period = self.config.get('position_monitor.atr_period', 14)
        df = data_manager.get_stock_data(stock_code, count=period + 10)

        if len(df) < period:
            return 0.0

        df['prev_close'] = df['close'].shift(1)
        df['tr1'] = df['high'] - df['low']
        df['tr2'] = abs(df['high'] - df['prev_close'])
        df['tr3'] = abs(df['low'] - df['prev_close'])
        df['tr'] = df[['tr1', 'tr2', 'tr3']].max(axis=1)

        return df['tr'].rolling(window=period).mean().iloc[-1]

    def check_position(self, position: Dict, total_assets: float) -> List[Dict]:
        """检查单个持仓"""
        signals = []
        stock_code = position['stock_code']
        current_price = position['current_price']
        avg_price = position['avg_price']
        quantity = position['quantity']

        # 1. 亏损止损
        if self.config.get('position_monitor.loss_stop_enabled', True):
            threshold = self.config.get('position_monitor.loss_stop_percent', 5.0) / 100
            loss_ratio = (current_price - avg_price) / avg_price

            if loss_ratio <= -threshold:
                signals.append({
                    'stock_code': stock_code,
                    'signal_type': 'loss_stop',
                    'current_price': current_price,
                    'loss_ratio': loss_ratio,
                    'message': f"{stock_code} 亏损{abs(loss_ratio):.2%}，触发亏损止损",
                    'timestamp': datetime.now().isoformat()
                })

        # 2. 移动止损
        if self.config.get('position_monitor.trailing_stop_enabled', True):
            threshold = self.config.get('position_monitor.trailing_stop_percent', 5.0) / 100

            if stock_code not in self.high_prices:
                self.high_prices[stock_code] = max(current_price, avg_price)
            else:
                self.high_prices[stock_code] = max(self.high_prices[stock_code], current_price)

            high_price = self.high_prices[stock_code]
            drop_ratio = (high_price - current_price) / high_price

            if drop_ratio >= threshold:
                signals.append({
                    'stock_code': stock_code,
                    'signal_type': 'trailing_stop',
                    'current_price': current_price,
                    'high_price': high_price,
                    'drop_ratio': drop_ratio,
                    'message': f"{stock_code} 从最高点{high_price:.2f}下跌{drop_ratio:.2%}，触发移动止损",
                    'timestamp': datetime.now().isoformat()
                })

        # 3. ATR止损
        if self.config.get('position_monitor.atr_stop_enabled', True):
            multiplier = self.config.get('position_monitor.atr_multiplier', 1.5)
            atr = self.calculate_atr(stock_code)

            if atr > 0:
                atr_stop_price = avg_price - (atr * multiplier)
                if current_price <= atr_stop_price:
                    signals.append({
                        'stock_code': stock_code,
                        'signal_type': 'atr_stop',
                        'current_price': current_price,
                        'atr_stop_price': atr_stop_price,
                        'atr': atr,
                        'message': f"{stock_code} 跌破ATR止损位{atr_stop_price:.2f}",
                        'timestamp': datetime.now().isoformat()
                    })

        # 4. 组合止损
        if self.config.get('position_monitor.portfolio_stop_enabled', True):
            threshold = self.config.get('position_monitor.portfolio_loss_percent', 3.0) / 100
            loss_amount = (avg_price - current_price) * quantity
            portfolio_loss_ratio = loss_amount / total_assets

            if portfolio_loss_ratio >= threshold:
                signals.append({
                    'stock_code': stock_code,
                    'signal_type': 'portfolio_stop',
                    'current_price': current_price,
                    'loss_amount': loss_amount,
                    'portfolio_loss_ratio': portfolio_loss_ratio,
                    'message': f"{stock_code} 亏损占总资产{portfolio_loss_ratio:.2%}，触发组合止损",
                    'timestamp': datetime.now().isoformat()
                })

        return signals

    def monitor_positions(self) -> List[Dict]:
        """监控所有持仓"""
        signals = []

        account_info = data_manager.get_account_info()
        positions = account_info.get('positions', [])
        total_assets = account_info.get('total_value', 1000000)

        for position in positions:
            position_signals = self.check_position(position, total_assets)
            signals.extend(position_signals)

        # 保存到历史
        self.signals_history.extend(signals)
        max_signals = self.config.get('position_monitor.max_signals', 1000)
        if len(self.signals_history) > max_signals:
            self.signals_history = self.signals_history[-max_signals:]

        return signals

# 全局监控器实例
position_monitor = PositionMonitor()
