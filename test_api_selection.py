#!/usr/bin/env python3
"""
测试选股API
"""

import requests
import time
import json

def test_selection_api():
    """测试选股API"""
    print("🔍 测试选股API")
    print("=" * 50)
    
    base_url = "http://localhost:8000"
    
    # 1. 测试健康检查
    try:
        response = requests.get(f"{base_url}/health", timeout=5)
        if response.status_code == 200:
            print("✅ 后端服务正常运行")
        else:
            print(f"❌ 后端服务异常: {response.status_code}")
            return
    except Exception as e:
        print(f"❌ 无法连接后端服务: {e}")
        return
    
    # 2. 测试选股预设
    try:
        response = requests.get(f"{base_url}/api/stock-selection/presets", timeout=10)
        if response.status_code == 200:
            presets = response.json()
            print(f"✅ 获取选股预设成功: {len(presets.get('data', []))} 个预设")
        else:
            print(f"❌ 获取选股预设失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 获取选股预设异常: {e}")
    
    # 3. 测试选股功能
    print(f"\n🚀 测试选股功能...")
    
    # 创建选股请求
    selection_request = {
        "criteria": {
            "volume_min": 0.1,  # 宽松条件
            "condition_logic": "flexible"
        },
        "custom_name": "API测试选股",
        "max_results": 50
    }
    
    try:
        print("📤 发送选股请求...")
        response = requests.post(
            f"{base_url}/api/stock-selection/select",
            json=selection_request,
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                task_id = data['data']['task_id']
                print(f"✅ 选股任务启动成功")
                print(f"   任务ID: {task_id}")
                
                # 4. 轮询任务状态
                print(f"\n🔄 轮询任务状态...")
                max_polls = 60  # 最多轮询60次
                poll_count = 0
                
                while poll_count < max_polls:
                    try:
                        status_response = requests.get(
                            f"{base_url}/api/stock-selection/status/{task_id}",
                            timeout=10
                        )
                        
                        if status_response.status_code == 200:
                            status_data = status_response.json()
                            if status_data.get('success'):
                                task_status = status_data['data']
                                status = task_status.get('status', 'unknown')
                                progress = task_status.get('progress', 0)
                                message = task_status.get('message', '')
                                
                                print(f"   状态: {status}, 进度: {progress}%, 消息: {message}")
                                
                                if status == 'completed':
                                    results = task_status.get('result', [])
                                    print(f"\n✅ 选股完成!")
                                    print(f"📈 选中股票数量: {len(results)}")
                                    
                                    if results:
                                        print(f"\n📊 前5只选中的股票:")
                                        for i, stock in enumerate(results[:5]):
                                            print(f"  {i+1}. {stock.get('stock_code', 'N/A')} - {stock.get('stock_name', 'N/A')}")
                                            print(f"     评分: {stock.get('score', 0):.2f}")
                                    
                                    return True
                                    
                                elif status == 'failed':
                                    error = task_status.get('error', '未知错误')
                                    print(f"❌ 选股失败: {error}")
                                    return False
                                
                                elif status in ['pending', 'running']:
                                    time.sleep(2)  # 等待2秒后继续轮询
                                    poll_count += 1
                                    continue
                                else:
                                    print(f"❌ 未知状态: {status}")
                                    return False
                            else:
                                print(f"❌ 状态查询失败: {status_data.get('message', '未知错误')}")
                                return False
                        else:
                            print(f"❌ 状态查询请求失败: {status_response.status_code}")
                            return False
                            
                    except Exception as e:
                        print(f"❌ 状态查询异常: {e}")
                        return False
                
                print(f"❌ 任务超时，已轮询 {max_polls} 次")
                return False
                
            else:
                print(f"❌ 选股任务启动失败: {data.get('message', '未知错误')}")
                return False
        else:
            print(f"❌ 选股请求失败: {response.status_code}")
            print(f"   响应: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 选股请求异常: {e}")
        return False

if __name__ == "__main__":
    success = test_selection_api()
    if success:
        print(f"\n🎉 选股API测试成功!")
    else:
        print(f"\n❌ 选股API测试失败!")
