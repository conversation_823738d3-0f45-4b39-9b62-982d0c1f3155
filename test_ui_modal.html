<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试新界面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .warning {
            background-color: #fff3cd;
            border-color: #ffeaa7;
            color: #856404;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .btn {
            display: inline-block;
            padding: 10px 20px;
            margin: 5px;
            background-color: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            border: none;
            cursor: pointer;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .btn-success {
            background-color: #28a745;
        }
        .btn-success:hover {
            background-color: #1e7e34;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 QMT-TRADER 新界面测试</h1>
        <p>请按照以下步骤测试新的可勾选选股界面：</p>
        
        <div class="test-section success">
            <h3>✅ 第1步：访问选股页面</h3>
            <p>点击下面的链接访问选股页面：</p>
            <a href="http://localhost:3000/stock-selection" class="btn btn-success" target="_blank">
                打开选股页面
            </a>
            <p><small>应该看到"智能选股"页面，包含预设策略和"开始选股"按钮</small></p>
        </div>
        
        <div class="test-section warning">
            <h3>⚠️ 第2步：点击"开始选股"按钮</h3>
            <p>在选股页面中，找到并点击蓝色的"开始选股"按钮</p>
            <p><strong>预期结果：</strong>应该弹出一个大的模态框，标题为"设置选股条件"</p>
            <p><small>如果没有弹出模态框，请刷新页面重试</small></p>
        </div>
        
        <div class="test-section warning">
            <h3>🔍 第3步：查看可勾选条件</h3>
            <p>在弹出的模态框中，您应该看到：</p>
            <ul>
                <li>📊 <strong>RSI指标</strong>：RSI超卖、RSI超买、RSI中性</li>
                <li>📈 <strong>布林带指标</strong>：向上突破、向下突破、收窄</li>
                <li>📊 <strong>成交量指标</strong>：成交量放大、成交量萎缩</li>
                <li>📈 <strong>趋势指标</strong>：均线金叉、均线死叉、强势动量、弱势动量</li>
                <li>⚡ <strong>波动率指标</strong>：高波动率、低波动率</li>
                <li>🎯 <strong>价格位置</strong>：接近高点、接近低点</li>
            </ul>
            <p><small>每个条件都应该有复选框可以勾选</small></p>
        </div>
        
        <div class="test-section warning">
            <h3>⚙️ 第4步：测试条件组合模式</h3>
            <p>在模态框底部，您应该看到三种条件组合模式：</p>
            <ul>
                <li>🎯 <strong>灵活模式</strong>（推荐）：基于评分系统</li>
                <li>🔒 <strong>严格模式</strong>：所有条件都必须满足</li>
                <li>🔓 <strong>宽松模式</strong>：满足任意条件即可</li>
            </ul>
            <p><small>默认应该选中"灵活模式"</small></p>
        </div>
        
        <div class="test-section success">
            <h3>🚀 第5步：执行选股测试</h3>
            <p>勾选几个感兴趣的条件，然后点击"开始选股"按钮</p>
            <p><strong>预期结果：</strong></p>
            <ul>
                <li>模态框关闭</li>
                <li>页面显示选股进度或结果</li>
                <li>后端日志应该简洁，不再有大量datamanager输出</li>
            </ul>
        </div>
        
        <div class="test-section">
            <h3>🔧 故障排除</h3>
            <p><strong>如果模态框没有弹出：</strong></p>
            <ol>
                <li>按F12打开开发者工具，查看Console是否有错误</li>
                <li>刷新页面重试</li>
                <li>检查网络连接</li>
            </ol>
            
            <p><strong>如果界面还是旧的：</strong></p>
            <ol>
                <li>强制刷新页面（Ctrl+F5）</li>
                <li>清除浏览器缓存</li>
                <li>尝试无痕模式</li>
            </ol>
        </div>
        
        <div class="test-section success">
            <h3>📋 测试检查清单</h3>
            <p>请确认以下项目：</p>
            <ul>
                <li>☐ 能访问选股页面</li>
                <li>☐ 点击"开始选股"有反应</li>
                <li>☐ 弹出模态框显示选股条件</li>
                <li>☐ 可以勾选不同的条件</li>
                <li>☐ 可以选择条件组合模式</li>
                <li>☐ 可以执行选股</li>
                <li>☐ 后端日志简洁</li>
            </ul>
        </div>
        
        <div class="test-section">
            <h3>📞 需要帮助？</h3>
            <p>如果遇到问题，请提供以下信息：</p>
            <ul>
                <li>浏览器类型和版本</li>
                <li>具体的错误现象</li>
                <li>开发者工具中的错误信息</li>
                <li>后端日志输出</li>
            </ul>
        </div>
    </div>
    
    <script>
        // 简单的页面交互
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🎯 QMT-TRADER 界面测试页面已加载');
            console.log('请按照步骤测试新的可勾选选股界面');
        });
    </script>
</body>
</html>
