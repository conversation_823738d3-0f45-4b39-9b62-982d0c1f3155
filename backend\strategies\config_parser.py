"""
策略配置解析器
负责解析策略配置文件，验证配置有效性，并动态生成策略实例
"""

import json
import yaml
import os
from typing import Dict, Any, List, Optional, Type, Union
from pathlib import Path
import importlib
import inspect

from backend.core.logger import get_logger
from backend.strategies.config_driven_strategy import (
    MultiSignalStrategy,
    StrategyConfigSchema,
    ConditionConfig,
    SignalConfig,
    RiskConfig,
    CONDITION_EVALUATORS
)

logger = get_logger(__name__)

class ConfigValidationError(Exception):
    """配置验证错误"""
    pass

class StrategyConfigParser:
    """策略配置解析器"""
    
    def __init__(self, templates_dir: str = "backend/config/strategy_templates"):
        self.templates_dir = Path(templates_dir)
        self.templates_dir.mkdir(parents=True, exist_ok=True)
        
        # 支持的文件格式
        self.supported_formats = ['.json', '.yaml', '.yml']
        
        logger.info(f"📁 策略配置解析器初始化，模板目录: {self.templates_dir}")
    
    def parse_config_file(self, config_path: Union[str, Path]) -> StrategyConfigSchema:
        """解析配置文件"""
        config_file = Path(config_path)
        
        if not config_file.exists():
            raise FileNotFoundError(f"配置文件不存在: {config_path}")
        
        if config_file.suffix.lower() not in self.supported_formats:
            raise ValueError(f"不支持的文件格式: {config_file.suffix}")
        
        try:
            # 读取配置文件
            with open(config_file, 'r', encoding='utf-8') as f:
                if config_file.suffix.lower() in ['.yaml', '.yml']:
                    config_data = yaml.safe_load(f)
                else:
                    config_data = json.load(f)
            
            # 解析配置
            config_schema = self.parse_config_dict(config_data)
            
            logger.info(f"✅ 配置文件解析成功: {config_path}")
            return config_schema
            
        except Exception as e:
            logger.error(f"❌ 配置文件解析失败: {config_path} - {e}")
            raise ConfigValidationError(f"配置文件解析失败: {e}")
    
    def parse_config_dict(self, config_data: Dict[str, Any]) -> StrategyConfigSchema:
        """解析配置字典"""
        try:
            # 验证必需字段
            self._validate_required_fields(config_data)
            
            # 解析买入信号
            buy_signals = self._parse_signals(config_data.get('buy_signals', []))
            
            # 解析卖出信号
            sell_signals = self._parse_signals(config_data.get('sell_signals', []))
            
            # 解析风险管理配置
            risk_config = self._parse_risk_config(config_data.get('risk_management', {}))
            
            # 创建策略配置模式
            config_schema = StrategyConfigSchema(
                name=config_data['name'],
                display_name=config_data.get('display_name', config_data['name']),
                description=config_data.get('description', ''),
                version=config_data.get('version', '1.0.0'),
                author=config_data.get('author', 'Unknown'),
                category=config_data.get('category', 'custom'),
                buy_signals=buy_signals,
                sell_signals=sell_signals,
                risk_management=risk_config,
                universe=config_data.get('universe', []),
                rebalance_frequency=config_data.get('rebalance_frequency', 'daily'),
                benchmark=config_data.get('benchmark', '000300.SH'),
                indicators=config_data.get('indicators', {}),
                custom_params=config_data.get('custom_params', {})
            )
            
            # 验证配置
            self._validate_config_schema(config_schema)
            
            logger.info(f"✅ 策略配置解析成功: {config_schema.name}")
            return config_schema
            
        except Exception as e:
            logger.error(f"❌ 配置字典解析失败: {e}")
            raise ConfigValidationError(f"配置解析失败: {e}")
    
    def _validate_required_fields(self, config_data: Dict[str, Any]):
        """验证必需字段"""
        required_fields = ['name', 'display_name', 'description']
        
        for field in required_fields:
            if field not in config_data or not config_data[field]:
                raise ConfigValidationError(f"缺少必需字段: {field}")
    
    def _parse_signals(self, signals_data: List[Dict[str, Any]]) -> List[SignalConfig]:
        """解析信号配置"""
        signals = []
        
        for signal_data in signals_data:
            # 解析条件
            conditions = []
            for cond_data in signal_data.get('conditions', []):
                condition = ConditionConfig(
                    type=cond_data['type'],
                    name=cond_data['name'],
                    enabled=cond_data.get('enabled', True),
                    params=cond_data.get('params', {}),
                    weight=cond_data.get('weight', 1.0),
                    description=cond_data.get('description', '')
                )
                
                # 验证条件类型
                if condition.type not in CONDITION_EVALUATORS:
                    logger.warning(f"⚠️ 未知的条件类型: {condition.type}")
                
                conditions.append(condition)
            
            # 创建信号配置
            signal = SignalConfig(
                name=signal_data['name'],
                conditions=conditions,
                logic=signal_data.get('logic', 'AND'),
                min_conditions=signal_data.get('min_conditions', 1),
                signal_strength=signal_data.get('signal_strength', 1.0),
                description=signal_data.get('description', '')
            )
            
            signals.append(signal)
        
        return signals
    
    def _parse_risk_config(self, risk_data: Dict[str, Any]) -> RiskConfig:
        """解析风险管理配置"""
        return RiskConfig(
            stop_loss=risk_data.get('stop_loss'),
            take_profit=risk_data.get('take_profit'),
            trailing_stop=risk_data.get('trailing_stop'),
            max_loss_per_trade=risk_data.get('max_loss_per_trade'),
            max_positions=risk_data.get('max_positions', 5),
            position_size=risk_data.get('position_size', 0.2),
            risk_per_trade=risk_data.get('risk_per_trade', 0.02)
        )
    
    def _validate_config_schema(self, config_schema: StrategyConfigSchema):
        """验证配置模式"""
        # 验证信号配置
        if not config_schema.buy_signals and not config_schema.sell_signals:
            raise ConfigValidationError("至少需要配置买入信号或卖出信号")
        
        # 验证风险管理参数
        risk = config_schema.risk_management
        if risk.position_size <= 0 or risk.position_size > 1:
            raise ConfigValidationError("仓位大小必须在0-1之间")
        
        if risk.max_positions <= 0:
            raise ConfigValidationError("最大持仓数必须大于0")
        
        # 验证止损止盈参数
        if risk.stop_loss and (risk.stop_loss <= 0 or risk.stop_loss >= 1):
            raise ConfigValidationError("止损比例必须在0-1之间")
        
        if risk.take_profit and (risk.take_profit <= 0 or risk.take_profit >= 1):
            raise ConfigValidationError("止盈比例必须在0-1之间")
        
        # 验证股票池
        if config_schema.universe:
            for stock_code in config_schema.universe:
                if not isinstance(stock_code, str) or len(stock_code) < 6:
                    raise ConfigValidationError(f"无效的股票代码: {stock_code}")
    
    def create_strategy_instance(self, config_path: Union[str, Path]) -> MultiSignalStrategy:
        """创建策略实例"""
        try:
            # 解析配置
            config_schema = self.parse_config_file(config_path)

            # 创建策略实例
            strategy = MultiSignalStrategy()
            strategy.config_schema = config_schema

            logger.info(f"✅ 策略实例创建成功: {config_schema.name}")
            return strategy

        except Exception as e:
            logger.error(f"❌ 策略实例创建失败: {e}")
            raise

    def create_strategy_from_dict(self, config_dict: Dict[str, Any]) -> MultiSignalStrategy:
        """从配置字典创建策略实例"""
        try:
            # 解析配置
            config_schema = self.parse_config_dict(config_dict)

            # 创建策略实例
            strategy = MultiSignalStrategy()
            strategy.config_schema = config_schema

            logger.info(f"✅ 策略实例创建成功: {config_schema.name}")
            return strategy

        except Exception as e:
            logger.error(f"❌ 策略实例创建失败: {e}")
            raise
    
    def list_template_configs(self) -> List[Dict[str, Any]]:
        """列出所有模板配置"""
        templates = []
        
        for file_path in self.templates_dir.glob('*'):
            if file_path.suffix.lower() in self.supported_formats:
                try:
                    config_schema = self.parse_config_file(file_path)
                    templates.append({
                        'file_path': str(file_path),
                        'name': config_schema.name,
                        'display_name': config_schema.display_name,
                        'description': config_schema.description,
                        'category': config_schema.category,
                        'version': config_schema.version,
                        'author': config_schema.author
                    })
                except Exception as e:
                    logger.warning(f"⚠️ 跳过无效的模板文件: {file_path} - {e}")
        
        logger.info(f"📋 找到 {len(templates)} 个策略模板")
        return templates
    
    def validate_config_file(self, config_path: Union[str, Path]) -> Dict[str, Any]:
        """验证配置文件"""
        try:
            config_schema = self.parse_config_file(config_path)
            return {
                'valid': True,
                'message': '配置文件验证通过',
                'config': config_schema
            }
        except Exception as e:
            return {
                'valid': False,
                'message': str(e),
                'config': None
            }
    
    def save_config_to_file(self, config_schema: StrategyConfigSchema, 
                           file_path: Union[str, Path], format: str = 'json'):
        """保存配置到文件"""
        file_path = Path(file_path)
        
        # 确保目录存在
        file_path.parent.mkdir(parents=True, exist_ok=True)
        
        try:
            # 转换为字典
            config_dict = self._schema_to_dict(config_schema)
            
            # 保存文件
            with open(file_path, 'w', encoding='utf-8') as f:
                if format.lower() in ['yaml', 'yml']:
                    yaml.dump(config_dict, f, default_flow_style=False, allow_unicode=True)
                else:
                    json.dump(config_dict, f, indent=2, ensure_ascii=False)
            
            logger.info(f"✅ 配置文件保存成功: {file_path}")
            
        except Exception as e:
            logger.error(f"❌ 配置文件保存失败: {e}")
            raise
    
    def _schema_to_dict(self, config_schema: StrategyConfigSchema) -> Dict[str, Any]:
        """将配置模式转换为字典"""
        return {
            'name': config_schema.name,
            'display_name': config_schema.display_name,
            'description': config_schema.description,
            'version': config_schema.version,
            'author': config_schema.author,
            'category': config_schema.category,
            'buy_signals': [self._signal_to_dict(signal) for signal in config_schema.buy_signals],
            'sell_signals': [self._signal_to_dict(signal) for signal in config_schema.sell_signals],
            'risk_management': {
                'stop_loss': config_schema.risk_management.stop_loss,
                'take_profit': config_schema.risk_management.take_profit,
                'trailing_stop': config_schema.risk_management.trailing_stop,
                'max_loss_per_trade': config_schema.risk_management.max_loss_per_trade,
                'max_positions': config_schema.risk_management.max_positions,
                'position_size': config_schema.risk_management.position_size,
                'risk_per_trade': config_schema.risk_management.risk_per_trade
            },
            'universe': config_schema.universe,
            'rebalance_frequency': config_schema.rebalance_frequency,
            'benchmark': config_schema.benchmark,
            'indicators': config_schema.indicators,
            'custom_params': config_schema.custom_params
        }
    
    def _signal_to_dict(self, signal: SignalConfig) -> Dict[str, Any]:
        """将信号配置转换为字典"""
        return {
            'name': signal.name,
            'description': signal.description,
            'logic': signal.logic,
            'min_conditions': signal.min_conditions,
            'signal_strength': signal.signal_strength,
            'conditions': [
                {
                    'type': cond.type,
                    'name': cond.name,
                    'enabled': cond.enabled,
                    'weight': cond.weight,
                    'params': cond.params,
                    'description': cond.description
                }
                for cond in signal.conditions
            ]
        }

# 全局配置解析器实例
config_parser = StrategyConfigParser()
