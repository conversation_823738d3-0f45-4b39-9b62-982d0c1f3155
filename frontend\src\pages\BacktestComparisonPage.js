import React, { useState, useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
import { Card as ShadCard, Card<PERSON>ontent as <PERSON><PERSON><PERSON><PERSON>Content, Card<PERSON>eader as <PERSON><PERSON><PERSON><PERSON><PERSON>eader, CardTitle as ShadCardTitle } from '../components/UI/card.jsx';
import { But<PERSON> as ShadButton } from '../components/UI/button.jsx';
import { DataTable } from '../components/UI/table.jsx';
import { Tag } from '../components/UI/tag.jsx';
import { Stat } from '../components/UI/stat.jsx';
import { BarChart3, TrendingUp, TrendingDown, ArrowLeft, Download } from 'lucide-react';
import { toast } from 'sonner';

const BacktestComparisonPage = () => {
  const [searchParams] = useSearchParams();
  const [comparisonData, setComparisonData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [chartData, setChartData] = useState(null);

  // 从URL参数获取要对比的任务ID
  const taskIds = searchParams.get('task_ids')?.split(',') || [];

  // 加载对比数据
  const loadComparisonData = async () => {
    if (taskIds.length < 2) {
      toast.error('至少需要2个回测结果进行对比');
      return;
    }

    try {
      setLoading(true);
      
      const response = await fetch(`/api/backtest/compare?task_ids=${taskIds.join(',')}`);
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const data = await response.json();
      setComparisonData(data.results || []);
      
      // 准备图表数据
      prepareChartData(data.results || []);
      
    } catch (error) {
      console.error('加载对比数据失败:', error);
      toast.error('加载对比数据失败: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  // 准备图表数据
  const prepareChartData = (results) => {
    const chartData = {
      labels: results.map(r => r.strategy_name),
      datasets: [
        {
          label: '总收益率 (%)',
          data: results.map(r => (r.total_return * 100).toFixed(2)),
          backgroundColor: 'rgba(59, 130, 246, 0.5)',
          borderColor: 'rgb(59, 130, 246)',
          borderWidth: 1
        },
        {
          label: '夏普比率',
          data: results.map(r => r.sharpe_ratio?.toFixed(2) || 0),
          backgroundColor: 'rgba(16, 185, 129, 0.5)',
          borderColor: 'rgb(16, 185, 129)',
          borderWidth: 1
        },
        {
          label: '最大回撤 (%)',
          data: results.map(r => Math.abs(r.max_drawdown * 100).toFixed(2)),
          backgroundColor: 'rgba(239, 68, 68, 0.5)',
          borderColor: 'rgb(239, 68, 68)',
          borderWidth: 1
        }
      ]
    };
    
    setChartData(chartData);
  };

  // 导出对比报告
  const exportComparison = async () => {
    try {
      // 创建对比报告数据
      const reportData = {
        comparison_date: new Date().toISOString(),
        task_ids: taskIds,
        results: comparisonData,
        summary: {
          best_return: Math.max(...comparisonData.map(r => r.total_return)),
          best_sharpe: Math.max(...comparisonData.map(r => r.sharpe_ratio || 0)),
          lowest_drawdown: Math.min(...comparisonData.map(r => Math.abs(r.max_drawdown)))
        }
      };

      // 创建并下载文件
      const blob = new Blob([JSON.stringify(reportData, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `backtest_comparison_${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      toast.success('对比报告导出成功');
    } catch (error) {
      console.error('导出失败:', error);
      toast.error('导出失败: ' + error.message);
    }
  };

  // 对比表格列定义
  const comparisonColumns = [
    {
      accessorKey: 'strategy_name',
      header: '策略名称',
      cell: ({ row }) => (
        <div className="font-medium">{row.getValue('strategy_name')}</div>
      ),
    },
    {
      accessorKey: 'start_date',
      header: '回测期间',
      cell: ({ row }) => (
        <div className="text-sm">
          {row.getValue('start_date')} ~ {row.original.end_date}
        </div>
      ),
    },
    {
      accessorKey: 'initial_capital',
      header: '初始资金',
      cell: ({ row }) => `¥${row.getValue('initial_capital')?.toLocaleString() || '0'}`,
    },
    {
      accessorKey: 'final_capital',
      header: '最终资金',
      cell: ({ row }) => `¥${row.getValue('final_capital')?.toLocaleString() || '0'}`,
    },
    {
      accessorKey: 'total_return',
      header: '总收益率',
      cell: ({ row }) => {
        const value = row.getValue('total_return');
        const percentage = (value * 100).toFixed(2);
        return (
          <span className={value >= 0 ? 'text-green-600 font-semibold' : 'text-red-600 font-semibold'}>
            {percentage}%
          </span>
        );
      },
    },
    {
      accessorKey: 'annual_return',
      header: '年化收益',
      cell: ({ row }) => {
        const value = row.getValue('annual_return');
        const percentage = (value * 100).toFixed(2);
        return (
          <span className={value >= 0 ? 'text-green-600 font-semibold' : 'text-red-600 font-semibold'}>
            {percentage}%
          </span>
        );
      },
    },
    {
      accessorKey: 'sharpe_ratio',
      header: '夏普比率',
      cell: ({ row }) => {
        const value = row.getValue('sharpe_ratio');
        return <span className="font-mono">{value?.toFixed(3) || 'N/A'}</span>;
      },
    },
    {
      accessorKey: 'max_drawdown',
      header: '最大回撤',
      cell: ({ row }) => {
        const value = row.getValue('max_drawdown');
        const percentage = (Math.abs(value) * 100).toFixed(2);
        return <span className="text-red-600">-{percentage}%</span>;
      },
    },
    {
      accessorKey: 'win_rate',
      header: '胜率',
      cell: ({ row }) => {
        const value = row.getValue('win_rate');
        const percentage = (value * 100).toFixed(1);
        return <span>{percentage}%</span>;
      },
    },
    {
      accessorKey: 'total_trades',
      header: '交易次数',
      cell: ({ row }) => <span>{row.getValue('total_trades')}</span>,
    },
    {
      accessorKey: 'profit_factor',
      header: '盈利因子',
      cell: ({ row }) => {
        const value = row.getValue('profit_factor');
        return <span className="font-mono">{value?.toFixed(2) || 'N/A'}</span>;
      },
    },
  ];

  // 计算统计数据
  const getStatistics = () => {
    if (comparisonData.length === 0) return null;

    const returns = comparisonData.map(r => r.total_return);
    const sharpeRatios = comparisonData.map(r => r.sharpe_ratio || 0);
    const drawdowns = comparisonData.map(r => Math.abs(r.max_drawdown));

    return {
      bestReturn: Math.max(...returns),
      worstReturn: Math.min(...returns),
      avgReturn: returns.reduce((a, b) => a + b, 0) / returns.length,
      bestSharpe: Math.max(...sharpeRatios),
      lowestDrawdown: Math.min(...drawdowns),
      highestDrawdown: Math.max(...drawdowns)
    };
  };

  const statistics = getStatistics();

  useEffect(() => {
    if (taskIds.length >= 2) {
      loadComparisonData();
    }
  }, []);

  if (taskIds.length < 2) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <BarChart3 className="h-16 w-16 mx-auto mb-4 text-gray-400" />
          <h2 className="text-xl font-semibold mb-2">无效的对比请求</h2>
          <p className="text-gray-600 mb-4">至少需要2个回测结果进行对比</p>
          <ShadButton onClick={() => window.history.back()}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            返回
          </ShadButton>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6 p-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">回测结果对比</h1>
          <p className="text-gray-600 mt-1">对比 {comparisonData.length} 个回测结果</p>
        </div>
        <div className="flex gap-2">
          <ShadButton onClick={() => window.history.back()} variant="outline">
            <ArrowLeft className="h-4 w-4 mr-2" />
            返回
          </ShadButton>
          <ShadButton onClick={exportComparison} disabled={comparisonData.length === 0}>
            <Download className="h-4 w-4 mr-2" />
            导出报告
          </ShadButton>
        </div>
      </div>

      {/* 统计概览 */}
      {statistics && (
        <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
          <Stat
            title="最佳收益率"
            value={`${(statistics.bestReturn * 100).toFixed(2)}%`}
            icon={<TrendingUp className="h-5 w-5" />}
            color="green"
          />
          <Stat
            title="最差收益率"
            value={`${(statistics.worstReturn * 100).toFixed(2)}%`}
            icon={<TrendingDown className="h-5 w-5" />}
            color="red"
          />
          <Stat
            title="平均收益率"
            value={`${(statistics.avgReturn * 100).toFixed(2)}%`}
            icon={<BarChart3 className="h-5 w-5" />}
            color="blue"
          />
          <Stat
            title="最佳夏普比率"
            value={statistics.bestSharpe.toFixed(3)}
            icon={<TrendingUp className="h-5 w-5" />}
            color="green"
          />
          <Stat
            title="最小回撤"
            value={`${(statistics.lowestDrawdown * 100).toFixed(2)}%`}
            icon={<TrendingUp className="h-5 w-5" />}
            color="green"
          />
          <Stat
            title="最大回撤"
            value={`${(statistics.highestDrawdown * 100).toFixed(2)}%`}
            icon={<TrendingDown className="h-5 w-5" />}
            color="red"
          />
        </div>
      )}

      {/* 对比表格 */}
      <ShadCard>
        <ShadCardHeader>
          <ShadCardTitle>详细对比</ShadCardTitle>
        </ShadCardHeader>
        <ShadCardContent>
          <DataTable
            data={comparisonData}
            columns={comparisonColumns}
            loading={loading}
          />
        </ShadCardContent>
      </ShadCard>

      {/* 性能排名 */}
      {comparisonData.length > 0 && (
        <ShadCard>
          <ShadCardHeader>
            <ShadCardTitle>性能排名</ShadCardTitle>
          </ShadCardHeader>
          <ShadCardContent>
            <div className="space-y-4">
              {/* 按总收益率排名 */}
              <div>
                <h4 className="font-medium mb-2">总收益率排名</h4>
                <div className="space-y-2">
                  {[...comparisonData]
                    .sort((a, b) => b.total_return - a.total_return)
                    .map((result, index) => (
                      <div key={result.task_id} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                        <div className="flex items-center gap-2">
                          <span className="font-bold text-lg w-6">{index + 1}</span>
                          <span className="font-medium">{result.strategy_name}</span>
                        </div>
                        <span className={`font-semibold ${
                          result.total_return >= 0 ? 'text-green-600' : 'text-red-600'
                        }`}>
                          {(result.total_return * 100).toFixed(2)}%
                        </span>
                      </div>
                    ))}
                </div>
              </div>

              {/* 按夏普比率排名 */}
              <div>
                <h4 className="font-medium mb-2">夏普比率排名</h4>
                <div className="space-y-2">
                  {[...comparisonData]
                    .sort((a, b) => (b.sharpe_ratio || 0) - (a.sharpe_ratio || 0))
                    .map((result, index) => (
                      <div key={result.task_id} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                        <div className="flex items-center gap-2">
                          <span className="font-bold text-lg w-6">{index + 1}</span>
                          <span className="font-medium">{result.strategy_name}</span>
                        </div>
                        <span className="font-semibold font-mono">
                          {result.sharpe_ratio?.toFixed(3) || 'N/A'}
                        </span>
                      </div>
                    ))}
                </div>
              </div>
            </div>
          </ShadCardContent>
        </ShadCard>
      )}
    </div>
  );
};

export default BacktestComparisonPage;
