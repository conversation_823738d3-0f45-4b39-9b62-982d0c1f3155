#!/usr/bin/env python3
"""
信号生成器 - 从现有策略中提取纯信号逻辑
将策略逻辑与执行框架解耦，支持回测和实盘复用
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional
from abc import ABC, abstractmethod
from dataclasses import dataclass
from enum import Enum

class SignalType(Enum):
    BUY = "buy"
    SELL = "sell"
    HOLD = "hold"

@dataclass
class Signal:
    """交易信号"""
    stock_code: str
    signal_type: SignalType
    strength: float  # 信号强度 0-1
    price: float
    quantity: int
    reasons: List[str]  # 信号原因列表
    
    def to_dict(self):
        return {
            'stock_code': self.stock_code,
            'signal_type': self.signal_type.value,
            'strength': self.strength,
            'price': self.price,
            'quantity': self.quantity,
            'reasons': self.reasons
        }

class SignalGenerator(ABC):
    """信号生成器基类"""
    
    def __init__(self, params: Dict):
        self.params = params
    
    @abstractmethod
    def generate_signals(self, market_data: Dict[str, pd.DataFrame]) -> List[Signal]:
        """生成交易信号"""
        pass
    
    @abstractmethod
    def get_name(self) -> str:
        """获取生成器名称"""
        pass

class MultiSignalGenerator(SignalGenerator):
    """多信号生成器 - 从MultiSignalStrategyBT提取的逻辑"""
    
    def __init__(self, params: Dict):
        super().__init__(params)
        
        # 信号开关
        self.buy_signal_ma_cross = params.get('buy_signal_ma_cross', True)
        self.buy_signal_rsi_oversold = params.get('buy_signal_rsi_oversold', False)
        self.buy_signal_volume_breakout = params.get('buy_signal_volume_breakout', True)
        
        self.sell_signal_ma_cross = params.get('sell_signal_ma_cross', True)
        self.sell_signal_rsi_overbought = params.get('sell_signal_rsi_overbought', False)
        self.sell_signal_stop_loss = params.get('sell_signal_stop_loss', True)
        
        # 技术指标参数
        self.ma_short_period = params.get('ma_short_period', 5)
        self.ma_long_period = params.get('ma_long_period', 20)
        self.rsi_period = params.get('rsi_period', 14)
        self.rsi_oversold = params.get('rsi_oversold', 30)
        self.rsi_overbought = params.get('rsi_overbought', 70)
        self.volume_multiplier = params.get('volume_multiplier', 2.0)
        self.stop_loss_pct = params.get('stop_loss_pct', 0.05)
        
        # 资金管理参数
        self.max_positions = params.get('max_positions', 5)
        self.position_size = params.get('position_size', 0.2)
    
    def get_name(self) -> str:
        return "多信号生成器"
    
    def generate_signals(self, market_data: Dict[str, pd.DataFrame]) -> List[Signal]:
        """生成交易信号"""
        signals = []
        
        for stock_code, df in market_data.items():
            if len(df) < max(self.ma_long_period, self.rsi_period):
                continue
            
            # 计算技术指标
            indicators = self._calculate_indicators(df)
            
            # 生成买入信号
            buy_signals = self._evaluate_buy_signals(stock_code, df, indicators)
            signals.extend(buy_signals)
            
            # 生成卖出信号
            sell_signals = self._evaluate_sell_signals(stock_code, df, indicators)
            signals.extend(sell_signals)
        
        return signals
    
    def _calculate_indicators(self, df: pd.DataFrame) -> Dict:
        """计算技术指标"""
        indicators = {}
        
        # 移动平均线
        indicators['ma_short'] = df['close'].rolling(window=self.ma_short_period).mean()
        indicators['ma_long'] = df['close'].rolling(window=self.ma_long_period).mean()
        
        # RSI
        indicators['rsi'] = self._calculate_rsi(df['close'], self.rsi_period)
        
        # 成交量均线
        indicators['volume_ma'] = df['volume'].rolling(window=20).mean()
        
        return indicators
    
    def _calculate_rsi(self, prices: pd.Series, period: int) -> pd.Series:
        """计算RSI指标"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
    
    def _evaluate_buy_signals(self, stock_code: str, df: pd.DataFrame, indicators: Dict) -> List[Signal]:
        """评估买入信号"""
        signals = []
        reasons = []
        signal_strength = 0.0
        
        current_price = df['close'].iloc[-1]
        current_volume = df['volume'].iloc[-1]
        
        # 均线金叉信号
        if self.buy_signal_ma_cross:
            ma_short_current = indicators['ma_short'].iloc[-1]
            ma_long_current = indicators['ma_long'].iloc[-1]
            ma_short_prev = indicators['ma_short'].iloc[-2]
            ma_long_prev = indicators['ma_long'].iloc[-2]
            
            if (ma_short_current > ma_long_current and 
                ma_short_prev <= ma_long_prev):
                reasons.append(f"均线金叉({self.ma_short_period}日>{self.ma_long_period}日)")
                signal_strength += 0.4
        
        # RSI超卖信号
        if self.buy_signal_rsi_oversold:
            current_rsi = indicators['rsi'].iloc[-1]
            if current_rsi < self.rsi_oversold:
                reasons.append(f"RSI超卖({current_rsi:.1f}<{self.rsi_oversold})")
                signal_strength += 0.3
        
        # 成交量突破信号
        if self.buy_signal_volume_breakout:
            volume_ma = indicators['volume_ma'].iloc[-1]
            if current_volume > volume_ma * self.volume_multiplier:
                reasons.append(f"成交量突破({current_volume/volume_ma:.1f}倍)")
                signal_strength += 0.3
        
        # 如果有信号且强度足够，生成买入信号
        if reasons and signal_strength >= 0.3:
            # 计算买入数量（简化版）
            quantity = int(10000 * self.position_size / current_price / 100) * 100
            
            signals.append(Signal(
                stock_code=stock_code,
                signal_type=SignalType.BUY,
                strength=min(signal_strength, 1.0),
                price=current_price,
                quantity=quantity,
                reasons=reasons
            ))
        
        return signals
    
    def _evaluate_sell_signals(self, stock_code: str, df: pd.DataFrame, indicators: Dict) -> List[Signal]:
        """评估卖出信号"""
        signals = []
        reasons = []
        signal_strength = 0.0
        
        current_price = df['close'].iloc[-1]
        
        # 均线死叉信号
        if self.sell_signal_ma_cross:
            ma_short_current = indicators['ma_short'].iloc[-1]
            ma_long_current = indicators['ma_long'].iloc[-1]
            ma_short_prev = indicators['ma_short'].iloc[-2]
            ma_long_prev = indicators['ma_long'].iloc[-2]
            
            if (ma_short_current < ma_long_current and 
                ma_short_prev >= ma_long_prev):
                reasons.append(f"均线死叉({self.ma_short_period}日<{self.ma_long_period}日)")
                signal_strength += 0.4
        
        # RSI超买信号
        if self.sell_signal_rsi_overbought:
            current_rsi = indicators['rsi'].iloc[-1]
            if current_rsi > self.rsi_overbought:
                reasons.append(f"RSI超买({current_rsi:.1f}>{self.rsi_overbought})")
                signal_strength += 0.3
        
        # 如果有信号且强度足够，生成卖出信号
        if reasons and signal_strength >= 0.3:
            signals.append(Signal(
                stock_code=stock_code,
                signal_type=SignalType.SELL,
                strength=min(signal_strength, 1.0),
                price=current_price,
                quantity=0,  # 卖出数量需要根据实际持仓确定
                reasons=reasons
            ))
        
        return signals

class MovingAverageGenerator(SignalGenerator):
    """均线策略信号生成器"""
    
    def __init__(self, params: Dict):
        super().__init__(params)
        self.short_period = params.get('short_period', 10)
        self.long_period = params.get('long_period', 30)
    
    def get_name(self) -> str:
        return "均线策略生成器"
    
    def generate_signals(self, market_data: Dict[str, pd.DataFrame]) -> List[Signal]:
        """生成均线交叉信号"""
        signals = []
        
        for stock_code, df in market_data.items():
            if len(df) < self.long_period:
                continue
            
            # 计算均线
            ma_short = df['close'].rolling(window=self.short_period).mean()
            ma_long = df['close'].rolling(window=self.long_period).mean()
            
            current_price = df['close'].iloc[-1]
            
            # 金叉买入
            if (ma_short.iloc[-1] > ma_long.iloc[-1] and 
                ma_short.iloc[-2] <= ma_long.iloc[-2]):
                
                signals.append(Signal(
                    stock_code=stock_code,
                    signal_type=SignalType.BUY,
                    strength=0.8,
                    price=current_price,
                    quantity=int(10000 / current_price / 100) * 100,
                    reasons=[f"均线金叉({self.short_period}日>{self.long_period}日)"]
                ))
            
            # 死叉卖出
            elif (ma_short.iloc[-1] < ma_long.iloc[-1] and 
                  ma_short.iloc[-2] >= ma_long.iloc[-2]):
                
                signals.append(Signal(
                    stock_code=stock_code,
                    signal_type=SignalType.SELL,
                    strength=0.8,
                    price=current_price,
                    quantity=0,
                    reasons=[f"均线死叉({self.short_period}日<{self.long_period}日)"]
                ))
        
        return signals
