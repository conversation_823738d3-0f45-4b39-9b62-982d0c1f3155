import * as React from 'react'

const colorMap = {
  red: 'bg-red-100 text-red-800 border-red-200',
  blue: 'bg-blue-100 text-blue-800 border-blue-200',
  green: 'bg-green-100 text-green-800 border-green-200',
  yellow: 'bg-yellow-100 text-yellow-800 border-yellow-200',
  gray: 'bg-gray-100 text-gray-800 border-gray-200',
}

export function Tag({ color = 'gray', children, className = '' }) {
  const colorClass = colorMap[color] || colorMap.gray
  return (
    <span className={`inline-flex items-center px-2 py-1 text-xs font-medium rounded border ${colorClass} ${className}`}>
      {children}
    </span>
  )
}
