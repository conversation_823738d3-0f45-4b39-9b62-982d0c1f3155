#!/usr/bin/env python3
"""
简单的异步选股测试
"""

import requests
import time
import json

def test_async_selection():
    """测试异步选股功能"""
    print("Testing async stock selection...")
    
    # 启动选股任务
    payload = {
        'criteria': {'volume_min': 0.1, 'condition_logic': 'flexible'},
        'custom_name': 'async_test',
        'max_results': 10
    }
    
    print("Starting selection task...")
    try:
        response = requests.post('http://localhost:8000/api/stock-selection/select', json=payload, timeout=5)
        data = response.json()
        
        if data.get('success'):
            task_id = data['data']['task_id']
            print(f"Task started successfully! Task ID: {task_id}")
            
            # 轮询状态
            for i in range(20):  # 最多轮询20次
                time.sleep(1)
                status_response = requests.get(f'http://localhost:8000/api/stock-selection/task/{task_id}', timeout=5)
                status_data = status_response.json()
                
                if status_data.get('success'):
                    status = status_data['data']
                    print(f"[{i+1}] {status['status']} - {status['progress']}% - {status['message']}")
                    
                    if status['status'] in ['completed', 'failed']:
                        if status['status'] == 'completed':
                            print(f"Selection completed! Selected {status['selected_count']} stocks")
                            if 'result' in status:
                                print(f"Total results: {status['result']['total_selected']}")
                        else:
                            print(f"Selection failed: {status.get('error', 'Unknown error')}")
                        break
                else:
                    print("Failed to get status")
                    break
        else:
            print("Failed to start task")
            
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    test_async_selection()
