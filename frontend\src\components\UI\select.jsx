import * as React from 'react'
import * as RadixSelect from '@radix-ui/react-select'
import { ChevronDown } from 'lucide-react'

export function Select({ value, onValueChange, children, placeholder, className = '', multiple = false }) {
  return (
    <RadixSelect.Root value={value} onValueChange={onValueChange}>
      <RadixSelect.Trigger className={`inline-flex items-center justify-between h-9 w-full rounded-md border bg-white px-3 text-sm ${className}`}>
        <RadixSelect.Value placeholder={placeholder} />
        <RadixSelect.Icon>
          <ChevronDown className="w-4 h-4" />
        </RadixSelect.Icon>
      </RadixSelect.Trigger>
      <RadixSelect.Content className="bg-white rounded-md shadow-md border">
        <RadixSelect.Viewport className="p-1">
          {children}
        </RadixSelect.Viewport>
      </RadixSelect.Content>
    </RadixSelect.Root>
  )
}

export function SelectItem({ value, children }) {
  return (
    <RadixSelect.Item value={value} className="px-3 py-1.5 text-sm rounded hover:bg-gray-100 cursor-pointer">
      <RadixSelect.ItemText>{children}</RadixSelect.ItemText>
    </RadixSelect.Item>
  )
}

