import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Card as Shad<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> as <PERSON>had<PERSON><PERSON>Header, Card<PERSON><PERSON>le as Shad<PERSON>ardTitle, CardContent as ShadCardContent } from './UI/card.jsx';
import { <PERSON><PERSON> as ShadButton } from './UI/button.jsx';
import { Alert as ShadAlert } from './UI/alert.jsx';
import { DataTable } from './UI/table.jsx';
import { Tag } from './UI/tag.jsx';
import { Switch } from './UI/switch.jsx';
import { 
  Shield, 
  AlertTriangle, 
  Volume2, 
  VolumeX, 
  RefreshCw, 
  Clock,
  TrendingDown,
  DollarSign,
  Eye,
  X,
  CheckCircle,
  XCircle,
  Pause
} from 'lucide-react';
import soundAlert from '../utils/soundAlert.js';
import { toast } from 'sonner';

const EnhancedPositionMonitor = () => {
  const [isMonitoring, setIsMonitoring] = useState(false);
  const [soundEnabled, setSoundEnabled] = useState(true);
  const [positions, setPositions] = useState([]);
  const [stopLossSignals, setStopLossSignals] = useState([]);
  const [pendingOrders, setPendingOrders] = useState([]);
  const [monitorStatus, setMonitorStatus] = useState(null);
  const [loading, setLoading] = useState(false);
  const [lastUpdate, setLastUpdate] = useState(null);

  // 自动刷新定时器
  const refreshInterval = useRef(null);
  const signalCheckInterval = useRef(null);
  
  // 已播放声音的信号ID集合，避免重复播放
  const playedSignals = useRef(new Set());

  // 加载持仓监控状态
  const loadMonitorStatus = useCallback(async () => {
    try {
      const response = await fetch('/api/position-monitor/status');
      const data = await response.json();
      
      if (data.success) {
        setMonitorStatus(data.data);
        setIsMonitoring(data.data.is_running);
      }
    } catch (error) {
      console.error('获取监控状态失败:', error);
    }
  }, []);

  // 加载止损信号
  const loadStopLossSignals = useCallback(async () => {
    try {
      const response = await fetch('/api/position-monitor/signals?limit=50');
      const signals = await response.json();
      
      setStopLossSignals(signals || []);
      
      // 检查新信号并播放声音
      if (soundEnabled && signals && signals.length > 0) {
        signals.forEach(signal => {
          const signalId = `${signal.stock_code}_${signal.signal_type}_${signal.timestamp}`;
          
          if (!playedSignals.current.has(signalId)) {
            playedSignals.current.add(signalId);
            soundAlert.playStopLossAlert(signal);
            
            // 显示Toast通知
            toast.error(`🚨 ${signal.message}`, {
              duration: 5000,
              action: {
                label: '查看详情',
                onClick: () => console.log('查看信号详情:', signal)
              }
            });
          }
        });
      }
      
      setLastUpdate(new Date());
    } catch (error) {
      console.error('获取止损信号失败:', error);
    }
  }, [soundEnabled]);

  // 加载待处理订单
  const loadPendingOrders = useCallback(async () => {
    try {
      const response = await fetch('/api/position-monitor/pending-orders');
      const data = await response.json();
      
      if (data.success) {
        const orders = data.pending_orders || [];
        
        // 检查订单状态变化
        if (pendingOrders.length > 0 && orders.length < pendingOrders.length) {
          // 有订单完成了
          soundAlert.playOrderFilledAlert();
          toast.success('✅ 订单状态已更新');
        }
        
        setPendingOrders(orders);
      }
    } catch (error) {
      console.error('获取待处理订单失败:', error);
    }
  }, [pendingOrders.length]);

  // 加载持仓数据
  const loadPositions = useCallback(async () => {
    try {
      const response = await fetch('/api/data/account');
      const data = await response.json();
      
      if (data.success && data.data.positions) {
        setPositions(data.data.positions);
      }
    } catch (error) {
      console.error('获取持仓数据失败:', error);
    }
  }, []);

  // 启动/停止监控
  const toggleMonitoring = async () => {
    setLoading(true);
    
    try {
      const endpoint = isMonitoring ? '/api/position-monitor/stop' : '/api/position-monitor/start';
      const response = await fetch(endpoint, { method: 'POST' });
      const data = await response.json();
      
      if (data.success) {
        setIsMonitoring(!isMonitoring);
        toast.success(isMonitoring ? '监控已停止' : '监控已启动');
        
        if (!isMonitoring) {
          // 启动监控时开始自动刷新
          startAutoRefresh();
        } else {
          // 停止监控时清除自动刷新
          stopAutoRefresh();
        }
      } else {
        toast.error(data.message || '操作失败');
      }
    } catch (error) {
      console.error('切换监控状态失败:', error);
      toast.error('操作失败: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  // 手动检查
  const manualCheck = async () => {
    setLoading(true);
    
    try {
      const response = await fetch('/api/position-monitor/check', { method: 'POST' });
      const data = await response.json();
      
      if (data.signals && data.signals.length > 0) {
        toast.warning(`发现 ${data.signals.length} 个止损信号`);
      } else {
        toast.success('检查完成，暂无止损信号');
      }
      
      // 刷新数据
      await Promise.all([
        loadStopLossSignals(),
        loadPendingOrders(),
        loadPositions()
      ]);
    } catch (error) {
      console.error('手动检查失败:', error);
      toast.error('检查失败: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  // 取消订单
  const cancelOrder = async (orderId) => {
    if (!window.confirm('确定要取消这个订单吗？')) {
      return;
    }
    
    try {
      const response = await fetch(`/api/position-monitor/cancel-order/${orderId}`, {
        method: 'POST'
      });
      const data = await response.json();
      
      if (data.success) {
        toast.success('订单取消成功');
        loadPendingOrders();
      } else {
        toast.error(data.message || '取消订单失败');
      }
    } catch (error) {
      console.error('取消订单失败:', error);
      toast.error('取消订单失败: ' + error.message);
    }
  };

  // 开始自动刷新
  const startAutoRefresh = () => {
    // 清除现有定时器
    stopAutoRefresh();
    
    // 设置数据刷新定时器（每30秒）
    refreshInterval.current = setInterval(() => {
      Promise.all([
        loadPositions(),
        loadPendingOrders()
      ]);
    }, 30000);
    
    // 设置信号检查定时器（每10秒）
    signalCheckInterval.current = setInterval(() => {
      loadStopLossSignals();
    }, 10000);
  };

  // 停止自动刷新
  const stopAutoRefresh = () => {
    if (refreshInterval.current) {
      clearInterval(refreshInterval.current);
      refreshInterval.current = null;
    }
    
    if (signalCheckInterval.current) {
      clearInterval(signalCheckInterval.current);
      signalCheckInterval.current = null;
    }
  };

  // 切换声音
  const toggleSound = () => {
    const newSoundEnabled = !soundEnabled;
    setSoundEnabled(newSoundEnabled);
    soundAlert.setEnabled(newSoundEnabled);
    
    if (newSoundEnabled) {
      soundAlert.testSound();
      toast.success('声音警告已启用');
    } else {
      toast.info('声音警告已禁用');
    }
  };

  // 组件初始化
  useEffect(() => {
    // 初始化数据加载
    Promise.all([
      loadMonitorStatus(),
      loadStopLossSignals(),
      loadPendingOrders(),
      loadPositions()
    ]);

    // 设置声音状态
    soundAlert.setEnabled(soundEnabled);

    // 组件卸载时清理
    return () => {
      stopAutoRefresh();
    };
  }, []);

  // 监控状态变化时处理自动刷新
  useEffect(() => {
    if (isMonitoring) {
      startAutoRefresh();
    } else {
      stopAutoRefresh();
    }
  }, [isMonitoring]);

  // 持仓表格列定义
  const positionColumns = [
    {
      accessorKey: 'stock_code',
      header: '股票代码',
    },
    {
      accessorKey: 'stock_name',
      header: '股票名称',
    },
    {
      accessorKey: 'quantity',
      header: '持仓数量',
      cell: ({ row }) => row.getValue('quantity').toLocaleString(),
    },
    {
      accessorKey: 'avg_price',
      header: '成本价',
      cell: ({ row }) => `¥${row.getValue('avg_price').toFixed(2)}`,
    },
    {
      accessorKey: 'current_price',
      header: '现价',
      cell: ({ row }) => `¥${row.getValue('current_price').toFixed(2)}`,
    },
    {
      accessorKey: 'pnl_ratio',
      header: '盈亏比例',
      cell: ({ row }) => {
        const ratio = row.getValue('pnl_ratio') || 0;
        const isProfit = ratio >= 0;
        return (
          <span className={isProfit ? 'text-green-600' : 'text-red-600'}>
            {isProfit ? '+' : ''}{(ratio * 100).toFixed(2)}%
          </span>
        );
      },
    },
  ];

  return (
    <div className="space-y-6">
      {/* 控制面板 */}
      <ShadCard>
        <ShadCardHeader>
          <ShadCardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Shield className="w-5 h-5" />
              持仓监控
            </div>
            <div className="flex items-center gap-2">
              {lastUpdate && (
                <span className="text-sm text-gray-500">
                  最后更新: {lastUpdate.toLocaleTimeString()}
                </span>
              )}
            </div>
          </ShadCardTitle>
        </ShadCardHeader>
        <ShadCardContent>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <ShadButton
                onClick={toggleMonitoring}
                disabled={loading}
                variant={isMonitoring ? "destructive" : "default"}
              >
                {loading ? (
                  <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                ) : (
                  <Shield className="w-4 h-4 mr-2" />
                )}
                {isMonitoring ? '停止监控' : '启动监控'}
              </ShadButton>

              <ShadButton
                onClick={manualCheck}
                disabled={loading}
                variant="outline"
              >
                <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                手动检查
              </ShadButton>
            </div>

            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <Switch
                  checked={soundEnabled}
                  onCheckedChange={toggleSound}
                />
                {soundEnabled ? (
                  <Volume2 className="w-4 h-4 text-green-600" />
                ) : (
                  <VolumeX className="w-4 h-4 text-gray-400" />
                )}
                <span className="text-sm">声音警告</span>
              </div>

              <div className="flex items-center gap-2">
                <div className={`w-2 h-2 rounded-full ${isMonitoring ? 'bg-green-500' : 'bg-gray-400'}`} />
                <span className="text-sm">
                  {isMonitoring ? '监控中' : '已停止'}
                </span>
              </div>
            </div>
          </div>
        </ShadCardContent>
      </ShadCard>

      {/* 统计面板 */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <ShadCard>
          <ShadCardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">持仓股票</p>
                <p className="text-2xl font-bold">{positions.length}</p>
              </div>
              <DollarSign className="w-8 h-8 text-blue-500" />
            </div>
          </ShadCardContent>
        </ShadCard>

        <ShadCard>
          <ShadCardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">止损信号</p>
                <p className="text-2xl font-bold text-red-600">{stopLossSignals.length}</p>
              </div>
              <AlertTriangle className="w-8 h-8 text-red-500" />
            </div>
          </ShadCardContent>
        </ShadCard>

        <ShadCard>
          <ShadCardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">待处理订单</p>
                <p className="text-2xl font-bold text-orange-600">{pendingOrders.length}</p>
              </div>
              <Clock className="w-8 h-8 text-orange-500" />
            </div>
          </ShadCardContent>
        </ShadCard>

        <ShadCard>
          <ShadCardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">监控状态</p>
                <p className={`text-2xl font-bold ${isMonitoring ? 'text-green-600' : 'text-gray-400'}`}>
                  {isMonitoring ? '运行中' : '已停止'}
                </p>
              </div>
              <Shield className={`w-8 h-8 ${isMonitoring ? 'text-green-500' : 'text-gray-400'}`} />
            </div>
          </ShadCardContent>
        </ShadCard>
      </div>

      {/* 持仓和订单并排显示 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 当前持仓 */}
        <ShadCard>
          <ShadCardHeader>
            <ShadCardTitle className="flex items-center gap-2">
              <DollarSign className="w-5 h-5" />
              当前持仓 ({positions.length})
            </ShadCardTitle>
          </ShadCardHeader>
          <ShadCardContent>
            {positions.length > 0 ? (
              <DataTable
                columns={positionColumns}
                data={positions}
              />
            ) : (
              <div className="text-center py-8 text-gray-500">
                <DollarSign className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>暂无持仓数据</p>
              </div>
            )}
          </ShadCardContent>
        </ShadCard>

        {/* 待处理订单 */}
        <ShadCard>
          <ShadCardHeader>
            <ShadCardTitle className="flex items-center gap-2">
              <Clock className="w-5 h-5 text-orange-500" />
              待处理订单 ({pendingOrders.length})
            </ShadCardTitle>
          </ShadCardHeader>
          <ShadCardContent>
            {pendingOrders.length > 0 ? (
              <div className="space-y-3">
                {pendingOrders.map((order, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-orange-50 rounded-lg border border-orange-200">
                    <div className="flex items-center gap-3">
                      <Clock className="w-4 h-4 text-orange-500" />
                      <div>
                        <div className="font-medium">{order.stock_code}</div>
                        <div className="text-sm text-gray-600">
                          卖出 {order.quantity} 股 @ ¥{order.price?.toFixed(2)}
                        </div>
                        <div className="text-xs text-gray-500">
                          订单号: {order.order_id} | {order.created_time}
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Tag color="orange" size="sm">待处理</Tag>
                      <ShadButton
                        size="sm"
                        variant="outline"
                        onClick={() => cancelOrder(order.order_id)}
                      >
                        <X className="w-3 h-3 mr-1" />
                        取消
                      </ShadButton>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                <Clock className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>暂无待处理订单</p>
              </div>
            )}
          </ShadCardContent>
        </ShadCard>
      </div>

      {/* 止损信号历史 */}
      {stopLossSignals.length > 0 && (
        <ShadCard>
          <ShadCardHeader>
            <ShadCardTitle className="flex items-center gap-2">
              <AlertTriangle className="w-5 h-5 text-red-500" />
              止损信号历史 ({stopLossSignals.length})
            </ShadCardTitle>
          </ShadCardHeader>
          <ShadCardContent>
            <div className="space-y-3 max-h-96 overflow-y-auto">
              {stopLossSignals.slice(0, 20).map((signal, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-red-50 rounded-lg border border-red-200">
                  <div className="flex items-center gap-3">
                    <TrendingDown className="w-4 h-4 text-red-500" />
                    <div>
                      <div className="font-medium text-red-700">{signal.stock_code}</div>
                      <div className="text-sm text-gray-700">{signal.message}</div>
                      <div className="text-xs text-gray-500">
                        {new Date(signal.timestamp).toLocaleString()}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Tag 
                      color={signal.signal_type === 'portfolio_stop' ? 'red' : 'orange'} 
                      size="sm"
                    >
                      {signal.signal_type === 'loss_stop' && '亏损止损'}
                      {signal.signal_type === 'trailing_stop' && '移动止损'}
                      {signal.signal_type === 'portfolio_stop' && '组合止损'}
                      {signal.signal_type === 'atr_stop' && 'ATR止损'}
                    </Tag>
                  </div>
                </div>
              ))}
            </div>
          </ShadCardContent>
        </ShadCard>
      )}
    </div>
  );
};

export default EnhancedPositionMonitor;
