import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { Card as ShadCard, CardHeader as <PERSON>had<PERSON><PERSON>Header, CardTitle as <PERSON>had<PERSON>ard<PERSON>itle, CardContent as ShadCardContent } from '../components/UI/card.jsx';
import { <PERSON><PERSON> as ShadButton } from '../components/UI/button.jsx';
import { DataTable } from '../components/UI/table.jsx';
import { Spinner } from '../components/UI/spinner.jsx';
import { Alert as ShadAlert } from '../components/UI/alert.jsx';
import { ArrowLeft, TrendingUp, TrendingDown, DollarSign, BarChart3, Download, RefreshCw } from 'lucide-react';
import { backtestAPI, apiUtils } from '../services/api';
import { toast as imported_toast } from 'sonner';

const BacktestResultDetailPage = () => {
  const { taskId } = useParams();
  const [loading, setLoading] = useState(true);
  const [result, setResult] = useState(null);
  const [error, setError] = useState(null);

  // 分页状态
  const [tradesPage, setTradesPage] = useState(1);
  const [tradesPageSize, setTradesPageSize] = useState(20);

  // 分页组件
  const PaginationComponent = ({
    currentPage,
    pageSize,
    totalItems,
    onPageChange,
    onPageSizeChange,
    itemName = "项"
  }) => {
    const totalPages = Math.ceil(totalItems / pageSize);
    const startItem = (currentPage - 1) * pageSize + 1;
    const endItem = Math.min(currentPage * pageSize, totalItems);

    return (
      <div className="flex items-center justify-between mt-4 p-4 bg-gray-50 rounded-lg">
        <div className="flex items-center gap-4">
          <span className="text-sm text-gray-600">
            显示 {startItem}-{endItem} 项，共 {totalItems} {itemName}
          </span>
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-600">每页显示:</span>
            <select
              value={pageSize}
              onChange={(e) => onPageSizeChange(Number(e.target.value))}
              className="border rounded px-2 py-1 text-sm"
            >
              <option value={10}>10</option>
              <option value={20}>20</option>
              <option value={50}>50</option>
              <option value={100}>100</option>
            </select>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <ShadButton
            variant="outline"
            size="sm"
            onClick={() => onPageChange(currentPage - 1)}
            disabled={currentPage <= 1}
          >
            上一页
          </ShadButton>

          <div className="flex items-center gap-1">
            {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
              let pageNum;
              if (totalPages <= 5) {
                pageNum = i + 1;
              } else if (currentPage <= 3) {
                pageNum = i + 1;
              } else if (currentPage >= totalPages - 2) {
                pageNum = totalPages - 4 + i;
              } else {
                pageNum = currentPage - 2 + i;
              }

              return (
                <ShadButton
                  key={pageNum}
                  variant={currentPage === pageNum ? "default" : "outline"}
                  size="sm"
                  onClick={() => onPageChange(pageNum)}
                  className="w-8 h-8 p-0"
                >
                  {pageNum}
                </ShadButton>
              );
            })}
          </div>

          <ShadButton
            variant="outline"
            size="sm"
            onClick={() => onPageChange(currentPage + 1)}
            disabled={currentPage >= totalPages}
          >
            下一页
          </ShadButton>
        </div>
      </div>
    );
  };

  useEffect(() => {
    if (taskId) {
      loadResult();
    }
  }, [taskId]);

  const loadResult = async () => {
    setLoading(true);
    try {
      const response = await backtestAPI.getBacktestResults(taskId);
      if (apiUtils.isSuccess(response)) {
        setResult(apiUtils.getData(response));
      } else {
        setError('获取回测结果失败');
      }
    } catch (err) {
      setError(apiUtils.handleError(err, '获取回测结果失败'));
    } finally {
      setLoading(false);
    }
  };

  const exportResults = () => {
    if (!result) return;
    
    try {
      // 导出为JSON格式
      const dataStr = JSON.stringify(result, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });
      const url = URL.createObjectURL(dataBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `backtest_result_${taskId}.json`;
      link.click();
      URL.revokeObjectURL(url);
      
      imported_toast.success('回测结果已导出');
    } catch (err) {
      imported_toast.error('导出失败: ' + err.message);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-20">
        <Spinner size="lg" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Link to="/backtest" className="inline-flex items-center gap-2 text-blue-600 hover:text-blue-800">
            <ArrowLeft className="w-4 h-4" />
            返回回测页面
          </Link>
        </div>
        <ShadAlert
          title="错误"
          description={error}
          variant="error"
        />
      </div>
    );
  }

  if (!result) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Link to="/backtest" className="inline-flex items-center gap-2 text-blue-600 hover:text-blue-800">
            <ArrowLeft className="w-4 h-4" />
            返回回测页面
          </Link>
        </div>
        <div className="text-center py-20">
          <p className="text-gray-500">未找到回测结果</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 页面头部 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Link to="/backtest" className="inline-flex items-center gap-2 text-blue-600 hover:text-blue-800">
            <ArrowLeft className="w-4 h-4" />
            返回回测页面
          </Link>
          <h1 className="text-2xl font-bold text-gray-900">回测结果详情</h1>
        </div>
        <div className="flex items-center gap-2">
          <ShadButton onClick={loadResult} className="inline-flex items-center gap-2">
            <RefreshCw className="w-4 h-4" />
            刷新
          </ShadButton>
          <ShadButton onClick={exportResults} className="inline-flex items-center gap-2">
            <Download className="w-4 h-4" />
            导出结果
          </ShadButton>
        </div>
      </div>

      {/* 核心指标概览 */}
      <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
        <ShadCard>
          <ShadCardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">总收益率</p>
                <p className={`text-2xl font-bold ${result.total_return >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {(result.total_return * 100).toFixed(2)}%
                </p>
              </div>
              <TrendingUp className={`h-8 w-8 ${result.total_return >= 0 ? 'text-green-600' : 'text-red-600'}`} />
            </div>
          </ShadCardContent>
        </ShadCard>

        <ShadCard>
          <ShadCardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">年化收益率</p>
                <p className={`text-2xl font-bold ${result.annual_return >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {(result.annual_return * 100).toFixed(2)}%
                </p>
              </div>
              <BarChart3 className="h-8 w-8 text-blue-600" />
            </div>
          </ShadCardContent>
        </ShadCard>

        <ShadCard>
          <ShadCardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">最大回撤</p>
                <p className="text-2xl font-bold text-red-600">
                  {(result.max_drawdown * 100).toFixed(2)}%
                </p>
              </div>
              <TrendingDown className="h-8 w-8 text-red-600" />
            </div>
          </ShadCardContent>
        </ShadCard>

        <ShadCard>
          <ShadCardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">夏普比率</p>
                <p className="text-2xl font-bold text-purple-600">
                  {result.sharpe_ratio?.toFixed(2) || '0.00'}
                </p>
              </div>
              <DollarSign className="h-8 w-8 text-purple-600" />
            </div>
          </ShadCardContent>
        </ShadCard>
      </div>

      {/* 详细信息 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <ShadCard>
          <ShadCardHeader>
            <ShadCardTitle>回测概览</ShadCardTitle>
          </ShadCardHeader>
          <ShadCardContent>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span>任务ID:</span>
                <span className="font-mono text-sm">{result.task_id}</span>
              </div>
              <div className="flex justify-between">
                <span>策略名称:</span>
                <span className="font-bold">{result.strategy_name}</span>
              </div>
              <div className="flex justify-between">
                <span>回测期间:</span>
                <span>{result.start_date} ~ {result.end_date}</span>
              </div>
              <div className="flex justify-between">
                <span>初始资金:</span>
                <span>¥{result.initial_capital?.toLocaleString()}</span>
              </div>
              <div className="flex justify-between">
                <span>最终资金:</span>
                <span className={result.final_capital >= result.initial_capital ? 'text-green-600 font-bold' : 'text-red-600 font-bold'}>
                  ¥{result.final_capital?.toLocaleString()}
                </span>
              </div>
              <div className="flex justify-between">
                <span>绝对收益:</span>
                <span className={result.final_capital >= result.initial_capital ? 'text-green-600 font-bold' : 'text-red-600 font-bold'}>
                  ¥{(result.final_capital - result.initial_capital)?.toLocaleString()}
                </span>
              </div>
              <div className="flex justify-between">
                <span>创建时间:</span>
                <span>{new Date(result.created_at).toLocaleString()}</span>
              </div>
              <div className="flex justify-between">
                <span>完成时间:</span>
                <span>{new Date(result.completed_at).toLocaleString()}</span>
              </div>
            </div>
          </ShadCardContent>
        </ShadCard>

        <ShadCard>
          <ShadCardHeader>
            <ShadCardTitle>交易统计</ShadCardTitle>
          </ShadCardHeader>
          <ShadCardContent>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span>总交易次数:</span>
                <span className="font-bold">{result.total_trades}</span>
              </div>
              <div className="flex justify-between">
                <span>盈利交易:</span>
                <span className="font-bold text-green-600">{result.profit_trades}</span>
              </div>
              <div className="flex justify-between">
                <span>亏损交易:</span>
                <span className="font-bold text-red-600">{result.loss_trades}</span>
              </div>
              <div className="flex justify-between">
                <span>胜率:</span>
                <span className="font-bold">{(result.win_rate * 100).toFixed(2)}%</span>
              </div>
              <div className="flex justify-between">
                <span>平均盈利:</span>
                <span className="font-bold text-green-600">
                  ¥{result.avg_profit?.toFixed(2) || '0.00'}
                </span>
              </div>
              <div className="flex justify-between">
                <span>平均亏损:</span>
                <span className="font-bold text-red-600">
                  ¥{result.avg_loss?.toFixed(2) || '0.00'}
                </span>
              </div>
              <div className="flex justify-between">
                <span>盈亏比:</span>
                <span className="font-bold">{result.profit_factor?.toFixed(2) || '0.00'}</span>
              </div>
            </div>
          </ShadCardContent>
        </ShadCard>
      </div>

      {/* 完整交易记录 */}
      {result.trades && result.trades.length > 0 && (() => {
        const startIndex = (tradesPage - 1) * tradesPageSize;
        const endIndex = startIndex + tradesPageSize;
        const paginatedTrades = result.trades.slice(startIndex, endIndex);

        return (
          <ShadCard>
            <ShadCardHeader>
              <ShadCardTitle>完整交易记录 ({result.trades.length}笔)</ShadCardTitle>
            </ShadCardHeader>
            <ShadCardContent>
              <DataTable
                data={paginatedTrades}
              columns={[
                {
                  accessorKey: 'id',
                  header: '序号',
                  cell: info => info.getValue(),
                },
                {
                  accessorKey: 'date',
                  header: '日期',
                  cell: info => info.getValue(),
                },
                {
                  accessorKey: 'time',
                  header: '时间',
                  cell: info => info.getValue(),
                },
                {
                  accessorKey: 'stock_code',
                  header: '股票代码',
                  cell: info => info.getValue(),
                },
                {
                  accessorKey: 'stock_name',
                  header: '股票名称',
                  cell: info => info.getValue(),
                },
                {
                  accessorKey: 'action_name',
                  header: '操作',
                  cell: info => {
                    const action = info.row.original.action;
                    return (
                      <span className={`px-2 py-1 rounded text-xs font-medium ${
                        action === 'buy' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                      }`}>
                        {info.getValue()}
                      </span>
                    );
                  },
                },
                {
                  accessorKey: 'price',
                  header: '价格',
                  cell: info => `¥${info.getValue()?.toFixed(2) || '0.00'}`,
                },
                {
                  accessorKey: 'quantity',
                  header: '数量',
                  cell: info => `${info.getValue()?.toLocaleString() || '0'}股`,
                },
                {
                  accessorKey: 'amount',
                  header: '金额',
                  cell: info => `¥${info.getValue()?.toLocaleString() || '0'}`,
                },
                {
                  accessorKey: 'commission',
                  header: '手续费',
                  cell: info => `¥${info.getValue()?.toFixed(2) || '0.00'}`,
                },
                {
                  accessorKey: 'profit_loss',
                  header: '盈亏',
                  cell: info => {
                    const value = info.getValue();
                    if (value === null || value === undefined) return '-';
                    return (
                      <span className={value >= 0 ? 'text-green-600 font-medium' : 'text-red-600 font-medium'}>
                        ¥{value?.toFixed(2)}
                      </span>
                    );
                  },
                },
                {
                  accessorKey: 'profit_loss_pct',
                  header: '盈亏比例',
                  cell: info => {
                    const value = info.getValue();
                    if (value === null || value === undefined) return '-';
                    return (
                      <span className={value >= 0 ? 'text-green-600 font-medium' : 'text-red-600 font-medium'}>
                        {value?.toFixed(2)}%
                      </span>
                    );
                  },
                },
                {
                  accessorKey: 'hold_days',
                  header: '持有天数',
                  cell: info => {
                    const value = info.getValue();
                    return value ? `${value}天` : '-';
                  },
                },
                {
                  accessorKey: 'reason',
                  header: '交易原因',
                  cell: info => (
                    <span className="text-xs text-gray-600 max-w-40 truncate" title={info.getValue()}>
                      {info.getValue()}
                    </span>
                  ),
                },
              ]}
            />

            <PaginationComponent
              currentPage={tradesPage}
              pageSize={tradesPageSize}
              totalItems={result.trades.length}
              onPageChange={setTradesPage}
              onPageSizeChange={(newSize) => {
                setTradesPageSize(newSize);
                setTradesPage(1); // 重置到第一页
              }}
              itemName="笔交易"
            />
          </ShadCardContent>
        </ShadCard>
        );
      })()}
    </div>
  );
};

export default BacktestResultDetailPage;
