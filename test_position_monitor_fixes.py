#!/usr/bin/env python3
"""
测试持仓监控修复
"""

import requests
import json

BASE_URL = "http://localhost:8000"

def test_api_endpoint(endpoint, method="GET", data=None):
    """测试API接口"""
    url = f"{BASE_URL}{endpoint}"
    print(f"\n测试 {method} {endpoint}")

    try:
        if method == "GET":
            response = requests.get(url, timeout=10)
        elif method == "POST":
            response = requests.post(url, json=data, timeout=10)

        print(f"状态码: {response.status_code}")

        if response.status_code == 200:
            result = response.json()
            print(f"响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
            return result
        else:
            print(f"错误: {response.text}")
            return None

    except requests.exceptions.RequestException as e:
        print(f"请求失败: {e}")
        return None
    except Exception as e:
        print(f"其他错误: {e}")
        return None

def main():
    """主测试函数"""
    print("=== 持仓监控修复测试 ===")
    
    # 测试1: 获取账户信息 (修复持仓显示为空的问题)
    print("\n1. 测试账户信息接口")
    test_api_endpoint("/api/data/account")
    
    # 测试2: 获取监控状态 (修复状态更新问题)
    print("\n2. 测试监控状态接口")
    test_api_endpoint("/api/position-monitor/status")
    
    # 测试3: 获取配置 (修复配置持久化问题)
    print("\n3. 测试配置接口")
    config_result = test_api_endpoint("/api/position-monitor/config")
    
    # 测试4: 更新配置
    if config_result and config_result.get('success'):
        print("\n4. 测试配置更新")
        test_config = {
            "loss_stop_enabled": True,
            "loss_stop_percent": 6.0,
            "trailing_stop_enabled": True,
            "trailing_stop_percent": 6.0,
            "auto_sell": False,
            "monitor_interval": 30
        }
        test_api_endpoint("/api/position-monitor/config", "POST", test_config)
    
    # 测试5: 获取待处理订单
    print("\n5. 测试待处理订单接口")
    test_api_endpoint("/api/position-monitor/pending-orders")
    
    # 测试6: 获取止损信号 (检查排序)
    print("\n6. 测试止损信号接口")
    signals_result = test_api_endpoint("/api/position-monitor/signals?limit=5")
    
    if signals_result and isinstance(signals_result, list):
        print("\n信号时间戳检查:")
        for i, signal in enumerate(signals_result):
            timestamp = signal.get('timestamp', 'N/A')
            print(f"  信号 {i+1}: {timestamp}")

if __name__ == "__main__":
    main()
