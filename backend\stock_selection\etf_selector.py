#!/usr/bin/env python3
"""
ETF选股器 - 专门用于选择ETF基金
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass

from backend.core.logger import get_logger
from backend.stock_selection.stock_selector import StockScore

logger = get_logger(__name__)

@dataclass
class ETFCriteria:
    """ETF选股条件"""
    # 基础筛选条件
    min_market_cap: Optional[float] = None  # 最小市值（亿元）
    max_market_cap: Optional[float] = None  # 最大市值（亿元）
    
    # 成交量条件
    min_avg_volume: Optional[float] = None  # 最小平均成交量
    min_volume_ratio: Optional[float] = None  # 最小成交量比率
    
    # 价格条件
    min_price: Optional[float] = None  # 最小价格
    max_price: Optional[float] = None  # 最大价格
    
    # 涨跌幅条件
    min_return_1d: Optional[float] = None  # 最小1日涨跌幅
    max_return_1d: Optional[float] = None  # 最大1日涨跌幅
    min_return_5d: Optional[float] = None  # 最小5日涨跌幅
    max_return_5d: Optional[float] = None  # 最大5日涨跌幅
    min_return_20d: Optional[float] = None  # 最小20日涨跌幅
    max_return_20d: Optional[float] = None  # 最大20日涨跌幅
    
    # 波动率条件
    min_volatility: Optional[float] = None  # 最小波动率
    max_volatility: Optional[float] = None  # 最大波动率
    
    # ETF类型筛选
    etf_types: Optional[List[str]] = None  # ETF类型：['stock', 'bond', 'commodity', 'money', 'cross_border']
    exclude_types: Optional[List[str]] = None  # 排除的ETF类型
    
    # 跟踪指数筛选
    track_indexes: Optional[List[str]] = None  # 跟踪的指数关键词
    exclude_indexes: Optional[List[str]] = None  # 排除的指数关键词
    
    # 时间范围
    lookback_days: int = 30
    min_trading_days: int = 20
    
    # 其他条件
    exclude_new_etf: bool = True  # 排除新上市ETF（上市不足60天）
    only_main_board: bool = False  # 仅主板ETF
    
    # 条件组合逻辑
    condition_logic: str = "flexible"  # "strict", "flexible", "any"

class ETFSelector:
    """ETF选股器"""
    
    def __init__(self):
        self.logger = get_logger(self.__class__.__name__)
        
        # 尝试导入xtquant
        try:
            import xtquant.xtdata as xt
            self.xt = xt
            self.xt_available = True
            self.logger.info("✅ xtquant模块加载成功")
        except ImportError as e:
            self.xt = None
            self.xt_available = False
            self.logger.warning(f"⚠️ xtquant模块不可用: {e}")
    
    def _get_etf_list_from_xttrader(self) -> List[Dict[str, str]]:
        """从xttrader获取ETF列表"""
        try:
            if not self.xt_available:
                return []
            
            etf_list = []
            
            # 获取ETF基金列表
            # 尝试多种方式获取ETF
            etf_sectors = [
                '沪深ETF',
                '上海ETF', 
                '深圳ETF',
                'ETF基金',
                '交易型开放式指数基金'
            ]
            
            for sector in etf_sectors:
                try:
                    etfs = self.xt.get_stock_list_in_sector(sector)
                    if etfs:
                        self.logger.info(f"从{sector}获取到 {len(etfs)} 只ETF")
                        for etf_code in etfs:
                            if self._is_etf_code(etf_code):
                                etf_list.append({
                                    'code': etf_code,
                                    'name': self._get_etf_name(etf_code),
                                    'sector': sector
                                })
                except Exception as e:
                    self.logger.debug(f"从{sector}获取ETF失败: {e}")
                    continue
            
            # 去重
            unique_etfs = {}
            for etf in etf_list:
                unique_etfs[etf['code']] = etf
            
            final_etf_list = list(unique_etfs.values())
            self.logger.info(f"从xttrader获取到 {len(final_etf_list)} 只ETF")
            
            return final_etf_list
            
        except Exception as e:
            self.logger.error(f"从xttrader获取ETF列表失败: {e}")
            return []
    
    def _is_etf_code(self, code: str) -> bool:
        """判断是否为ETF代码"""
        if not code or len(code) < 9:
            return False
        
        # ETF代码特征
        # 上海：51xxxx.SH, 58xxxx.SH
        # 深圳：15xxxx.SZ, 16xxxx.SZ
        if code.endswith('.SH'):
            stock_num = code[:6]
            return stock_num.startswith('51') or stock_num.startswith('58')
        elif code.endswith('.SZ'):
            stock_num = code[:6]
            return stock_num.startswith('15') or stock_num.startswith('16')
        
        return False
    
    def _get_etf_name(self, etf_code: str) -> str:
        """获取ETF名称"""
        try:
            if self.xt_available:
                info = self.xt.get_instrument_detail(etf_code)
                if info and 'InstrumentName' in info:
                    return info['InstrumentName']
            
            return etf_code.split('.')[0]
        except:
            return etf_code.split('.')[0]
    
    def _get_etf_data_from_xttrader(self, etf_code: str, start_date: str, end_date: str) -> Optional[pd.DataFrame]:
        """从xttrader获取ETF数据"""
        try:
            if not self.xt_available:
                return None
            
            # 获取日线数据
            data = self.xt.get_market_data(
                stock_list=[etf_code],
                period='1d',
                start_time=start_date,
                end_time=end_date,
                fill_data=True
            )
            
            if data and isinstance(data, dict):
                required_fields = ['time', 'open', 'high', 'low', 'close', 'volume']
                if all(field in data for field in required_fields):
                    if etf_code not in data['time'].index:
                        return None
                    
                    # 获取各字段数据
                    time_data = data['time'].loc[etf_code]
                    open_data = data['open'].loc[etf_code]
                    high_data = data['high'].loc[etf_code]
                    low_data = data['low'].loc[etf_code]
                    close_data = data['close'].loc[etf_code]
                    volume_data = data['volume'].loc[etf_code]
                    
                    # 转换时间戳为日期
                    dates = pd.to_datetime(time_data, unit='ms')
                    
                    # 构建DataFrame
                    df = pd.DataFrame({
                        'open': open_data.values,
                        'high': high_data.values,
                        'low': low_data.values,
                        'close': close_data.values,
                        'volume': volume_data.values
                    }, index=dates)
                    
                    # 确保数据类型正确
                    for col in ['open', 'high', 'low', 'close', 'volume']:
                        df[col] = pd.to_numeric(df[col], errors='coerce')
                    
                    # 删除无效数据
                    df = df.dropna()
                    
                    if len(df) > 0:
                        return df
            
            return None
            
        except Exception as e:
            self.logger.error(f"从xttrader获取ETF数据失败 {etf_code}: {e}")
            return None
    
    def _calculate_etf_indicators(self, df: pd.DataFrame) -> Dict[str, Any]:
        """计算ETF技术指标"""
        try:
            if df is None or len(df) < 5:
                return {}
            
            indicators = {}
            
            # 基础价格信息
            indicators['current_price'] = float(df['close'].iloc[-1])
            indicators['prev_close'] = float(df['close'].iloc[-2]) if len(df) > 1 else indicators['current_price']
            
            # 成交量信息
            indicators['current_volume'] = float(df['volume'].iloc[-1])
            indicators['avg_volume_20'] = float(df['volume'].tail(20).mean()) if len(df) >= 20 else indicators['current_volume']
            indicators['volume_ratio'] = indicators['current_volume'] / indicators['avg_volume_20'] if indicators['avg_volume_20'] > 0 else 1.0
            
            # 收益率计算
            if len(df) >= 2:
                indicators['return_1d'] = (indicators['current_price'] - indicators['prev_close']) / indicators['prev_close']
            else:
                indicators['return_1d'] = 0.0
            
            if len(df) >= 6:
                price_5d_ago = float(df['close'].iloc[-6])
                indicators['return_5d'] = (indicators['current_price'] - price_5d_ago) / price_5d_ago
            else:
                indicators['return_5d'] = 0.0
            
            if len(df) >= 21:
                price_20d_ago = float(df['close'].iloc[-21])
                indicators['return_20d'] = (indicators['current_price'] - price_20d_ago) / price_20d_ago
            else:
                indicators['return_20d'] = 0.0
            
            # 波动率计算（20日）
            if len(df) >= 20:
                returns = df['close'].pct_change().dropna()
                indicators['volatility'] = float(returns.tail(20).std() * np.sqrt(252))  # 年化波动率
            else:
                indicators['volatility'] = 0.0
            
            # 价格区间
            if len(df) >= 20:
                indicators['high_20d'] = float(df['high'].tail(20).max())
                indicators['low_20d'] = float(df['low'].tail(20).min())
                indicators['price_position'] = (indicators['current_price'] - indicators['low_20d']) / (indicators['high_20d'] - indicators['low_20d']) if indicators['high_20d'] > indicators['low_20d'] else 0.5
            else:
                indicators['high_20d'] = indicators['current_price']
                indicators['low_20d'] = indicators['current_price']
                indicators['price_position'] = 0.5
            
            # 移动平均线
            if len(df) >= 5:
                indicators['ma5'] = float(df['close'].tail(5).mean())
            if len(df) >= 10:
                indicators['ma10'] = float(df['close'].tail(10).mean())
            if len(df) >= 20:
                indicators['ma20'] = float(df['close'].tail(20).mean())
            
            return indicators
            
        except Exception as e:
            self.logger.error(f"计算ETF技术指标失败: {e}")
            return {}
    
    def _classify_etf_type(self, etf_name: str) -> str:
        """根据ETF名称分类ETF类型"""
        name_lower = etf_name.lower()
        
        # 股票型ETF
        if any(keyword in name_lower for keyword in ['300', '500', '50', 'a股', '创业板', '科创', '中证', '上证', '深证', '沪深']):
            return 'stock'
        
        # 债券型ETF
        if any(keyword in name_lower for keyword in ['债', '国债', '企债', '信用']):
            return 'bond'
        
        # 商品型ETF
        if any(keyword in name_lower for keyword in ['黄金', '白银', '原油', '商品', '有色', '农业']):
            return 'commodity'
        
        # 货币型ETF
        if any(keyword in name_lower for keyword in ['货币', '现金', '短债']):
            return 'money'
        
        # 跨境ETF
        if any(keyword in name_lower for keyword in ['美股', '港股', '德国', '日本', '纳斯达克', '标普', '恒生']):
            return 'cross_border'
        
        # 默认为股票型
        return 'stock'
    
    def _check_etf_criteria(self, indicators: Dict[str, Any], etf_name: str, criteria: ETFCriteria) -> Tuple[bool, float]:
        """检查ETF选股条件"""
        try:
            score = 0.0
            condition_met_count = 0
            total_conditions = 0
            
            # 价格条件检查
            current_price = indicators.get('current_price', 0)
            if criteria.min_price is not None:
                total_conditions += 1
                if current_price >= criteria.min_price:
                    condition_met_count += 1
                    score += 5
            
            if criteria.max_price is not None:
                total_conditions += 1
                if current_price <= criteria.max_price:
                    condition_met_count += 1
                    score += 5
            
            # 成交量条件检查
            volume_ratio = indicators.get('volume_ratio', 0)
            if criteria.min_volume_ratio is not None:
                total_conditions += 1
                if volume_ratio >= criteria.min_volume_ratio:
                    condition_met_count += 1
                    score += 10
            
            avg_volume = indicators.get('avg_volume_20', 0)
            if criteria.min_avg_volume is not None:
                total_conditions += 1
                if avg_volume >= criteria.min_avg_volume:
                    condition_met_count += 1
                    score += 5
            
            # 收益率条件检查
            return_1d = indicators.get('return_1d', 0)
            if criteria.min_return_1d is not None:
                total_conditions += 1
                if return_1d >= criteria.min_return_1d:
                    condition_met_count += 1
                    score += 8
            
            if criteria.max_return_1d is not None:
                total_conditions += 1
                if return_1d <= criteria.max_return_1d:
                    condition_met_count += 1
                    score += 8
            
            return_20d = indicators.get('return_20d', 0)
            if criteria.min_return_20d is not None:
                total_conditions += 1
                if return_20d >= criteria.min_return_20d:
                    condition_met_count += 1
                    score += 10
            
            # 波动率条件检查
            volatility = indicators.get('volatility', 0)
            if criteria.min_volatility is not None:
                total_conditions += 1
                if volatility >= criteria.min_volatility:
                    condition_met_count += 1
                    score += 5
            
            if criteria.max_volatility is not None:
                total_conditions += 1
                if volatility <= criteria.max_volatility:
                    condition_met_count += 1
                    score += 5
            
            # ETF类型检查
            etf_type = self._classify_etf_type(etf_name)
            if criteria.etf_types is not None:
                total_conditions += 1
                if etf_type in criteria.etf_types:
                    condition_met_count += 1
                    score += 15
            
            if criteria.exclude_types is not None:
                total_conditions += 1
                if etf_type not in criteria.exclude_types:
                    condition_met_count += 1
                    score += 10
            
            # 跟踪指数检查
            if criteria.track_indexes is not None:
                total_conditions += 1
                if any(keyword in etf_name for keyword in criteria.track_indexes):
                    condition_met_count += 1
                    score += 12
            
            if criteria.exclude_indexes is not None:
                total_conditions += 1
                if not any(keyword in etf_name for keyword in criteria.exclude_indexes):
                    condition_met_count += 1
                    score += 8
            
            # 基础评分（流动性、稳定性等）
            if volume_ratio > 0.5:  # 成交活跃
                score += 5
            if 0.05 <= volatility <= 0.3:  # 适中波动率
                score += 5
            if current_price > 1.0:  # 价格合理
                score += 3
            
            # 根据条件组合逻辑决定是否通过
            if criteria.condition_logic == "strict":
                passed = (condition_met_count == total_conditions) if total_conditions > 0 else True
            elif criteria.condition_logic == "any":
                passed = (condition_met_count > 0) if total_conditions > 0 else True
            else:  # flexible
                if total_conditions > 0:
                    condition_ratio = condition_met_count / total_conditions
                    passed = condition_ratio >= 0.5 or score >= 15
                else:
                    passed = score >= 10
            
            return passed, max(score, 0)
            
        except Exception as e:
            self.logger.error(f"检查ETF选股条件失败: {e}")
            return False, 0.0
    
    def select_etfs(self, criteria: ETFCriteria, custom_name: str = "ETF选股") -> List[StockScore]:
        """执行ETF选股"""
        self.logger.info(f"开始执行ETF选股，条件: {custom_name}")
        
        selected_etfs = []
        
        try:
            if not self.xt_available:
                self.logger.error("xttrader不可用，无法执行ETF选股")
                return []
            
            # 获取ETF列表
            all_etfs = self._get_etf_list_from_xttrader()
            self.logger.info(f"获取到 {len(all_etfs)} 只ETF")
            
            if not all_etfs:
                self.logger.error("未获取到ETF列表")
                return []
            
            # 计算日期范围
            end_date = datetime.now()
            extra_days = 60 if criteria.exclude_new_etf else 30
            start_date = end_date - timedelta(days=criteria.lookback_days + extra_days)
            
            processed_count = 0
            selected_count = 0
            
            for etf in all_etfs:
                try:
                    etf_code = etf['code']
                    etf_name = etf.get('name', etf_code)
                    
                    processed_count += 1
                    
                    if processed_count % 10 == 0:
                        self.logger.info(f"ETF选股进度: {processed_count}/{len(all_etfs)} ({processed_count/len(all_etfs)*100:.1f}%), 已选中: {selected_count}")
                    
                    # 获取ETF数据
                    df = self._get_etf_data_from_xttrader(
                        etf_code=etf_code,
                        start_date=start_date.strftime('%Y%m%d'),
                        end_date=end_date.strftime('%Y%m%d')
                    )
                    
                    if df is None or len(df) < criteria.min_trading_days:
                        continue
                    
                    # 排除新ETF
                    if criteria.exclude_new_etf and len(df) < 60:
                        continue
                    
                    # 计算技术指标
                    indicators = self._calculate_etf_indicators(df)
                    if not indicators:
                        continue
                    
                    # 检查选股条件
                    passed, score = self._check_etf_criteria(indicators, etf_name, criteria)
                    
                    if passed and score > 0:
                        etf_score = StockScore(
                            stock_code=etf_code,
                            stock_name=etf_name,
                            score=score,
                            indicators=indicators,
                            alpha_factors={},  # ETF不需要Alpha因子
                            selection_date=datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                        )
                        selected_etfs.append(etf_score)
                        selected_count += 1
                
                except Exception as e:
                    self.logger.error(f"处理ETF {etf.get('code', 'unknown')} 失败: {e}")
                    continue
            
            # 按评分排序
            selected_etfs.sort(key=lambda x: x.score, reverse=True)
            
            self.logger.info(f"ETF选股完成，共选中 {len(selected_etfs)} 只ETF")
            
            return selected_etfs
            
        except Exception as e:
            self.logger.error(f"ETF选股执行失败: {e}")
            return []
