/**
 * 声音警告工具类
 * 用于播放不同类型的警告声音
 */

class SoundAlert {
  constructor() {
    this.audioContext = null;
    this.isEnabled = true;
    this.volume = 0.35; // 默认音量调低一半
    
    // 初始化音频上下文
    this.initAudioContext();
    
    // 预定义的声音类型
    this.soundTypes = {
      STOP_LOSS: 'stop_loss',
      TRAILING_STOP: 'trailing_stop',
      PORTFOLIO_STOP: 'portfolio_stop',
      ORDER_FILLED: 'order_filled',
      CONNECTION_LOST: 'connection_lost',
      CRITICAL_ALERT: 'critical_alert'
    };
    
    // 声音配置
    this.soundConfigs = {
      [this.soundTypes.STOP_LOSS]: {
        frequency: 800,
        duration: 500,
        pattern: [1, 0.2, 1, 0.2, 1], // 三声短促警告
        color: '#ef4444'
      },
      [this.soundTypes.TRAILING_STOP]: {
        frequency: 600,
        duration: 300,
        pattern: [1, 0.3, 1], // 两声警告
        color: '#f97316'
      },
      [this.soundTypes.PORTFOLIO_STOP]: {
        frequency: 1000,
        duration: 800,
        pattern: [1, 0.1, 1, 0.1, 1, 0.1, 1], // 四声急促警告
        color: '#dc2626'
      },
      [this.soundTypes.ORDER_FILLED]: {
        frequency: 400,
        duration: 200,
        pattern: [1], // 单声提示
        color: '#10b981'
      },
      [this.soundTypes.CONNECTION_LOST]: {
        frequency: 300,
        duration: 1000,
        pattern: [1], // 长声警告
        color: '#6b7280'
      },
      [this.soundTypes.CRITICAL_ALERT]: {
        frequency: 1200,
        duration: 600,
        pattern: [1, 0.1, 1, 0.1, 1, 0.1, 1, 0.1, 1], // 五声急促警告
        color: '#991b1b'
      }
    };
  }

  initAudioContext() {
    try {
      // 创建音频上下文
      this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
    } catch (error) {
      console.warn('音频上下文初始化失败:', error);
      this.isEnabled = false;
    }
  }

  /**
   * 播放警告声音
   * @param {string} type - 声音类型
   * @param {Object} options - 可选参数
   */
  async playAlert(type, options = {}) {
    if (!this.isEnabled || !this.audioContext) {
      console.warn('声音警告已禁用或音频上下文不可用');
      return;
    }

    // 恢复音频上下文（某些浏览器需要用户交互后才能播放声音）
    if (this.audioContext.state === 'suspended') {
      try {
        await this.audioContext.resume();
      } catch (error) {
        console.warn('无法恢复音频上下文:', error);
        return;
      }
    }

    const config = this.soundConfigs[type];
    if (!config) {
      console.warn('未知的声音类型:', type);
      return;
    }

    // 合并配置
    const finalConfig = {
      ...config,
      ...options,
      volume: options.volume !== undefined ? options.volume : this.volume
    };

    // 播放声音模式
    this.playPattern(finalConfig);
    
    // 显示视觉提示
    this.showVisualAlert(type, finalConfig);
  }

  /**
   * 播放声音模式
   * @param {Object} config - 声音配置
   */
  playPattern(config) {
    const { frequency, duration, pattern, volume } = config;
    let currentTime = this.audioContext.currentTime;

    pattern.forEach((intensity, index) => {
      if (intensity > 0) {
        // 创建振荡器
        const oscillator = this.audioContext.createOscillator();
        const gainNode = this.audioContext.createGain();

        // 连接节点
        oscillator.connect(gainNode);
        gainNode.connect(this.audioContext.destination);

        // 设置频率和波形
        oscillator.frequency.setValueAtTime(frequency, currentTime);
        oscillator.type = 'sine';

        // 设置音量包络
        gainNode.gain.setValueAtTime(0, currentTime);
        gainNode.gain.linearRampToValueAtTime(volume * intensity, currentTime + 0.01);
        gainNode.gain.exponentialRampToValueAtTime(0.001, currentTime + duration / 1000);

        // 播放
        oscillator.start(currentTime);
        oscillator.stop(currentTime + duration / 1000);
      }

      // 计算下一个声音的开始时间
      currentTime += (duration + 100) / 1000; // 100ms间隔
    });
  }

  /**
   * 显示视觉警告提示
   * @param {string} type - 警告类型
   * @param {Object} config - 配置
   */
  showVisualAlert(type, config) {
    // 创建视觉提示元素
    const alertElement = document.createElement('div');
    alertElement.className = 'sound-alert-visual';
    alertElement.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: ${config.color};
      color: white;
      padding: 12px 20px;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.3);
      z-index: 10000;
      font-weight: bold;
      font-size: 14px;
      animation: soundAlertPulse 0.5s ease-in-out;
      max-width: 300px;
    `;

    // 设置提示内容
    const messages = {
      [this.soundTypes.STOP_LOSS]: '🚨 止损警告',
      [this.soundTypes.TRAILING_STOP]: '⚠️ 移动止损',
      [this.soundTypes.PORTFOLIO_STOP]: '🔥 组合止损',
      [this.soundTypes.ORDER_FILLED]: '✅ 订单成交',
      [this.soundTypes.CONNECTION_LOST]: '📡 连接断开',
      [this.soundTypes.CRITICAL_ALERT]: '🚨 严重警告'
    };

    alertElement.textContent = messages[type] || '⚠️ 系统警告';

    // 添加CSS动画
    if (!document.getElementById('sound-alert-styles')) {
      const style = document.createElement('style');
      style.id = 'sound-alert-styles';
      style.textContent = `
        @keyframes soundAlertPulse {
          0% { transform: scale(0.8); opacity: 0; }
          50% { transform: scale(1.1); opacity: 1; }
          100% { transform: scale(1); opacity: 1; }
        }
        .sound-alert-visual {
          transition: all 0.3s ease-out;
        }
      `;
      document.head.appendChild(style);
    }

    // 添加到页面
    document.body.appendChild(alertElement);

    // 自动移除
    setTimeout(() => {
      if (alertElement.parentNode) {
        alertElement.style.opacity = '0';
        alertElement.style.transform = 'translateX(100%)';
        setTimeout(() => {
          if (alertElement.parentNode) {
            alertElement.parentNode.removeChild(alertElement);
          }
        }, 300);
      }
    }, 3000);
  }

  /**
   * 播放止损警告
   * @param {Object} signal - 止损信号
   */
  playStopLossAlert(signal) {
    const { signal_type } = signal;
    
    switch (signal_type) {
      case 'loss_stop':
        this.playAlert(this.soundTypes.STOP_LOSS);
        break;
      case 'trailing_stop':
        this.playAlert(this.soundTypes.TRAILING_STOP);
        break;
      case 'portfolio_stop':
        this.playAlert(this.soundTypes.PORTFOLIO_STOP);
        break;
      default:
        this.playAlert(this.soundTypes.CRITICAL_ALERT);
    }
  }

  /**
   * 播放订单成交提示
   */
  playOrderFilledAlert() {
    this.playAlert(this.soundTypes.ORDER_FILLED);
  }

  /**
   * 播放连接断开警告
   */
  playConnectionLostAlert() {
    this.playAlert(this.soundTypes.CONNECTION_LOST);
  }

  /**
   * 设置音量
   * @param {number} volume - 音量 (0-1)
   */
  setVolume(volume) {
    this.volume = Math.max(0, Math.min(1, volume));
  }

  /**
   * 启用/禁用声音
   * @param {boolean} enabled - 是否启用
   */
  setEnabled(enabled) {
    this.isEnabled = enabled;
  }

  /**
   * 测试声音
   * @param {string} type - 声音类型
   */
  testSound(type = this.soundTypes.STOP_LOSS) {
    this.playAlert(type);
  }
}

// 创建全局实例
const soundAlert = new SoundAlert();

export default soundAlert;
