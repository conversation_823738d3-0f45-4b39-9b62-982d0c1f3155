#!/usr/bin/env python3
"""
诊断xttrader连接问题
"""

import os
import sys
import json

def check_environment():
    """检查Python环境"""
    print("=== Python环境检查 ===")
    print(f"Python版本: {sys.version}")
    print(f"Python路径: {sys.executable}")
    print(f"当前工作目录: {os.getcwd()}")

def check_xtquant():
    """检查xtquant模块"""
    print("\n=== xtquant模块检查 ===")
    try:
        import xtquant
        print("✅ xtquant模块导入成功")
        print(f"xtquant版本: {getattr(xtquant, '__version__', '未知')}")
        
        # 检查子模块
        try:
            from xtquant import xtdata, xttrader
            print("✅ xtdata和xttrader模块导入成功")
            return True
        except ImportError as e:
            print(f"❌ xtdata/xttrader导入失败: {e}")
            return False
            
    except ImportError as e:
        print(f"❌ xtquant模块导入失败: {e}")
        return False

def check_qmt_path():
    """检查QMT路径"""
    print("\n=== QMT路径检查 ===")
    
    # 从配置文件读取路径
    config_file = "backend/config/live_trading.json"
    if os.path.exists(config_file):
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        qmt_path = config.get('live_trading', {}).get('min_path')
        if qmt_path:
            print(f"配置中的QMT路径: {qmt_path}")
            if os.path.exists(qmt_path):
                print("✅ QMT路径存在")
                return True
            else:
                print("❌ QMT路径不存在")
        else:
            print("❌ 配置中未找到QMT路径")
    else:
        print(f"❌ 配置文件不存在: {config_file}")
    
    # 尝试查找常见的QMT路径
    print("\n尝试查找QMT安装路径:")
    possible_paths = [
        "C:\\Program Files\\QMT",
        "C:\\Program Files (x86)\\QMT", 
        "D:\\QMT",
        "C:\\QMT",
        "D:\\国金证券QMT交易端",
        "C:\\国金证券QMT交易端",
        "D:\\国金证券QMT交易端\\userdata_mini",
        "C:\\国金证券QMT交易端\\userdata_mini"
    ]
    
    found_paths = []
    for path in possible_paths:
        if os.path.exists(path):
            print(f"✅ 找到: {path}")
            found_paths.append(path)
        else:
            print(f"❌ 不存在: {path}")
    
    return len(found_paths) > 0

def test_data_manager():
    """测试data_manager"""
    print("\n=== data_manager测试 ===")
    try:
        from backend.data.data_manager import data_manager
        print("✅ data_manager导入成功")
        
        # 测试获取股票列表
        print("测试获取股票列表...")
        result = data_manager.get_stock_list(page=1, page_size=3)
        
        if result.get('success'):
            stocks = result.get('data', [])
            print(f"✅ 获取到 {len(stocks)} 只股票")
            
            if stocks:
                # 测试获取股票数据
                test_stock = stocks[0]
                stock_code = test_stock['code']
                print(f"\n测试获取股票数据: {test_stock['name']}({stock_code})")
                
                try:
                    df = data_manager.get_stock_data(stock_code, period='1d', count=5)
                    if df is not None and len(df) > 0:
                        print(f"✅ 获取到股票数据: {len(df)} 条")
                        print("最新数据:")
                        print(df.tail(1).to_string())
                        return True
                    else:
                        print("❌ 未获取到股票数据")
                except Exception as e:
                    print(f"❌ 获取股票数据失败: {e}")
            else:
                print("❌ 股票列表为空")
        else:
            print(f"❌ 获取股票列表失败: {result}")
            
    except Exception as e:
        print(f"❌ data_manager测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    return False

def test_strategy_execution():
    """测试策略执行"""
    print("\n=== 策略执行测试 ===")
    
    import requests
    BASE_URL = "http://localhost:8000"
    
    try:
        # 测试API连接
        response = requests.get(f"{BASE_URL}/api/data/stocks?page=1&page_size=3", timeout=5)
        print(f"API连接测试: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                stocks = data.get('data', [])
                print(f"✅ API获取到 {len(stocks)} 只股票")
                
                if stocks:
                    # 测试启动策略
                    test_stock = stocks[0]
                    strategy_data = {
                        'name': 'xttrader诊断测试',
                        'strategy_type': 'bollinger_bands',
                        'config': {
                            'initial_capital': 50000,
                            'paper_trading': True,
                            'stock_codes': [test_stock['code']],
                            'bb_period': 10,
                            'bb_std': 1.5
                        }
                    }
                    
                    print(f"\n启动测试策略...")
                    response = requests.post(f"{BASE_URL}/api/live/strategies", json=strategy_data, timeout=10)
                    
                    if response.status_code == 200:
                        result = response.json()
                        if result.get('success'):
                            strategy_id = result['strategy_id']
                            print(f"✅ 策略启动成功: {strategy_id}")
                            
                            # 获取策略详情
                            response = requests.get(f"{BASE_URL}/api/live/strategies/{strategy_id}", timeout=5)
                            if response.status_code == 200:
                                strategy = response.json()
                                print(f"策略详情:")
                                print(f"  task_id: {strategy.get('task_id')}")
                                print(f"  状态: {strategy.get('status')}")
                                print(f"  错误信息: {strategy.get('error_message')}")
                            
                            # 清理测试策略
                            requests.delete(f"{BASE_URL}/api/live/strategies/{strategy_id}")
                            print("测试策略已清理")
                            return True
                        else:
                            print(f"❌ 策略启动失败: {result.get('message')}")
                    else:
                        print(f"❌ 策略启动请求失败: {response.status_code}")
                        print(f"响应内容: {response.text}")
            else:
                print(f"❌ API返回失败: {data}")
        else:
            print(f"❌ API连接失败: {response.status_code}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 网络请求失败: {e}")
    except Exception as e:
        print(f"❌ 策略执行测试失败: {e}")
    
    return False

def main():
    """主诊断函数"""
    print("🔍 开始诊断xttrader连接问题")
    
    # 1. 检查Python环境
    check_environment()
    
    # 2. 检查xtquant模块
    xtquant_ok = check_xtquant()
    
    # 3. 检查QMT路径
    qmt_path_ok = check_qmt_path()
    
    # 4. 测试data_manager
    data_manager_ok = False
    if xtquant_ok and qmt_path_ok:
        data_manager_ok = test_data_manager()
    
    # 5. 测试策略执行
    strategy_ok = False
    if data_manager_ok:
        strategy_ok = test_strategy_execution()
    
    # 总结
    print(f"\n🎯 诊断结果总结:")
    print(f"xtquant模块: {'✅ OK' if xtquant_ok else '❌ FAIL'}")
    print(f"QMT路径: {'✅ OK' if qmt_path_ok else '❌ FAIL'}")
    print(f"data_manager: {'✅ OK' if data_manager_ok else '❌ FAIL'}")
    print(f"策略执行: {'✅ OK' if strategy_ok else '❌ FAIL'}")
    
    if not xtquant_ok:
        print(f"\n💡 建议:")
        print(f"1. 确保已安装xtquant模块: pip install xtquant")
        print(f"2. 确保在正确的conda环境中: conda activate qmt")
        
    if not qmt_path_ok:
        print(f"\n💡 建议:")
        print(f"1. 检查QMT是否已正确安装")
        print(f"2. 更新backend/config/live_trading.json中的min_path配置")
        print(f"3. 确保QMT路径指向userdata_mini目录")
    
    if not data_manager_ok:
        print(f"\n💡 建议:")
        print(f"1. 检查QMT是否正在运行")
        print(f"2. 检查账户配置是否正确")
        print(f"3. 查看后端日志获取详细错误信息")

if __name__ == "__main__":
    main()
