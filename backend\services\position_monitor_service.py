#!/usr/bin/env python3
"""
持仓监控服务
"""

import asyncio
from typing import List, Dict, Any, Optional
from datetime import datetime

from backend.strategies.position_monitor import position_monitor
from backend.config.position_monitor_config import position_monitor_config
from backend.core.logger import get_service_logger

logger = get_service_logger('position_monitor')

class PositionMonitorService:
    """持仓监控服务"""

    def __init__(self):
        self.is_running = False
        self.monitor_task = None
        self.trade_logs = []  # 交易日志历史
        self.pending_orders = {}  # 待处理订单 {stock_code: order_info}
        self.processed_signals = set()  # 已处理的信号（防重复）

    async def start_monitoring(self):
        """启动监控"""
        if self.is_running:
            return

        self.is_running = True
        self.monitor_task = asyncio.create_task(self._monitor_loop())
        logger.info("持仓监控服务已启动")

    async def stop_monitoring(self):
        """停止监控"""
        if not self.is_running:
            return

        self.is_running = False
        if self.monitor_task:
            self.monitor_task.cancel()
            self.monitor_task = None

        logger.info("持仓监控服务已停止")

    async def _monitor_loop(self):
        """监控循环"""
        while self.is_running:
            try:
                # 1. 检查待处理订单状态
                self._check_pending_orders()

                # 2. 检查持仓
                signals = position_monitor.monitor_positions()

                if signals:
                    logger.info(f"检测到 {len(signals)} 个止损信号")

                    for signal in signals:
                        logger.warning(f"止损信号: {signal['message']}")

                        # 生成信号ID，防止重复处理
                        signal_id = self._generate_signal_id(signal)
                        stock_code = signal.get('stock_code')

                        # 检查是否已处理过此信号
                        if signal_id in self.processed_signals:
                            logger.debug(f"信号已处理过，跳过: {stock_code}")
                            continue

                        # 检查是否有待处理的订单
                        if self._has_pending_order(stock_code):
                            logger.warning(f"⚠️ {stock_code} 存在待处理订单，跳过新的卖出信号")
                            continue

                        # 如果启用自动卖出
                        if position_monitor_config.get('position_monitor.auto_sell', False):
                            success = self._execute_real_sell_order_sync(signal)
                            if success:
                                logger.info(f"✅ 自动卖出订单提交成功: {signal['stock_code']}")
                                # 标记信号已处理
                                self.processed_signals.add(signal_id)
                            else:
                                logger.error(f"❌ 自动卖出订单提交失败: {signal['stock_code']}")
                        else:
                            logger.info(f"📋 仅提醒模式，不执行卖出: {signal['stock_code']}")
                            # 即使不执行，也标记为已处理，避免重复提醒
                            self.processed_signals.add(signal_id)

                # 3. 等待下一次检查
                interval = position_monitor_config.get('position_monitor.monitor_interval', 60)
                await asyncio.sleep(interval)

            except Exception as e:
                logger.error(f"监控循环异常: {e}")
                await asyncio.sleep(10)  # 异常时短暂等待

    def get_status(self) -> Dict[str, Any]:
        """获取监控状态"""
        return {
            'is_running': self.is_running,
            'config': position_monitor_config.config.get('position_monitor', {}),
            'signals_count': len(position_monitor.signals_history),
            'last_check': datetime.now().isoformat() if self.is_running else None
        }

    async def manual_check(self) -> List[Dict[str, Any]]:
        """手动检查一次"""
        logger.info("执行手动持仓检查")
        return position_monitor.monitor_positions()

    def _execute_real_sell_order_sync(self, signal: Dict[str, Any]) -> bool:
        """执行真实的QMT卖出订单 (同步版本)"""
        stock_code = signal.get('stock_code')
        signal_type = signal.get('signal_type')

        logger.info(f"🔥 执行真实卖出: {stock_code} (触发原因: {signal_type})")

        # 获取账户信息和持仓
        from backend.data.data_manager import data_manager

        account_info = data_manager.get_account_info()
        positions = account_info.get('positions', [])

        # 找到对应的持仓
        target_position = None
        for position in positions:
            if position['stock_code'] == stock_code:
                target_position = position
                break

        if not target_position:
            logger.error(f"❌ 未找到{stock_code}的持仓信息")
            self._log_trade_execution(stock_code, 0, 0, signal_type, False)
            return False

        quantity = target_position['quantity']
        current_price = target_position['current_price']

        logger.info(f"📊 准备卖出: {stock_code}, 数量: {quantity}, 当前价: {current_price}")

        # 检查QMT交易功能是否可用
        if not hasattr(data_manager, 'xt_trader') or not data_manager.xt_trader:
            logger.error(f"❌ QMT交易功能不可用，无法执行卖出")
            self._log_trade_execution(stock_code, quantity, current_price, signal_type, False)
            return False

        # 执行QMT卖出订单
        logger.info(f"🔄 开始执行QMT卖出订单...")
        try:
            # 使用QMT交易接口
            order_result = self._place_qmt_sell_order(stock_code, quantity, current_price)

            if order_result and order_result.get('success'):
                order_id = order_result.get('order_id')
                logger.info(f"✅ QMT卖出订单提交成功: {stock_code}, 订单号: {order_id}")

                # 添加到待处理订单列表
                self.pending_orders[stock_code] = {
                    'order_id': order_id,
                    'stock_code': stock_code,
                    'quantity': quantity,
                    'price': current_price,
                    'signal_type': signal_type,
                    'submit_time': datetime.now().isoformat(),
                    'status': 'pending'
                }

                # 记录交易日志（初始状态为待确认）
                self._log_trade_execution(stock_code, quantity, current_price, signal_type, None, order_id, '订单已提交，等待成交')
                return True
            else:
                error_msg = order_result.get('message', '未知错误') if order_result else '返回结果为空'
                logger.error(f"❌ QMT卖出订单提交失败: {stock_code}, 原因: {error_msg}")
                self._log_trade_execution(stock_code, quantity, current_price, signal_type, False, None, error_msg)
                return False

        except Exception as e:
            logger.error(f"❌ 执行卖出订单异常: {stock_code}, 错误: {e}")
            self._log_trade_execution(stock_code, quantity, current_price, signal_type, False)
            return False

    async def _execute_real_sell_order(self, signal: Dict[str, Any]) -> bool:
        """执行真实的QMT卖出订单"""
        stock_code = signal.get('stock_code')
        signal_type = signal.get('signal_type')

        logger.info(f"🔥 执行真实卖出: {stock_code} (触发原因: {signal_type})")

        # 获取账户信息和持仓
        from backend.data.data_manager import data_manager

        account_info = data_manager.get_account_info()
        positions = account_info.get('positions', [])

        # 找到对应的持仓
        target_position = None
        for position in positions:
            if position['stock_code'] == stock_code:
                target_position = position
                break

        if not target_position:
            logger.error(f"❌ 未找到{stock_code}的持仓信息")
            return False

        quantity = target_position['quantity']
        current_price = target_position['current_price']

        logger.info(f"📊 准备卖出: {stock_code}, 数量: {quantity}, 当前价: {current_price}")

        # 检查QMT交易功能是否可用
        if not hasattr(data_manager, 'xt_trader') or not data_manager.xt_trader:
            logger.error(f"❌ QMT交易功能不可用，无法执行卖出")
            self._log_trade_execution(stock_code, quantity, current_price, signal_type, False)
            return False

        # 执行QMT卖出订单
        logger.info(f"🔄 开始执行QMT卖出订单...")
        try:
            # 使用QMT交易接口
            order_result = self._place_qmt_sell_order(stock_code, quantity, current_price)

            if order_result and order_result.get('success'):
                order_id = order_result.get('order_id')
                logger.info(f"✅ QMT卖出订单提交成功: {stock_code}, 订单号: {order_id}")

                # 记录交易日志
                self._log_trade_execution(stock_code, quantity, current_price, signal_type, True, order_id)
                return True
            else:
                error_msg = order_result.get('message', '未知错误') if order_result else '返回结果为空'
                logger.error(f"❌ QMT卖出订单提交失败: {stock_code}, 原因: {error_msg}")
                self._log_trade_execution(stock_code, quantity, current_price, signal_type, False)
                return False

        except Exception as e:
            logger.error(f"❌ 执行卖出订单异常: {stock_code}, 错误: {e}")
            self._log_trade_execution(stock_code, quantity, current_price, signal_type, False)
            return False

    def _place_qmt_sell_order(self, stock_code: str, quantity: int, price: float) -> Dict[str, Any]:
        """提交QMT卖出订单"""
        logger.info(f"📤 提交QMT卖出订单: {stock_code}, 数量: {quantity}, 价格: {price}")

        # 使用真实的QMT交易器
        from backend.trading.qmt_trader import qmt_trader

        # 执行真实卖出
        result = qmt_trader.sell_stock(stock_code, quantity, price)

        if result['success']:
            logger.info(f"✅ QMT卖出订单成功: 订单号 {result.get('order_id')}")
        else:
            logger.error(f"❌ QMT卖出订单失败: {result.get('message')}")

        return result

    def _log_trade_execution(self, stock_code: str, quantity: int, price: float,
                           signal_type: str, success: Optional[bool], order_id: str = None, message: str = ""):
        """记录交易执行日志"""
        from backend.core.logger import get_trading_logger
        trade_logger = get_trading_logger('auto_sell')

        if success is None:
            status = "待确认"
        elif success:
            status = "成功"
        else:
            status = "失败"

        trade_logger.info(f"自动卖出{status}: {stock_code}, 数量: {quantity}, "
                         f"价格: {price}, 触发: {signal_type}, 订单号: {order_id}, 时间: {datetime.now()}")

        # 添加到交易日志历史
        trade_log = {
            'action': '卖出',
            'stock_code': stock_code,
            'quantity': quantity,
            'price': price,
            'success': success,
            'order_id': order_id,
            'trigger_reason': signal_type,
            'timestamp': datetime.now().isoformat(),
            'message': message or f"自动卖出{status}",
            'status': status
        }

        self.trade_logs.append(trade_log)

        # 限制历史记录大小
        max_logs = position_monitor_config.get('position_monitor.max_signals', 1000)
        if len(self.trade_logs) > max_logs:
            self.trade_logs = self.trade_logs[-max_logs:]

    def get_trade_logs(self, limit: int = 20) -> List[Dict[str, Any]]:
        """获取交易日志"""
        logs = self.trade_logs[-limit:] if limit > 0 else self.trade_logs
        # 按时间倒序排列，最新的在最上面
        return list(reversed(logs))

    def _check_pending_orders(self):
        """检查待处理订单状态"""
        if not self.pending_orders:
            return

        from backend.trading.qmt_trader import qmt_trader

        completed_orders = []
        updated_orders = []

        for stock_code, order_info in self.pending_orders.items():
            order_id = order_info.get('order_id')
            if not order_id:
                continue

            # 查询订单状态
            status = qmt_trader.get_order_status(order_id)

            # 记录状态变化
            old_status = order_info.get('last_status', 'unknown')
            new_status = 'filled' if status.get('is_filled') else ('partial' if status.get('is_partial') else 'pending')

            # 更新订单状态
            order_info['last_status'] = new_status
            order_info['last_check'] = datetime.now().isoformat()

            if status.get('found'):
                order_data = status.get('order_info', {})
                is_filled = status.get('is_filled', False)
                is_partial = status.get('is_partial', False)

                # 更新订单详细信息
                order_info.update({
                    'filled_volume': status.get('filled_volume', 0),
                    'order_volume': status.get('order_volume', order_info.get('quantity', 0)),
                    'order_status': status.get('order_status', 0)
                })

                # 如果状态发生变化，记录到更新列表
                if old_status != new_status:
                    updated_orders.append({
                        'stock_code': stock_code,
                        'order_id': order_id,
                        'old_status': old_status,
                        'new_status': new_status,
                        'order_info': order_info
                    })

                if is_filled:
                    # 订单完全成交
                    logger.info(f"✅ 订单完全成交: {stock_code}, 订单号: {order_id}")
                    self._update_trade_log_status(order_id, True, '完全成交')
                    completed_orders.append(stock_code)

                elif is_partial:
                    # 订单部分成交
                    filled_volume = status.get('filled_volume', 0)
                    order_volume = status.get('order_volume', 0)
                    logger.info(f"⚠️ 订单部分成交: {stock_code}, 订单号: {order_id}, "
                              f"成交: {filled_volume}/{order_volume}")
                    self._update_trade_log_status(order_id, False, f'部分成交 {filled_volume}/{order_volume}')

                else:
                    # 订单未成交，检查是否需要撤单
                    order_time = order_info.get('submit_time')
                    if order_time:
                        from datetime import datetime, timedelta
                        submit_time = datetime.fromisoformat(order_time)
                        if datetime.now() - submit_time > timedelta(minutes=5):  # 5分钟未成交考虑撤单
                            logger.warning(f"⏰ 订单超时未成交: {stock_code}, 订单号: {order_id}")
                            # 可以选择撤单或继续等待
            else:
                # 订单未找到，可能已经被撤销或其他原因
                logger.warning(f"❓ 订单未找到: {stock_code}, 订单号: {order_id}")
                completed_orders.append(stock_code)

        # 移除已完成的订单
        for stock_code in completed_orders:
            del self.pending_orders[stock_code]

        if completed_orders:
            logger.info(f"✅ 清理了{len(completed_orders)}个已完成的订单")

        if updated_orders:
            logger.info(f"📋 {len(updated_orders)}个订单状态发生变化")
            for update in updated_orders:
                logger.info(f"   {update['stock_code']}: {update['old_status']} -> {update['new_status']}")

        return {
            'completed_orders': completed_orders,
            'updated_orders': updated_orders,
            'total_pending': len(self.pending_orders)
        }

    def _update_trade_log_status(self, order_id: int, success: bool, message: str):
        """更新交易日志状态"""
        for log in self.trade_logs:
            if log.get('order_id') == order_id:
                log['success'] = success
                log['message'] = message
                log['update_time'] = datetime.now().isoformat()
                break

    def _has_pending_order(self, stock_code: str) -> bool:
        """检查是否有待处理的订单"""
        return stock_code in self.pending_orders

    def _generate_signal_id(self, signal: Dict[str, Any]) -> str:
        """生成信号唯一ID"""
        stock_code = signal.get('stock_code', '')
        signal_type = signal.get('signal_type', '')
        timestamp = signal.get('timestamp', '')
        return f"{stock_code}_{signal_type}_{timestamp}"

    def cancel_pending_order(self, order_id: int) -> Dict[str, Any]:
        """取消待处理订单"""
        logger.info(f"🚫 尝试取消订单: {order_id}")

        # 查找对应的待处理订单
        target_stock_code = None
        target_order = None

        for stock_code, order_info in self.pending_orders.items():
            if order_info.get('order_id') == order_id:
                target_stock_code = stock_code
                target_order = order_info
                break

        if not target_order:
            return {
                'success': False,
                'message': f'未找到订单号 {order_id} 的待处理订单',
                'order_id': order_id
            }

        # 调用QMT取消订单
        from backend.trading.qmt_trader import qmt_trader

        cancel_result = qmt_trader.cancel_order(order_id)

        if cancel_result.get('success'):
            # 取消成功，从待处理订单中移除
            del self.pending_orders[target_stock_code]

            # 更新交易日志
            self._update_trade_log_status(order_id, False, '订单已取消')

            logger.info(f"✅ 订单取消成功: {order_id}")

            return {
                'success': True,
                'message': f'订单 {order_id} 取消成功',
                'order_id': order_id,
                'stock_code': target_stock_code
            }
        else:
            logger.error(f"❌ 订单取消失败: {order_id}, 原因: {cancel_result.get('message')}")

            return {
                'success': False,
                'message': f'订单取消失败: {cancel_result.get("message")}',
                'order_id': order_id
            }

# 全局服务实例
position_monitor_service = PositionMonitorService()
