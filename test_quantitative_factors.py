#!/usr/bin/env python3
"""
测试量化因子功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import numpy as np
import pandas as pd
from backend.stock_selection.quantitative_factors import QuantitativeFactors
from backend.stock_selection.stock_selector import StockSelector, SelectionCriteria
from backend.core.logger import get_logger

logger = get_logger(__name__)

def create_test_data():
    """创建测试数据"""
    # 创建100天的模拟股票数据
    np.random.seed(42)
    dates = pd.date_range('2024-01-01', periods=100, freq='D')
    
    # 模拟价格走势
    initial_price = 10.0
    returns = np.random.normal(0.001, 0.02, 100)  # 日收益率
    prices = [initial_price]
    
    for ret in returns[1:]:
        prices.append(prices[-1] * (1 + ret))
    
    # 创建OHLCV数据
    close = np.array(prices)
    high = close * (1 + np.abs(np.random.normal(0, 0.01, 100)))
    low = close * (1 - np.abs(np.random.normal(0, 0.01, 100)))
    open_price = close * (1 + np.random.normal(0, 0.005, 100))
    volume = np.random.randint(1000000, 10000000, 100)
    amount = close * volume
    
    df = pd.DataFrame({
        'date': dates,
        'open': open_price,
        'high': high,
        'low': low,
        'close': close,
        'volume': volume,
        'amount': amount
    })
    
    return df

def create_test_fundamental_data():
    """创建测试基本面数据"""
    return {
        # 价值因子
        'pe_ttm': 15.5,
        'pb_ratio': 1.8,
        'ps_ttm': 2.3,
        'dividend_yield': 0.025,
        'ev_ebitda': 12.0,
        'market_cap': 50000000000,  # 500亿市值
        
        # 质量因子
        'roe': 18.5,
        'roa': 8.2,
        'gross_margin': 35.6,
        'net_margin': 12.3,
        'asset_turnover': 0.8,
        'equity_multiplier': 2.1,
        
        # 成长因子
        'revenue_growth': 22.5,
        'profit_growth': 28.3,
        'eps_growth': 25.1,
        'rd_growth': 15.8,
        'revenue_cagr_3y': 20.2,
        'profit_cagr_3y': 24.7
    }

def test_quantitative_factors():
    """测试量化因子计算"""
    print("🧮 测试量化因子计算")
    print("=" * 60)
    
    # 创建测试数据
    df = create_test_data()
    fundamental_data = create_test_fundamental_data()
    
    # 创建量化因子计算器
    quant_factors = QuantitativeFactors()
    
    print(f"📊 测试数据:")
    print(f"   数据长度: {len(df)} 天")
    print(f"   价格范围: {df['close'].min():.2f} - {df['close'].max():.2f}")
    print(f"   成交量范围: {df['volume'].min():,} - {df['volume'].max():,}")
    
    # 计算所有因子
    print(f"\n🔍 计算量化因子...")
    factors = quant_factors.calculate_all_factors(df, fundamental_data)
    
    if not factors:
        print("❌ 量化因子计算失败")
        return False
    
    print(f"✅ 成功计算 {len(factors)} 个因子")
    
    # 显示价值因子
    print(f"\n💰 价值因子 (Value Factors):")
    value_factors = ['ep_ratio', 'bp_ratio', 'sp_ratio', 'dividend_yield', 'pe_ttm', 'pb_ratio', 'ps_ttm', 'value_score']
    for factor in value_factors:
        if factor in factors:
            value = factors[factor]
            print(f"   {factor}: {value:.4f}" if isinstance(value, (int, float)) else f"   {factor}: {value}")
    
    # 显示质量因子
    print(f"\n🏆 质量因子 (Quality Factors):")
    quality_factors = ['roe', 'roa', 'gross_margin', 'net_margin', 'quality_score']
    for factor in quality_factors:
        if factor in factors:
            value = factors[factor]
            print(f"   {factor}: {value:.4f}" if isinstance(value, (int, float)) else f"   {factor}: {value}")
    
    # 显示成长因子
    print(f"\n📈 成长因子 (Growth Factors):")
    growth_factors = ['revenue_growth', 'profit_growth', 'eps_growth', 'growth_score']
    for factor in growth_factors:
        if factor in factors:
            value = factors[factor]
            print(f"   {factor}: {value:.4f}" if isinstance(value, (int, float)) else f"   {factor}: {value}")
    
    # 显示动量因子
    print(f"\n🚀 动量因子 (Momentum Factors):")
    momentum_factors = ['momentum_1m', 'momentum_3m', 'momentum_12m', 'momentum_acceleration', 'volume_momentum', 'momentum_score']
    for factor in momentum_factors:
        if factor in factors:
            value = factors[factor]
            print(f"   {factor}: {value:.4f}" if isinstance(value, (int, float)) else f"   {factor}: {value}")
    
    # 显示风险控制因子
    print(f"\n🛡️ 风险控制因子 (Risk Factors):")
    risk_factors = ['volatility', 'beta', 'max_drawdown', 'var_95', 'downside_volatility', 'risk_score']
    for factor in risk_factors:
        if factor in factors:
            value = factors[factor]
            print(f"   {factor}: {value:.4f}" if isinstance(value, (int, float)) else f"   {factor}: {value}")
    
    # 显示技术面因子
    print(f"\n📊 技术面因子 (Technical Factors):")
    technical_factors = ['rsi', 'macd', 'macd_trend', 'ma_arrangement', 'bb_position', 'price_position', 'technical_score']
    for factor in technical_factors:
        if factor in factors:
            value = factors[factor]
            print(f"   {factor}: {value:.4f}" if isinstance(value, (int, float)) else f"   {factor}: {value}")
    
    # 显示流动性因子
    print(f"\n💧 流动性因子 (Liquidity Factors):")
    liquidity_factors = ['avg_amount', 'volume_ratio', 'turnover_rate', 'amihud_illiquidity', 'liquidity_score']
    for factor in liquidity_factors:
        if factor in factors:
            value = factors[factor]
            print(f"   {factor}: {value:.4f}" if isinstance(value, (int, float)) else f"   {factor}: {value}")
    
    return True

def test_enhanced_selection():
    """测试增强的选股功能"""
    print(f"\n🎯 测试增强选股功能")
    print("=" * 60)
    
    # 创建选股器
    selector = StockSelector()
    
    # 测试价值投资策略
    print(f"\n1️⃣ 测试价值投资策略...")
    value_criteria = SelectionCriteria(
        exclude_etf=True,
        pe_max=20,               # 市盈率<20
        pb_max=2.5,              # 市净率<2.5
        dividend_yield_min=0.02, # 股息率>2%
        roe_min=15,              # ROE>15%
        condition_logic="flexible"
    )
    
    print(f"📋 价值投资条件:")
    print(f"   PE < {value_criteria.pe_max}")
    print(f"   PB < {value_criteria.pb_max}")
    print(f"   股息率 > {value_criteria.dividend_yield_min}")
    print(f"   ROE > {value_criteria.roe_min}")
    
    # 测试成长投资策略
    print(f"\n2️⃣ 测试成长投资策略...")
    growth_criteria = SelectionCriteria(
        exclude_etf=True,
        revenue_growth_min=20,   # 营收增长>20%
        profit_growth_min=25,    # 利润增长>25%
        eps_growth_min=20,       # EPS增长>20%
        roe_min=18,              # ROE>18%
        condition_logic="flexible"
    )
    
    print(f"📋 成长投资条件:")
    print(f"   营收增长 > {growth_criteria.revenue_growth_min}%")
    print(f"   利润增长 > {growth_criteria.profit_growth_min}%")
    print(f"   EPS增长 > {growth_criteria.eps_growth_min}%")
    print(f"   ROE > {growth_criteria.roe_min}%")
    
    # 测试动量投资策略
    print(f"\n3️⃣ 测试动量投资策略...")
    momentum_criteria = SelectionCriteria(
        exclude_etf=True,
        momentum_1m_min=0.05,    # 1月涨幅>5%
        momentum_3m_min=0.15,    # 3月涨幅>15%
        volume_ratio_min=1.5,    # 量比>1.5
        volatility_max=0.4,      # 波动率<40%
        condition_logic="flexible"
    )
    
    print(f"📋 动量投资条件:")
    print(f"   1月动量 > {momentum_criteria.momentum_1m_min}")
    print(f"   3月动量 > {momentum_criteria.momentum_3m_min}")
    print(f"   量比 > {momentum_criteria.volume_ratio_min}")
    print(f"   波动率 < {momentum_criteria.volatility_max}")
    
    # 测试ETF选择
    print(f"\n4️⃣ 测试ETF选择...")
    etf_criteria = SelectionCriteria(
        only_etf=True,
        volume_ratio_min=0.8,
        avg_amount_min=1000,     # 日均成交额>1000万
        condition_logic="flexible"
    )
    
    print(f"📋 ETF选择条件:")
    print(f"   仅ETF: {etf_criteria.only_etf}")
    print(f"   量比 > {etf_criteria.volume_ratio_min}")
    print(f"   日均成交额 > {etf_criteria.avg_amount_min}万元")
    
    print(f"\n✅ 选股条件配置测试完成")
    print(f"💡 注意: 由于没有xttrader环境，无法执行实际选股")

def main():
    """主函数"""
    print("🎯 量化因子功能测试")
    print("=" * 80)
    
    try:
        # 测试量化因子计算
        success = test_quantitative_factors()
        
        if success:
            # 测试增强选股功能
            test_enhanced_selection()
            
            print(f"\n" + "=" * 80)
            print(f"✅ 量化因子功能测试完成！")
            
            print(f"\n💡 实现的核心功能:")
            print(f"   ✅ 价值因子 - EP、BP、SP、股息率等")
            print(f"   ✅ 质量因子 - ROE、ROA、毛利率等")
            print(f"   ✅ 成长因子 - 营收增长、利润增长、EPS增长等")
            print(f"   ✅ 动量因子 - 1M/3M/12M动量、动量加速度等")
            print(f"   ✅ 风险控制因子 - 波动率、Beta、最大回撤等")
            print(f"   ✅ 技术面因子 - RSI、MACD、均线系统等")
            print(f"   ✅ 流动性因子 - 成交额、换手率、量比等")
            print(f"   ✅ ETF筛选支持 - 完整的ETF识别和分类")
            
            print(f"\n🎯 使用建议:")
            print(f"   • 价值投资：使用PE、PB、股息率等价值因子")
            print(f"   • 成长投资：使用营收增长、利润增长等成长因子")
            print(f"   • 动量投资：使用短中长期动量因子")
            print(f"   • 风险控制：使用波动率、Beta、回撤等风险因子")
            print(f"   • ETF投资：使用ETF筛选功能选择合适基金")
        else:
            print(f"\n❌ 量化因子功能测试失败")
            
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
