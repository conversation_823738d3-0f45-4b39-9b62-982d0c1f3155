#!/usr/bin/env python3
"""
测试增强的选股功能 - 包含ETF筛选和更多因子
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from backend.stock_selection.stock_selector import StockSelector, SelectionCriteria
from backend.core.logger import get_logger

logger = get_logger(__name__)

def test_etf_selection():
    """测试ETF选股功能"""
    print("🎯 测试ETF选股功能")
    print("=" * 60)
    
    # 创建选股器
    selector = StockSelector()
    
    # 测试1：仅选择ETF
    print("\n1️⃣ 测试仅选择ETF...")
    criteria_etf_only = SelectionCriteria(
        only_etf=True,
        volume_ratio_min=0.5,  # 成交活跃
        condition_logic="flexible",
        lookback_days=30,
        min_trading_days=20
    )
    
    print(f"📋 选股条件:")
    print(f"   仅ETF: {criteria_etf_only.only_etf}")
    print(f"   最小量比: {criteria_etf_only.volume_ratio_min}")
    print(f"   条件逻辑: {criteria_etf_only.condition_logic}")
    
    try:
        etf_results = selector.select_stocks_with_progress(criteria_etf_only, "仅ETF选股测试")
        print(f"✅ ETF选股完成，共选中 {len(etf_results)} 只ETF")
        
        if etf_results:
            print(f"\n📊 前5只ETF:")
            for i, stock in enumerate(etf_results[:5]):
                print(f"   {i+1}. {stock.stock_code} - {stock.stock_name} (评分: {stock.score:.1f})")
        
    except Exception as e:
        print(f"❌ ETF选股失败: {e}")
    
    # 测试2：选择特定类型的ETF
    print(f"\n2️⃣ 测试选择股票型ETF...")
    criteria_stock_etf = SelectionCriteria(
        only_etf=True,
        etf_types=['stock'],  # 仅股票型ETF
        volume_ratio_min=0.8,
        condition_logic="flexible"
    )
    
    try:
        stock_etf_results = selector.select_stocks_with_progress(criteria_stock_etf, "股票型ETF选股")
        print(f"✅ 股票型ETF选股完成，共选中 {len(stock_etf_results)} 只")
        
        if stock_etf_results:
            print(f"\n📊 前3只股票型ETF:")
            for i, stock in enumerate(stock_etf_results[:3]):
                print(f"   {i+1}. {stock.stock_code} - {stock.stock_name} (评分: {stock.score:.1f})")
        
    except Exception as e:
        print(f"❌ 股票型ETF选股失败: {e}")

def test_momentum_factors():
    """测试动量因子选股"""
    print(f"\n🚀 测试动量因子选股功能")
    print("=" * 60)
    
    selector = StockSelector()
    
    # 测试动量选股
    print("\n1️⃣ 测试强势动量股票...")
    criteria_momentum = SelectionCriteria(
        exclude_etf=True,  # 排除ETF，只选股票
        momentum_1m_min=0.05,  # 1月涨幅>5%
        momentum_3m_min=0.10,  # 3月涨幅>10%
        volume_ratio_min=1.2,  # 成交活跃
        volatility_max=0.4,    # 控制风险
        condition_logic="flexible",
        lookback_days=90,  # 需要更长的历史数据
        min_trading_days=60
    )
    
    print(f"📋 动量选股条件:")
    print(f"   1月动量 > {criteria_momentum.momentum_1m_min}")
    print(f"   3月动量 > {criteria_momentum.momentum_3m_min}")
    print(f"   量比 > {criteria_momentum.volume_ratio_min}")
    print(f"   波动率 < {criteria_momentum.volatility_max}")
    
    try:
        momentum_results = selector.select_stocks_with_progress(criteria_momentum, "强势动量选股")
        print(f"✅ 动量选股完成，共选中 {len(momentum_results)} 只股票")
        
        if momentum_results:
            print(f"\n📊 前5只强势股票:")
            for i, stock in enumerate(momentum_results[:5]):
                indicators = stock.indicators
                mom_1m = indicators.get('momentum_1m', 0) * 100
                mom_3m = indicators.get('momentum_3m', 0) * 100
                vol_ratio = indicators.get('volume_ratio', 0)
                print(f"   {i+1}. {stock.stock_code} - {stock.stock_name}")
                print(f"      评分: {stock.score:.1f}, 1月涨幅: {mom_1m:.1f}%, 3月涨幅: {mom_3m:.1f}%, 量比: {vol_ratio:.1f}")
        
    except Exception as e:
        print(f"❌ 动量选股失败: {e}")

def test_risk_control_factors():
    """测试风险控制因子选股"""
    print(f"\n🛡️ 测试风险控制因子选股")
    print("=" * 60)
    
    selector = StockSelector()
    
    # 测试低风险选股
    print("\n1️⃣ 测试低风险稳健股票...")
    criteria_low_risk = SelectionCriteria(
        exclude_etf=True,
        volatility_max=0.25,      # 低波动率
        beta_min=0.7,             # Beta适中
        beta_max=1.3,
        max_drawdown_max=0.20,    # 低回撤
        avg_amount_min=1000,      # 流动性好（1000万以上）
        volume_ratio_min=0.8,     # 成交相对活跃
        condition_logic="strict", # 严格模式，所有条件都要满足
        lookback_days=120,        # 更长历史数据
        min_trading_days=90
    )
    
    print(f"📋 低风险选股条件:")
    print(f"   波动率 < {criteria_low_risk.volatility_max}")
    print(f"   Beta: {criteria_low_risk.beta_min} - {criteria_low_risk.beta_max}")
    print(f"   最大回撤 < {criteria_low_risk.max_drawdown_max}")
    print(f"   日均成交额 > {criteria_low_risk.avg_amount_min}万元")
    print(f"   条件逻辑: {criteria_low_risk.condition_logic}")
    
    try:
        low_risk_results = selector.select_stocks_with_progress(criteria_low_risk, "低风险稳健选股")
        print(f"✅ 低风险选股完成，共选中 {len(low_risk_results)} 只股票")
        
        if low_risk_results:
            print(f"\n📊 前5只低风险股票:")
            for i, stock in enumerate(low_risk_results[:5]):
                indicators = stock.indicators
                volatility = indicators.get('volatility', 0)
                beta = indicators.get('beta', 1)
                max_dd = indicators.get('max_drawdown', 0)
                avg_amount = indicators.get('avg_amount', 0)
                print(f"   {i+1}. {stock.stock_code} - {stock.stock_name}")
                print(f"      评分: {stock.score:.1f}, 波动率: {volatility:.3f}, Beta: {beta:.2f}")
                print(f"      最大回撤: {max_dd:.3f}, 日均成交额: {avg_amount:.0f}万元")
        
    except Exception as e:
        print(f"❌ 低风险选股失败: {e}")

def test_comprehensive_selection():
    """测试综合选股策略"""
    print(f"\n🎯 测试综合选股策略")
    print("=" * 60)
    
    selector = StockSelector()
    
    # 综合选股：技术面 + 基本面 + 风险控制
    print("\n1️⃣ 测试综合优质股票选股...")
    criteria_comprehensive = SelectionCriteria(
        exclude_etf=True,
        
        # 技术面条件
        rsi_min=30,
        rsi_max=70,               # RSI适中
        ma_arrangement='bullish', # 均线多头排列
        volume_ratio_min=1.0,     # 成交活跃
        
        # 动量条件
        momentum_1m_min=0.02,     # 1月涨幅>2%
        momentum_3m_max=0.50,     # 3月涨幅<50%（避免过热）
        
        # 风险控制
        volatility_max=0.35,      # 控制波动率
        beta_max=1.5,             # 控制Beta
        
        # 流动性
        avg_amount_min=500,       # 日均成交额>500万
        turnover_rate_min=1.0,    # 换手率>1%
        turnover_rate_max=15.0,   # 换手率<15%
        
        condition_logic="flexible",
        lookback_days=60,
        min_trading_days=40
    )
    
    print(f"📋 综合选股条件:")
    print(f"   RSI: {criteria_comprehensive.rsi_min} - {criteria_comprehensive.rsi_max}")
    print(f"   均线排列: {criteria_comprehensive.ma_arrangement}")
    print(f"   1月动量 > {criteria_comprehensive.momentum_1m_min}")
    print(f"   波动率 < {criteria_comprehensive.volatility_max}")
    print(f"   换手率: {criteria_comprehensive.turnover_rate_min}% - {criteria_comprehensive.turnover_rate_max}%")
    
    try:
        comprehensive_results = selector.select_stocks_with_progress(criteria_comprehensive, "综合优质选股")
        print(f"✅ 综合选股完成，共选中 {len(comprehensive_results)} 只股票")
        
        if comprehensive_results:
            print(f"\n📊 前10只综合优质股票:")
            for i, stock in enumerate(comprehensive_results[:10]):
                indicators = stock.indicators
                rsi = indicators.get('rsi', 50)
                ma_arr = indicators.get('ma_arrangement', 'neutral')
                mom_1m = indicators.get('momentum_1m', 0) * 100
                volatility = indicators.get('volatility', 0)
                turnover = indicators.get('turnover_rate', 0)
                
                print(f"   {i+1}. {stock.stock_code} - {stock.stock_name}")
                print(f"      评分: {stock.score:.1f}, RSI: {rsi:.1f}, 均线: {ma_arr}")
                print(f"      1月涨幅: {mom_1m:.1f}%, 波动率: {volatility:.3f}, 换手率: {turnover:.1f}%")
        
    except Exception as e:
        print(f"❌ 综合选股失败: {e}")

def show_selection_presets():
    """显示选股预设"""
    print(f"\n📚 选股预设示例")
    print("=" * 60)
    
    presets = [
        {
            "name": "all_etfs",
            "display_name": "所有ETF",
            "description": "选择所有可交易的ETF基金",
            "criteria": SelectionCriteria(
                only_etf=True,
                volume_ratio_min=0.3,
                condition_logic="flexible"
            )
        },
        {
            "name": "active_stocks",
            "display_name": "活跃股票",
            "description": "选择成交活跃的股票",
            "criteria": SelectionCriteria(
                exclude_etf=True,
                volume_ratio_min=2.0,
                turnover_rate_min=3.0,
                avg_amount_min=1000,
                condition_logic="flexible"
            )
        },
        {
            "name": "momentum_stocks",
            "display_name": "动量股票",
            "description": "选择具有良好动量的股票",
            "criteria": SelectionCriteria(
                exclude_etf=True,
                momentum_1m_min=0.05,
                momentum_3m_min=0.15,
                volume_ratio_min=1.5,
                volatility_max=0.4,
                condition_logic="flexible"
            )
        },
        {
            "name": "stable_stocks",
            "display_name": "稳健股票",
            "description": "选择低风险稳健的股票",
            "criteria": SelectionCriteria(
                exclude_etf=True,
                volatility_max=0.25,
                beta_max=1.2,
                max_drawdown_max=0.15,
                avg_amount_min=2000,
                condition_logic="strict"
            )
        }
    ]
    
    for preset in presets:
        print(f"\n🎯 {preset['display_name']} ({preset['name']})")
        print(f"   描述: {preset['description']}")
        criteria = preset['criteria']
        
        # 显示主要条件
        conditions = []
        if criteria.only_etf:
            conditions.append("仅ETF")
        if criteria.exclude_etf:
            conditions.append("排除ETF")
        if criteria.volume_ratio_min:
            conditions.append(f"量比>{criteria.volume_ratio_min}")
        if criteria.momentum_1m_min:
            conditions.append(f"1月动量>{criteria.momentum_1m_min}")
        if criteria.volatility_max:
            conditions.append(f"波动率<{criteria.volatility_max}")
        if criteria.avg_amount_min:
            conditions.append(f"成交额>{criteria.avg_amount_min}万")
        
        print(f"   主要条件: {', '.join(conditions)}")

def main():
    """主函数"""
    print("🎯 增强选股功能测试")
    print("=" * 80)
    
    try:
        # 测试ETF选股
        test_etf_selection()
        
        # 测试动量因子
        test_momentum_factors()
        
        # 测试风险控制因子
        test_risk_control_factors()
        
        # 测试综合选股
        test_comprehensive_selection()
        
        # 显示预设
        show_selection_presets()
        
        print(f"\n" + "=" * 80)
        print(f"✅ 增强选股功能测试完成！")
        
        print(f"\n💡 新增功能总结:")
        print(f"   ✅ ETF筛选支持 - 可选择、排除或仅选ETF")
        print(f"   ✅ 动量因子 - 1月、3月、12月动量指标")
        print(f"   ✅ 风险控制因子 - 波动率、Beta、最大回撤")
        print(f"   ✅ 流动性因子 - 成交额、换手率、量比")
        print(f"   ✅ 技术面增强 - 均线排列、成交量动量等")
        print(f"   ✅ 综合评分系统 - 多维度智能评分")
        
        print(f"\n🎯 使用建议:")
        print(f"   • ETF投资：使用ETF筛选功能选择合适的基金")
        print(f"   • 趋势投资：使用动量因子捕捉强势股票")
        print(f"   • 稳健投资：使用风险控制因子选择低风险股票")
        print(f"   • 综合策略：结合多种因子进行全面筛选")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
