#!/usr/bin/env node
/**
 * 测试前端错误修复
 */

const fs = require('fs');
const path = require('path');

function checkReactImports() {
  console.log('🔍 检查React Hook导入...');
  
  const filesToCheck = [
    'frontend/src/components/LiveTradingMonitor.jsx',
    'frontend/src/hooks/useLiveTradingWebSocket.js',
    'frontend/src/pages/MultiStrategyLiveTradingPage.js'
  ];
  
  let allPassed = true;
  
  filesToCheck.forEach(filePath => {
    const fullPath = path.join(__dirname, filePath);
    
    if (!fs.existsSync(fullPath)) {
      console.log(`❌ 文件不存在: ${filePath}`);
      allPassed = false;
      return;
    }
    
    const content = fs.readFileSync(fullPath, 'utf8');
    
    // 检查是否使用了useRef但没有导入
    const usesUseRef = content.includes('useRef(');
    const importsUseRef = content.includes('useRef') && content.includes('from \'react\'');
    
    if (usesUseRef && !importsUseRef) {
      console.log(`❌ ${filePath}: 使用了useRef但没有导入`);
      allPassed = false;
    } else if (usesUseRef && importsUseRef) {
      console.log(`✅ ${filePath}: useRef导入正确`);
    } else {
      console.log(`ℹ️ ${filePath}: 未使用useRef`);
    }
    
    // 检查其他常用Hook的导入
    const hooks = ['useState', 'useEffect', 'useCallback', 'useMemo'];
    hooks.forEach(hook => {
      const usesHook = content.includes(`${hook}(`);
      const importsHook = content.includes(hook) && content.includes('from \'react\'');
      
      if (usesHook && !importsHook) {
        console.log(`❌ ${filePath}: 使用了${hook}但没有导入`);
        allPassed = false;
      }
    });
  });
  
  return allPassed;
}

function checkLiveTradingMonitorSpecifically() {
  console.log('\n🎯 专门检查LiveTradingMonitor.jsx...');
  
  const filePath = path.join(__dirname, 'frontend/src/components/LiveTradingMonitor.jsx');
  
  if (!fs.existsSync(filePath)) {
    console.log('❌ LiveTradingMonitor.jsx 文件不存在');
    return false;
  }
  
  const content = fs.readFileSync(filePath, 'utf8');
  
  // 检查导入行
  const importLine = content.split('\n')[0];
  console.log(`📋 导入行: ${importLine}`);
  
  // 检查是否包含所有必要的Hook
  const requiredHooks = ['useState', 'useEffect', 'useRef'];
  const missingHooks = requiredHooks.filter(hook => !importLine.includes(hook));
  
  if (missingHooks.length === 0) {
    console.log('✅ 所有必要的Hook都已导入');
    return true;
  } else {
    console.log(`❌ 缺少Hook导入: ${missingHooks.join(', ')}`);
    return false;
  }
}

function provideSolution() {
  console.log('\n💡 解决方案:');
  console.log('如果仍有错误，请确保以下文件的导入正确:');
  
  console.log('\n1. LiveTradingMonitor.jsx 应该有:');
  console.log('   import React, { useState, useEffect, useRef } from \'react\';');
  
  console.log('\n2. useLiveTradingWebSocket.js 应该有:');
  console.log('   import { useState, useEffect, useRef, useCallback } from \'react\';');
  
  console.log('\n3. 如果错误仍然存在:');
  console.log('   - 清除浏览器缓存 (Ctrl+F5)');
  console.log('   - 重启前端开发服务器 (npm start)');
  console.log('   - 检查浏览器控制台的完整错误信息');
}

function main() {
  console.log('🧪 前端错误修复验证');
  console.log('=' * 50);
  
  const importsOk = checkReactImports();
  const monitorOk = checkLiveTradingMonitorSpecifically();
  
  if (importsOk && monitorOk) {
    console.log('\n✅ 所有检查通过！');
    console.log('🎉 useRef错误应该已经修复');
    console.log('\n📋 下一步:');
    console.log('1. 刷新浏览器页面 (Ctrl+F5)');
    console.log('2. 访问: http://localhost:3000/multi-strategy');
    console.log('3. 检查是否还有错误');
  } else {
    console.log('\n❌ 发现问题，需要修复');
    provideSolution();
  }
  
  console.log('\n' + '=' * 50);
}

if (require.main === module) {
  main();
}
