import * as React from 'react'
import { twMerge } from 'tailwind-merge'

const variantStyles = {
  default: 'border-gray-200 bg-white text-gray-700',
  error: 'border-red-200 bg-red-50 text-red-700',
  warning: 'border-amber-200 bg-amber-50 text-amber-800',
  success: 'border-emerald-200 bg-emerald-50 text-emerald-700',
}

export function Alert({ title, description, variant = 'default', closable = false, onClose, className = '' }) {
  return (
    <div className={twMerge('rounded-md border p-3', variantStyles[variant] || variantStyles.default, className)}>
      <div className="flex items-start gap-2">
        <div className="flex-1">
          {title && <div className="font-semibold mb-1">{title}</div>}
          {description && <div className="text-sm leading-6">{description}</div>}
        </div>
        {closable && (
          <button onClick={onClose} className="text-xs px-2 py-1 rounded bg-black/5 hover:bg-black/10">关闭</button>
        )}
      </div>
    </div>
  )
}

