#!/usr/bin/env python3
"""
启动前端服务器脚本
"""

import subprocess
import os
import sys

def main():
    print("🚀 启动QMT-TRADER前端服务器...")
    print("📍 访问地址: http://localhost:3000")
    print("🔗 后端代理: http://localhost:8001")
    print("=" * 50)
    
    try:
        # 切换到前端目录
        frontend_dir = os.path.join(os.path.dirname(__file__), 'frontend')
        
        if not os.path.exists(frontend_dir):
            print("❌ 前端目录不存在")
            return
        
        # 启动前端服务器
        print("正在启动前端服务器...")
        
        # 设置环境变量
        env = os.environ.copy()
        env['BROWSER'] = 'none'  # 不自动打开浏览器
        
        # 启动npm start
        process = subprocess.Popen(
            ['npm', 'start'],
            cwd=frontend_dir,
            env=env,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )
        
        print("✅ 前端服务器启动中...")
        print("请等待编译完成后访问 http://localhost:3000")
        print("按 Ctrl+C 停止服务器")
        
        # 实时输出日志
        try:
            for line in process.stdout:
                print(line.rstrip())
        except KeyboardInterrupt:
            print("\n🛑 正在停止前端服务器...")
            process.terminate()
            process.wait()
            print("✅ 前端服务器已停止")
            
    except FileNotFoundError:
        print("❌ npm 命令未找到，请确保已安装 Node.js")
    except Exception as e:
        print(f"❌ 启动失败: {e}")

if __name__ == "__main__":
    main()
