import React, { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import { DataTable } from '../components/UI/table';
import { Card as Shad<PERSON>ard, <PERSON><PERSON>ead<PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>le as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Card<PERSON>ontent as ShadCardContent } from '../components/UI/card.jsx';
import { Stat } from '../components/UI/stat.jsx';
import { Spinner } from '../components/UI/spinner.jsx';
import { toast as imported_toast } from 'sonner';
import ReactECharts from 'echarts-for-react';
import { backtestAPI, apiUtils } from '../services/api';

const BacktestResultsPage = () => {
  const { taskId } = useParams();
  const [loading, setLoading] = useState(false);
  const [results, setResults] = useState(null);

  const loadResults = async () => {
    setLoading(true);
    try {
      const response = await backtestAPI.getBacktestResults(taskId);
      if (apiUtils.isSuccess(response)) {
        setResults(apiUtils.getData(response));
      }
    } catch (err) {
      imported_toast.error('获取回测结果失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (taskId) loadResults();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [taskId]);

  if (loading) {
    return (
      <div style={{ padding: 24, textAlign: 'center' }}>
        <Spinner size="lg" />
      </div>
    );
  }

  if (!results) {
    return <div style={{ padding: 24 }}>暂无回测结果</div>;
  }

  // 尝试从后端结果中获取 equity 或 nav 序列；如果没有，就不渲染对应图表
  const equity = results.equity || results.nav || [];

  // 计算基于 equity 的回撤序列与最大回撤
  let drawdownSeries = [];
  if (Array.isArray(equity) && equity.length > 0) {
    let peak = equity[0].value ?? equity[0][1] ?? equity[0];
    drawdownSeries = equity.map(point => {
      const val = point.value ?? point[1] ?? point;
      if (val > peak) peak = val;
      const dd = (val - peak) / peak;
      return { time: point.time ?? point[0] ?? '', value: +(dd * 100).toFixed(2) };
    });
  }

  // 计算按月收益（假设 equity 的 time 为可解析日期字符串）
  let monthlyReturn = [];
  if (Array.isArray(equity) && equity.length > 1) {
    const byMonth = {};
    equity.forEach(p => {
      const t = p.time ?? p[0] ?? '';
      const v = p.value ?? p[1] ?? p;
      const d = new Date(t);
      if (!isNaN(d)) {
        const key = `${d.getFullYear()}-${(d.getMonth() + 1).toString().padStart(2, '0')}`;
        if (!byMonth[key]) byMonth[key] = { first: v, last: v };
        byMonth[key].last = v;

      }
    });
    monthlyReturn = Object.entries(byMonth)
      .sort((a, b) => a[0].localeCompare(b[0]))
      .map(([k, v]) => ({ month: k, ret: +(((v.last - v.first) / v.first) * 100).toFixed(2) }));
  }

  // ECharts 配置
  const equityOption = Array.isArray(equity) && equity.length > 0 ? {
    tooltip: { trigger: 'axis' },
    grid: { left: 48, right: 16, top: 24, bottom: 40 },
    xAxis: { type: 'category', data: equity.map(p => p.time ?? p[0] ?? '') },
    yAxis: { type: 'value', axisLabel: { formatter: val => `¥${Number(val).toLocaleString()}` } },
    series: [{ type: 'line', name: '权益曲线', data: equity.map(p => p.value ?? p[1] ?? p), smooth: true }],
  } : null;

  const drawdownOption = Array.isArray(drawdownSeries) && drawdownSeries.length > 0 ? {
    tooltip: { trigger: 'axis' },
    grid: { left: 48, right: 16, top: 24, bottom: 40 },
    xAxis: { type: 'category', data: drawdownSeries.map(p => p.time) },
    yAxis: { type: 'value', axisLabel: { formatter: val => `${val}%` } },
    series: [{ type: 'line', name: '回撤(%)', data: drawdownSeries.map(p => p.value), smooth: true, areaStyle: {} }],
  } : null;

  const monthlyOption = Array.isArray(monthlyReturn) && monthlyReturn.length > 0 ? {
    tooltip: { trigger: 'axis' },
    grid: { left: 48, right: 16, top: 24, bottom: 40 },
    xAxis: { type: 'category', data: monthlyReturn.map(p => p.month) },
    yAxis: { type: 'value', axisLabel: { formatter: val => `${val}%` } },
    series: [{ type: 'bar', name: '月度收益(%)', data: monthlyReturn.map(p => p.ret), itemStyle: { color: params => params.value >= 0 ? '#52c41a' : '#ff4d4f' } }],
  } : null;

  return (
    <div style={{ padding: 24 }}>
      <ShadCard>
        <ShadCardHeader>
          <ShadCardTitle>回测概览</ShadCardTitle>
        </ShadCardHeader>
        <ShadCardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Stat title="总收益率" value={results.total_return * 100} precision={2} suffix="%" />
            <Stat title="年化收益" value={results.annual_return * 100} precision={2} suffix="%" />
            <Stat title="夏普比率" value={results.sharpe_ratio} precision={3} />
            <Stat title="最大回撤" value={results.max_drawdown * 100} precision={2} suffix="%" />
          </div>
        </ShadCardContent>
      </ShadCard>

      {/* 图表区 */}
      {equityOption && (
        <ShadCard className="mt-4">
          <ShadCardHeader><ShadCardTitle>权益曲线</ShadCardTitle></ShadCardHeader>
          <ShadCardContent>
            <ReactECharts option={equityOption} style={{ height: 320 }} />
          </ShadCardContent>
        </ShadCard>
      )}

      {drawdownOption && (
        <ShadCard className="mt-4">
          <ShadCardHeader><ShadCardTitle>回撤曲线</ShadCardTitle></ShadCardHeader>
          <ShadCardContent>
            <ReactECharts option={drawdownOption} style={{ height: 240 }} />
          </ShadCardContent>
        </ShadCard>
      )}

      {monthlyOption && (
        <ShadCard className="mt-4">
          <ShadCardHeader><ShadCardTitle>月度收益(%)</ShadCardTitle></ShadCardHeader>
          <ShadCardContent>
            <ReactECharts option={monthlyOption} style={{ height: 240 }} />
          </ShadCardContent>
        </ShadCard>
      )}

      {Array.isArray(results.trades) && results.trades.length > 0 && (
        <ShadCard className="mt-4">
          <ShadCardHeader><ShadCardTitle>交易明细</ShadCardTitle></ShadCardHeader>
          <ShadCardContent>
            <DataTable
              columns={[
                { accessorKey: 'datetime', header: '时间', cell: info => info.getValue() },
                { accessorKey: 'stock', header: '代码', cell: info => info.getValue() },
                { accessorKey: 'side', header: '方向', cell: info => info.getValue() },
                { accessorKey: 'price', header: '价格', cell: info => info.getValue() },
                { accessorKey: 'size', header: '数量', cell: info => info.getValue() },
                { accessorKey: 'commission', header: '手续费', cell: info => info.getValue() },
                { accessorKey: 'slippage', header: '滑点', cell: info => info.getValue() },
              ]}
              data={results.trades}
            />
          </ShadCardContent>
        </ShadCard>
      )}
    </div>
  );
};

export default BacktestResultsPage;

