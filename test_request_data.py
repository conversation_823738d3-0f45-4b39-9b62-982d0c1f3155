#!/usr/bin/env python3
"""
测试请求数据传递
"""

import sys
import os
import requests
import json

# 添加项目路径
sys.path.append(os.path.dirname(__file__))

def test_api_request_data():
    """测试API请求数据"""
    try:
        print("🔍 测试API请求数据...")
        
        # 模拟前端发送的数据
        test_data = {
            "strategy_name": "bollinger_bands",
            "initial_capital": 100000,
            "commission": 0.001,
            "stock_codes": ["000001.SZ"],  # 确保这里有股票代码
            "paper_trading": True,
            "strategy_params": {
                "period": 20,
                "std_dev": 2.0
            }
        }
        
        print("📤 发送的数据:")
        print(json.dumps(test_data, indent=2, ensure_ascii=False))
        
        print("\n📤 发送启动请求...")
        response = requests.post(
            'http://localhost:8000/api/live/start',
            json=test_data,
            headers={'Content-Type': 'application/json'}
        )
        
        print(f"📋 响应状态: {response.status_code}")
        print(f"📋 响应内容: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("✅ API调用成功")
                return True, data.get('task_id')
            else:
                print(f"❌ API调用失败: {data.get('message', 'Unknown error')}")
                return False, None
        else:
            print(f"❌ HTTP错误: {response.text}")
            return False, None
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False, None

def test_empty_stock_codes():
    """测试空股票代码"""
    try:
        print("\n🔍 测试空股票代码...")
        
        # 模拟空股票代码的情况
        test_data = {
            "strategy_name": "bollinger_bands",
            "initial_capital": 100000,
            "commission": 0.001,
            "stock_codes": [],  # 空数组
            "paper_trading": True,
            "strategy_params": {
                "period": 20,
                "std_dev": 2.0
            }
        }
        
        print("📤 发送空股票代码数据:")
        print(json.dumps(test_data, indent=2, ensure_ascii=False))
        
        response = requests.post(
            'http://localhost:8000/api/live/start',
            json=test_data,
            headers={'Content-Type': 'application/json'}
        )
        
        print(f"📋 响应状态: {response.status_code}")
        print(f"📋 响应内容: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("✅ 空股票代码时使用默认值成功")
                return True, data.get('task_id')
            else:
                print(f"❌ 空股票代码处理失败: {data.get('message', 'Unknown error')}")
                return False, None
        else:
            print(f"❌ HTTP错误: {response.text}")
            return False, None
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False, None

def test_missing_stock_codes():
    """测试缺少股票代码字段"""
    try:
        print("\n🔍 测试缺少股票代码字段...")
        
        # 模拟缺少stock_codes字段的情况
        test_data = {
            "strategy_name": "bollinger_bands",
            "initial_capital": 100000,
            "commission": 0.001,
            # "stock_codes": [],  # 完全缺少这个字段
            "paper_trading": True,
            "strategy_params": {
                "period": 20,
                "std_dev": 2.0
            }
        }
        
        print("📤 发送缺少股票代码字段的数据:")
        print(json.dumps(test_data, indent=2, ensure_ascii=False))
        
        response = requests.post(
            'http://localhost:8000/api/live/start',
            json=test_data,
            headers={'Content-Type': 'application/json'}
        )
        
        print(f"📋 响应状态: {response.status_code}")
        print(f"📋 响应内容: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("✅ 缺少股票代码字段时使用默认值成功")
                return True, data.get('task_id')
            else:
                print(f"❌ 缺少股票代码字段处理失败: {data.get('message', 'Unknown error')}")
                return False, None
        else:
            print(f"❌ HTTP错误: {response.text}")
            return False, None
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False, None

def cleanup_tasks(task_ids):
    """清理测试任务"""
    print("\n🧹 清理测试任务...")
    for task_id in task_ids:
        if task_id:
            try:
                requests.post(f'http://localhost:8000/api/live/stop/{task_id}')
                requests.delete(f'http://localhost:8000/api/live/delete/{task_id}')
                print(f"✅ 清理任务: {task_id[:8]}...")
            except:
                print(f"⚠️ 清理失败: {task_id[:8]}...")

if __name__ == "__main__":
    print("🧪 请求数据传递测试")
    print("=" * 60)
    
    task_ids = []
    
    # 测试正常股票代码
    success1, task_id1 = test_api_request_data()
    if task_id1:
        task_ids.append(task_id1)
    
    # 测试空股票代码
    success2, task_id2 = test_empty_stock_codes()
    if task_id2:
        task_ids.append(task_id2)
    
    # 测试缺少股票代码字段
    success3, task_id3 = test_missing_stock_codes()
    if task_id3:
        task_ids.append(task_id3)
    
    # 清理测试任务
    if task_ids:
        import time
        time.sleep(2)
        cleanup_tasks(task_ids)
    
    print("\n" + "=" * 60)
    print("🎯 测试结果:")
    print(f"  正常股票代码: {'✅' if success1 else '❌'}")
    print(f"  空股票代码: {'✅' if success2 else '❌'}")
    print(f"  缺少股票代码字段: {'✅' if success3 else '❌'}")
    
    if success1:
        print("\n✅ 正常情况下API工作正常")
        print("   问题可能在前端发送的数据格式")
    else:
        print("\n❌ API本身有问题")
        print("   请检查后端日志获取详细错误信息")
    
    print("\n💡 建议:")
    print("1. 检查前端发送的实际数据格式")
    print("2. 查看后端日志中的调试信息")
    print("3. 确认stock_codes字段是否正确传递")
