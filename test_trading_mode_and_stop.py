#!/usr/bin/env python3
"""
测试交易模式显示和停止功能
"""

import sys
import os
import requests
import json
import time

# 添加项目路径
sys.path.append(os.path.dirname(__file__))

def test_trading_mode_display():
    """测试交易模式显示"""
    try:
        print("🔍 测试交易模式显示...")
        
        # 启动纸上交易
        paper_data = {
            "strategy_name": "bollinger_bands",
            "initial_capital": 100000,
            "commission": 0.001,
            "stock_codes": ["000001.SZ"],
            "paper_trading": True,  # 纸上交易
            "strategy_params": {
                "period": 20,
                "std_dev": 2.0
            }
        }
        
        print("📤 启动纸上交易...")
        response = requests.post(
            'http://localhost:8000/api/live/start',
            json=paper_data,
            headers={'Content-Type': 'application/json'}
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                paper_task_id = data.get('task_id')
                print(f"✅ 纸上交易启动成功: {paper_task_id[:8]}...")
            else:
                print(f"❌ 纸上交易启动失败: {data}")
                return None
        else:
            print(f"❌ 纸上交易API错误: {response.status_code}")
            return None
        
        # 启动实盘交易
        real_data = {
            "strategy_name": "bollinger_bands",
            "initial_capital": 50000,
            "commission": 0.001,
            "stock_codes": ["000002.SZ"],
            "paper_trading": False,  # 实盘交易
            "strategy_params": {
                "period": 15,
                "std_dev": 1.5
            }
        }
        
        print("📤 启动实盘交易...")
        response = requests.post(
            'http://localhost:8000/api/live/start',
            json=real_data,
            headers={'Content-Type': 'application/json'}
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                real_task_id = data.get('task_id')
                print(f"✅ 实盘交易启动成功: {real_task_id[:8]}...")
            else:
                print(f"❌ 实盘交易启动失败: {data}")
                return paper_task_id
        else:
            print(f"❌ 实盘交易API错误: {response.status_code}")
            return paper_task_id
        
        # 等待一下让任务启动
        time.sleep(2)
        
        # 获取所有运行中的策略
        print("📋 获取运行中的策略...")
        response = requests.get('http://localhost:8000/api/live/results')
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                results = data.get('data', [])
                print(f"✅ 获取到 {len(results)} 个策略")
                
                for result in results:
                    task_id = result.get('task_id', '')[:8]
                    strategy_name = result.get('strategy_name', '')
                    paper_trading = result.get('paper_trading', True)
                    status = result.get('status', '')
                    
                    mode = "纸上交易" if paper_trading else "实盘交易"
                    print(f"  - {task_id}... | {strategy_name} | {mode} | {status}")
                
                return [paper_task_id, real_task_id]
            else:
                print(f"❌ 获取策略失败: {data}")
        else:
            print(f"❌ 获取策略API错误: {response.status_code}")
        
        return [paper_task_id, real_task_id]
        
    except Exception as e:
        print(f"❌ 测试交易模式显示失败: {e}")
        return None

def test_stop_strategy(task_ids):
    """测试停止策略功能"""
    try:
        print("\n🛑 测试停止策略功能...")
        
        if not task_ids:
            print("❌ 没有可停止的策略")
            return
        
        for task_id in task_ids:
            if not task_id:
                continue
                
            print(f"🛑 停止策略: {task_id[:8]}...")
            
            response = requests.post(f'http://localhost:8000/api/live/stop/{task_id}')
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    print(f"✅ 策略 {task_id[:8]}... 停止成功")
                else:
                    print(f"❌ 策略 {task_id[:8]}... 停止失败: {data}")
            else:
                print(f"❌ 停止策略API错误: {response.status_code}")
                try:
                    error_data = response.json()
                    print(f"   错误详情: {error_data}")
                except:
                    print(f"   错误文本: {response.text}")
        
        # 等待一下让状态更新
        time.sleep(1)
        
        # 验证策略状态
        print("📋 验证策略状态...")
        response = requests.get('http://localhost:8000/api/live/results')
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                results = data.get('data', [])
                print(f"✅ 当前策略状态:")
                
                for result in results:
                    task_id = result.get('task_id', '')[:8]
                    strategy_name = result.get('strategy_name', '')
                    paper_trading = result.get('paper_trading', True)
                    status = result.get('status', '')
                    
                    mode = "纸上交易" if paper_trading else "实盘交易"
                    status_icon = "🟢" if status == "running" else "🔴" if status == "stopped" else "🟡"
                    print(f"  {status_icon} {task_id}... | {strategy_name} | {mode} | {status}")
            else:
                print(f"❌ 获取策略状态失败: {data}")
        else:
            print(f"❌ 获取策略状态API错误: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 测试停止策略失败: {e}")

def test_frontend_integration():
    """测试前端集成"""
    print("\n🌐 前端集成测试...")
    
    print("✅ 前端修改内容:")
    print("  1. 添加了 paper_trading 字段到 LiveTradingResult")
    print("  2. 在策略表格中添加了'交易模式'列")
    print("  3. 纸上交易显示绿色标签，实盘交易显示红色标签")
    print("  4. 停止按钮在策略运行时启用，停止后禁用")
    
    print("\n🔗 测试步骤:")
    print("  1. 访问: http://localhost:3000/multi-strategy")
    print("  2. 点击'添加策略'")
    print("  3. 配置策略参数")
    print("  4. 选择'纸上交易'或'实盘交易'模式")
    print("  5. 启动策略，观察表格中的'交易模式'列")
    print("  6. 点击停止按钮测试停止功能")

if __name__ == "__main__":
    print("🧪 交易模式和停止功能测试")
    print("=" * 50)
    
    # 测试交易模式显示
    task_ids = test_trading_mode_display()
    
    # 测试停止功能
    if task_ids:
        test_stop_strategy(task_ids)
    
    # 前端集成说明
    test_frontend_integration()
    
    print("\n" + "=" * 50)
    print("🎯 测试总结:")
    print("✅ 后端支持 paper_trading 字段")
    print("✅ API 返回交易模式信息")
    print("✅ 停止策略功能正常")
    print("✅ 前端表格显示交易模式")
    print("✅ 停止按钮功能完整")
