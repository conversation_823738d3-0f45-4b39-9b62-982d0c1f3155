#!/usr/bin/env python3
"""
股票池管理API
"""

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import List, Dict, Any, Optional

from backend.stock_selection.stock_pool_manager import stock_pool_manager
from backend.core.logger import get_logger

logger = get_logger(__name__)

router = APIRouter(prefix="/api/stock-pools", tags=["股票池管理"])

class CreatePoolFromSelectionRequest(BaseModel):
    """从选股结果创建股票池请求"""
    selection_file: str
    pool_name: str
    display_name: Optional[str] = None
    description: Optional[str] = None
    max_stocks: Optional[int] = None
    score_threshold: Optional[float] = None

class CreateCustomPoolRequest(BaseModel):
    """创建自定义股票池请求"""
    pool_name: str
    stock_codes: List[str]
    display_name: Optional[str] = None
    description: Optional[str] = None

@router.get("/selection-results")
async def get_selection_results():
    """获取可用的选股结果"""
    try:
        results = stock_pool_manager.get_available_selection_results()
        
        return {
            'success': True,
            'data': results,
            'message': f'获取到 {len(results)} 个选股结果'
        }
        
    except Exception as e:
        logger.error(f"获取选股结果失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取选股结果失败: {str(e)}")

@router.post("/create-from-selection")
async def create_pool_from_selection(request: CreatePoolFromSelectionRequest):
    """从选股结果创建股票池"""
    try:
        success = stock_pool_manager.create_pool_from_selection(
            selection_file=request.selection_file,
            pool_name=request.pool_name,
            display_name=request.display_name,
            description=request.description,
            max_stocks=request.max_stocks,
            score_threshold=request.score_threshold
        )
        
        if success:
            return {
                'success': True,
                'message': f'成功创建股票池: {request.pool_name}'
            }
        else:
            raise HTTPException(status_code=400, detail="创建股票池失败")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建股票池失败: {e}")
        raise HTTPException(status_code=500, detail=f"创建股票池失败: {str(e)}")

@router.post("/create-custom")
async def create_custom_pool(request: CreateCustomPoolRequest):
    """创建自定义股票池"""
    try:
        success = stock_pool_manager.create_custom_pool(
            pool_name=request.pool_name,
            stock_codes=request.stock_codes,
            display_name=request.display_name,
            description=request.description
        )
        
        if success:
            return {
                'success': True,
                'message': f'成功创建自定义股票池: {request.pool_name}'
            }
        else:
            raise HTTPException(status_code=400, detail="创建自定义股票池失败")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建自定义股票池失败: {e}")
        raise HTTPException(status_code=500, detail=f"创建自定义股票池失败: {str(e)}")

@router.get("/")
async def get_available_pools():
    """获取可用的股票池"""
    try:
        pools = stock_pool_manager.get_available_pools()
        
        return {
            'success': True,
            'data': pools,
            'message': f'获取到 {len(pools)} 个股票池'
        }
        
    except Exception as e:
        logger.error(f"获取股票池列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取股票池列表失败: {str(e)}")

@router.get("/{pool_name}")
async def get_pool_info(pool_name: str):
    """获取股票池详细信息"""
    try:
        pool_info = stock_pool_manager.get_pool_info(pool_name)
        
        if pool_info:
            return {
                'success': True,
                'data': pool_info,
                'message': f'获取股票池信息成功: {pool_name}'
            }
        else:
            raise HTTPException(status_code=404, detail=f"股票池不存在: {pool_name}")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取股票池信息失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取股票池信息失败: {str(e)}")

@router.get("/{pool_name}/stocks")
async def get_pool_stocks(pool_name: str):
    """获取股票池的股票代码列表"""
    try:
        stock_codes = stock_pool_manager.get_pool_stocks(pool_name)
        
        if stock_codes:
            return {
                'success': True,
                'data': {
                    'pool_name': pool_name,
                    'stock_codes': stock_codes,
                    'total_stocks': len(stock_codes)
                },
                'message': f'获取股票池股票列表成功: {len(stock_codes)}只股票'
            }
        else:
            raise HTTPException(status_code=404, detail=f"股票池不存在或为空: {pool_name}")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取股票池股票列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取股票池股票列表失败: {str(e)}")

@router.delete("/{pool_name}")
async def delete_pool(pool_name: str):
    """删除股票池"""
    try:
        success = stock_pool_manager.delete_pool(pool_name)
        
        if success:
            return {
                'success': True,
                'message': f'成功删除股票池: {pool_name}'
            }
        else:
            raise HTTPException(status_code=404, detail=f"股票池不存在: {pool_name}")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除股票池失败: {e}")
        raise HTTPException(status_code=500, detail=f"删除股票池失败: {str(e)}")

@router.get("/{pool_name}/validate")
async def validate_pool_stocks(pool_name: str):
    """验证股票池中的股票代码"""
    try:
        pool_info = stock_pool_manager.get_pool_info(pool_name)
        
        if not pool_info:
            raise HTTPException(status_code=404, detail=f"股票池不存在: {pool_name}")
        
        stock_codes = pool_info.get('stock_codes', [])
        
        # 这里可以添加更详细的验证逻辑，比如检查股票是否存在、是否停牌等
        valid_codes = []
        invalid_codes = []
        
        for code in stock_codes:
            if stock_pool_manager._is_valid_stock_code(code):
                valid_codes.append(code)
            else:
                invalid_codes.append(code)
        
        return {
            'success': True,
            'data': {
                'pool_name': pool_name,
                'total_stocks': len(stock_codes),
                'valid_stocks': len(valid_codes),
                'invalid_stocks': len(invalid_codes),
                'valid_codes': valid_codes,
                'invalid_codes': invalid_codes
            },
            'message': f'验证完成: {len(valid_codes)}只有效，{len(invalid_codes)}只无效'
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"验证股票池失败: {e}")
        raise HTTPException(status_code=500, detail=f"验证股票池失败: {str(e)}")

@router.post("/{pool_name}/export")
async def export_pool(pool_name: str, format: str = "json"):
    """导出股票池"""
    try:
        pool_info = stock_pool_manager.get_pool_info(pool_name)
        
        if not pool_info:
            raise HTTPException(status_code=404, detail=f"股票池不存在: {pool_name}")
        
        if format.lower() == "json":
            return {
                'success': True,
                'data': pool_info,
                'message': f'导出股票池成功: {pool_name}'
            }
        elif format.lower() == "csv":
            # 返回CSV格式的股票代码列表
            stock_codes = pool_info.get('stock_codes', [])
            csv_content = "stock_code\n" + "\n".join(stock_codes)
            
            return {
                'success': True,
                'data': {
                    'pool_name': pool_name,
                    'format': 'csv',
                    'content': csv_content,
                    'total_stocks': len(stock_codes)
                },
                'message': f'导出CSV格式成功: {len(stock_codes)}只股票'
            }
        else:
            raise HTTPException(status_code=400, detail=f"不支持的导出格式: {format}")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"导出股票池失败: {e}")
        raise HTTPException(status_code=500, detail=f"导出股票池失败: {str(e)}")
