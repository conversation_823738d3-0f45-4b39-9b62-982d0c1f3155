"""
买入持有策略
简单的买入并持有到期的策略
"""

import asyncio
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, Any, List
import logging

from .base_strategy import (
    AbstractStrategy, TradeRecord, DailyReturn, BacktestContext,
    register_strategy
)

logger = logging.getLogger(__name__)


class BuyHoldStrategy(AbstractStrategy):
    """买入持有策略"""
    
    @property
    def strategy_name(self) -> str:
        return "buy_hold"
    
    @property
    def display_name(self) -> str:
        return "买入持有策略"
    
    @property
    def description(self) -> str:
        return "在回测开始时买入选定股票，持有到回测结束时卖出"
    
    def __init__(self):
        super().__init__()
        self.max_stocks = 3  # 最多买入3只股票
        self.position_size = 0.33  # 每只股票占资金的比例
        
    def load_config(self, config: Dict[str, Any]) -> bool:
        """加载策略配置"""
        try:
            self.max_stocks = config.get('max_stocks', 3)
            self.position_size = config.get('position_size', 0.33)
            
            # 验证配置
            if self.max_stocks <= 0 or self.max_stocks > 10:
                logger.error(f"max_stocks 必须在1-10之间，当前值: {self.max_stocks}")
                return False
                
            if self.position_size <= 0 or self.position_size > 1:
                logger.error(f"position_size 必须在0-1之间，当前值: {self.position_size}")
                return False
            
            logger.info(f"买入持有策略配置: max_stocks={self.max_stocks}, position_size={self.position_size}")
            return True
            
        except Exception as e:
            logger.error(f"加载配置失败: {e}")
            return False
    
    def initialize(self, context: BacktestContext) -> bool:
        """初始化策略"""
        try:
            self.context = context
            self.current_capital = context.initial_capital
            
            logger.info(f"买入持有策略初始化完成")
            logger.info(f"回测时间: {context.start_date} 到 {context.end_date}")
            logger.info(f"初始资金: ¥{context.initial_capital:,.2f}")
            logger.info(f"股票池: {len(context.stock_pool)}只股票")
            
            return True
            
        except Exception as e:
            logger.error(f"初始化失败: {e}")
            return False
    
    async def run(self, stock_data: Dict[str, Any], progress_callback=None) -> bool:
        """执行回测策略"""
        try:
            logger.info("开始执行买入持有策略")
            
            # 更新进度
            if progress_callback:
                await progress_callback(10, "准备股票数据...")
            
            # 选择股票
            selected_stocks = self._select_stocks(stock_data)
            logger.info(f"选择了{len(selected_stocks)}只股票进行投资")
            
            # 更新进度
            if progress_callback:
                await progress_callback(30, "执行买入操作...")
            
            # 获取交易日期
            all_dates = self._get_trading_dates(stock_data)
            if not all_dates:
                logger.error("没有找到交易日期")
                return False
            
            # 在第一个交易日买入
            await self._execute_buy_orders(selected_stocks, stock_data, all_dates[0])
            
            # 更新进度
            if progress_callback:
                await progress_callback(60, "计算每日收益...")
            
            # 计算每日收益
            await self._calculate_daily_returns(stock_data, all_dates, progress_callback)
            
            # 更新进度
            if progress_callback:
                await progress_callback(90, "执行卖出操作...")
            
            # 在最后一个交易日卖出
            await self._execute_sell_orders(stock_data, all_dates[-1])
            
            # 更新进度
            if progress_callback:
                await progress_callback(100, "策略执行完成")
            
            logger.info(f"买入持有策略执行完成，共产生{len(self.trades)}笔交易")
            return True
            
        except Exception as e:
            logger.error(f"策略执行失败: {e}")
            return False
    
    def _select_stocks(self, stock_data: Dict[str, Any]) -> List[str]:
        """选择要投资的股票"""
        # 简单选择前N只有数据的股票
        available_stocks = []
        for stock_code, data in stock_data.items():
            if isinstance(data, pd.DataFrame) and not data.empty:
                available_stocks.append(stock_code)
        
        # 限制数量
        selected = available_stocks[:self.max_stocks]
        logger.info(f"从{len(available_stocks)}只可用股票中选择了{len(selected)}只")
        return selected
    
    def _get_trading_dates(self, stock_data: Dict[str, Any]) -> List[str]:
        """获取交易日期列表"""
        all_dates = set()
        for stock_code, data in stock_data.items():
            if isinstance(data, pd.DataFrame) and not data.empty:
                dates = data['date'].dt.strftime('%Y-%m-%d').tolist()
                all_dates.update(dates)
        
        return sorted(list(all_dates))
    
    async def _execute_buy_orders(self, selected_stocks: List[str], stock_data: Dict[str, Any], buy_date: str):
        """执行买入订单"""
        capital_per_stock = self.current_capital * self.position_size
        
        for stock_code in selected_stocks:
            data = stock_data[stock_code]
            day_data = data[data['date'].dt.strftime('%Y-%m-%d') == buy_date]
            
            if day_data.empty:
                continue
            
            buy_price = day_data.iloc[0]['close']
            quantity = int(capital_per_stock / buy_price / 100) * 100  # 按手买入
            
            if quantity > 0:
                trade_amount = quantity * buy_price
                commission_fee = trade_amount * self.context.commission
                total_cost = trade_amount + commission_fee
                
                # 创建交易记录
                trade = TradeRecord(
                    id=0,  # 将在add_trade中设置
                    date=buy_date,
                    time='09:30:00',
                    stock_code=stock_code,
                    stock_name=self.get_stock_name(stock_code),
                    action='buy',
                    action_name='买入',
                    price=round(buy_price, 2),
                    quantity=quantity,
                    amount=round(trade_amount, 2),
                    commission=round(commission_fee, 2),
                    reason='买入持有策略-建仓',
                    total_cost=round(total_cost, 2),
                    portfolio_value_before=self.current_capital,
                    portfolio_value_after=self.current_capital - total_cost,
                    cash_change=-total_cost,
                    position_change=f"+{quantity}股"
                )
                
                self.add_trade(trade)
                
                # 更新持仓
                self.positions[stock_code] = {
                    'quantity': quantity,
                    'avg_price': buy_price,
                    'total_cost': total_cost
                }
                
                # 更新资金
                self.current_capital -= total_cost
                
                logger.info(f"买入 {stock_code} {quantity}股 @¥{buy_price:.2f}")
    
    async def _execute_sell_orders(self, stock_data: Dict[str, Any], sell_date: str):
        """执行卖出订单"""
        for stock_code, position in self.positions.items():
            data = stock_data[stock_code]
            day_data = data[data['date'].dt.strftime('%Y-%m-%d') == sell_date]
            
            if day_data.empty:
                continue
            
            sell_price = day_data.iloc[0]['close']
            quantity = position['quantity']
            trade_amount = quantity * sell_price
            commission_fee = trade_amount * self.context.commission
            net_amount = trade_amount - commission_fee
            
            profit_loss = net_amount - position['total_cost']
            profit_loss_pct = profit_loss / position['total_cost'] * 100
            
            # 计算持有天数
            first_trade = next((t for t in self.trades if t.stock_code == stock_code and t.action == 'buy'), None)
            hold_days = 0
            if first_trade:
                buy_date_dt = datetime.strptime(first_trade.date, '%Y-%m-%d')
                sell_date_dt = datetime.strptime(sell_date, '%Y-%m-%d')
                hold_days = (sell_date_dt - buy_date_dt).days
            
            # 创建交易记录
            trade = TradeRecord(
                id=0,  # 将在add_trade中设置
                date=sell_date,
                time='15:00:00',
                stock_code=stock_code,
                stock_name=self.get_stock_name(stock_code),
                action='sell',
                action_name='卖出',
                price=round(sell_price, 2),
                quantity=quantity,
                amount=round(trade_amount, 2),
                commission=round(commission_fee, 2),
                reason='买入持有策略-清仓',
                net_amount=round(net_amount, 2),
                profit_loss=round(profit_loss, 2),
                profit_loss_pct=round(profit_loss_pct, 2),
                hold_days=hold_days,
                portfolio_value_before=self.current_capital,
                portfolio_value_after=self.current_capital + net_amount,
                cash_change=net_amount,
                position_change=f"-{quantity}股"
            )
            
            self.add_trade(trade)
            
            # 更新资金
            self.current_capital += net_amount
            
            logger.info(f"卖出 {stock_code} {quantity}股 @¥{sell_price:.2f}, 盈亏: ¥{profit_loss:.2f}")
    
    async def _calculate_daily_returns(self, stock_data: Dict[str, Any], all_dates: List[str], progress_callback=None):
        """计算每日收益"""
        for i, date in enumerate(all_dates):
            portfolio_value = self.current_capital
            
            # 计算持仓市值
            for stock_code, position in self.positions.items():
                if stock_code in stock_data:
                    data = stock_data[stock_code]
                    day_data = data[data['date'].dt.strftime('%Y-%m-%d') == date]
                    
                    if not day_data.empty:
                        current_price = day_data.iloc[0]['close']
                        position_value = position['quantity'] * current_price
                        portfolio_value += position_value
            
            # 计算日收益率
            return_rate = 0.0
            if i > 0 and self.daily_returns:
                prev_value = self.daily_returns[-1].value
                return_rate = (portfolio_value - prev_value) / prev_value if prev_value > 0 else 0.0
            
            # 添加每日收益记录
            daily_return = DailyReturn(
                date=date,
                value=portfolio_value,
                return_rate=return_rate,
                cash=self.current_capital,
                positions_value=portfolio_value - self.current_capital
            )
            
            self.add_daily_return(daily_return)
            
            # 更新进度
            if progress_callback and i % 10 == 0:
                progress = 60 + int((i / len(all_dates)) * 30)
                await progress_callback(progress, f"计算每日收益... {i+1}/{len(all_dates)}")


# 注册策略
register_strategy(BuyHoldStrategy)
