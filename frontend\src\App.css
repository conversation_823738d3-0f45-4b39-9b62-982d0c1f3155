.App {
  text-align: center;
}

.App-logo {
  height: 40vmin;
  pointer-events: none;
}

@media (prefers-reduced-motion: no-preference) {
  .App-logo {
    animation: App-logo-spin infinite 20s linear;
  }
}

.App-header {
  background-color: #282c34;
  padding: 20px;
  color: white;
}

.App-link {
  color: #61dafb;
}

@keyframes App-logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 自定义样式 */
.logo {
  height: 32px;
  margin: 16px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
}

.ant-layout-sider-collapsed .logo {
  margin: 16px 8px;
}

/* 表格样式优化 */
.ant-table-thead > tr > th {
  background-color: #fafafa;
  font-weight: 600;
}

/* 状态标签样式 */
.status-tag {
  font-weight: 500;
}

/* 统计卡片样式 */
.ant-statistic-title {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.65);
}

.ant-statistic-content {
  font-size: 24px;
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ant-col {
    margin-bottom: 16px;
  }
  
  .ant-statistic-content {
    font-size: 20px;
  }
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  flex-direction: column;
}

.loading-container .ant-spin {
  margin-bottom: 16px;
}

/* 错误状态样式 */
.error-container {
  text-align: center;
  padding: 50px;
  color: #ff4d4f;
}

/* 空状态样式 */
.empty-container {
  text-align: center;
  padding: 50px;
  color: #999;
}

/* 按钮组样式 */
.button-group {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.button-group .ant-btn {
  margin-bottom: 8px;
}

/* 卡片标题样式 */
.card-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 16px;
}

/* 进度条样式 */
.progress-container {
  margin: 16px 0;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.65);
}

/* 表单样式优化 */
.ant-form-item-label > label {
  font-weight: 500;
}

/* 模态框样式 */
.ant-modal-header {
  border-bottom: 1px solid #f0f0f0;
}

.ant-modal-footer {
  border-top: 1px solid #f0f0f0;
}

/* 标签样式 */
.custom-tag {
  border-radius: 4px;
  font-weight: 500;
}

/* 数据展示样式 */
.data-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.data-row:last-child {
  border-bottom: none;
}

.data-label {
  font-weight: 500;
  color: rgba(0, 0, 0, 0.85);
}

.data-value {
  color: rgba(0, 0, 0, 0.65);
}

/* 成功/失败颜色 */
.success-text {
  color: #52c41a;
}

.error-text {
  color: #ff4d4f;
}

.warning-text {
  color: #faad14;
}

/* 动画效果 */
.fade-in {
  animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
