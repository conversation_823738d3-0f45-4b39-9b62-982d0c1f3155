import React from 'react';
import { Alert as ShadAlert } from '../components/UI/alert.jsx';
import { Card as ShadCard, CardHeader as Shad<PERSON>ardHeader, CardTitle as Shad<PERSON>ardTitle, CardContent as ShadCardContent } from '../components/UI/card.jsx';

const SystemConfigPage = () => {
  return (
    <div>
      <h2>系统配置</h2>
      
      <ShadAlert
        title="开发中"
        description="系统配置页面正在开发中，敬请期待。"
        variant="default"
        className="mb-4"
      />

      <ShadCard>
        <ShadCardHeader>
          <ShadCardTitle>系统信息</ShadCardTitle>
        </ShadCardHeader>
        <ShadCardContent>
          <p>平台版本: 1.0.0</p>
          <p>后端框架: FastAPI + Backtrader</p>
          <p>前端框架: React + shadcn/ui</p>
          <p>数据源: xtquant</p>
        </ShadCardContent>
      </ShadCard>
    </div>
  );
};

export default SystemConfigPage;
