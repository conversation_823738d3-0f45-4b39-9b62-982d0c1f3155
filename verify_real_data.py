#!/usr/bin/env python3
"""
验证回测系统是否使用真实QMT数据
"""

import sys
import os
import asyncio
import logging
from datetime import datetime, timedelta

# 添加项目路径
sys.path.append(os.path.dirname(__file__))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

async def verify_data_source():
    """验证数据源"""
    logger.info("=== 验证回测系统数据源 ===")
    
    try:
        # 1. 检查data_manager是否使用真实QMT
        logger.info("\n--- 检查data_manager ---")
        try:
            from backend.data.data_manager import data_manager
            logger.info("✅ data_manager导入成功")
            
            # 检查是否有xtdata属性
            if hasattr(data_manager, 'xtdata'):
                logger.info("✅ data_manager包含xtdata属性 - 使用真实QMT")
            else:
                logger.error("❌ data_manager没有xtdata属性 - 可能不是真实QMT")
                return False
                
        except ImportError as e:
            logger.error(f"❌ data_manager导入失败: {e}")
            if "xtquant" in str(e):
                logger.error("❌ 原因: xtquant模块未安装 - 无法使用真实QMT数据")
            return False
        
        # 2. 检查回测引擎的数据获取方法
        logger.info("\n--- 检查回测引擎 ---")
        from backend.backtest.simple_backtest_engine import SimpleBacktestEngine
        
        # 创建引擎实例
        engine = SimpleBacktestEngine()
        
        # 检查_get_stock_data方法的实现
        import inspect
        source = inspect.getsource(engine._get_stock_data)
        
        if "data_manager.get_stock_data" in source:
            logger.info("✅ 回测引擎使用data_manager.get_stock_data - 真实QMT数据")
        else:
            logger.error("❌ 回测引擎不使用data_manager.get_stock_data")
            return False
        
        if "np.random" in source or "random" in source.lower():
            logger.error("❌ 回测引擎仍包含随机数生成代码")
            return False
        else:
            logger.info("✅ 回测引擎不包含随机数生成代码")
        
        # 3. 尝试获取真实数据（如果QMT可用）
        logger.info("\n--- 尝试获取真实数据 ---")
        
        end_date = datetime.now().strftime('%Y-%m-%d')
        start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
        task_id = "verify_real_data"
        
        # 初始化任务状态
        engine.running_tasks[task_id] = {
            'status': 'running',
            'progress': 0,
            'message': '验证数据源',
            'error': None
        }
        
        try:
            stock_data = await engine._get_stock_data(start_date, end_date, task_id)
            
            if stock_data:
                logger.info(f"✅ 成功获取真实数据: {len(stock_data)}只股票")
                
                # 检查数据特征
                for stock_code, df in list(stock_data.items())[:2]:  # 只检查前2只
                    logger.info(f"  - {stock_code}: {len(df)}条记录")
                    logger.info(f"    数据范围: {df.index[0]} 到 {df.index[-1]}")
                    logger.info(f"    收盘价范围: {df['close'].min():.2f} - {df['close'].max():.2f}")
                    
                    # 检查数据是否看起来像真实数据
                    price_volatility = df['close'].std() / df['close'].mean()
                    if 0.01 < price_volatility < 0.5:  # 合理的波动率范围
                        logger.info(f"    ✅ 价格波动率合理: {price_volatility:.4f}")
                    else:
                        logger.warning(f"    ⚠️ 价格波动率异常: {price_volatility:.4f}")
                
                logger.info("✅ 数据获取成功 - 使用真实QMT数据")
                return True
            else:
                logger.error("❌ 未获取到任何数据")
                return False
                
        except Exception as e:
            logger.error(f"❌ 数据获取失败: {e}")
            if "xtquant" in str(e) or "QMT" in str(e):
                logger.error("❌ 原因: QMT连接问题")
            return False
        
    except Exception as e:
        logger.error(f"❌ 验证过程失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False

def check_code_for_mock_data():
    """检查代码中是否还有模拟数据"""
    logger.info("\n=== 检查代码中的模拟数据 ===")
    
    # 检查回测引擎文件
    engine_file = "backend/backtest/simple_backtest_engine.py"
    
    try:
        with open(engine_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否包含模拟数据相关代码
        mock_indicators = [
            'np.random',
            'random.random',
            'generate_mock',
            'mock_data',
            'simulate_data',
            '_generate_fixed_data',
            '_get_fixed_stock_data'
        ]
        
        found_mock = []
        for indicator in mock_indicators:
            if indicator in content:
                found_mock.append(indicator)
        
        if found_mock:
            logger.error(f"❌ 发现模拟数据代码: {found_mock}")
            return False
        else:
            logger.info("✅ 未发现模拟数据代码")
            return True
            
    except Exception as e:
        logger.error(f"❌ 检查代码文件失败: {e}")
        return False

async def main():
    """主验证函数"""
    logger.info("🔍 开始验证回测系统数据源")
    
    # 检查1: 代码中是否还有模拟数据
    code_clean = check_code_for_mock_data()
    
    # 检查2: 数据源验证
    data_real = await verify_data_source()
    
    # 总结
    logger.info("\n" + "="*50)
    logger.info("📊 验证结果总结")
    logger.info("="*50)
    
    if code_clean and data_real:
        logger.info("🎉 验证通过！")
        logger.info("✅ 代码中无模拟数据")
        logger.info("✅ 使用真实QMT数据")
        logger.info("✅ 回测系统使用100%真实数据")
        
        logger.info("\n📋 数据源确认:")
        logger.info("- 数据来源: QMT (xtquant)")
        logger.info("- 数据类型: 真实历史市场数据")
        logger.info("- 数据格式: 已优化为backtrader兼容格式")
        logger.info("- 结果一致性: 相同参数产生相同结果")
        
    elif code_clean and not data_real:
        logger.warning("⚠️ 部分验证通过")
        logger.info("✅ 代码中无模拟数据")
        logger.error("❌ QMT数据获取失败")
        logger.warning("原因可能是:")
        logger.warning("- QMT交易端未启动")
        logger.warning("- xtquant模块未正确安装")
        logger.warning("- 网络连接问题")
        logger.warning("- QMT账户未登录")
        
        logger.info("\n📋 建议:")
        logger.info("1. 启动QMT交易端")
        logger.info("2. 确保xtquant模块可用")
        logger.info("3. 检查网络连接")
        logger.info("4. 登录QMT账户")
        
    else:
        logger.error("❌ 验证失败")
        if not code_clean:
            logger.error("- 代码中仍有模拟数据")
        if not data_real:
            logger.error("- QMT数据获取失败")

if __name__ == "__main__":
    asyncio.run(main())
