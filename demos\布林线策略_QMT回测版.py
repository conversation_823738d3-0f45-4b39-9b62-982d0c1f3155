# coding:gbk

"""
# -*- coding: utf-8 -*-
布林线交易策略回测模型（可直接在QMT平台运行）

策略逻辑：
1. 根据布林线中线判断趋势，只在上涨趋势中买入
2. 当股价从上轨回落到中线附近时买入（距离中线<=1/10上轨到中线的距离）
3. 卖出条件：跌破中线、亏损3%、盈利回撤20%

使用方法：
1. 在QMT策略回测界面导入此文件
2. 选择沪深300指数或个股运行
3. 设置回测时间和初始资金
4. 运行回测查看结果

作者：AI Assistant
日期：2025-08-09
"""

import pandas as pd
import numpy as np
import time
import datetime

def safe_order_shares(stock, shares, order_type, price, context, account_id):
    """
    安全的下单函数包装器
    """
    try:
        # 尝试使用QMT的下单函数
        order_shares(stock, shares, order_type, price, context, account_id)
        return True
    except NameError:
        # 如果order_shares函数不存在，输出模拟信息
        action = "买入" if shares > 0 else "卖出"
        print(f"   📝 模拟{action}: {stock} {abs(shares)}股@{price:.2f}元")
        return True
    except Exception as e:
        print(f"   ❌ 下单失败: {str(e)}")
        return False

def init(ContextInfo):
    """
    策略初始化函数
    """
    print("=" * 60)
    print("🚀 布林线交易策略启动")
    print("=" * 60)

    # ==================== 回测时间配置 ====================

    # 回测开始和结束时间（格式：'YYYY-MM-DD'）
    ContextInfo.backtest_start_date = '2022-01-01'  # 回测开始日期
    ContextInfo.backtest_end_date = '2024-12-31'    # 回测结束日期

    print(f"📅 回测时间设置：")
    print(f"   开始日期: {ContextInfo.backtest_start_date}")
    print(f"   结束日期: {ContextInfo.backtest_end_date}")

    # ==================== 策略参数设置 ====================

    # 布林线参数
    ContextInfo.bollinger_period = 20      # 布林线周期
    ContextInfo.bollinger_std = 2.0        # 布林线标准差倍数

    # 趋势判断参数（优化后）
    ContextInfo.trend_period = 20          # 趋势判断周期（改为20天）
    ContextInfo.trend_ma_period = 20       # 趋势判断用的移动平均线周期
    ContextInfo.min_trend_slope = 0.001    # 最小趋势斜率（0.1%）
    ContextInfo.benchmark_code = '000300.SH'  # 基准指数代码（沪深300）
    ContextInfo.relative_strength_threshold = 1.2  # 相对强度阈值（比基准强20%）

    # 仓位管理参数
    ContextInfo.max_positions = 10         # 最大持仓股票数
    ContextInfo.position_weight = 0.1      # 单只股票权重（10%）
    ContextInfo.min_trade_amount = 10000   # 最小交易金额

    # 风控参数
    ContextInfo.stop_loss = 0.03           # 止损比例（3%）
    ContextInfo.profit_retracement = 0.20  # 盈利回撤比例（20%）
    ContextInfo.buy_zone_ratio = 0.1       # 买入区间比例（中线上方1/10距离内）

    # 调仓频率
    ContextInfo.rebalance_freq = 1         # 调仓频率（天）
    
    print("📊 策略参数设置：")
    print(f"   布林线周期: {ContextInfo.bollinger_period}天")
    print(f"   布林线标准差: {ContextInfo.bollinger_std}")
    print(f"   趋势判断周期: {ContextInfo.trend_period}天")
    print(f"   最小趋势斜率: {ContextInfo.min_trend_slope * 100:.2f}%")
    print(f"   基准指数: {ContextInfo.benchmark_code}")
    print(f"   相对强度阈值: {ContextInfo.relative_strength_threshold}")
    print(f"   最大持仓数: {ContextInfo.max_positions}只")
    print(f"   单股权重: {ContextInfo.position_weight * 100}%")
    print(f"   止损比例: {ContextInfo.stop_loss * 100}%")
    print(f"   盈利回撤: {ContextInfo.profit_retracement * 100}%")
    
    # ==================== 股票池设置 ====================

    print("📊 股票池配置选项：")
    print("   'HS300' - 沪深300成分股（约300只，推荐新手）")
    print("   'MULTI_INDEX' - 多指数组合（约800只，推荐大多数用户）")
    print("   'ALL_A' - 全市场A股（4000+只，谨慎使用）")
    print("   'CUSTOM' - 自定义股票池（约25只精选股票）")
    print()

    # 🔧 在这里修改股票池模式 🔧
    ContextInfo.stock_pool_mode = 'HS300'  # 👈 修改这里选择股票池类型

    print(f"✅ 当前选择的股票池模式: {ContextInfo.stock_pool_mode}")

    # 设置股票池
    ContextInfo.s = setup_stock_universe(ContextInfo)
    ContextInfo.set_universe(ContextInfo.s)
    
    # ==================== 数据结构初始化 ====================
    
    # 持仓记录
    ContextInfo.holdings = {stock: 0 for stock in ContextInfo.s}
    
    # 买入价格记录
    ContextInfo.buypoint = {stock: 0 for stock in ContextInfo.s}
    
    # 最高盈利记录（用于回撤计算）
    ContextInfo.max_profits = {stock: 0 for stock in ContextInfo.s}
    
    # 资金管理
    ContextInfo.money = ContextInfo.capital
    ContextInfo.profit = 0
    
    # 交易统计
    ContextInfo.trade_count = 0
    ContextInfo.win_count = 0
    ContextInfo.loss_count = 0
    
    # 账户设置
    ContextInfo.accountID = 'testS'

    # 基准指数初始化
    ContextInfo.current_benchmark = None  # 当前实际使用的基准指数

    print(f"💰 初始资金: {ContextInfo.capital:,.2f}元")
    print("🔍 基准指数将在首次运行时自动检测")
    print("✅ 策略初始化完成")
    print("=" * 60)

def handlebar(ContextInfo):
    """
    主策略函数，每个交易日调用 - 调试版本
    """
    d = ContextInfo.barpos

    # 前10个交易日输出调试信息
    if d <= 10:
        print(f"🔍 调试: 第{d}个交易日开始")

    # 需要足够的历史数据
    min_bars = ContextInfo.bollinger_period + ContextInfo.trend_period + 5
    if d < min_bars:
        if d <= 10:
            print(f"⏳ 调试: 等待历史数据，需要{min_bars}个，当前{d}个")
        return

    # 获取当前日期（简化版本）
    current_date_str = f"第{d}个交易日"

    # 简化：暂时跳过回测时间检查
    # if current_date and not is_in_backtest_period(current_date, ContextInfo):
    #     return

    # 每5天或前10天输出运行确认
    if d <= 10 or d % 20 == 0:
        print(f"✅ 调试: 策略正在运行 - 第{d}个交易日 ({current_date_str})")
    
    # 每5个交易日调仓一次
    if d % ContextInfo.rebalance_freq == 0:
        print(f"\n{'='*20} {current_date_str} 调仓日 {'='*20}")
        
        # 获取当前价格
        print("📝 调试: 获取当前价格数据...")
        try:
            current_prices = ContextInfo.get_history_data(1, '1d', 'close', 3)
            if not current_prices:
                print("❌ 无法获取当前价格数据")
                return
            print(f"✅ 调试: 成功获取{len(current_prices)}只股票的价格数据")
        except Exception as e:
            print(f"❌ 调试: 获取价格数据失败: {str(e)}")
            return

        # 生成买卖信号
        print("📝 调试: 生成买卖信号...")
        try:
            buy_signals, sell_signals = generate_signals(ContextInfo)
            buy_count = sum(buy_signals.values())
            sell_count = sum(sell_signals.values())
            print(f"✅ 调试: 信号生成完成 - 买入{buy_count}个，卖出{sell_count}个")
        except Exception as e:
            print(f"❌ 调试: 信号生成失败: {str(e)}")
            return

        # 执行卖出操作
        if sell_count > 0:
            print("📝 调试: 执行卖出操作...")
            try:
                execute_sell_orders(ContextInfo, sell_signals, current_prices)
                print(f"✅ 调试: 卖出操作完成")
            except Exception as e:
                print(f"❌ 调试: 卖出操作失败: {str(e)}")

        # 执行买入操作
        if buy_count > 0:
            print("📝 调试: 执行买入操作...")
            try:
                execute_buy_orders(ContextInfo, buy_signals, current_prices)
                print(f"✅ 调试: 买入操作完成")
            except Exception as e:
                print(f"❌ 调试: 买入操作失败: {str(e)}")

        # 打印投资组合状态
        print("📝 调试: 打印投资组合状态...")
        try:
            print_portfolio_status(ContextInfo, current_prices)
        except Exception as e:
            print(f"❌ 调试: 状态打印失败: {str(e)}")

def generate_signals(ContextInfo):
    """
    生成买卖信号 - 增强统计版本
    """
    buy_signals = {stock: 0 for stock in ContextInfo.s}
    sell_signals = {stock: 0 for stock in ContextInfo.s}

    # 统计变量
    total_stocks = len(ContextInfo.s)
    processed_stocks = 0
    data_insufficient = 0
    trend_failed = 0
    buy_condition_failed = 0

    # 获取历史数据长度（增加更多数据）
    history_length = max(80, ContextInfo.bollinger_period + ContextInfo.trend_period + 20)  # 至少80个交易日

    print(f"📊 开始分析{total_stocks}只股票...")

    for stock in ContextInfo.s:
        print(f"🔍 开始处理股票: {stock}")

        # 获取历史价格数据
        history_data = ContextInfo.get_history_data(history_length, '1d', 'close', 3)

        if stock not in history_data or len(history_data[stock]) < history_length:
            print(f"❌ {stock} 数据不足: 需要{history_length}个，实际{len(history_data.get(stock, []))}个")
            data_insufficient += 1
            continue

        prices = np.array(history_data[stock])
        current_price = prices[-1]
        processed_stocks += 1

        print(f"✅ {stock} 数据获取成功: {len(prices)}个价格点，当前价格{current_price:.2f}")

        # 计算布林线
        bollinger_result = calculate_bollinger_bands(prices, ContextInfo.bollinger_period, ContextInfo.bollinger_std)
        if bollinger_result is None:
            print(f"❌ {stock} 布林线计算失败")
            continue

        middle_line, upper_line, lower_line = bollinger_result
        print(f"✅ {stock} 布林线计算成功: 中线{middle_line:.2f}, 上线{upper_line:.2f}")

        # 判断趋势（复杂版本）
        print(f"🔍 开始判断 {stock} 的趋势...")
        trend_up = is_uptrend_advanced(ContextInfo, stock, prices)
        print(f"✅ {stock} 趋势判断完成: {'上涨趋势' if trend_up else '非上涨趋势'}")

        # 当前持仓
        current_holding = ContextInfo.holdings[stock]

        if current_holding > 0:
            print(f"📊 {stock} 已持仓{current_holding}股，检查卖出条件...")
            # 已持仓，检查卖出条件
            if check_sell_conditions(ContextInfo, stock, current_price, middle_line):
                sell_signals[stock] = 1
                print(f"📉 {stock} 触发卖出信号")
        else:
            print(f"📊 {stock} 未持仓，检查买入条件...")
            # 未持仓，检查买入条件
            if trend_up:
                print(f"✅ {stock} 趋势通过，检查买入条件...")
                # 趋势通过，检查买入条件
                if check_buy_conditions(ContextInfo, stock, current_price, middle_line, upper_line):
                    buy_signals[stock] = 1
                    print(f"📈 {stock} 触发买入信号")
                else:
                    print(f"❌ {stock} 买入条件未通过")
                    buy_condition_failed += 1
            else:
                print(f"❌ {stock} 趋势未通过")
                # 趋势未通过
                trend_failed += 1

        print(f"✅ {stock} 处理完成\n")

    # 输出统计信息
    print(f"📊 信号生成统计:")
    print(f"   总股票数: {total_stocks}")
    print(f"   成功处理: {processed_stocks}")
    print(f"   数据不足: {data_insufficient}")
    print(f"   趋势未通过: {trend_failed}")
    print(f"   买入条件未通过: {buy_condition_failed}")
    print(f"   买入信号: {sum(buy_signals.values())}")
    print(f"   卖出信号: {sum(sell_signals.values())}")
    print()

    return buy_signals, sell_signals

def calculate_bollinger_bands(prices, period, std_multiplier):
    """
    计算布林线指标
    """
    if len(prices) < period:
        return None
    
    # 计算移动平均线（中轨）
    recent_prices = prices[-period:]
    middle_line = np.mean(recent_prices)
    
    # 计算标准差
    std_dev = np.std(recent_prices)
    
    # 计算上轨和下轨
    upper_line = middle_line + (std_multiplier * std_dev)
    lower_line = middle_line - (std_multiplier * std_dev)
    
    return middle_line, upper_line, lower_line

def is_uptrend_simple(prices, trend_period):
    """
    简化的趋势判断函数
    """
    if len(prices) < trend_period + 5:
        return False

    # 简单比较：当前价格 vs N天前价格
    current_price = prices[-1]
    past_price = prices[-trend_period]

    return current_price > past_price

def is_uptrend_advanced(ContextInfo, stock, prices):
    """
    复杂的趋势判断函数 - 移除异常捕获版本

    综合考虑多个因素：
    1. 多周期移动平均线排列
    2. 价格动量
    3. 波动率分析
    4. 相对强度（与市场基准比较）
    """
    print(f"🔍 开始分析 {stock} 的趋势...")

    # 根据实际数据长度调整需求
    min_required = max(30, ContextInfo.bollinger_period + 10)  # 最少30个，或布林线周期+10

    if len(prices) < min_required:
        print(f"❌ {stock} 历史数据不足: {len(prices)} < {min_required}")
        return False

    print(f"✅ {stock} 历史数据充足: {len(prices)}个")

    # 根据实际数据长度调整计算周期
    actual_ma60_period = min(60, len(prices) - 5)  # 60日均线，但不超过实际数据长度
    actual_ma20_period = min(20, len(prices) - 5)  # 20日均线
    actual_ma10_period = min(10, len(prices) - 5)  # 10日均线
    actual_ma5_period = min(5, len(prices) - 5)    # 5日均线

    print(f"📊 {stock} 调整后的计算周期: MA5={actual_ma5_period}, MA10={actual_ma10_period}, MA20={actual_ma20_period}, MA60={actual_ma60_period}")

    # 1. 多周期移动平均线排列判断（使用调整后的周期）
    ma5 = np.mean(prices[-actual_ma5_period:])      # 5日均线
    ma10 = np.mean(prices[-actual_ma10_period:])    # 10日均线
    ma20 = np.mean(prices[-actual_ma20_period:])    # 20日均线
    ma60 = np.mean(prices[-actual_ma60_period:])    # 60日均线

    print(f"📊 {stock} 均线计算完成: MA5={ma5:.2f}, MA10={ma10:.2f}, MA20={ma20:.2f}, MA60={ma60:.2f}")

    # 均线多头排列：放宽要求，更实用的判断逻辑
    if actual_ma60_period >= 60:
        # 如果有足够数据，使用放宽的判断：短期均线 > 长期均线，允许中间有交叉
        ma_bullish = (ma5 > ma20) and (ma20 > ma60) and (ma5 > ma10)
        print(f"📊 {stock} 使用放宽均线判断：MA5>MA20>MA60 且 MA5>MA10")
    else:
        # 如果数据不足，简化判断
        ma_bullish = (ma5 > ma20) and (ma10 > ma60)
        print(f"📊 {stock} 使用简化均线判断（数据不足60天）：MA5>MA20 且 MA10>MA60")

    # 2. 价格相对于均线的位置
    current_price = prices[-1]
    price_above_ma20 = current_price > ma20

    # 3. 均线斜率判断（趋势强度）- 使用调整后的周期
    ma20_slope = calculate_slope(prices, actual_ma20_period, min(5, actual_ma20_period//4))  # 20日均线斜率
    ma60_slope = calculate_slope(prices, actual_ma60_period, min(10, actual_ma60_period//6)) # 60日均线斜率

    # 根据数据长度调整斜率要求
    if len(prices) >= 60:
        slope_positive = ma20_slope > 0.001 and ma60_slope > 0  # 完整要求
    else:
        slope_positive = ma20_slope > 0.0005  # 简化要求，只看20日均线斜率
        print(f"📊 {stock} 使用简化斜率判断（数据不足60天）")

    # 4. 价格动量分析（根据数据长度调整）
    momentum_period_5 = min(5, len(prices) - 1)
    momentum_period_10 = min(10, len(prices) - 1)

    if len(prices) > momentum_period_5:
        momentum_5 = (prices[-1] - prices[-momentum_period_5-1]) / prices[-momentum_period_5-1]   # 5日动量
    else:
        momentum_5 = 0

    if len(prices) > momentum_period_10:
        momentum_10 = (prices[-1] - prices[-momentum_period_10-1]) / prices[-momentum_period_10-1] # 10日动量
    else:
        momentum_10 = 0

    momentum_positive = momentum_5 > 0 and momentum_10 > 0

    # 5. 波动率分析（避免过度波动的股票）
    volatility = np.std(prices[-20:]) / np.mean(prices[-20:])
    volatility_reasonable = volatility < 0.05  # 波动率小于5%

    # 6. 相对强度分析（与基准比较）- 根据数据长度调整
    relative_strength_ok = True  # 默认通过

    # 根据数据长度调整比较周期
    comparison_period = min(20, len(prices) - 1)

    if len(prices) > comparison_period:
        benchmark_strength = get_benchmark_performance(ContextInfo, comparison_period)
        stock_performance = (prices[-1] - prices[-comparison_period-1]) / prices[-comparison_period-1]

        if benchmark_strength is not None:
            relative_strength = stock_performance / benchmark_strength if benchmark_strength != 0 else 1
            relative_strength_ok = relative_strength > 1.1  # 跑赢基准10%
            print(f"📊 {stock} 相对强度: 股票{stock_performance:.2%} vs 基准{benchmark_strength:.2%} = {relative_strength:.2f}")
        else:
            print(f"📊 {stock} 基准数据获取失败，跳过相对强度判断")
    else:
        print(f"📊 {stock} 数据不足，跳过相对强度判断")

    # 综合判断逻辑
    # 必须满足的核心条件
    core_conditions = ma_bullish and price_above_ma20 and slope_positive

    # 最终判断：核心条件必须满足，加分条件满足2个以上
    bonus_score = sum([momentum_positive, volatility_reasonable, relative_strength_ok])
    trend_up = core_conditions and bonus_score >= 2

    # 详细输出每只股票的分析结果
    print(f"📊 {stock} 复杂趋势分析:")
    print(f"   当前价格: {current_price:.2f}")
    print(f"   均线数据: MA5({ma5:.2f}) MA10({ma10:.2f}) MA20({ma20:.2f}) MA60({ma60:.2f})")
    print(f"   均线排列: {'✅' if ma_bullish else '❌'} (要求: MA5>MA10>MA20>MA60)")
    print(f"   价格位置: {'✅' if price_above_ma20 else '❌'} (要求: 价格>{ma20:.2f})")
    print(f"   均线斜率: {'✅' if slope_positive else '❌'} (MA20:{ma20_slope:.4f}, MA60:{ma60_slope:.4f})")
    print(f"   价格动量: {'✅' if momentum_positive else '❌'} (5日:{momentum_5:.2%}, 10日:{momentum_10:.2%})")
    print(f"   波动率: {'✅' if volatility_reasonable else '❌'} ({volatility:.2%} < 5%)")
    print(f"   相对强度: {'✅' if relative_strength_ok else '❌'}")
    print(f"   核心条件: {'✅ 通过' if core_conditions else '❌ 未通过'}")
    print(f"   加分条件: {bonus_score}/3 (需要≥2)")
    print(f"   最终判断: {'✅ 强势上涨趋势' if trend_up else '❌ 非强势上涨趋势'}")
    print()

    return trend_up

def calculate_slope(prices, ma_period, slope_period):
    """
    计算移动平均线的斜率 - 移除异常捕获版本

    参数:
        prices: 价格数组
        ma_period: 移动平均线周期
        slope_period: 斜率计算周期

    返回:
        slope: 斜率值（归一化）
    """
    if len(prices) < ma_period + slope_period:
        print(f"⚠️ 斜率计算数据不足: 需要{ma_period + slope_period}个，实际{len(prices)}个")
        return 0

    # 计算当前和过去的移动平均线
    current_ma = np.mean(prices[-ma_period:])
    past_ma = np.mean(prices[-ma_period-slope_period:-slope_period])

    print(f"📊 斜率计算: 当前MA{current_ma:.2f}, 过去MA{past_ma:.2f}")

    # 计算斜率（归一化）
    slope = (current_ma - past_ma) / past_ma if past_ma > 0 else 0

    print(f"📊 计算得到斜率: {slope:.6f}")

    return slope

def get_benchmark_performance(ContextInfo, period):
    """
    获取基准指数的表现 - 修正版本

    参数:
        ContextInfo: 策略上下文
        period: 计算周期

    返回:
        performance: 基准指数收益率
    """
    # 扩展基准指数代码列表，包含更多可能的格式
    benchmark_codes = [
        '000300.SH', 'SH000300', '000300',  # 沪深300的多种格式
        '000001.SH', 'SH000001', '000001',  # 上证指数的多种格式
        '399001.SZ', 'SZ399001', '399001',  # 深证成指的多种格式
        '399006.SZ', 'SZ399006', '399006'   # 创业板指的多种格式
    ]

    print(f"🔍 开始获取基准指数数据，周期{period}天...")

    # 先获取一次数据，看看都有什么股票代码
    all_data = ContextInfo.get_history_data(period + 10, '1d', 'close', 3)
    print(f"� 获取到的所有数据包含 {len(all_data)} 个代码")

    # 查找可能的基准指数代码
    available_codes = list(all_data.keys())
    print(f"🔍 可用的代码示例: {available_codes[:10]}...")  # 显示前10个代码

    # 尝试从可用代码中找到基准指数
    for benchmark_code in benchmark_codes:
        if benchmark_code in available_codes:
            print(f"✅ 找到基准指数代码: {benchmark_code}")

            benchmark_prices = np.array(all_data[benchmark_code])
            print(f"✅ 获取到{benchmark_code}数据: {len(benchmark_prices)}个价格点")

            if len(benchmark_prices) >= period + 1:
                performance = (benchmark_prices[-1] - benchmark_prices[-period-1]) / benchmark_prices[-period-1]
                print(f"✅ {benchmark_code}表现计算成功: {performance:.4f}")
                return performance
            else:
                print(f"❌ {benchmark_code}数据不足: 需要{period+1}个，实际{len(benchmark_prices)}个")
        else:
            print(f"❌ 未找到{benchmark_code}代码")

    # 如果没找到基准指数，尝试使用股票池中的平均表现作为基准
    print("🔄 尝试使用股票池平均表现作为基准...")

    stock_performances = []
    for stock_code in list(available_codes)[:20]:  # 取前20只股票
        if stock_code in all_data:
            stock_prices = np.array(all_data[stock_code])
            if len(stock_prices) >= period + 1:
                stock_perf = (stock_prices[-1] - stock_prices[-period-1]) / stock_prices[-period-1]
                stock_performances.append(stock_perf)

    if stock_performances:
        avg_performance = np.mean(stock_performances)
        print(f"✅ 使用股票池平均表现作为基准: {avg_performance:.4f} (基于{len(stock_performances)}只股票)")
        return avg_performance

    print("❌ 所有基准获取方法都失败，返回默认值")
    return 0.02  # 返回2%的默认基准收益率

def is_uptrend(prices, trend_period, ma_period):
    """
    判断趋势是否向上（保留原函数以防其他地方调用）
    """
    if len(prices) < trend_period + ma_period:
        return False

    # 计算当前和之前的移动平均线
    current_ma = np.mean(prices[-ma_period:])
    previous_ma = np.mean(prices[-ma_period-trend_period:-trend_period])

    return current_ma > previous_ma

def check_buy_conditions(ContextInfo, stock, current_price, middle_line, upper_line):
    """
    检查买入条件 - 增强日志版本
    """
    # 计算买入区间
    upper_to_middle_distance = upper_line - middle_line
    buy_threshold = middle_line + (upper_to_middle_distance * ContextInfo.buy_zone_ratio)

    # 检查价格是否在买入区间
    price_in_buy_zone = middle_line <= current_price <= buy_threshold

    # 检查持仓数量限制
    current_positions = sum(1 for holding in ContextInfo.holdings.values() if holding > 0)
    position_limit_ok = current_positions < ContextInfo.max_positions

    # 检查资金是否充足
    position_value = ContextInfo.money * ContextInfo.position_weight
    shares_needed = int(position_value / current_price / 100) * 100
    fund_sufficient = shares_needed >= 100 and position_value <= ContextInfo.money

    # 详细的买入条件日志（每10只股票输出一次）
    if hash(stock) % 10 == 0:
        print(f"🔍 {stock} 买入条件详细检查:")
        print(f"   布林线: 中线{middle_line:.2f}, 上线{upper_line:.2f}")
        print(f"   买入区间: {middle_line:.2f} ≤ 价格 ≤ {buy_threshold:.2f}")
        print(f"   当前价格: {current_price:.2f}")
        print(f"   价格条件: {'✅' if price_in_buy_zone else '❌'}")
        print(f"   持仓限制: {'✅' if position_limit_ok else '❌'} ({current_positions}/{ContextInfo.max_positions})")
        print(f"   资金条件: {'✅' if fund_sufficient else '❌'}")
        print(f"   可用资金: {ContextInfo.money:.2f}")
        print(f"   拟投金额: {position_value:.2f}")
        print(f"   拟买股数: {shares_needed}股")
        print(f"   综合结果: {'✅ 满足买入条件' if (price_in_buy_zone and position_limit_ok and fund_sufficient) else '❌ 不满足买入条件'}")
        print()

    return price_in_buy_zone and position_limit_ok and fund_sufficient

def check_sell_conditions(ContextInfo, stock, current_price, middle_line):
    """
    检查卖出条件
    """
    buy_price = ContextInfo.buypoint[stock]
    current_holding = ContextInfo.holdings[stock]
    
    if current_holding <= 0 or buy_price <= 0:
        return False
    
    # 计算当前盈亏
    current_profit_rate = (current_price - buy_price) / buy_price
    current_profit_amount = (current_price - buy_price) * current_holding
    
    # 更新最高盈利记录
    if current_profit_amount > ContextInfo.max_profits[stock]:
        ContextInfo.max_profits[stock] = current_profit_amount
    
    # 条件1：跌破中线
    if current_price < middle_line:
        print(f"   卖出原因: {stock} 跌破布林线中线")
        return True
    
    # 条件2：亏损止损
    if current_profit_rate <= -ContextInfo.stop_loss:
        print(f"   卖出原因: {stock} 亏损止损({ContextInfo.stop_loss:.1%})")
        return True
    
    # 条件3：盈利回撤
    if ContextInfo.max_profits[stock] > 0:
        retracement_amount = ContextInfo.max_profits[stock] - current_profit_amount
        retracement_rate = retracement_amount / ContextInfo.max_profits[stock]
        
        if retracement_rate >= ContextInfo.profit_retracement:
            print(f"   卖出原因: {stock} 盈利回撤({ContextInfo.profit_retracement:.1%})")
            return True
    
    return False

def execute_sell_orders(ContextInfo, sell_signals, current_prices):
    """
    执行卖出订单
    """
    for stock in ContextInfo.s:
        if sell_signals[stock] == 1 and ContextInfo.holdings[stock] > 0:
            try:
                current_price = current_prices[stock][-1]
                shares_to_sell = ContextInfo.holdings[stock]
                buy_price = ContextInfo.buypoint[stock]

                # 计算盈亏
                sell_value = shares_to_sell * current_price
                commission = sell_value * 0.0003  # 手续费
                net_proceeds = sell_value - commission

                cost_basis = buy_price * shares_to_sell
                profit = net_proceeds - cost_basis
                profit_rate = profit / cost_basis if cost_basis > 0 else 0

                print(f"💰 卖出: {stock}")
                print(f"   数量: {shares_to_sell}股")
                print(f"   买入价: {buy_price:.2f}元")
                print(f"   卖出价: {current_price:.2f}元")
                print(f"   盈亏: {profit:.2f}元 ({profit_rate:.2%})")

                # 下单
                safe_order_shares(stock, -shares_to_sell, 'fix', current_price, ContextInfo, ContextInfo.accountID)

                # 更新记录
                ContextInfo.money += net_proceeds
                ContextInfo.profit += profit
                ContextInfo.holdings[stock] = 0
                ContextInfo.buypoint[stock] = 0
                ContextInfo.max_profits[stock] = 0

                # 更新统计
                ContextInfo.trade_count += 1
                if profit > 0:
                    ContextInfo.win_count += 1
                else:
                    ContextInfo.loss_count += 1

            except Exception as e:
                print(f"❌ 卖出{stock}失败: {str(e)}")

def execute_buy_orders(ContextInfo, buy_signals, current_prices):
    """
    执行买入订单
    """
    # 统计买入信号数量
    buy_count = sum(buy_signals.values())
    if buy_count == 0:
        return

    # 计算每只股票的买入金额
    available_positions = ContextInfo.max_positions - sum(1 for h in ContextInfo.holdings.values() if h > 0)
    actual_buy_count = min(buy_count, available_positions)

    if actual_buy_count <= 0:
        print("⚠️ 已达最大持仓数限制，无法买入新股票")
        return

    # 选择买入的股票（按信号顺序）
    buy_stocks = [stock for stock in ContextInfo.s if buy_signals[stock] == 1][:actual_buy_count]

    for stock in buy_stocks:
        try:
            current_price = current_prices[stock][-1]

            # 计算买入金额和股数
            position_value = ContextInfo.money * ContextInfo.position_weight
            shares_to_buy = int(position_value / current_price / 100) * 100  # 向下取整到100股

            if shares_to_buy < 100:
                print(f"⚠️ {stock} 资金不足，无法买入")
                continue

            buy_value = shares_to_buy * current_price
            commission = buy_value * 0.0003  # 手续费
            total_cost = buy_value + commission

            if total_cost > ContextInfo.money:
                print(f"⚠️ {stock} 资金不足，需要{total_cost:.2f}元，可用{ContextInfo.money:.2f}元")
                continue

            print(f"🛒 买入: {stock}")
            print(f"   数量: {shares_to_buy}股")
            print(f"   价格: {current_price:.2f}元")
            print(f"   金额: {buy_value:.2f}元")
            print(f"   手续费: {commission:.2f}元")
            print(f"   总成本: {total_cost:.2f}元")

            # 下单
            safe_order_shares(stock, shares_to_buy, 'fix', current_price, ContextInfo, ContextInfo.accountID)

            # 更新记录
            ContextInfo.holdings[stock] = shares_to_buy
            ContextInfo.buypoint[stock] = current_price
            ContextInfo.max_profits[stock] = 0
            ContextInfo.money -= total_cost
            ContextInfo.profit -= commission

        except Exception as e:
            print(f"❌ 买入{stock}失败: {str(e)}")

def print_portfolio_status(ContextInfo, current_prices):
    """
    打印投资组合状态
    """
    print(f"\n{'='*15} 投资组合状态 {'='*15}")

    # 统计持仓
    total_positions = 0
    total_market_value = 0
    total_cost = 0

    print("📊 当前持仓:")
    for stock in ContextInfo.s:
        holding = ContextInfo.holdings[stock]
        if holding > 0 and stock in current_prices:
            current_price = current_prices[stock][-1]
            buy_price = ContextInfo.buypoint[stock]
            market_value = holding * current_price
            cost_value = holding * buy_price
            profit = market_value - cost_value
            profit_rate = profit / cost_value if cost_value > 0 else 0

            total_positions += 1
            total_market_value += market_value
            total_cost += cost_value

            print(f"   {stock}: {holding}股@{buy_price:.2f}元")
            print(f"      现价: {current_price:.2f}元, 市值: {market_value:.2f}元")
            print(f"      盈亏: {profit:.2f}元 ({profit_rate:.2%})")

    if total_positions == 0:
        print("   暂无持仓")

    # 计算总资产
    total_assets = ContextInfo.money + total_market_value
    total_return = (total_assets - ContextInfo.capital) / ContextInfo.capital

    print(f"\n💰 资金状况:")
    print(f"   初始资金: {ContextInfo.capital:,.2f}元")
    print(f"   可用资金: {ContextInfo.money:,.2f}元")
    print(f"   持仓市值: {total_market_value:,.2f}元")
    print(f"   总资产: {total_assets:,.2f}元")
    print(f"   总收益: {total_assets - ContextInfo.capital:,.2f}元")
    print(f"   总收益率: {total_return:.2%}")

    print(f"\n📈 交易统计:")
    print(f"   持仓股票数: {total_positions}只")
    print(f"   总交易次数: {ContextInfo.trade_count}次")
    print(f"   盈利次数: {ContextInfo.win_count}次")
    print(f"   亏损次数: {ContextInfo.loss_count}次")

    if ContextInfo.trade_count > 0:
        win_rate = ContextInfo.win_count / ContextInfo.trade_count
        print(f"   胜率: {win_rate:.2%}")

    print("=" * 45)

    # 绘制收益率曲线（如果不是回测模式）
    if not ContextInfo.do_back_test:
        profit_ratio = (total_assets - ContextInfo.capital) / ContextInfo.capital
        ContextInfo.paint('profit_ratio', profit_ratio, -1, 0)

def is_in_backtest_period(current_date, ContextInfo):
    """
    检查当前日期是否在回测时间范围内
    """
    try:
        # 将字符串日期转换为可比较的格式
        if isinstance(current_date, str):
            current_date_str = current_date
        else:
            current_date_str = str(current_date)

        # 提取日期部分（YYYY-MM-DD）
        if len(current_date_str) >= 10:
            current_date_str = current_date_str[:10]

        # 比较日期字符串
        return (ContextInfo.backtest_start_date <= current_date_str <= ContextInfo.backtest_end_date)
    except:
        # 如果日期解析失败，默认允许交易
        return True

def calculate_trend_slope(prices, period):
    """
    计算价格趋势斜率（归一化）

    参数:
        prices: 价格数组
        period: 计算周期

    返回:
        slope: 归一化斜率（百分比形式）
    """
    if len(prices) < period:
        return 0

    # 取最近period个价格
    recent_prices = prices[-period:]

    # 计算起始和结束价格
    start_price = recent_prices[0]
    end_price = recent_prices[-1]

    # 计算总收益率
    total_return = (end_price - start_price) / start_price if start_price > 0 else 0

    # 计算年化斜率（假设252个交易日为一年）
    annualized_slope = total_return * (252 / period)

    return annualized_slope

def get_benchmark_slope(ContextInfo, period):
    """
    获取基准指数的趋势斜率（优化版本，支持多种基准指数）

    参数:
        ContextInfo: 策略上下文
        period: 计算周期

    返回:
        benchmark_slope: 基准指数斜率
    """
    # 定义多种可能的基准指数代码（按优先级排序）
    benchmark_candidates = [
        '000300.SH',  # 沪深300
        'SH000300',   # 沪深300（另一种格式）
        '399300.SZ',  # 沪深300（深交所格式）
        '000001.SH',  # 上证指数
        'SH000001',   # 上证指数（另一种格式）
        '399001.SZ',  # 深证成指
        'SZ399001'    # 深证成指（另一种格式）
    ]

    # 尝试获取基准指数数据
    for benchmark_code in benchmark_candidates:
        try:
            print(f"🔍 尝试获取基准指数: {benchmark_code}")

            # 获取基准指数历史数据
            benchmark_data = ContextInfo.get_history_data(period + 10, '1d', 'close', 3)

            if benchmark_code in benchmark_data:
                benchmark_prices = np.array(benchmark_data[benchmark_code])

                if len(benchmark_prices) >= period:
                    # 计算基准指数斜率
                    benchmark_slope = calculate_trend_slope(benchmark_prices, period)
                    print(f"✅ 成功获取基准指数 {benchmark_code} 数据，斜率: {benchmark_slope:.4f}")

                    # 更新当前使用的基准指数代码
                    ContextInfo.current_benchmark = benchmark_code
                    return benchmark_slope
                else:
                    print(f"⚠️ {benchmark_code} 数据长度不足: {len(benchmark_prices)} < {period}")
            else:
                print(f"⚠️ 未找到 {benchmark_code} 数据")

        except Exception as e:
            print(f"⚠️ 获取 {benchmark_code} 数据失败: {str(e)}")
            continue

    # 如果所有基准指数都获取失败，使用股票池平均表现作为基准
    try:
        print("🔄 尝试使用股票池平均表现作为基准")
        return calculate_stock_pool_benchmark(ContextInfo, period)
    except:
        print("⚠️ 股票池基准计算失败，使用固定默认值")
        return 0.03  # 默认3%年化收益率

def calculate_stock_pool_benchmark(ContextInfo, period):
    """
    使用股票池的平均表现作为基准
    """
    try:
        # 获取股票池数据
        stock_slopes = []

        # 随机选择10只股票计算平均斜率
        import random
        sample_stocks = random.sample(ContextInfo.s, min(10, len(ContextInfo.s)))

        for stock in sample_stocks:
            try:
                stock_data = ContextInfo.get_history_data(period + 5, '1d', 'close', 3)
                if stock in stock_data and len(stock_data[stock]) >= period:
                    stock_prices = np.array(stock_data[stock])
                    stock_slope = calculate_trend_slope(stock_prices, period)
                    stock_slopes.append(stock_slope)
            except:
                continue

        if stock_slopes:
            avg_slope = np.mean(stock_slopes)
            print(f"✅ 使用股票池平均斜率作为基准: {avg_slope:.4f}")
            return avg_slope
        else:
            return 0.03

    except Exception as e:
        print(f"❌ 股票池基准计算失败: {str(e)}")
        return 0.03

def is_uptrend_optimized(ContextInfo, stock, prices):
    """
    优化的趋势判断函数

    判断逻辑：
    1. 计算股票20天的价格斜率
    2. 计算基准指数20天的价格斜率
    3. 股票斜率必须为正且大于最小阈值
    4. 股票斜率必须大于基准斜率的相对强度阈值倍数

    参数:
        ContextInfo: 策略上下文
        stock: 股票代码
        prices: 股票价格数组

    返回:
        True: 符合上涨趋势条件
        False: 不符合上涨趋势条件
    """
    try:
        period = ContextInfo.trend_period

        # 计算股票价格斜率
        stock_slope = calculate_trend_slope(prices, period)

        # 获取基准指数斜率
        benchmark_slope = get_benchmark_slope(ContextInfo, period)

        # 判断条件
        # 1. 股票斜率必须为正
        slope_positive = stock_slope > 0

        # 2. 股票斜率必须大于最小阈值
        slope_sufficient = stock_slope > ContextInfo.min_trend_slope

        # 3. 股票相对强度必须超过基准
        if benchmark_slope > 0:
            relative_strength = stock_slope / benchmark_slope
            relative_strong = relative_strength > ContextInfo.relative_strength_threshold
        else:
            # 如果基准为负，只要股票为正就算相对强势
            relative_strong = stock_slope > 0

        # 综合判断
        trend_up = slope_positive and slope_sufficient and relative_strong

        # 输出详细的趋势分析（减少日志频率，避免过多输出）
        stock_hash = hash(stock) % 20  # 改为每20只股票输出一次
        if stock_hash == 0:  # 只对部分股票输出详细日志
            print(f"📊 {stock} 趋势分析:")
            print(f"   股票{period}日斜率: {stock_slope:.4f} ({stock_slope*100:.2f}%年化)")
            print(f"   基准{period}日斜率: {benchmark_slope:.4f} ({benchmark_slope*100:.2f}%年化)")
            if benchmark_slope > 0:
                relative_strength_value = stock_slope / benchmark_slope
                print(f"   相对强度: {relative_strength_value:.2f}")
            else:
                print(f"   相对强度: N/A (基准为负)")
            print(f"   斜率为正: {'✅' if slope_positive else '❌'} (要求>0)")
            print(f"   斜率充足: {'✅' if slope_sufficient else '❌'} (要求>{ContextInfo.min_trend_slope:.3f})")
            print(f"   相对强势: {'✅' if relative_strong else '❌'} (要求>{ContextInfo.relative_strength_threshold:.1f}倍)")
            print(f"   综合判断: {'✅ 上涨趋势' if trend_up else '❌ 非上涨趋势'}")

            # 如果使用了备用基准，显示当前基准
            if hasattr(ContextInfo, 'current_benchmark'):
                print(f"   当前基准: {ContextInfo.current_benchmark}")

        return trend_up

    except Exception as e:
        print(f"❌ {stock} 趋势判断失败: {str(e)}")
        return False

def setup_stock_universe(ContextInfo):
    """
    智能股票池设置函数

    支持多种股票池模式：
    - 'HS300': 沪深300成分股
    - 'ALL_A': 全市场A股（谨慎使用）
    - 'MULTI_INDEX': 多指数组合（推荐）
    - 'CUSTOM': 自定义股票池
    """
    mode = ContextInfo.stock_pool_mode

    if mode == 'HS300':
        return setup_hs300_universe(ContextInfo)
    elif mode == 'ALL_A':
        return setup_all_a_universe(ContextInfo)
    elif mode == 'MULTI_INDEX':
        return setup_multi_index_universe(ContextInfo)
    elif mode == 'CUSTOM':
        return setup_custom_universe(ContextInfo)
    else:
        print(f"⚠️ 未知股票池模式: {mode}，使用默认沪深300")
        return setup_hs300_universe(ContextInfo)

def setup_hs300_universe(ContextInfo):
    """设置沪深300股票池"""
    try:
        stocks = ContextInfo.get_sector('000300.SH')
        if stocks and len(stocks) > 0:
            print(f"📈 股票池设置成功：沪深300成分股，共{len(stocks)}只股票")
            return list(stocks)
    except Exception as e:
        print(f"⚠️ 获取沪深300失败: {str(e)}")

    # 备用股票池
    backup_stocks = ['000001.SZ', '000002.SZ', '600000.SH', '600036.SH', '000858.SZ',
                    '000166.SZ', '600519.SH', '000725.SZ', '002415.SZ', '600276.SH']
    print(f"🔄 使用备用股票池：{len(backup_stocks)}只股票")
    return backup_stocks

def setup_all_a_universe(ContextInfo):
    """设置全市场A股股票池（谨慎使用）"""
    try:
        # 尝试多种获取全市场股票的方法
        methods = [
            ('ALL_A', '全市场A股'),
            ('SH_A', '沪市A股'),
            ('SZ_A', '深市A股')
        ]

        all_stocks = []

        for method, name in methods:
            try:
                stocks = ContextInfo.get_sector(method)
                if stocks:
                    all_stocks.extend(list(stocks))
                    print(f"✅ 获取{name}成功：{len(stocks)}只股票")
            except:
                print(f"⚠️ 获取{name}失败")
                continue

        if all_stocks:
            # 去重
            unique_stocks = list(set(all_stocks))

            # 过滤掉一些不适合的股票（可选）
            filtered_stocks = filter_stocks(unique_stocks)

            print(f"📊 全市场股票池设置成功，共{len(filtered_stocks)}只股票")
            print(f"⚠️ 注意：全市场股票池计算量大，建议适当调整参数")

            return filtered_stocks
        else:
            print("❌ 无法获取全市场股票，使用沪深300")
            return setup_hs300_universe(ContextInfo)

    except Exception as e:
        print(f"❌ 设置全市场股票池失败: {str(e)}")
        return setup_hs300_universe(ContextInfo)

def setup_multi_index_universe(ContextInfo):
    """设置多指数组合股票池（推荐）"""
    # 按优先级尝试多个指数
    index_options = [
        ('000300.SH', '沪深300', 1.0),      # 权重100%
        ('000905.SH', '中证500', 0.5),      # 权重50%
        ('000852.SH', '中证1000', 0.3),     # 权重30%
        ('399006.SZ', '创业板指', 0.3),      # 权重30%
    ]

    all_stocks = []
    successful_indices = []

    for index_code, index_name, weight in index_options:
        try:
            stocks = ContextInfo.get_sector(index_code)
            if stocks and len(stocks) > 0:
                # 根据权重选择股票数量
                stock_list = list(stocks)
                selected_count = max(int(len(stock_list) * weight), 10)  # 至少10只
                selected_stocks = stock_list[:selected_count]

                all_stocks.extend(selected_stocks)
                successful_indices.append((index_name, len(selected_stocks)))
                print(f"✅ 获取{index_name}成功：选择{len(selected_stocks)}只股票")
        except Exception as e:
            print(f"⚠️ 获取{index_name}失败: {str(e)}")
            continue

    if all_stocks:
        # 去重
        unique_stocks = list(set(all_stocks))
        print(f"📊 多指数组合股票池设置成功，共{len(unique_stocks)}只股票")
        print(f"   包含指数: {', '.join([f'{name}({count}只)' for name, count in successful_indices])}")
        return unique_stocks
    else:
        print("❌ 所有指数获取失败，使用沪深300")
        return setup_hs300_universe(ContextInfo)

def setup_custom_universe(ContextInfo):
    """设置自定义股票池"""
    # 可以根据需要修改这个列表
    custom_stocks = [
        # 大盘蓝筹
        '000001.SZ', '000002.SZ', '600000.SH', '600036.SH', '601166.SH',
        # 科技股
        '000858.SZ', '002415.SZ', '300059.SZ', '002821.SZ', '300015.SZ',
        # 消费股
        '600519.SH', '000568.SZ', '002304.SZ', '600887.SH', '000895.SZ',
        # 医药股
        '000661.SZ', '600276.SH', '300015.SZ', '002821.SZ', '600196.SH',
        # 新能源
        '300750.SZ', '002594.SZ', '300274.SZ', '002460.SZ', '688599.SH'
    ]

    print(f"📊 自定义股票池设置成功，共{len(custom_stocks)}只股票")
    print("   包含：大盘蓝筹、科技、消费、医药、新能源等板块")

    return custom_stocks

def filter_stocks(stocks):
    """
    过滤股票池，去除不适合的股票
    """
    filtered = []

    for stock in stocks:
        # 过滤条件（可根据需要调整）
        if (
            not stock.startswith('ST') and      # 排除ST股票
            not stock.startswith('*ST') and     # 排除*ST股票
            not stock.endswith('.BJ') and       # 排除北交所股票（可选）
            len(stock) >= 9                     # 确保股票代码格式正确
        ):
            filtered.append(stock)

    print(f"🔍 股票过滤完成：{len(stocks)} -> {len(filtered)}只股票")
    return filtered
