import * as React from 'react'
import * as RadixTabs from '@radix-ui/react-tabs'
import { twMerge } from 'tailwind-merge'

export function Tabs({ value, onValueChange, children, className = '' }) {
  return (
    <RadixTabs.Root value={value} onValueChange={onValueChange} className={twMerge('w-full', className)}>
      {children}
    </RadixTabs.Root>
  )
}

export function TabsList({ children, className = '' }) {
  return (
    <RadixTabs.List className={twMerge('inline-flex items-center gap-1 rounded-md bg-gray-100 p-1', className)}>
      {children}
    </RadixTabs.List>
  )
}

export function TabsTrigger({ value, children, className = '' }) {
  return (
    <RadixTabs.Trigger
      value={value}
      className={twMerge(
        'px-3 py-1.5 text-sm rounded-md data-[state=active]:bg-white data-[state=active]:shadow-sm data-[state=active]:text-gray-900 text-gray-600 hover:text-gray-900',
        className
      )}
    >
      {children}
    </RadixTabs.Trigger>
  )
}

export function TabsContent({ value, children, className = '' }) {
  return (
    <RadixTabs.Content value={value} className={twMerge('mt-4', className)}>
      {children}
    </RadixTabs.Content>
  )
}

