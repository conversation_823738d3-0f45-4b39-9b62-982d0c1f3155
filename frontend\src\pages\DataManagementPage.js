import React, { useState, useEffect } from 'react';
// import {
//   Form,
// } from 'antd';
import { Download, RefreshCw, CheckCircle } from 'lucide-react';
import { Card as Shad<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>ead<PERSON>, <PERSON><PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Card<PERSON>ontent as Shad<PERSON>ardContent } from '../components/UI/card.jsx';
import { Button as ShadButton } from '../components/UI/button.jsx';
import { Alert as ShadAlert } from '../components/UI/alert.jsx';
import { Progress as ShadProgress } from '../components/UI/progress.jsx';
import { Stat } from '../components/UI/stat.jsx';
import { Tag } from '../components/UI/tag.jsx';
import { Modal } from '../components/UI/modal.jsx';
import { DataTable } from '../components/UI/table.jsx';
import { DateRangePicker } from '../components/UI/date-range.jsx';
import moment from 'moment';
import { dataAPI, apiUtils } from '../services/api';
import { toast as imported_toast } from 'sonner';

const DataManagementPage = () => {
  const [loading, setLoading] = useState(false);
  const [stockList, setStockList] = useState([]);
  const [dataStatus, setDataStatus] = useState(null);
  const [selectedStocks, setSelectedStocks] = useState([]);
  const [downloadModalVisible, setDownloadModalVisible] = useState(false);
  const [downloadProgress, setDownloadProgress] = useState(null);
  const [error, setError] = useState(null);

  // 分页状态
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(100);
  const [totalStocks, setTotalStocks] = useState(0);
  const [totalPages, setTotalPages] = useState(0);

  // 表格列定义
  const columns = [
    {
      accessorKey: 'code',
      header: '股票代码',
      cell: info => info.getValue(),
    },
    {
      accessorKey: 'name',
      header: '股票名称',
      cell: info => info.getValue(),
    },
    {
      accessorKey: 'exchange',
      header: '交易所',
      cell: info => (
        <Tag color={info.getValue() === 'SH' ? 'red' : 'blue'}>
          {info.getValue()}
        </Tag>
      ),
    },
    {
      accessorKey: 'industry',
      header: '行业',
      cell: info => info.getValue(),
    },
    {
      accessorKey: 'sector',
      header: '板块',
      cell: info => info.getValue(),
    },
    {
      id: 'data_status',
      header: '数据状态',
      cell: () => (
        <Tag color="green">
          <CheckCircle className="w-3 h-3 mr-1" /> 可用
        </Tag>
      ),
    },
    {
      id: 'action',
      header: '操作',
      cell: info => (
        <ShadButton
          size="sm"
          onClick={() => handleDownloadSingle(info.row.original.code)}
          className="text-xs px-2 py-1"
        >
          更新数据
        </ShadButton>
      ),
    },
  ];

  // 加载数据
  const loadData = async (page = 1, size = 100) => {
    setLoading(true);
    setError(null);

    // 确保参数是数字类型
    const pageNum = Number(page) || 1;
    const sizeNum = Number(size) || 100;

    console.log('loadData called with:', { page: pageNum, size: sizeNum });
    console.log('Parameter types:', {
      pageType: typeof pageNum,
      sizeType: typeof sizeNum,
      pageValue: pageNum,
      sizeValue: sizeNum
    });

    try {
      console.log('Calling dataAPI.getStockList with params:', { page: pageNum, page_size: sizeNum });

      const [stockResponse, statusResponse] = await Promise.all([
        dataAPI.getStockList({ page: pageNum, page_size: sizeNum }),
        dataAPI.getDataStatus()
      ]);

      if (apiUtils.isSuccess(stockResponse)) {
        // 由于axios拦截器已经返回了response.data，所以stockResponse就是后端的完整响应
        setStockList(stockResponse.data || []);
        setTotalStocks(stockResponse.total || 0);
        setTotalPages(stockResponse.total_pages || 0);
        setCurrentPage(stockResponse.page || 1);
        setPageSize(stockResponse.page_size || 100);
      }

      if (apiUtils.isSuccess(statusResponse)) {
        setDataStatus(statusResponse.data || statusResponse);
      }

    } catch (err) {
      setError(apiUtils.handleError(err, '加载数据失败'));
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    console.log('DataManagementPage mounted, loading initial data...');
    // 直接调用API而不是通过loadData函数
    const loadInitialData = async () => {
      setLoading(true);
      setError(null);

      try {
        console.log('直接调用API...');
        const [stockResponse, statusResponse] = await Promise.all([
          dataAPI.getStockList({ page: 1, page_size: 100 }),
          dataAPI.getDataStatus()
        ]);

        console.log('Stock response:', stockResponse);

        if (apiUtils.isSuccess(stockResponse)) {
          // 由于axios拦截器已经返回了response.data，所以stockResponse就是后端的完整响应
          setStockList(stockResponse.data || []);
          setTotalStocks(stockResponse.total || 0);
          setTotalPages(stockResponse.total_pages || 0);
          setCurrentPage(stockResponse.page || 1);
          setPageSize(stockResponse.page_size || 100);

          console.log('Data loaded successfully:', {
            stockCount: stockResponse.data?.length || 0,
            total: stockResponse.total,
            page: stockResponse.page,
            pageSize: stockResponse.page_size
          });
        } else {
          console.error('API response not successful:', stockResponse);
        }

        if (apiUtils.isSuccess(statusResponse)) {
          setDataStatus(statusResponse.data || statusResponse);
        }

      } catch (err) {
        setError(apiUtils.handleError(err, '加载数据失败'));
      } finally {
        setLoading(false);
      }
    };

    loadInitialData();
  }, []);

  // 处理批量下载
  const handleBatchDownload = () => {
    if (selectedStocks.length === 0) {
      imported_toast.warning('请先选择要下载的股票');
      return;
    }
    setDownloadModalVisible(true);
  };

  // 处理单个股票下载
  const handleDownloadSingle = (stockCode) => {
    setSelectedStocks([stockCode]);
    setDownloadModalVisible(true);
  };

  // 执行下载
  const handleDownload = async () => {
    setLoading(true);
    setDownloadModalVisible(false);

    try {
      const startDate = moment().subtract(1, 'year').format('YYYY-MM-DD');
      const endDate = moment().format('YYYY-MM-DD');

      const response = await dataAPI.downloadData(
        selectedStocks,
        startDate,
        endDate
      );

      if (apiUtils.isSuccess(response)) {
        imported_toast.success('数据下载任务已启动');

        // 模拟下载进度
        setDownloadProgress({ current: 0, total: selectedStocks.length });

        // 这里应该实现真实的进度监控
        // 暂时用定时器模拟
        let current = 0;
        const interval = setInterval(() => {
          current += 1;
          setDownloadProgress({ current, total: selectedStocks.length });

          if (current >= selectedStocks.length) {
            clearInterval(interval);
            setDownloadProgress(null);
            imported_toast.success('数据下载完成');
            loadData(Number(currentPage) || 1, Number(pageSize) || 100); // 重新加载数据状态
          }
        }, 1000);
      }

    } catch (err) {
      setError(apiUtils.handleError(err, '下载数据失败'));
    } finally {
      setLoading(false);
    }
  };

  // 表格行选择配置（暂时简化，后续可扩展为支持多选的 DataTable）

  return (
    <div>
      <h2>数据管理</h2>

      {error && (
        <ShadAlert
          title="错误"
          description={error}
          variant="error"
          closable
          className="mb-4"
          onClose={() => setError(null)}
        />
      )}

      {/* 数据状态概览 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
        <ShadCard>
          <ShadCardContent className="p-4">
            <Stat
              title="股票总数"
              value={dataStatus?.stock_count || 0}
              suffix="只"
            />
          </ShadCardContent>
        </ShadCard>
        <ShadCard>
          <ShadCardContent className="p-4">
            <Stat
              title="数据记录"
              value={dataStatus?.record_count || 0}
              suffix="条"
            />
          </ShadCardContent>
        </ShadCard>
        <ShadCard>
          <ShadCardContent className="p-4">
            <Stat
              title="数据源"
              value={dataStatus?.xt_available ? "xtquant" : "模拟数据"}
            />
          </ShadCardContent>
        </ShadCard>
        <ShadCard>
          <ShadCardContent className="p-4">
            <Stat
              title="数据范围"
              value={
                dataStatus?.date_range?.start && dataStatus?.date_range?.end ?
                `${dataStatus.date_range.start} ~ ${dataStatus.date_range.end}` :
                '无数据'
              }
            />
          </ShadCardContent>
        </ShadCard>
      </div>

      {/* 下载进度 */}
      {downloadProgress && (
        <ShadCard className="mb-4">
          <ShadCardContent>
            <div className="mb-2">
              正在下载数据... ({downloadProgress.current}/{downloadProgress.total})
            </div>
            <ShadProgress
              value={Math.round((downloadProgress.current / downloadProgress.total) * 100)}
            />
          </ShadCardContent>
        </ShadCard>
      )}

      {/* 操作按钮 */}
      <ShadCard className="mb-4">
        <ShadCardContent>
          <div className="flex flex-wrap gap-2">
            <ShadButton
              onClick={handleBatchDownload}
              disabled={selectedStocks.length === 0 || loading}
              className="bg-blue-600 hover:bg-blue-700 text-white inline-flex items-center gap-2"
            >
              <Download className="w-4 h-4" />
              批量下载 ({selectedStocks.length})
            </ShadButton>
            <ShadButton
              onClick={loadData}
              disabled={loading}
              className="inline-flex items-center gap-2"
            >
              <RefreshCw className="w-4 h-4" />
              刷新列表
            </ShadButton>
            <ShadButton
              onClick={() => {
                // 快速选择：选择前10只股票
                const quickSelect = stockList.slice(0, 10).map(stock => stock.code);
                setSelectedStocks(quickSelect);
              }}
            >
              快速选择(前10只)
            </ShadButton>
            <ShadButton
              onClick={() => setSelectedStocks([])}
            >
              清空选择
            </ShadButton>
          </div>
        </ShadCardContent>
      </ShadCard>

      {/* 股票列表 */}
      <ShadCard>
        <ShadCardHeader>
          <div className="flex justify-between items-center">
            <ShadCardTitle>
              股票列表 (第{currentPage}页，共{totalStocks}只)
            </ShadCardTitle>
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <span>每页显示:</span>
              <select
                value={pageSize}
                onChange={(e) => {
                  const newSize = parseInt(e.target.value) || 100;
                  setPageSize(newSize);
                  setCurrentPage(1);
                  loadData(1, newSize);
                }}
                className="border rounded px-2 py-1"
              >
                <option value={50}>50</option>
                <option value={100}>100</option>
                <option value={200}>200</option>
                <option value={500}>500</option>
              </select>
            </div>
          </div>
        </ShadCardHeader>
        <ShadCardContent>
          <DataTable
            columns={columns}
            data={stockList}
          />

          {/* 分页控件 */}
          {totalPages > 1 && (
            <div className="flex justify-between items-center mt-4 pt-4 border-t">
              <div className="text-sm text-gray-600">
                显示第 {(currentPage - 1) * pageSize + 1} - {Math.min(currentPage * pageSize, totalStocks)} 条，共 {totalStocks} 条
              </div>
              <div className="flex items-center gap-2">
                <ShadButton
                  size="sm"
                  disabled={currentPage <= 1}
                  onClick={() => {
                    const newPage = Math.max(1, Number(currentPage) - 1);
                    setCurrentPage(newPage);
                    loadData(newPage, Number(pageSize) || 100);
                  }}
                >
                  上一页
                </ShadButton>

                <div className="flex items-center gap-1">
                  {/* 页码按钮 */}
                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                    let pageNum;
                    if (totalPages <= 5) {
                      pageNum = i + 1;
                    } else if (currentPage <= 3) {
                      pageNum = i + 1;
                    } else if (currentPage >= totalPages - 2) {
                      pageNum = totalPages - 4 + i;
                    } else {
                      pageNum = currentPage - 2 + i;
                    }

                    return (
                      <ShadButton
                        key={pageNum}
                        size="sm"
                        variant={currentPage === pageNum ? "default" : "outline"}
                        onClick={() => {
                          const page = Number(pageNum) || 1;
                          setCurrentPage(page);
                          loadData(page, Number(pageSize) || 100);
                        }}
                        className="w-8 h-8 p-0"
                      >
                        {pageNum}
                      </ShadButton>
                    );
                  })}
                </div>

                <ShadButton
                  size="sm"
                  disabled={currentPage >= totalPages}
                  onClick={() => {
                    const newPage = Number(currentPage) + 1;
                    setCurrentPage(newPage);
                    loadData(newPage, Number(pageSize) || 100);
                  }}
                >
                  下一页
                </ShadButton>
              </div>
            </div>
          )}
        </ShadCardContent>
      </ShadCard>

      {/* 下载配置模态框 */}
      <Modal
        title="下载数据配置"
        open={downloadModalVisible}
        onClose={() => setDownloadModalVisible(false)}
      >
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-1">选中股票</label>
            <div className="text-sm text-gray-600">
              {selectedStocks.length > 5 ?
                `已选择 ${selectedStocks.length} 只股票` :
                selectedStocks.join(', ')
              }
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium mb-1">日期范围</label>
            <DateRangePicker />
          </div>

          <div className="flex gap-2 pt-4">
            <ShadButton
              onClick={handleDownload}
              disabled={loading}
              className="bg-blue-600 hover:bg-blue-700 text-white flex-1"
            >
              开始下载
            </ShadButton>
            <ShadButton
              onClick={() => setDownloadModalVisible(false)}
              className="flex-1"
            >
              取消
            </ShadButton>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default DataManagementPage;
