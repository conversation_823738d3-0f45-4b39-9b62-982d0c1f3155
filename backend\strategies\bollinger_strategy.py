# -*- coding: utf-8 -*-
"""
布林线交易策略
基于配置驱动的布林线策略实现
"""

import backtrader as bt
import numpy as np
from typing import Dict, Any
import logging
from .base_strategy import ConfigurableStrategy

logger = logging.getLogger(__name__)

class BollingerBandsStrategy(ConfigurableStrategy):
    """布林线交易策略"""
    
    strategy_name = "bollinger_bands"
    strategy_version = "2.0.0"
    strategy_description = "基于布林线的趋势跟踪策略"
    
    def init_indicators(self):
        """初始化技术指标"""
        # 获取参数
        bollinger_period = self.get_param_value('bollinger_period', 20)
        bollinger_std = self.get_param_value('bollinger_std', 2.0)
        
        # 为每个数据源创建指标
        for i, data in enumerate(self.datas):
            stock_code = getattr(data, '_name', f'data_{i}')
            
            # 布林线指标
            bollinger = bt.indicators.BollingerBands(
                data.close,
                period=bollinger_period,
                devfactor=bollinger_std
            )
            
            # 移动平均线
            sma_5 = bt.indicators.SimpleMovingAverage(data.close, period=5)
            sma_10 = bt.indicators.SimpleMovingAverage(data.close, period=10)
            sma_20 = bt.indicators.SimpleMovingAverage(data.close, period=20)
            sma_60 = bt.indicators.SimpleMovingAverage(data.close, period=60)
            
            # RSI指标
            rsi = bt.indicators.RelativeStrengthIndex(data.close, period=14)
            
            # 成交量移动平均
            volume_sma = bt.indicators.SimpleMovingAverage(data.volume, period=20)
            
            # 存储指标
            self.indicators[stock_code] = {
                'bollinger': bollinger,
                'sma_5': sma_5,
                'sma_10': sma_10,
                'sma_20': sma_20,
                'sma_60': sma_60,
                'rsi': rsi,
                'volume_sma': volume_sma
            }
        
        logger.info(f"为 {len(self.datas)} 个数据源初始化了技术指标")
    
    def generate_signals(self, data) -> Dict[str, Any]:
        """生成交易信号"""
        stock_code = getattr(data, '_name', 'Unknown')
        
        if stock_code not in self.indicators:
            return {'buy': False, 'sell': False}
        
        indicators = self.indicators[stock_code]
        
        # 确保有足够的数据
        if len(data) < 60:
            return {'buy': False, 'sell': False}
        
        # 获取当前值
        current_price = data.close[0]
        bollinger = indicators['bollinger']
        
        # 布林线值
        bb_upper = bollinger.lines.top[0]
        bb_middle = bollinger.lines.mid[0]
        bb_lower = bollinger.lines.bot[0]
        
        # 移动平均线值
        sma_5 = indicators['sma_5'][0]
        sma_10 = indicators['sma_10'][0]
        sma_20 = indicators['sma_20'][0]
        sma_60 = indicators['sma_60'][0]
        
        # RSI值
        rsi = indicators['rsi'][0]
        
        # 成交量
        current_volume = data.volume[0]
        volume_sma = indicators['volume_sma'][0]
        
        # 生成买入信号
        buy_signal = self._check_buy_conditions(
            current_price, bb_upper, bb_middle, bb_lower,
            sma_5, sma_10, sma_20, sma_60, rsi, current_volume, volume_sma
        )
        
        # 生成卖出信号
        sell_signal = self._check_sell_conditions(
            current_price, bb_upper, bb_middle, bb_lower,
            sma_5, sma_10, sma_20, sma_60, rsi
        )
        
        return {
            'buy': buy_signal,
            'sell': sell_signal,
            'price': current_price,
            'bb_upper': bb_upper,
            'bb_middle': bb_middle,
            'bb_lower': bb_lower,
            'rsi': rsi,
            'volume_ratio': current_volume / volume_sma if volume_sma > 0 else 1
        }
    
    def _check_buy_conditions(self, price, bb_upper, bb_middle, bb_lower,
                            sma_5, sma_10, sma_20, sma_60, rsi, volume, volume_sma) -> bool:
        """检查买入条件"""
        
        # 获取参数
        buy_zone_ratio = self.get_param_value('buy_zone_ratio', 0.3)
        min_rsi = self.get_param_value('min_rsi', 30)
        max_rsi = self.get_param_value('max_rsi', 70)
        min_volume_ratio = self.get_param_value('min_volume_ratio', 1.2)
        
        # 1. 价格在布林线买入区间内
        buy_threshold = bb_middle + (bb_upper - bb_middle) * buy_zone_ratio
        price_condition = bb_middle <= price <= buy_threshold
        
        # 2. 趋势条件 - 简化版本
        trend_condition = sma_5 > sma_20 and sma_20 > sma_60
        
        # 3. RSI条件 - 避免超买
        rsi_condition = min_rsi < rsi < max_rsi
        
        # 4. 成交量条件 - 放量
        volume_condition = volume > volume_sma * min_volume_ratio if volume_sma > 0 else True
        
        # 5. 价格不在布林线下轨附近（避免下跌趋势）
        not_near_lower = price > bb_lower * 1.02
        
        # 综合判断
        buy_signal = (price_condition and trend_condition and 
                     rsi_condition and volume_condition and not_near_lower)
        
        if buy_signal:
            logger.debug(f"买入信号触发: 价格={price:.2f}, 布林线中线={bb_middle:.2f}, RSI={rsi:.1f}")
        
        return buy_signal
    
    def _check_sell_conditions(self, price, bb_upper, bb_middle, bb_lower,
                             sma_5, sma_10, sma_20, sma_60, rsi) -> bool:
        """检查卖出条件"""
        
        # 获取参数
        stop_loss = self.get_param_value('stop_loss', 0.03)
        profit_target = self.get_param_value('profit_target', 0.1)
        max_rsi = self.get_param_value('max_rsi', 70)
        
        # 1. 跌破布林线中线
        below_middle = price < bb_middle
        
        # 2. RSI超买
        rsi_overbought = rsi > max_rsi
        
        # 3. 价格接近布林线上轨（获利了结）
        near_upper = price > bb_upper * 0.98
        
        # 4. 趋势转弱
        trend_weak = sma_5 < sma_10
        
        # 综合判断
        sell_signal = below_middle or rsi_overbought or (near_upper and trend_weak)
        
        if sell_signal:
            logger.debug(f"卖出信号触发: 价格={price:.2f}, 布林线中线={bb_middle:.2f}, RSI={rsi:.1f}")
        
        return sell_signal
    
    def process_sell_signal(self, data, signals: Dict[str, Any]):
        """处理卖出信号 - 增强版本"""
        stock_code = getattr(data, '_name', 'Unknown')
        
        # 检查是否持仓
        position = self.getposition(data)
        if position.size <= 0:
            return
        
        # 获取买入价格（如果有记录）
        buy_price = getattr(position, 'price', data.close[0])
        current_price = data.close[0]
        
        # 计算盈亏
        profit_rate = (current_price - buy_price) / buy_price if buy_price > 0 else 0
        
        # 止损检查
        stop_loss = self.get_param_value('stop_loss', 0.03)
        if profit_rate <= -stop_loss:
            logger.info(f"止损卖出: {stock_code} 亏损{profit_rate:.2%}")
        
        # 执行卖出
        super().process_sell_signal(data, signals)
    
    @classmethod
    def get_default_config(cls) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            "name": cls.strategy_name,
            "description": cls.strategy_description,
            "version": cls.strategy_version,
            "author": "System",
            "parameters": {
                "bollinger_period": {
                    "value": 20,
                    "type": "int",
                    "min": 5,
                    "max": 100,
                    "description": "布林线计算周期"
                },
                "bollinger_std": {
                    "value": 2.0,
                    "type": "float",
                    "min": 1.0,
                    "max": 3.0,
                    "description": "布林线标准差倍数"
                },
                "max_positions": {
                    "value": 5,
                    "type": "int",
                    "min": 1,
                    "max": 20,
                    "description": "最大持仓数量"
                },
                "position_size": {
                    "value": 0.1,
                    "type": "float",
                    "min": 0.01,
                    "max": 1.0,
                    "description": "单笔仓位大小"
                },
                "stop_loss": {
                    "value": 0.03,
                    "type": "float",
                    "min": 0.01,
                    "max": 0.1,
                    "description": "止损比例"
                },
                "profit_target": {
                    "value": 0.1,
                    "type": "float",
                    "min": 0.02,
                    "max": 0.5,
                    "description": "目标盈利比例"
                },
                "buy_zone_ratio": {
                    "value": 0.3,
                    "type": "float",
                    "min": 0.1,
                    "max": 0.8,
                    "description": "买入区间比例"
                },
                "min_rsi": {
                    "value": 30,
                    "type": "int",
                    "min": 10,
                    "max": 50,
                    "description": "RSI最小值"
                },
                "max_rsi": {
                    "value": 70,
                    "type": "int",
                    "min": 50,
                    "max": 90,
                    "description": "RSI最大值"
                },
                "min_volume_ratio": {
                    "value": 1.2,
                    "type": "float",
                    "min": 0.5,
                    "max": 3.0,
                    "description": "最小成交量比例"
                },
                "stock_pool": {
                    "value": ["000001.SZ", "000002.SZ", "600000.SH", "600036.SH", "600519.SH"],
                    "type": "list",
                    "description": "股票池"
                }
            }
        }
