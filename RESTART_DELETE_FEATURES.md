# 重启和删除策略功能

## ✅ **新增功能完成！**

### **🎯 功能1：重启暂停的策略**

#### **后端实现**
- ✅ `POST /api/live/restart/{task_id}` - 重启API
- ✅ `restart_live_trading()` - 重启逻辑
- ✅ 状态验证：只能重启已停止的策略

#### **前端实现**
- ✅ 绿色播放按钮 `▶️`
- ✅ 只在停止状态显示
- ✅ 悬停效果：绿色背景
- ✅ Tooltip提示："重启策略"

#### **使用方法**
1. 停止一个运行中的策略
2. 在策略表格中会出现绿色的重启按钮
3. 点击重启按钮
4. 确认重启操作
5. 策略状态变为"运行中"

### **🎯 功能2：删除策略**

#### **后端实现**
- ✅ `DELETE /api/live/delete/{task_id}` - 删除API
- ✅ `delete_live_trading()` - 删除逻辑
- ✅ 安全机制：只能删除已停止的策略

#### **前端实现**
- ✅ 灰色垃圾桶按钮 `🗑️`
- ✅ 只在停止状态显示
- ✅ 悬停效果：变红色
- ✅ Tooltip提示："删除策略"
- ✅ 二次确认对话框

#### **使用方法**
1. 停止一个运行中的策略
2. 在策略表格中会出现垃圾桶删除按钮
3. 点击删除按钮
4. 确认删除操作（不可恢复）
5. 策略从列表中移除

## 🎨 **UI界面效果**

### **运行中的策略**
```
| 策略名称 | 状态 | 交易模式 | 操作 |
|---------|------|---------|------|
| bollinger_bands_12345678 | 🟢 运行中 | 🟢 纸上交易 | [👁️] [🛑] |
```

**按钮说明：**
- 👁️ **查看按钮**：蓝色，查看策略详情
- 🛑 **停止按钮**：红色，停止策略

### **已停止的策略**
```
| 策略名称 | 状态 | 交易模式 | 操作 |
|---------|------|---------|------|
| bollinger_bands_87654321 | 🔴 已停止 | 🔴 实盘交易 | [👁️] [▶️] [🗑️] |
```

**按钮说明：**
- 👁️ **查看按钮**：蓝色，查看策略详情
- ▶️ **重启按钮**：绿色，重启策略
- 🗑️ **删除按钮**：灰色→红色，删除策略

## 🔧 **技术实现**

### **动态按钮显示逻辑**
```javascript
const isRunning = strategy.status === 'running';
const isStopped = strategy.status === 'stopped';

return (
  <div className="flex items-center gap-1">
    {/* 查看按钮 - 始终显示 */}
    <ViewButton />
    
    {/* 重启按钮 - 只在停止状态显示 */}
    {isStopped && <RestartButton />}
    
    {/* 停止按钮 - 只在运行状态显示 */}
    {isRunning && <StopButton />}
    
    {/* 删除按钮 - 只在停止状态显示 */}
    {isStopped && <DeleteButton />}
  </div>
);
```

### **API调用流程**
```javascript
// 重启策略
const handleRestartStrategy = async (strategy) => {
  if (!confirm(`确定要重启策略 ${strategy.name} 吗？`)) return;
  
  const response = await liveAPI.restartLiveTrading(strategy.task_id);
  if (apiUtils.isSuccess(response)) {
    toast.success(`策略 ${strategy.name} 已重启`);
    loadRunningStrategies(); // 刷新列表
  }
};

// 删除策略
const handleDeleteStrategy = async (strategy) => {
  if (!confirm(`确定要删除策略 ${strategy.name} 吗？\n\n注意：删除后将无法恢复！`)) return;
  
  const response = await liveAPI.deleteLiveTrading(strategy.task_id);
  if (apiUtils.isSuccess(response)) {
    toast.success(`策略 ${strategy.name} 已删除`);
    loadRunningStrategies(); // 刷新列表
  }
};
```

## 🛡️ **安全机制**

### **重启限制**
- ✅ 只能重启状态为'stopped'的策略
- ✅ 运行中的策略不显示重启按钮
- ✅ API层面验证策略状态

### **删除限制**
- ✅ 只能删除状态为'stopped'的策略
- ✅ 运行中的策略不显示删除按钮
- ✅ 二次确认对话框防止误删
- ✅ 删除后无法恢复的明确提示

### **状态一致性**
- ✅ 操作后自动刷新策略列表
- ✅ 实时更新按钮显示状态
- ✅ Toast消息确认操作结果

## 🚀 **使用指南**

### **完整操作流程**

1. **启动策略**
   ```
   添加策略 → 配置参数 → 启动交易
   状态：运行中
   按钮：[👁️查看] [🛑停止]
   ```

2. **停止策略**
   ```
   点击停止按钮 → 确认停止
   状态：已停止
   按钮：[👁️查看] [▶️重启] [🗑️删除]
   ```

3. **重启策略**
   ```
   点击重启按钮 → 确认重启
   状态：运行中
   按钮：[👁️查看] [🛑停止]
   ```

4. **删除策略**
   ```
   点击删除按钮 → 确认删除（不可恢复）
   结果：策略从列表中移除
   ```

### **最佳实践**

1. **测试建议**
   - 先使用纸上交易模式测试
   - 确认策略逻辑正确后再用实盘
   - 定期检查策略表现

2. **管理建议**
   - 及时停止表现不佳的策略
   - 删除不再需要的策略记录
   - 保持策略列表整洁

3. **安全建议**
   - 删除前确认策略不再需要
   - 重要策略数据建议备份
   - 谨慎操作实盘交易策略

## 🎯 **测试验证**

访问 `http://localhost:3000/multi-strategy` 进行测试：

1. ✅ 启动一个策略，观察运行状态按钮
2. ✅ 停止策略，观察停止状态按钮
3. ✅ 测试重启功能，验证状态变化
4. ✅ 测试删除功能，确认策略移除
5. ✅ 验证按钮悬停效果和提示

现在您拥有了完整的策略生命周期管理功能！
