#!/usr/bin/env python3
"""
简单测试选股功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from backend.stock_selection.stock_selector import StockSelector, SelectionCriteria
from backend.core.logger import get_logger

logger = get_logger(__name__)

def test_simple_selection():
    """测试简单选股"""
    print("🔍 测试简单选股功能")
    print("=" * 50)
    
    # 创建选股器
    selector = StockSelector()
    
    # 创建非常宽松的条件
    criteria = SelectionCriteria(
        volume_min=0.1,  # 成交量比例大于0.1（很宽松）
        condition_logic="flexible",
        lookback_days=30,
        min_trading_days=20
    )
    
    print(f"📋 选股条件:")
    print(f"   成交量比例 > {criteria.volume_min}")
    print(f"   条件逻辑: {criteria.condition_logic}")
    print(f"   回看天数: {criteria.lookback_days}")
    print(f"   最少交易天数: {criteria.min_trading_days}")
    
    # 执行选股，但只处理前100只股票来快速测试
    try:
        print("\n🚀 开始执行选股（仅前100只股票）...")
        
        # 获取股票列表
        all_stocks = selector._get_stocks_from_xttrader()
        if not all_stocks:
            print("❌ 无法获取股票列表")
            return
        
        print(f"📊 总股票数: {len(all_stocks)}")
        print(f"🎯 测试前100只股票")
        
        # 手动处理前100只股票
        from datetime import datetime, timedelta
        
        end_date = datetime.now()
        start_date = end_date - timedelta(days=criteria.lookback_days + 30)
        
        selected_stocks = []
        processed_count = 0
        
        for stock in all_stocks[:100]:  # 只处理前100只
            try:
                stock_code = stock['code']
                stock_name = stock.get('name', stock_code)
                
                processed_count += 1
                
                if processed_count % 10 == 0:
                    print(f"   进度: {processed_count}/100")
                
                # 排除ST股票
                if criteria.exclude_st and ('ST' in stock_name or '*ST' in stock_name):
                    continue
                
                # 获取股票数据
                df = selector._get_stock_data_from_xttrader(
                    stock_code=stock_code,
                    start_date=start_date.strftime('%Y%m%d'),
                    end_date=end_date.strftime('%Y%m%d')
                )
                
                if df is None or len(df) < criteria.min_trading_days:
                    continue
                
                # 排除新股
                if criteria.exclude_new_stock and len(df) < 60:
                    continue
                
                # 计算技术指标
                indicators = selector.calculate_technical_indicators(df)
                if not indicators:
                    continue
                
                # 计算Alpha101因子
                alpha_factors = selector.calculate_alpha101_factors(df)
                
                # 检查选股条件
                passed, score = selector.check_selection_criteria(indicators, alpha_factors, criteria)
                
                if passed and score > 0:
                    from backend.stock_selection.stock_selector import StockScore
                    stock_score = StockScore(
                        stock_code=stock_code,
                        stock_name=stock_name,
                        score=score,
                        indicators=indicators,
                        alpha_factors=alpha_factors,
                        selection_date=datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    )
                    selected_stocks.append(stock_score)
                    print(f"✅ 选中: {stock_code} - {stock_name}, 评分: {score:.2f}")
                
            except Exception as e:
                print(f"❌ 处理 {stock.get('code', 'unknown')} 失败: {e}")
                continue
        
        print(f"\n📊 测试结果:")
        print(f"   处理股票数: {processed_count}")
        print(f"   选中股票数: {len(selected_stocks)}")
        
        if selected_stocks:
            print(f"\n🎯 选中的股票:")
            for i, stock in enumerate(selected_stocks[:10]):
                print(f"  {i+1}. {stock.stock_code} - {stock.stock_name}")
                print(f"     评分: {stock.score:.2f}")
                print(f"     成交量比: {stock.indicators.get('volume_ratio', 'N/A'):.4f}")
                print(f"     RSI: {stock.indicators.get('rsi', 'N/A'):.2f}")
                print()
        else:
            print("❌ 没有选中任何股票")
            
            # 分析原因
            print("\n🔍 分析可能原因:")
            print("   让我们检查几只股票的详细情况...")
            
            for i, stock in enumerate(all_stocks[:5]):
                stock_code = stock['code']
                print(f"\n   股票 {i+1}: {stock_code}")
                
                try:
                    df = selector._get_stock_data_from_xttrader(
                        stock_code=stock_code,
                        start_date=start_date.strftime('%Y%m%d'),
                        end_date=end_date.strftime('%Y%m%d')
                    )
                    
                    if df is not None:
                        print(f"     数据: ✅ {len(df)}天")
                        
                        indicators = selector.calculate_technical_indicators(df)
                        if indicators:
                            volume_ratio = indicators.get('volume_ratio', 0)
                            print(f"     成交量比: {volume_ratio:.4f}")
                            
                            alpha_factors = selector.calculate_alpha101_factors(df)
                            passed, score = selector.check_selection_criteria(indicators, alpha_factors, criteria)
                            print(f"     条件检查: {'通过' if passed else '不通过'}, 评分: {score:.2f}")
                        else:
                            print(f"     技术指标: ❌")
                    else:
                        print(f"     数据: ❌")
                        
                except Exception as e:
                    print(f"     错误: {e}")
        
    except Exception as e:
        print(f"❌ 选股测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_simple_selection()
