#!/usr/bin/env python3
"""
测试异步选股系统
"""

import requests
import time
import json

def test_async_selection():
    """测试异步选股功能"""
    print("🧪 测试异步选股系统")
    print("=" * 50)
    
    # 1. 启动选股任务
    print("📤 启动选股任务...")
    
    payload = {
        "criteria": {
            "volume_min": 0.1,
            "condition_logic": "flexible"
        },
        "custom_name": "异步选股测试",
        "max_results": 10
    }
    
    try:
        response = requests.post(
            'http://localhost:8000/api/stock-selection/select',
            json=payload,
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                task_id = data['data']['task_id']
                print(f"✅ 任务启动成功！")
                print(f"   任务ID: {task_id}")
                print(f"   任务名称: {data['data']['custom_name']}")
                
                # 2. 轮询任务状态
                print(f"\n🔄 开始轮询任务状态...")
                poll_task_status(task_id)
                
            else:
                print(f"❌ 启动任务失败: {data.get('message', '未知错误')}")
        else:
            print(f"❌ API请求失败: {response.status_code}")
            print(f"   响应: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")

def poll_task_status(task_id):
    """轮询任务状态"""
    start_time = time.time()
    last_progress = -1
    
    while True:
        try:
            response = requests.get(
                f'http://localhost:8000/api/stock-selection/task/{task_id}',
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    status = data['data']
                    
                    # 只在进度变化时输出
                    if status['progress'] != last_progress:
                        elapsed = time.time() - start_time
                        print(f"   [{elapsed:.1f}s] {status['status']} - {status['progress']}% - {status['message']}")
                        
                        if status['processed_count'] > 0:
                            print(f"      处理进度: {status['processed_count']}/{status['total_count']}")
                            print(f"      已选中: {status['selected_count']} 只股票")
                            if status['current_stock']:
                                print(f"      当前处理: {status['current_stock']}")
                        
                        last_progress = status['progress']
                    
                    # 检查任务是否完成
                    if status['status'] == 'completed':
                        elapsed = time.time() - start_time
                        print(f"\n🎉 选股完成！总耗时: {elapsed:.1f}秒")
                        
                        if 'result' in status:
                            result = status['result']
                            print(f"📊 选股结果:")
                            print(f"   总选中: {result['total_selected']} 只股票")
                            print(f"   文件名: {result['filename']}")
                            
                            if result['selected_stocks']:
                                print(f"\n🏆 前5只股票:")
                                for i, stock in enumerate(result['selected_stocks'][:5]):
                                    print(f"   {i+1}. {stock['stock_code']} - {stock['stock_name']} (评分: {stock['score']:.2f})")
                        
                        break
                        
                    elif status['status'] == 'failed':
                        elapsed = time.time() - start_time
                        print(f"\n❌ 选股失败！总耗时: {elapsed:.1f}秒")
                        if status.get('error'):
                            print(f"   错误: {status['error']}")
                        break
                    
                    # 等待2秒后继续轮询
                    time.sleep(2)
                    
                else:
                    print(f"❌ 获取任务状态失败: {data.get('message', '未知错误')}")
                    break
            else:
                print(f"❌ API请求失败: {response.status_code}")
                break
                
        except Exception as e:
            print(f"❌ 轮询异常: {e}")
            break

def test_task_not_found():
    """测试不存在的任务ID"""
    print("\n🧪 测试不存在的任务ID...")
    
    fake_task_id = "fake-task-id-12345"
    
    try:
        response = requests.get(
            f'http://localhost:8000/api/stock-selection/task/{fake_task_id}',
            timeout=5
        )
        
        if response.status_code == 404:
            print("✅ 正确返回404错误")
        else:
            print(f"❌ 期望404，实际返回: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")

def main():
    """主函数"""
    print("🎯 QMT-TRADER 异步选股系统测试")
    print("=" * 60)
    
    # 测试异步选股
    test_async_selection()
    
    # 测试错误情况
    test_task_not_found()
    
    print("\n" + "=" * 60)
    print("✅ 异步选股系统测试完成")
    
    print("\n💡 现在您可以:")
    print("   1. 访问 http://localhost:3000/stock-selection")
    print("   2. 点击'开始选股'按钮")
    print("   3. 设置选股条件")
    print("   4. 点击'开始选股'启动异步任务")
    print("   5. 查看实时进度显示")
    print("   6. 关闭页面不会中断选股进程")

if __name__ == "__main__":
    main()
