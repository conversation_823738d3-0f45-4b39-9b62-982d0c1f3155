#!/usr/bin/env python3
"""
测试QMTTrader方法修复
"""

import sys
import os
import requests
import json
import time

# 添加项目路径
sys.path.append(os.path.dirname(__file__))

def test_qmt_trader_methods():
    """测试QMTTrader方法是否存在"""
    try:
        print("🔍 测试QMTTrader方法...")
        
        # 导入QMTTrader
        from backend.trading.qmt_trader import QMTTrader
        
        # 创建实例
        trader = QMTTrader()
        
        # 检查必要的方法是否存在
        required_methods = [
            'get_account_info',
            'get_positions', 
            'buy',
            'sell',
            'sell_stock',
            'query_orders',
            'query_trades',
            'get_order_status'
        ]
        
        print("📋 检查QMTTrader方法:")
        all_methods_exist = True
        
        for method_name in required_methods:
            if hasattr(trader, method_name):
                method = getattr(trader, method_name)
                if callable(method):
                    print(f"  ✅ {method_name}: 存在且可调用")
                else:
                    print(f"  ❌ {method_name}: 存在但不可调用")
                    all_methods_exist = False
            else:
                print(f"  ❌ {method_name}: 不存在")
                all_methods_exist = False
        
        if all_methods_exist:
            print("✅ 所有必要方法都存在")
        else:
            print("❌ 部分方法缺失")
            return False
        
        # 测试方法调用（在非QMT环境下会返回默认值）
        print("\n🧪 测试方法调用:")
        
        # 测试get_account_info
        try:
            account_info = trader.get_account_info()
            print(f"  ✅ get_account_info: 返回 {type(account_info).__name__}")
        except Exception as e:
            print(f"  ⚠️ get_account_info: 异常 {e}")
        
        # 测试get_positions
        try:
            positions = trader.get_positions()
            print(f"  ✅ get_positions: 返回 {type(positions).__name__} (长度: {len(positions)})")
        except Exception as e:
            print(f"  ⚠️ get_positions: 异常 {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_strategy_startup():
    """测试策略启动"""
    try:
        print("\n🚀 测试策略启动...")
        
        # 启动一个策略
        test_data = {
            "strategy_name": "bollinger_bands",
            "initial_capital": 100000,
            "commission": 0.001,
            "stock_codes": ["000001.SZ"],
            "paper_trading": True,
            "strategy_params": {
                "period": 20,
                "std_dev": 2.0
            }
        }
        
        print("📤 启动测试策略...")
        response = requests.post(
            'http://localhost:8000/api/live/start',
            json=test_data,
            headers={'Content-Type': 'application/json'}
        )
        
        if response.status_code != 200:
            print(f"❌ 启动策略失败: {response.status_code}")
            print(f"   响应: {response.text}")
            return False
        
        data = response.json()
        if not data.get('success'):
            print(f"❌ 启动策略失败: {data}")
            return False
        
        task_id = data.get('task_id')
        print(f"✅ 策略启动成功: {task_id[:8]}...")
        
        # 等待策略初始化
        time.sleep(3)
        
        # 检查策略状态
        print("📋 检查策略状态...")
        response = requests.get('http://localhost:8000/api/live/results')
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                results = data.get('data', [])
                strategy = next((r for r in results if r['task_id'] == task_id), None)
                
                if strategy:
                    status = strategy.get('status', 'unknown')
                    print(f"📊 策略状态: {status}")
                    
                    if status == 'error':
                        print("❌ 策略状态为错误，可能仍有问题")
                        return False
                    elif status in ['running', 'stopped']:
                        print("✅ 策略状态正常")
                        
                        # 清理测试策略
                        print(f"🧹 清理测试策略: {task_id[:8]}...")
                        try:
                            requests.post(f'http://localhost:8000/api/live/stop/{task_id}')
                            time.sleep(1)
                            requests.delete(f'http://localhost:8000/api/live/delete/{task_id}')
                            print("✅ 测试策略已清理")
                        except:
                            print("⚠️ 清理失败，请手动清理")
                        
                        return True
                    else:
                        print(f"⚠️ 策略状态未知: {status}")
                        return False
                else:
                    print("❌ 未找到策略")
                    return False
            else:
                print(f"❌ 获取策略失败: {data}")
                return False
        else:
            print(f"❌ 获取策略API错误: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 测试策略启动失败: {e}")
        return False

def provide_solution():
    """提供解决方案"""
    print("\n💡 问题解决方案:")
    
    print("✅ 已修复的问题:")
    print("  1. 添加了 get_account_info() 方法")
    print("  2. 添加了 get_positions() 方法")
    print("  3. 添加了 buy() 方法")
    print("  4. 添加了 sell() 方法")
    
    print("\n🔧 修复内容:")
    print("  - get_account_info(): 获取账户资金信息")
    print("  - get_positions(): 获取持仓信息")
    print("  - buy(): 买入股票接口")
    print("  - sell(): 卖出股票接口")
    
    print("\n📋 如果策略仍然失败:")
    print("  1. 检查QMT连接状态")
    print("  2. 确认账户配置正确")
    print("  3. 检查股票代码格式")
    print("  4. 查看完整的错误日志")
    
    print("\n🎯 预期效果:")
    print("  - 策略启动不再出现 'get_account_info' 错误")
    print("  - 策略状态应该为 'running' 而不是 'error'")
    print("  - WebSocket订阅应该保持连接")

if __name__ == "__main__":
    print("🧪 QMTTrader方法修复测试")
    print("=" * 60)
    
    # 测试QMTTrader方法
    methods_ok = test_qmt_trader_methods()
    
    if methods_ok:
        # 测试策略启动
        startup_ok = test_strategy_startup()
        
        if startup_ok:
            print("\n🎉 所有测试通过！")
            print("✅ QMTTrader方法修复成功")
            print("✅ 策略启动正常")
        else:
            print("\n⚠️ 方法修复成功，但策略启动仍有问题")
    else:
        print("\n❌ QMTTrader方法仍有问题")
    
    # 提供解决方案
    provide_solution()
    
    print("\n" + "=" * 60)
    print("🎯 修复总结:")
    print("✅ 添加了缺失的QMTTrader方法")
    print("✅ 修复了策略启动时的方法调用错误")
    print("✅ 现在策略应该能正常启动了")
