"""
配置驱动策略框架
支持通过JSON/YAML配置文件定义策略逻辑，无需编写代码即可创建复杂策略
"""

import json
import yaml
from typing import Dict, Any, List, Optional, Union
from dataclasses import dataclass, asdict
from abc import ABC, abstractmethod
import pandas as pd
import numpy as np
from pathlib import Path

from backend.core.logger import get_logger
from backend.strategies.base_strategy import AbstractStrategy

logger = get_logger(__name__)

@dataclass
class ConditionConfig:
    """条件配置"""
    type: str                    # 条件类型
    name: str                    # 条件名称
    enabled: bool = True         # 是否启用
    params: Dict[str, Any] = None  # 条件参数
    weight: float = 1.0          # 条件权重
    description: str = ""        # 条件描述

    def __post_init__(self):
        if self.params is None:
            self.params = {}

@dataclass
class SignalConfig:
    """信号配置"""
    name: str                           # 信号名称
    conditions: List[ConditionConfig]   # 条件列表
    logic: str = "AND"                  # 逻辑关系：AND/OR
    min_conditions: int = 1             # 最少满足条件数
    signal_strength: float = 1.0        # 信号强度
    description: str = ""               # 信号描述

@dataclass
class RiskConfig:
    """风险管理配置"""
    stop_loss: Optional[float] = None      # 止损比例
    take_profit: Optional[float] = None    # 止盈比例
    trailing_stop: Optional[float] = None  # 移动止损
    max_loss_per_trade: Optional[float] = None  # 单笔最大亏损
    max_positions: int = 5                 # 最大持仓数
    position_size: float = 0.2             # 仓位大小
    risk_per_trade: float = 0.02           # 单笔风险比例

@dataclass
class StrategyConfigSchema:
    """策略配置模式"""
    name: str                           # 策略名称
    display_name: str                   # 显示名称
    description: str                    # 策略描述
    version: str                        # 版本号
    author: str                         # 作者
    category: str                       # 策略分类
    
    # 信号配置
    buy_signals: List[SignalConfig]     # 买入信号
    sell_signals: List[SignalConfig]    # 卖出信号
    
    # 风险管理
    risk_management: RiskConfig         # 风险管理配置
    
    # 其他配置
    universe: List[str] = None          # 股票池
    rebalance_frequency: str = "daily"  # 调仓频率
    benchmark: str = "000300.SH"        # 基准指数
    
    # 技术指标配置
    indicators: Dict[str, Dict[str, Any]] = None  # 技术指标配置
    
    # 自定义参数
    custom_params: Dict[str, Any] = None  # 自定义参数

    def __post_init__(self):
        if self.universe is None:
            self.universe = []
        if self.indicators is None:
            self.indicators = {}
        if self.custom_params is None:
            self.custom_params = {}

class ConditionEvaluator(ABC):
    """条件评估器基类"""
    
    @abstractmethod
    def evaluate(self, data: pd.DataFrame, params: Dict[str, Any]) -> bool:
        """评估条件是否满足"""
        pass
    
    @abstractmethod
    def get_description(self) -> str:
        """获取条件描述"""
        pass

class PriceCondition(ConditionEvaluator):
    """价格条件评估器"""
    
    def evaluate(self, data: pd.DataFrame, params: Dict[str, Any]) -> bool:
        """评估价格条件"""
        if len(data) < 2:
            return False
        
        condition_type = params.get('type', 'above')
        threshold = params.get('threshold', 0)
        field = params.get('field', 'close')
        
        current_value = data[field].iloc[-1]
        
        if condition_type == 'above':
            return current_value > threshold
        elif condition_type == 'below':
            return current_value < threshold
        elif condition_type == 'between':
            min_val = params.get('min', 0)
            max_val = params.get('max', float('inf'))
            return min_val <= current_value <= max_val
        
        return False
    
    def get_description(self) -> str:
        return "价格条件：基于价格字段的比较条件"

class MACondition(ConditionEvaluator):
    """移动平均线条件评估器"""
    
    def evaluate(self, data: pd.DataFrame, params: Dict[str, Any]) -> bool:
        """评估均线条件"""
        period = params.get('period', 20)
        condition_type = params.get('type', 'above')
        
        if len(data) < period:
            return False
        
        ma = data['close'].rolling(window=period).mean()
        current_price = data['close'].iloc[-1]
        current_ma = ma.iloc[-1]
        
        if condition_type == 'above':
            return current_price > current_ma
        elif condition_type == 'below':
            return current_price < current_ma
        elif condition_type == 'cross_up':
            if len(data) < 2:
                return False
            prev_price = data['close'].iloc[-2]
            prev_ma = ma.iloc[-2]
            return (current_price > current_ma) and (prev_price <= prev_ma)
        elif condition_type == 'cross_down':
            if len(data) < 2:
                return False
            prev_price = data['close'].iloc[-2]
            prev_ma = ma.iloc[-2]
            return (current_price < current_ma) and (prev_price >= prev_ma)
        
        return False
    
    def get_description(self) -> str:
        return "均线条件：基于移动平均线的条件判断"

class VolumeCondition(ConditionEvaluator):
    """成交量条件评估器"""
    
    def evaluate(self, data: pd.DataFrame, params: Dict[str, Any]) -> bool:
        """评估成交量条件"""
        period = params.get('period', 20)
        multiplier = params.get('multiplier', 1.5)
        
        if len(data) < period:
            return False
        
        volume_ma = data['volume'].rolling(window=period).mean()
        current_volume = data['volume'].iloc[-1]
        current_volume_ma = volume_ma.iloc[-1]
        
        return current_volume > (current_volume_ma * multiplier)
    
    def get_description(self) -> str:
        return "成交量条件：基于成交量放大的条件判断"

class RSICondition(ConditionEvaluator):
    """RSI条件评估器"""
    
    def evaluate(self, data: pd.DataFrame, params: Dict[str, Any]) -> bool:
        """评估RSI条件"""
        period = params.get('period', 14)
        threshold = params.get('threshold', 30)
        condition_type = params.get('type', 'below')
        
        if len(data) < period + 1:
            return False
        
        # 计算RSI
        delta = data['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        
        current_rsi = rsi.iloc[-1]
        
        if condition_type == 'below':
            return current_rsi < threshold
        elif condition_type == 'above':
            return current_rsi > threshold
        
        return False
    
    def get_description(self) -> str:
        return "RSI条件：基于相对强弱指标的条件判断"

# 条件评估器注册表
CONDITION_EVALUATORS = {
    'price': PriceCondition(),
    'ma': MACondition(),
    'volume': VolumeCondition(),
    'rsi': RSICondition(),
}

class MultiSignalStrategy(AbstractStrategy):
    """多信号组合策略"""
    
    def __init__(self, config_path: Optional[str] = None, config_dict: Optional[Dict[str, Any]] = None):
        super().__init__()
        
        self.config_schema: Optional[StrategyConfigSchema] = None
        self.indicators_cache: Dict[str, pd.DataFrame] = {}
        
        # 加载配置
        if config_path:
            self.load_config_from_file(config_path)
        elif config_dict:
            self.load_config_from_dict(config_dict)
    
    def load_config_from_file(self, config_path: str):
        """从文件加载配置"""
        config_file = Path(config_path)
        
        if not config_file.exists():
            raise FileNotFoundError(f"配置文件不存在: {config_path}")
        
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                if config_file.suffix.lower() in ['.yaml', '.yml']:
                    config_data = yaml.safe_load(f)
                else:
                    config_data = json.load(f)
            
            self.load_config_from_dict(config_data)
            logger.info(f"✅ 从文件加载策略配置: {config_path}")
            
        except Exception as e:
            logger.error(f"❌ 加载配置文件失败: {e}")
            raise
    
    def load_config_from_dict(self, config_dict: Dict[str, Any]):
        """从字典加载配置"""
        try:
            # 解析信号配置
            buy_signals = []
            for signal_data in config_dict.get('buy_signals', []):
                conditions = []
                for cond_data in signal_data.get('conditions', []):
                    condition = ConditionConfig(**cond_data)
                    conditions.append(condition)
                
                signal = SignalConfig(
                    name=signal_data['name'],
                    conditions=conditions,
                    logic=signal_data.get('logic', 'AND'),
                    min_conditions=signal_data.get('min_conditions', 1),
                    signal_strength=signal_data.get('signal_strength', 1.0),
                    description=signal_data.get('description', '')
                )
                buy_signals.append(signal)
            
            # 解析卖出信号（类似买入信号）
            sell_signals = []
            for signal_data in config_dict.get('sell_signals', []):
                conditions = []
                for cond_data in signal_data.get('conditions', []):
                    condition = ConditionConfig(**cond_data)
                    conditions.append(condition)
                
                signal = SignalConfig(
                    name=signal_data['name'],
                    conditions=conditions,
                    logic=signal_data.get('logic', 'AND'),
                    min_conditions=signal_data.get('min_conditions', 1),
                    signal_strength=signal_data.get('signal_strength', 1.0),
                    description=signal_data.get('description', '')
                )
                sell_signals.append(signal)
            
            # 解析风险管理配置
            risk_data = config_dict.get('risk_management', {})
            risk_config = RiskConfig(**risk_data)
            
            # 创建策略配置模式
            self.config_schema = StrategyConfigSchema(
                name=config_dict['name'],
                display_name=config_dict.get('display_name', config_dict['name']),
                description=config_dict.get('description', ''),
                version=config_dict.get('version', '1.0.0'),
                author=config_dict.get('author', 'Unknown'),
                category=config_dict.get('category', 'custom'),
                buy_signals=buy_signals,
                sell_signals=sell_signals,
                risk_management=risk_config,
                universe=config_dict.get('universe', []),
                rebalance_frequency=config_dict.get('rebalance_frequency', 'daily'),
                benchmark=config_dict.get('benchmark', '000300.SH'),
                indicators=config_dict.get('indicators', {}),
                custom_params=config_dict.get('custom_params', {})
            )
            
            logger.info(f"✅ 配置驱动策略加载成功: {self.config_schema.name}")
            
        except Exception as e:
            logger.error(f"❌ 解析策略配置失败: {e}")
            raise
    
    @property
    def strategy_name(self) -> str:
        return self.config_schema.name if self.config_schema else "config_driven_strategy"
    
    @property
    def display_name(self) -> str:
        return self.config_schema.display_name if self.config_schema else "配置驱动策略"
    
    @property
    def description(self) -> str:
        return self.config_schema.description if self.config_schema else "基于配置文件的策略"

    def load_config(self, config: Dict[str, Any]) -> bool:
        """加载策略配置"""
        try:
            self.load_config_from_dict(config)
            return True
        except Exception as e:
            logger.error(f"❌ 加载配置失败: {e}")
            return False

    def initialize(self, context) -> bool:
        """初始化策略"""
        if not self.config_schema:
            logger.error("❌ 策略配置未加载")
            return False

        self.context = context
        self.current_capital = context.initial_capital

        logger.info(f"✅ 配置驱动策略初始化完成: {self.strategy_name}")
        return True

    async def run(self, stock_data: Dict[str, Any], progress_callback=None) -> bool:
        """执行策略"""
        if not self.config_schema:
            logger.error("❌ 策略配置未加载")
            return False

        try:
            # 遍历股票池
            universe = self.config_schema.universe
            if not universe:
                universe = list(stock_data.keys())

            total_stocks = len(universe)
            processed = 0

            for stock_code in universe:
                if stock_code not in stock_data:
                    continue

                data = stock_data[stock_code]
                if data.empty:
                    continue

                # 生成交易信号
                buy_signal = self.evaluate_buy_signals(data)
                sell_signal = self.evaluate_sell_signals(data)

                # 执行交易逻辑
                if buy_signal['signal']:
                    await self.execute_buy(stock_code, data, buy_signal)
                elif sell_signal['signal']:
                    await self.execute_sell(stock_code, data, sell_signal)

                processed += 1
                if progress_callback:
                    progress_callback(processed / total_stocks)

            logger.info(f"✅ 策略执行完成，处理了 {processed} 只股票")
            return True

        except Exception as e:
            logger.error(f"❌ 策略执行失败: {e}")
            return False

    def evaluate_buy_signals(self, data: pd.DataFrame) -> Dict[str, Any]:
        """评估买入信号"""
        if not self.config_schema or not self.config_schema.buy_signals:
            return {'signal': False, 'reason': '无买入信号配置'}

        for signal_config in self.config_schema.buy_signals:
            if self.evaluate_signal(data, signal_config):
                return {
                    'signal': True,
                    'reason': signal_config.description or signal_config.name,
                    'strength': signal_config.signal_strength
                }

        return {'signal': False, 'reason': '买入条件未满足'}

    def evaluate_sell_signals(self, data: pd.DataFrame) -> Dict[str, Any]:
        """评估卖出信号"""
        if not self.config_schema or not self.config_schema.sell_signals:
            return {'signal': False, 'reason': '无卖出信号配置'}

        for signal_config in self.config_schema.sell_signals:
            if self.evaluate_signal(data, signal_config):
                return {
                    'signal': True,
                    'reason': signal_config.description or signal_config.name,
                    'strength': signal_config.signal_strength
                }

        return {'signal': False, 'reason': '卖出条件未满足'}

    def evaluate_signal(self, data: pd.DataFrame, signal_config: SignalConfig) -> bool:
        """评估信号条件"""
        if not signal_config.conditions:
            return False

        satisfied_conditions = 0
        total_weight = 0
        satisfied_weight = 0

        for condition in signal_config.conditions:
            if not condition.enabled:
                continue

            total_weight += condition.weight

            # 获取条件评估器
            evaluator = CONDITION_EVALUATORS.get(condition.type)
            if not evaluator:
                logger.warning(f"⚠️ 未知的条件类型: {condition.type}")
                continue

            # 评估条件
            try:
                if evaluator.evaluate(data, condition.params):
                    satisfied_conditions += 1
                    satisfied_weight += condition.weight
            except Exception as e:
                logger.error(f"❌ 条件评估失败: {condition.name} - {e}")

        # 根据逻辑关系判断
        if signal_config.logic == "AND":
            return satisfied_conditions >= len([c for c in signal_config.conditions if c.enabled])
        elif signal_config.logic == "OR":
            return satisfied_conditions >= signal_config.min_conditions
        elif signal_config.logic == "WEIGHTED":
            return satisfied_weight / total_weight >= 0.6 if total_weight > 0 else False

        return False

    async def execute_buy(self, stock_code: str, data: pd.DataFrame, signal: Dict[str, Any]):
        """执行买入操作"""
        try:
            # 检查风险管理
            if not self.check_risk_limits(stock_code, 'buy'):
                return

            # 计算仓位大小
            position_size = self.calculate_position_size(stock_code, data)
            if position_size <= 0:
                return

            current_price = data['close'].iloc[-1]

            # 记录交易
            trade_record = {
                'date': data.index[-1].strftime('%Y-%m-%d'),
                'stock_code': stock_code,
                'action': 'buy',
                'quantity': position_size,
                'price': current_price,
                'amount': position_size * current_price,
                'reason': signal['reason']
            }

            self.trades.append(trade_record)

            # 更新持仓
            if stock_code not in self.positions:
                self.positions[stock_code] = {
                    'quantity': 0,
                    'avg_price': 0,
                    'total_cost': 0
                }

            pos = self.positions[stock_code]
            new_quantity = pos['quantity'] + position_size
            new_cost = pos['total_cost'] + (position_size * current_price)

            self.positions[stock_code] = {
                'quantity': new_quantity,
                'avg_price': new_cost / new_quantity,
                'total_cost': new_cost
            }

            # 更新资金
            self.current_capital -= position_size * current_price

            logger.info(f"📈 买入: {stock_code} {position_size}股 @{current_price:.2f} - {signal['reason']}")

        except Exception as e:
            logger.error(f"❌ 执行买入失败: {stock_code} - {e}")

    async def execute_sell(self, stock_code: str, data: pd.DataFrame, signal: Dict[str, Any]):
        """执行卖出操作"""
        try:
            # 检查是否有持仓
            if stock_code not in self.positions or self.positions[stock_code]['quantity'] <= 0:
                return

            position = self.positions[stock_code]
            sell_quantity = position['quantity']  # 全部卖出
            current_price = data['close'].iloc[-1]

            # 记录交易
            trade_record = {
                'date': data.index[-1].strftime('%Y-%m-%d'),
                'stock_code': stock_code,
                'action': 'sell',
                'quantity': sell_quantity,
                'price': current_price,
                'amount': sell_quantity * current_price,
                'reason': signal['reason']
            }

            self.trades.append(trade_record)

            # 更新资金
            self.current_capital += sell_quantity * current_price

            # 清空持仓
            self.positions[stock_code] = {
                'quantity': 0,
                'avg_price': 0,
                'total_cost': 0
            }

            logger.info(f"📉 卖出: {stock_code} {sell_quantity}股 @{current_price:.2f} - {signal['reason']}")

        except Exception as e:
            logger.error(f"❌ 执行卖出失败: {stock_code} - {e}")

    def check_risk_limits(self, stock_code: str, action: str) -> bool:
        """检查风险限制"""
        if not self.config_schema:
            return True

        risk_config = self.config_schema.risk_management

        # 检查最大持仓数
        if action == 'buy':
            current_positions = len([p for p in self.positions.values() if p['quantity'] > 0])
            if current_positions >= risk_config.max_positions:
                logger.debug(f"⚠️ 已达最大持仓数限制: {current_positions}/{risk_config.max_positions}")
                return False

        return True

    def calculate_position_size(self, stock_code: str, data: pd.DataFrame) -> int:
        """计算仓位大小"""
        if not self.config_schema:
            return 0

        risk_config = self.config_schema.risk_management
        current_price = data['close'].iloc[-1]

        # 基于固定比例计算
        target_value = self.current_capital * risk_config.position_size
        shares = int(target_value / current_price / 100) * 100  # 整手

        return max(shares, 100)  # 至少一手
