#!/usr/bin/env python3
"""
测试修复后的buy_hold策略
"""

import requests
import json
import time

BASE_URL = "http://localhost:8000"

def test_fixed_buy_hold_strategy():
    """测试修复后的buy_hold策略"""
    print("=== 测试修复后的buy_hold策略 ===")
    
    # 清理现有策略
    response = requests.get(f"{BASE_URL}/api/live/strategies")
    if response.status_code == 200:
        strategies = response.json()
        for strategy in strategies:
            if 'buy_hold' in strategy['name'].lower() or 'fix' in strategy['name'].lower():
                print(f"清理现有策略: {strategy['name']}")
                requests.delete(f"{BASE_URL}/api/live/strategies/{strategy['id']}")
    
    # 启动修复后的buy_hold策略
    strategy_data = {
        'name': 'buy_hold修复测试',
        'strategy_type': 'buy_hold',
        'config': {
            'initial_capital': 100000,
            'paper_trading': True,
            'stock_codes': ['000001.SZ', '000002.SZ', '000858.SZ'],  # 3只股票
            'max_stocks': 3,
            'position_size': 0.33
        }
    }
    
    print("启动修复后的buy_hold策略...")
    print(f"配置: {json.dumps(strategy_data, indent=2, ensure_ascii=False)}")
    
    response = requests.post(f"{BASE_URL}/api/live/strategies", json=strategy_data)
    
    if response.status_code == 200:
        result = response.json()
        if result.get('success'):
            strategy_id = result['strategy_id']
            print(f"✅ 策略启动成功: {strategy_id}")
            
            # 立即检查策略状态
            print(f"\n立即检查策略状态...")
            response = requests.get(f"{BASE_URL}/api/live/strategies/{strategy_id}")
            if response.status_code == 200:
                strategy = response.json()
                print(f"初始状态: 持仓:{strategy['positions']} 交易:{strategy['trade_count']} 盈亏:¥{strategy['pnl']:.2f}")
            
            # 监控策略状态变化
            print(f"\n监控策略状态变化 (60秒)...")
            for i in range(20):  # 监控20次，每次3秒
                time.sleep(3)
                
                response = requests.get(f"{BASE_URL}/api/live/strategies/{strategy_id}")
                if response.status_code == 200:
                    strategy = response.json()
                    print(f"[{i+1:2d}/20] 状态:{strategy['status']} 持仓:{strategy['positions']} 交易:{strategy['trade_count']} 盈亏:¥{strategy['pnl']:.2f}")
                    
                    error_message = strategy.get('error_message')
                    if error_message:
                        print(f"    ❌ 错误: {error_message}")
                        break
                    
                    if strategy['trade_count'] > 0:
                        print(f"    ✅ 策略开始交易！")
                        recent_trades = strategy.get('recent_trades', [])
                        print(f"    交易记录数量: {len(recent_trades)}")
                        for j, trade in enumerate(recent_trades[:5]):  # 显示前5笔交易
                            print(f"      [{j+1}] {trade}")
                        break
                    elif strategy['positions'] > 0:
                        print(f"    ✅ 策略建立持仓！")
                        # 继续监控，看是否有更多交易
                else:
                    print(f"    ❌ 获取策略状态失败")
                    break
            
            # 检查实盘引擎详细状态
            print(f"\n检查实盘引擎详细状态...")
            response = requests.get(f"{BASE_URL}/api/live/results")
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    results = data.get('data', [])
                    print(f"实盘引擎任务数量: {len(results)}")
                    
                    for result in results:
                        # 处理不同类型的result对象
                        if hasattr(result, 'strategy_name'):
                            strategy_name = result.strategy_name
                            task_id = getattr(result, 'task_id', 'N/A')
                            status = getattr(result, 'status', 'N/A')
                            stock_codes = getattr(result, 'stock_codes', [])
                            positions = getattr(result, 'positions', [])
                            trades = getattr(result, 'trades', [])
                            current_capital = getattr(result, 'current_capital', 0)
                            initial_capital = getattr(result, 'initial_capital', 0)
                        elif isinstance(result, dict):
                            strategy_name = result.get('strategy_name')
                            task_id = result.get('task_id', 'N/A')
                            status = result.get('status', 'N/A')
                            stock_codes = result.get('stock_codes', [])
                            positions = result.get('positions', [])
                            trades = result.get('trades', [])
                            current_capital = result.get('current_capital', 0)
                            initial_capital = result.get('initial_capital', 0)
                        else:
                            continue
                        
                        if strategy_name == 'buy_hold':
                            print(f"找到buy_hold任务详情:")
                            print(f"  task_id: {task_id}")
                            print(f"  状态: {status}")
                            print(f"  股票池: {stock_codes}")
                            print(f"  初始资金: ¥{initial_capital:,.2f}")
                            print(f"  当前资金: ¥{current_capital:,.2f}")
                            print(f"  持仓数: {len(positions)}")
                            print(f"  交易数: {len(trades)}")
                            
                            if positions:
                                print(f"  持仓详情:")
                                for pos in positions:
                                    print(f"    - {pos}")
                            
                            if trades:
                                print(f"  交易详情:")
                                for trade in trades[-5:]:  # 最近5笔
                                    print(f"    - {trade}")
                            
                            break
                    else:
                        print(f"❌ 未找到buy_hold任务")
            
            # 等待更长时间，看是否有延迟的交易
            if strategy['trade_count'] == 0:
                print(f"\n等待更长时间，观察是否有延迟交易...")
                for i in range(10):  # 再等待30秒
                    time.sleep(3)
                    response = requests.get(f"{BASE_URL}/api/live/strategies/{strategy_id}")
                    if response.status_code == 200:
                        strategy = response.json()
                        if strategy['trade_count'] > 0 or strategy['positions'] > 0:
                            print(f"[延迟检查] 持仓:{strategy['positions']} 交易:{strategy['trade_count']}")
                            break
                        else:
                            print(f"[延迟检查 {i+1}/10] 仍无交易...")
            
            # 清理
            print(f"\n清理测试策略...")
            requests.delete(f"{BASE_URL}/api/live/strategies/{strategy_id}")
            print(f"✅ 策略已清理")
            
            # 判断测试结果
            if strategy['trade_count'] > 0:
                print(f"\n🎉 测试成功！buy_hold策略产生了 {strategy['trade_count']} 笔交易")
                return True
            elif strategy['positions'] > 0:
                print(f"\n🎉 测试部分成功！buy_hold策略建立了 {strategy['positions']} 个持仓")
                return True
            else:
                print(f"\n⚠️ 测试未完全成功：策略运行正常但未产生交易或持仓")
                print(f"可能原因：")
                print(f"  1. 数据获取延迟")
                print(f"  2. 策略执行条件未满足")
                print(f"  3. 需要更长的等待时间")
                return False
        else:
            print(f"❌ 策略启动失败: {result.get('message')}")
            return False
    else:
        print(f"❌ 策略启动请求失败: {response.status_code}")
        print(f"响应: {response.text}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试修复后的buy_hold策略")
    
    success = test_fixed_buy_hold_strategy()
    
    print(f"\n🎯 测试结果: {'✅ 成功' if success else '❌ 需要进一步调试'}")
    
    if success:
        print(f"\n🎉 buy_hold策略修复成功！")
        print(f"✅ 策略逻辑修复完成")
        print(f"✅ 买入信号生成正常")
        print(f"✅ 交易执行机制工作")
    else:
        print(f"\n💡 进一步调试建议:")
        print(f"1. 检查策略执行日志")
        print(f"2. 确认数据源是否正常")
        print(f"3. 验证交易时间和市场状态")
        print(f"4. 检查资金和股票代码配置")

if __name__ == "__main__":
    main()
