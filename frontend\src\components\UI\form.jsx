import * as React from 'react'
import { cn } from '../../lib/utils'

// Form Context
const FormContext = React.createContext({})

export function Form({ 
  children, 
  onSubmit, 
  className = '',
  layout = 'vertical',
  ...props 
}) {
  const [values, setValues] = React.useState({})
  const [errors, setErrors] = React.useState({})
  const [touched, setTouched] = React.useState({})

  const setValue = (name, value) => {
    setValues(prev => ({ ...prev, [name]: value }))
    // 清除该字段的错误
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: undefined }))
    }
  }

  const setError = (name, error) => {
    setErrors(prev => ({ ...prev, [name]: error }))
  }

  const setFieldTouched = (name, isTouched = true) => {
    setTouched(prev => ({ ...prev, [name]: isTouched }))
  }

  const validateField = (name, rules = []) => {
    const value = values[name]
    
    for (const rule of rules) {
      if (rule.required && (!value || value === '')) {
        setError(name, rule.message || `${name} is required`)
        return false
      }
      
      if (rule.type === 'number' && value !== undefined && value !== '') {
        const numValue = Number(value)
        if (isNaN(numValue)) {
          setError(name, rule.message || `${name} must be a number`)
          return false
        }
        if (rule.min !== undefined && numValue < rule.min) {
          setError(name, rule.message || `${name} must be at least ${rule.min}`)
          return false
        }
        if (rule.max !== undefined && numValue > rule.max) {
          setError(name, rule.message || `${name} must be at most ${rule.max}`)
          return false
        }
      }
      
      if (rule.validator && typeof rule.validator === 'function') {
        const result = rule.validator(value)
        if (result !== true) {
          setError(name, result || rule.message || 'Validation failed')
          return false
        }
      }
    }
    
    setError(name, undefined)
    return true
  }

  const validateFields = (fieldNames) => {
    // 这里需要从子组件收集验证规则，简化实现
    return Promise.resolve(values)
  }

  const handleSubmit = (e) => {
    e.preventDefault()
    if (onSubmit) {
      onSubmit(values)
    }
  }

  const contextValue = {
    values,
    errors,
    touched,
    setValue,
    setError,
    setFieldTouched,
    validateField,
    validateFields,
    layout
  }

  return (
    <FormContext.Provider value={contextValue}>
      <form 
        onSubmit={handleSubmit}
        className={cn('space-y-4', className)}
        {...props}
      >
        {children}
      </form>
    </FormContext.Provider>
  )
}

export function FormItem({ 
  children, 
  label, 
  name,
  rules = [],
  required = false,
  className = '',
  ...props 
}) {
  const { values, errors, touched, setValue, setFieldTouched, validateField, layout } = React.useContext(FormContext)
  
  const hasError = errors[name] && touched[name]
  
  const handleChange = (value) => {
    setValue(name, value)
  }

  const handleBlur = () => {
    setFieldTouched(name, true)
    validateField(name, rules)
  }

  const layoutClasses = {
    vertical: 'flex flex-col space-y-2',
    horizontal: 'flex items-center space-x-4',
    inline: 'flex items-center space-x-2'
  }

  return (
    <div className={cn(layoutClasses[layout], className)} {...props}>
      {label && (
        <label className={cn(
          'text-sm font-medium text-gray-700',
          required && "after:content-['*'] after:text-red-500 after:ml-1",
          layout === 'horizontal' && 'w-24 flex-shrink-0'
        )}>
          {label}
        </label>
      )}
      
      <div className="flex-1">
        {React.cloneElement(children, {
          value: values[name],
          onChange: handleChange,
          onBlur: handleBlur,
          error: hasError,
          ...children.props
        })}
        
        {hasError && (
          <div className="mt-1 text-sm text-red-600">
            {errors[name]}
          </div>
        )}
      </div>
    </div>
  )
}

// Hook to use form context
export function useForm() {
  const context = React.useContext(FormContext)
  if (!context) {
    throw new Error('useForm must be used within a Form component')
  }
  return context
}
