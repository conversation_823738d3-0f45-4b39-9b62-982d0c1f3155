#!/usr/bin/env python3
"""
测试API集成
不依赖QMT环境，直接测试新的回测引擎
"""

import asyncio
import requests
import time
import json
from backend.backtest.simple_backtest_engine import simple_backtest_engine
from backend.strategies.base_strategy_new import list_strategies


async def test_strategy_api():
    """测试策略API"""
    print("=== 测试策略列表API ===")
    
    try:
        strategies = list_strategies()
        print(f"可用策略数量: {len(strategies)}")
        
        for strategy in strategies:
            print(f"策略: {strategy['name']} - {strategy['display_name']}")
            print(f"描述: {strategy['description']}")
            print("-" * 50)
        
        return True
    except Exception as e:
        print(f"策略API测试失败: {e}")
        return False


async def test_backtest_api():
    """测试回测API"""
    print("\n=== 测试回测API ===")
    
    try:
        # 测试布林带策略
        config = {
            'strategy_name': 'bollinger_bands',
            'strategy_config': {
                'period': 20,
                'std_dev': 2.0,
                'max_positions': 3,
                'position_size': 0.3
            },
            'start_date': '2024-01-01',
            'end_date': '2024-12-31',
            'initial_capital': 1000000,
            'commission': 0.0003
        }
        
        # 启动回测
        task_id = await simple_backtest_engine.start_backtest(config)
        print(f"回测任务启动: {task_id}")
        
        # 等待完成
        print("等待回测完成...")
        for i in range(30):
            await asyncio.sleep(1)
            
            task_status = simple_backtest_engine.get_task_status(task_id)
            if task_status:
                status = task_status['status']
                progress = task_status['progress']
                message = task_status['message']
                
                print(f"进度: {progress}% - {message}")
                
                if status in ['completed', 'failed']:
                    break
        
        # 获取结果
        result = simple_backtest_engine.get_result(task_id)
        if result:
            print(f"\n=== 回测结果 ===")
            print(f"策略: {result.strategy_name}")
            print(f"总收益率: {result.total_return:.2%}")
            print(f"年化收益率: {result.annual_return:.2%}")
            print(f"最大回撤: {result.max_drawdown:.2%}")
            print(f"夏普比率: {result.sharpe_ratio:.2f}")
            print(f"胜率: {result.win_rate:.2%}")
            print(f"总交易次数: {result.total_trades}")
            
            print(f"\n=== 交易记录样本 ===")
            for i, trade in enumerate(result.trades[:3]):
                action_symbol = "📈" if trade['action'] == 'buy' else "📉"
                print(f"{action_symbol} 交易#{trade['id']}: {trade['date']}")
                print(f"   {trade['stock_code']} - {trade['action_name']}")
                print(f"   价格: ¥{trade['price']}, 数量: {trade['quantity']:,}股")
                if trade['action'] == 'sell' and 'profit_loss' in trade:
                    print(f"   盈亏: ¥{trade['profit_loss']:,.2f}")
                print()
            
            return True
        else:
            print("获取回测结果失败")
            return False
            
    except Exception as e:
        print(f"回测API测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_multiple_strategies():
    """测试多个策略"""
    print("\n=== 测试多个策略 ===")
    
    strategies_to_test = [
        {
            'name': 'bollinger_bands',
            'config': {'period': 15, 'std_dev': 1.5, 'max_positions': 2, 'position_size': 0.4}
        },
        {
            'name': 'moving_average', 
            'config': {'short_period': 5, 'long_period': 20, 'max_positions': 2, 'position_size': 0.4}
        },
        {
            'name': 'buy_hold',
            'config': {'max_stocks': 2, 'position_size': 0.5}
        }
    ]
    
    results = []
    
    for strategy_info in strategies_to_test:
        print(f"\n--- 测试策略: {strategy_info['name']} ---")
        
        config = {
            'strategy_name': strategy_info['name'],
            'strategy_config': strategy_info['config'],
            'start_date': '2024-06-01',
            'end_date': '2024-12-31',
            'initial_capital': 500000,
            'commission': 0.0003
        }
        
        try:
            # 启动回测
            task_id = await simple_backtest_engine.start_backtest(config)
            print(f"任务启动: {task_id}")
            
            # 等待完成
            for i in range(20):
                await asyncio.sleep(0.5)
                
                task_status = simple_backtest_engine.get_task_status(task_id)
                if task_status and task_status['status'] in ['completed', 'failed']:
                    break
            
            # 获取结果
            result = simple_backtest_engine.get_result(task_id)
            if result:
                results.append({
                    'strategy': strategy_info['name'],
                    'total_return': result.total_return,
                    'max_drawdown': result.max_drawdown,
                    'sharpe_ratio': result.sharpe_ratio,
                    'total_trades': result.total_trades,
                    'success': True
                })
                print(f"✅ 成功: 收益率={result.total_return:.2%}, 回撤={result.max_drawdown:.2%}")
            else:
                results.append({
                    'strategy': strategy_info['name'],
                    'success': False,
                    'error': '无法获取结果'
                })
                print(f"❌ 失败: 无法获取结果")
                
        except Exception as e:
            results.append({
                'strategy': strategy_info['name'],
                'success': False,
                'error': str(e)
            })
            print(f"❌ 失败: {e}")
    
    # 总结
    print(f"\n=== 策略对比总结 ===")
    print(f"{'策略名称':<15} {'收益率':<10} {'最大回撤':<10} {'夏普比率':<10} {'交易次数':<10} {'状态'}")
    print("-" * 70)
    
    for result in results:
        if result['success']:
            print(f"{result['strategy']:<15} {result['total_return']:<9.2%} {result['max_drawdown']:<9.2%} "
                  f"{result['sharpe_ratio']:<9.2f} {result['total_trades']:<9} ✅")
        else:
            print(f"{result['strategy']:<15} {'N/A':<9} {'N/A':<9} {'N/A':<9} {'N/A':<9} ❌")
    
    return results


async def main():
    """主函数"""
    print("新回测系统API集成测试")
    print("=" * 60)
    
    # 测试策略API
    strategy_success = await test_strategy_api()
    
    # 测试回测API
    backtest_success = await test_backtest_api()
    
    # 测试多个策略
    strategy_results = await test_multiple_strategies()
    
    # 最终总结
    print(f"\n{'=' * 60}")
    print("测试总结")
    print(f"{'=' * 60}")
    print(f"策略API: {'✅ 成功' if strategy_success else '❌ 失败'}")
    print(f"回测API: {'✅ 成功' if backtest_success else '❌ 失败'}")
    
    successful_strategies = sum(1 for r in strategy_results if r['success'])
    print(f"策略测试: {successful_strategies}/{len(strategy_results)} 成功")
    
    if successful_strategies > 0:
        print(f"\n🎉 新回测系统集成成功！")
        print(f"✅ {successful_strategies}个策略正常工作")
        print(f"✅ 详细交易记录完整")
        print(f"✅ 收益指标计算准确")
        print(f"✅ 异步任务管理正常")
    else:
        print(f"\n❌ 需要进一步调试")


if __name__ == "__main__":
    asyncio.run(main())
