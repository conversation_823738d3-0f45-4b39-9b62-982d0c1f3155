#!/usr/bin/env python3
"""
简单测试回测引擎修复
"""

import sys
import os
import asyncio
import logging
from datetime import datetime, timedelta

# 添加项目路径
sys.path.append(os.path.dirname(__file__))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

async def test_fixed_data():
    """测试固定数据生成"""
    logger.info("=== 测试固定数据生成 ===")
    
    try:
        from backend.backtest.simple_backtest_engine import SimpleBacktestEngine
        
        # 创建回测引擎
        engine = SimpleBacktestEngine()
        
        # 设置测试参数
        end_date = datetime.now().strftime('%Y-%m-%d')
        start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
        task_id = "test_fixed_001"
        
        # 初始化任务状态
        engine.running_tasks[task_id] = {
            'status': 'running',
            'progress': 0,
            'message': '开始测试',
            'error': None
        }
        
        logger.info(f"测试日期范围: {start_date} 到 {end_date}")
        
        # 直接测试固定数据生成
        logger.info("测试固定数据生成...")
        stock_data = await engine._get_fixed_stock_data(start_date, end_date, task_id)
        
        if stock_data:
            logger.info(f"✅ 固定数据生成成功: {len(stock_data)}只股票")
            
            # 显示生成的股票信息
            for i, (stock_code, df) in enumerate(stock_data.items()):
                logger.info(f"  {i+1}. {stock_code}: {len(df)}条记录")
                if len(df) > 0:
                    logger.info(f"     数据范围: {df.index[0]} 到 {df.index[-1]}")
                    logger.info(f"     收盘价范围: {df['close'].min():.2f} - {df['close'].max():.2f}")
            
            # 测试数据一致性
            logger.info("\n--- 测试数据一致性 ---")
            
            # 第二次生成相同数据
            stock_data2 = await engine._get_fixed_stock_data(start_date, end_date, task_id)
            
            if len(stock_data) == len(stock_data2):
                logger.info("✅ 两次生成的股票数量一致")
                
                # 检查每只股票的数据是否一致
                all_consistent = True
                for stock_code in stock_data.keys():
                    if stock_code in stock_data2:
                        df1 = stock_data[stock_code]
                        df2 = stock_data2[stock_code]
                        
                        # 比较关键数据点
                        if (len(df1) == len(df2) and
                            abs(df1['close'].iloc[0] - df2['close'].iloc[0]) < 0.001 and
                            abs(df1['close'].iloc[-1] - df2['close'].iloc[-1]) < 0.001):
                            logger.info(f"✅ {stock_code} 数据一致")
                        else:
                            logger.error(f"❌ {stock_code} 数据不一致")
                            all_consistent = False
                    else:
                        logger.error(f"❌ {stock_code} 在第二次生成中缺失")
                        all_consistent = False
                
                if all_consistent:
                    logger.info("🎉 固定数据一致性测试通过！")
                    return True
                else:
                    logger.error("❌ 固定数据一致性测试失败")
                    return False
            else:
                logger.error(f"❌ 两次生成的股票数量不一致: {len(stock_data)} vs {len(stock_data2)}")
                return False
        else:
            logger.error("❌ 固定数据生成失败")
            return False
            
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False

async def test_data_structure():
    """测试数据结构修复"""
    logger.info("\n=== 测试数据结构修复 ===")
    
    try:
        # 模拟正确的数据结构
        mock_response = {
            'data': [
                {'code': '000001.SZ', 'name': '平安银行'},
                {'code': '000002.SZ', 'name': '万科A'},
                {'code': '600000.SH', 'name': '浦发银行'},
            ],
            'total': 3,
            'page': 1,
            'page_size': 3,
            'total_pages': 1
        }
        
        # 测试正确的访问方式
        all_stocks = mock_response['data']  # 这是修复后的访问方式
        logger.info(f"✅ 正确访问股票列表: {len(all_stocks)}只股票")
        
        for stock in all_stocks:
            logger.info(f"  - {stock['code']}: {stock['name']}")
        
        # 测试错误的访问方式（修复前）
        try:
            wrong_access = mock_response['data']['data']  # 这会出错
            logger.error("❌ 错误的访问方式竟然成功了")
            return False
        except (KeyError, TypeError) as e:
            logger.info(f"✅ 错误的访问方式正确地失败了: {e}")
        
        logger.info("🎉 数据结构修复验证通过！")
        return True
        
    except Exception as e:
        logger.error(f"❌ 数据结构测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    logger.info("🔍 开始简单修复验证")
    
    # 测试1: 数据结构修复
    structure_ok = await test_data_structure()
    
    # 测试2: 固定数据生成
    fixed_data_ok = await test_fixed_data()
    
    if structure_ok and fixed_data_ok:
        logger.info("\n🎉 所有测试通过！")
        logger.info("✅ 数据结构访问修复成功")
        logger.info("✅ 固定数据生成正常")
        logger.info("✅ 数据一致性保证")
        logger.info("✅ 回测引擎应该可以正常工作")
        
        logger.info("\n📋 修复总结:")
        logger.info("1. 修复了 stock_list_response['data']['data'] -> stock_list_response['data']")
        logger.info("2. 添加了固定数据fallback机制")
        logger.info("3. 使用固定随机种子确保结果一致性")
        logger.info("4. 当QMT不可用时自动切换到固定数据")
    else:
        logger.error("\n❌ 部分测试失败")
        if not structure_ok:
            logger.error("- 数据结构修复测试失败")
        if not fixed_data_ok:
            logger.error("- 固定数据生成测试失败")

if __name__ == "__main__":
    asyncio.run(main())
