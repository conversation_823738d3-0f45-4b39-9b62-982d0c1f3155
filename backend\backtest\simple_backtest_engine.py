"""
简化的回测引擎
基于backtrader，使用真实数据，支持策略注册机制
"""

import asyncio
import uuid
import pandas as pd
import backtrader as bt
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict

# 导入策略相关模块
from backend.strategies.base_strategy_new import get_strategy_class, list_strategies
from backend.strategies import bollinger_bands_strategy  # 确保策略被注册
from backend.strategies import multi_signal_strategy_bt  # 确保BT原生多信号策略被注册
from backend.core.logger import get_backtest_logger
from backend.storage.backtest_storage import backtest_storage

logger = get_backtest_logger('simple_backtest')


@dataclass
class BacktestResult:
    """回测结果"""
    task_id: str
    strategy_name: str
    start_date: str
    end_date: str
    initial_capital: float
    final_capital: float
    total_return: float
    annual_return: float
    max_drawdown: float
    sharpe_ratio: float
    win_rate: float
    total_trades: int
    profit_trades: int
    loss_trades: int
    avg_profit: float
    avg_loss: float
    profit_factor: float
    trades: List[Dict[str, Any]]
    daily_returns: List[Dict[str, Any]]
    status: str
    created_at: str
    completed_at: str


class DataFeed(bt.feeds.PandasData):
    """自定义数据源"""
    
    params = (
        ('datetime', None),
        ('open', 'open'),
        ('high', 'high'),
        ('low', 'low'),
        ('close', 'close'),
        ('volume', 'volume'),
        ('openinterest', None),
    )


class SimpleBacktestEngine:
    """简化的回测引擎"""
    
    def __init__(self):
        self.running_tasks = {}
        self.completed_tasks = {}
        
    async def start_backtest(self, config: Dict[str, Any]) -> str:
        """启动回测任务"""
        task_id = str(uuid.uuid4())
        
        # 创建任务记录
        self.running_tasks[task_id] = {
            'task_id': task_id,
            'status': 'running',
            'progress': 0,
            'message': '准备启动回测...',
            'config': config,
            'created_at': datetime.now().isoformat()
        }
        
        # 异步执行回测
        asyncio.create_task(self._execute_backtest(task_id, config))
        
        return task_id
    
    async def _execute_backtest(self, task_id: str, config: Dict[str, Any]):
        """执行回测"""
        # 临时移除try-catch以便调试
        # try:
        logger.info(f"开始回测任务: {task_id}")

        # 解析配置
        strategy_name = config.get('strategy_name', 'buy_hold')
        start_date = config.get('start_date', '2024-01-01')
        end_date = config.get('end_date', '2024-12-31')
        initial_capital = config.get('initial_capital', 1000000)
        commission = config.get('commission', 0.0003)

        # 获取股票池配置
        stock_codes = config.get('stock_codes', [])

        # 提取策略参数 - 从config中提取所有非系统参数作为策略参数
        system_params = {'strategy_name', 'start_date', 'end_date', 'initial_capital', 'commission', 'stock_codes'}
        strategy_config = {k: v for k, v in config.items() if k not in system_params}

        logger.info(f"📊 策略参数: {strategy_config}")
        logger.info(f"🎯 策略名称: {strategy_name}")
        logger.info(f"📈 股票池: {stock_codes if stock_codes else '使用默认股票池'}")

        # 更新进度
        self.running_tasks[task_id]['progress'] = 10
        self.running_tasks[task_id]['message'] = '获取股票数据...'

        # 获取股票数据
        stock_data = await self._get_stock_data(start_date, end_date, task_id, stock_codes, config)

        # 更新进度
        self.running_tasks[task_id]['progress'] = 30
        self.running_tasks[task_id]['message'] = '设置回测环境...'

        # 执行回测
        result = await self._run_backtest(
            strategy_name, strategy_config, stock_data,
            start_date, end_date, initial_capital, commission, task_id
        )

        # 保存结果
        self.running_tasks[task_id]['status'] = 'completed'
        self.running_tasks[task_id]['progress'] = 100
        self.running_tasks[task_id]['message'] = '回测完成'
        self.running_tasks[task_id]['result'] = result
        self.running_tasks[task_id]['completed_at'] = datetime.now().isoformat()

        # 保存结果到文件
        try:
            save_success = backtest_storage.save_result(result)
            if save_success:
                logger.info(f"✅ 回测结果已保存到文件: {task_id}")
            else:
                logger.error(f"❌ 回测结果保存到文件失败: {task_id}")
        except Exception as e:
            logger.error(f"❌ 保存回测结果到文件时发生异常: {task_id}, 错误: {e}")

        # 移动到完成列表
        self.completed_tasks[task_id] = self.running_tasks.pop(task_id)

        logger.info(f"🎉 回测任务完成: {task_id}")

        # except Exception as e:
        #     logger.error(f"回测任务失败: {task_id}, 错误: {e}")
        #     self.running_tasks[task_id]['status'] = 'failed'
        #     self.running_tasks[task_id]['message'] = f"回测失败: {str(e)}"
        #     self.running_tasks[task_id]['error'] = str(e)
    
    async def _get_stock_data(self, start_date: str, end_date: str, task_id: str, stock_codes: List[str] = None, config: Dict[str, Any] = None) -> Dict[str, pd.DataFrame]:
        """获取股票数据 - 优先使用QMT真实数据"""
        logger.info(f"开始获取QMT真实数据: {start_date} 到 {end_date}")

        # 更新进度
        self.running_tasks[task_id]['progress'] = 15
        self.running_tasks[task_id]['message'] = '连接QMT数据源...'

        # 使用真实的QMT数据 - 不使用任何虚拟数据
        from backend.data.data_manager import data_manager
        logger.info("✅ 连接QMT数据源")

        # 确定股票池 - 支持新的股票池配置格式
        final_stock_codes = []

        # 检查是否有股票池配置
        if config is None:
            config = {}
        stock_universe = config.get('stock_universe')
        if stock_universe and isinstance(stock_universe, dict):
            # 新格式：使用股票池配置
            final_stock_codes = stock_universe.get('stock_codes', [])
            universe_type = stock_universe.get('type', 'custom')
            universe_name = stock_universe.get('universe_name', '')

            logger.info(f"使用股票池配置: {universe_type}")
            if universe_name:
                logger.info(f"股票池名称: {universe_name}")
        elif stock_codes and len(stock_codes) > 0:
            # 兼容旧格式：直接使用stock_codes
            final_stock_codes = stock_codes
            logger.info(f"使用传统股票代码配置: {stock_codes}")

        if final_stock_codes and len(final_stock_codes) > 0:
            # 使用指定的股票池
            selected_stocks = [{'code': code, 'name': f'股票{code}'} for code in final_stock_codes]
            logger.info(f"使用指定股票池: {len(final_stock_codes)}只股票")
        else:
            # 尝试从股票池管理器获取默认股票池
            try:
                from backend.stock_selection.stock_pool_manager import stock_pool_manager

                # 获取可用的股票池
                available_pools = stock_pool_manager.get_available_pools()

                if available_pools:
                    # 使用最新的股票池
                    latest_pool = available_pools[0]
                    pool_stock_codes = stock_pool_manager.get_pool_stocks(latest_pool['name'])
                    # 限制数量以提高回测速度
                    pool_stock_codes = pool_stock_codes[:20]
                    selected_stocks = [{'code': code, 'name': f'股票{code}'} for code in pool_stock_codes]
                    logger.info(f"使用股票池: {latest_pool['display_name']}，选择{len(selected_stocks)}只股票进行回测")
                else:
                    # 如果没有自定义股票池，使用数据管理器获取默认股票
                    stock_list_response = data_manager.get_stock_list(page=1, page_size=20)
                    all_stocks = stock_list_response['data']
                    selected_stocks = all_stocks[:10]
                    logger.info(f"使用默认股票池，选择{len(selected_stocks)}只股票进行回测")

            except Exception as e:
                logger.warning(f"获取股票池失败，使用默认股票池: {e}")
                # 使用默认股票池
                stock_list_response = data_manager.get_stock_list(page=1, page_size=20)
                all_stocks = stock_list_response['data']
                selected_stocks = all_stocks[:10]
                logger.info(f"使用默认股票池，选择{len(selected_stocks)}只股票进行回测")

        stock_data = {}
        successful_count = 0

        for i, stock in enumerate(selected_stocks):
            stock_code = stock['code']
            stock_name = stock['name']

            # 更新进度
            progress = 20 + (i / len(selected_stocks)) * 10
            self.running_tasks[task_id]['progress'] = int(progress)
            self.running_tasks[task_id]['message'] = f'获取{stock_name}({stock_code})数据...'

            logger.debug(f"正在获取{stock_code}的历史数据...")
            # 使用用户指定的日期范围
            df = data_manager.get_stock_data(
                stock_code=stock_code,
                start_date=start_date,
                end_date=end_date
            )

            if len(df) > 5:  # 降低要求，只要有5条以上数据就可以
                stock_data[stock_code] = df
                successful_count += 1
                logger.info(f"✅ 成功获取{stock_name}({stock_code})数据: {len(df)}条记录，日期范围: {df.index[0]} 到 {df.index[-1]}")
            else:
                logger.warning(f"⚠️ {stock_code}数据不足({len(df)}条)，跳过")

        if not stock_data:
            raise ValueError("未能获取到任何有效的QMT股票数据，请检查QMT连接和数据源")

        logger.info(f"🎉 成功获取{successful_count}只股票的真实QMT数据")
        return stock_data


    
    async def _run_backtest(self, strategy_name: str, strategy_config: Dict[str, Any], 
                          stock_data: Dict[str, pd.DataFrame], start_date: str, end_date: str,
                          initial_capital: float, commission: float, task_id: str) -> BacktestResult:
        """运行回测"""
        
        # 更新进度
        self.running_tasks[task_id]['progress'] = 40
        self.running_tasks[task_id]['message'] = f'初始化策略: {strategy_name}'
        
        # 获取策略类
        strategy_class = get_strategy_class(strategy_name)
        if not strategy_class:
            raise ValueError(f"未找到策略: {strategy_name}")
        
        # 创建backtrader引擎
        cerebro = bt.Cerebro()
        
        # 设置初始资金和手续费
        cerebro.broker.setcash(initial_capital)
        cerebro.broker.setcommission(commission=commission)
        
        # 添加策略
        cerebro.addstrategy(strategy_class, config={'parameters': strategy_config})
        
        # 更新进度
        self.running_tasks[task_id]['progress'] = 60
        self.running_tasks[task_id]['message'] = '添加股票数据...'
        
        # 添加数据
        for stock_code, df in stock_data.items():
            if not df.empty:
                data = DataFeed(dataname=df, name=stock_code)
                cerebro.adddata(data)
        
        # 添加分析器
        cerebro.addanalyzer(bt.analyzers.SharpeRatio, _name='sharpe')
        cerebro.addanalyzer(bt.analyzers.DrawDown, _name='drawdown')
        cerebro.addanalyzer(bt.analyzers.Returns, _name='returns')
        cerebro.addanalyzer(bt.analyzers.TradeAnalyzer, _name='trades')
        
        # 更新进度
        self.running_tasks[task_id]['progress'] = 80
        self.running_tasks[task_id]['message'] = '执行回测...'
        
        # 运行回测
        results = cerebro.run()
        strategy_result = results[0]
        
        # 更新进度
        self.running_tasks[task_id]['progress'] = 90
        self.running_tasks[task_id]['message'] = '分析结果...'

        # 添加延迟，让前端能看到进度
        await asyncio.sleep(0.5)

        # 提取结果
        final_value = cerebro.broker.getvalue()
        logger.debug(f"Backtest results - initial_capital: {initial_capital}, final_value: {final_value}")

        # 安全计算总收益率
        if initial_capital and initial_capital > 0:
            total_return = (final_value - initial_capital) / initial_capital
        else:
            total_return = 0

        logger.debug(f"Calculated total_return: {total_return}")

        # 获取分析器结果
        sharpe_analysis = strategy_result.analyzers.sharpe.get_analysis()
        sharpe_ratio = sharpe_analysis.get('sharperatio', 0) if sharpe_analysis else 0

        logger.debug(f"Sharpe analysis: {sharpe_analysis}, sharpe_ratio: {sharpe_ratio}")

        drawdown_info = strategy_result.analyzers.drawdown.get_analysis()
        max_drawdown = drawdown_info.get('max', {}).get('drawdown', 0) or 0

        logger.debug(f"Drawdown info: {drawdown_info}, max_drawdown: {max_drawdown}")

        # 确保max_drawdown是数字
        if max_drawdown is None:
            max_drawdown = 0

        # 获取策略的详细数据
        strategy_data = strategy_result.get_analysis_data()
        detailed_trades = strategy_data.get('detailed_trades', [])
        daily_returns = strategy_data.get('daily_returns', [])

        # 如果backtrader的夏普比率为0，使用自定义计算
        if sharpe_ratio == 0 and daily_returns:
            sharpe_ratio = self._calculate_sharpe_ratio(daily_returns)
        
        # 计算交易统计
        sell_trades = [t for t in detailed_trades if t.get('action') == 'sell']
        profit_trades = [t for t in sell_trades if t.get('profit_loss', 0) > 0]
        loss_trades = [t for t in sell_trades if t.get('profit_loss', 0) < 0]  # 只计算真正亏损的交易

        # 安全的除法计算
        win_rate = len(profit_trades) / len(sell_trades) if sell_trades else 0
        avg_profit = sum(t.get('profit_loss', 0) for t in profit_trades) / len(profit_trades) if profit_trades else 0
        avg_loss = sum(abs(t.get('profit_loss', 0)) for t in loss_trades) / len(loss_trades) if loss_trades else 0

        # 安全的盈亏比计算
        if avg_loss > 0 and avg_profit > 0:
            profit_factor = avg_profit / avg_loss
        elif avg_profit > 0 and avg_loss == 0:
            profit_factor = float('inf')  # 无亏损交易
        else:
            profit_factor = 0
        
        # 计算年化收益率
        days = (datetime.strptime(end_date, '%Y-%m-%d') - datetime.strptime(start_date, '%Y-%m-%d')).days
        years = days / 365.25

        logger.debug(f"Annual return calculation - days: {days}, years: {years}, total_return: {total_return}")

        # 计算年化收益率
        if years > 0 and total_return is not None:
            annual_return = (1 + total_return) ** (1 / years) - 1
        else:
            annual_return = total_return if total_return is not None else 0

        logger.debug(f"Calculated annual_return: {annual_return}")
        
        # 更新进度
        self.running_tasks[task_id]['progress'] = 95
        self.running_tasks[task_id]['message'] = '生成报告...'

        # 添加延迟
        await asyncio.sleep(0.3)

        # 创建结果对象
        result = BacktestResult(
            task_id=task_id,
            strategy_name=strategy_name,
            start_date=start_date,
            end_date=end_date,
            initial_capital=initial_capital,
            final_capital=final_value,
            total_return=total_return,
            annual_return=annual_return,
            max_drawdown=-max_drawdown / 100,  # 转换为负数百分比
            sharpe_ratio=sharpe_ratio,
            win_rate=win_rate,
            total_trades=len(detailed_trades),
            profit_trades=len(profit_trades),
            loss_trades=len(loss_trades),
            avg_profit=avg_profit,
            avg_loss=avg_loss,
            profit_factor=profit_factor,
            trades=detailed_trades,
            daily_returns=daily_returns,
            status="completed",
            created_at=datetime.now().isoformat(),
            completed_at=datetime.now().isoformat()
        )
        
        # 安全格式化日志输出
        safe_total_return = total_return if total_return is not None else 0
        safe_sharpe_ratio = sharpe_ratio if sharpe_ratio is not None else 0

        logger.debug(f"Final values for logging - safe_total_return: {safe_total_return}, safe_sharpe_ratio: {safe_sharpe_ratio}")
        logger.info(f"回测完成: 总收益率={safe_total_return:.2%}, 夏普比率={safe_sharpe_ratio:.2f}")
        return result

    def _calculate_sharpe_ratio(self, daily_returns: List[Dict[str, Any]]) -> float:
        """自定义计算夏普比率"""
        import numpy as np

        # 提取日收益率
        returns = [dr.get('return', 0) for dr in daily_returns[1:]]  # 跳过第一天

        if not returns or len(returns) < 2:
            return 0.0

        # 计算年化夏普比率
        avg_return = np.mean(returns)
        std_return = np.std(returns, ddof=1)  # 使用样本标准差

        if std_return == 0:
            return 0.0

        # 年化处理（假设252个交易日）
        annual_return = avg_return * 252
        annual_std = std_return * np.sqrt(252)

        # 夏普比率（假设无风险利率为0）
        sharpe_ratio = annual_return / annual_std

        return sharpe_ratio
    
    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务状态"""
        if task_id in self.running_tasks:
            return self.running_tasks[task_id]
        elif task_id in self.completed_tasks:
            return self.completed_tasks[task_id]
        return None
    
    def get_all_tasks(self) -> Dict[str, List[Dict[str, Any]]]:
        """获取所有任务"""
        return {
            'running': list(self.running_tasks.values()),
            'completed': list(self.completed_tasks.values())
        }
    
    def get_result(self, task_id: str) -> Optional[BacktestResult]:
        """获取回测结果"""
        # 首先尝试从内存中获取
        task = self.get_task_status(task_id)
        if task and task.get('status') == 'completed':
            return task.get('result')

        # 如果内存中没有，尝试从文件中加载
        try:
            logger.info(f"📖 尝试从文件加载回测结果: {task_id}")
            result_dict = backtest_storage.get_result(task_id)
            if result_dict:
                # 将字典转换为BacktestResult对象
                result = BacktestResult(**result_dict)
                logger.info(f"✅ 成功从文件加载回测结果: {task_id}")
                return result
        except Exception as e:
            logger.error(f"❌ 从文件加载回测结果失败: {task_id}, 错误: {e}")

        return None


# 全局实例
simple_backtest_engine = SimpleBacktestEngine()
