#!/usr/bin/env python3
"""
简单的策略测试
"""

import requests
import json
import time

BASE_URL = "http://localhost:8000"

def test_data_api():
    """测试数据API"""
    print("=== 测试数据API ===")
    
    # 测试股票列表
    response = requests.get(f"{BASE_URL}/api/data/stocks?page=1&page_size=3")
    print(f"股票列表API: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        if data.get('success'):
            stocks = data.get('data', [])
            print(f"获取到 {len(stocks)} 只股票")
            
            if stocks:
                test_stock = stocks[0]
                stock_code = test_stock['code']
                print(f"测试股票: {test_stock['name']}({stock_code})")
                
                # 测试股票数据
                response = requests.get(f"{BASE_URL}/api/data/{stock_code}?period=1d&count=5")
                print(f"股票数据API: {response.status_code}")
                
                if response.status_code == 200:
                    data = response.json()
                    if data.get('success'):
                        stock_data = data.get('data', [])
                        print(f"获取到 {len(stock_data)} 条数据")
                        return True
                    else:
                        print(f"数据获取失败: {data.get('message')}")
                else:
                    print(f"股票数据API失败: {response.text}")
        else:
            print(f"股票列表获取失败: {data.get('message')}")
    else:
        print(f"股票列表API失败: {response.text}")
    
    return False

def test_strategy_execution():
    """测试策略执行"""
    print("\n=== 测试策略执行 ===")
    
    # 清理现有策略
    response = requests.get(f"{BASE_URL}/api/live/strategies")
    if response.status_code == 200:
        strategies = response.json()
        for strategy in strategies:
            if 'test' in strategy['name'].lower():
                requests.delete(f"{BASE_URL}/api/live/strategies/{strategy['id']}")
    
    # 启动策略
    strategy_data = {
        'name': 'simple_test_strategy',
        'strategy_type': 'bollinger_bands',
        'config': {
            'initial_capital': 50000,
            'paper_trading': True,
            'stock_codes': ['000001.SZ', '000002.SZ'],
            'bb_period': 10,
            'bb_std': 1.5
        }
    }
    
    print("启动策略...")
    response = requests.post(f"{BASE_URL}/api/live/strategies", json=strategy_data)
    
    if response.status_code == 200:
        result = response.json()
        if result.get('success'):
            strategy_id = result['strategy_id']
            print(f"策略启动成功: {strategy_id}")
            
            # 获取策略详情
            response = requests.get(f"{BASE_URL}/api/live/strategies/{strategy_id}")
            if response.status_code == 200:
                strategy = response.json()
                task_id = strategy.get('task_id')
                print(f"task_id: {task_id}")
                
                # 检查实盘引擎
                response = requests.get(f"{BASE_URL}/api/live/results")
                if response.status_code == 200:
                    data = response.json()
                    if data.get('success'):
                        results = data.get('data', [])
                        print(f"实盘引擎任务数量: {len(results)}")
                        
                        for result in results:
                            if result.get('task_id') == task_id:
                                print(f"找到对应任务:")
                                print(f"  状态: {result.get('status')}")
                                print(f"  股票池: {result.get('stock_codes', [])}")
                                print(f"  当前资金: {result.get('current_capital', 0)}")
                                break
                        else:
                            print("未找到对应的实盘引擎任务")
                
                # 观察策略状态
                print("\n观察策略状态...")
                for i in range(5):
                    time.sleep(2)
                    response = requests.get(f"{BASE_URL}/api/live/strategies/{strategy_id}")
                    if response.status_code == 200:
                        strategy = response.json()
                        print(f"[{i+1}] 持仓:{strategy['positions']} 交易:{strategy['trade_count']} 盈亏:{strategy['pnl']}")
                        
                        if strategy.get('error_message'):
                            print(f"    错误: {strategy['error_message']}")
                        
                        if strategy['trade_count'] > 0:
                            print("策略开始交易!")
                            break
                
                # 清理
                requests.delete(f"{BASE_URL}/api/live/strategies/{strategy_id}")
                print("策略已清理")
                return True
            else:
                print("获取策略详情失败")
        else:
            print(f"策略启动失败: {result.get('message')}")
    else:
        print(f"策略启动请求失败: {response.status_code}")
    
    return False

def main():
    """主函数"""
    print("开始简单策略测试")
    
    # 测试数据API
    data_ok = test_data_api()
    
    # 测试策略执行
    strategy_ok = test_strategy_execution()
    
    print(f"\n测试结果:")
    print(f"数据API: {'OK' if data_ok else 'FAIL'}")
    print(f"策略执行: {'OK' if strategy_ok else 'FAIL'}")

if __name__ == "__main__":
    main()
