import React, { useState, useEffect } from 'react';
import { Card as ShadCard, Card<PERSON>eader as <PERSON><PERSON><PERSON>ardHeader, Card<PERSON><PERSON>le as Shad<PERSON>ardTitle, CardContent as ShadCardContent } from '../components/UI/card.jsx';
import { But<PERSON> as ShadButton } from '../components/UI/button.jsx';
import { Alert as ShadAlert } from '../components/UI/alert.jsx';
import { DataTable } from '../components/UI/table.jsx';
import { Tag } from '../components/UI/tag.jsx';
import { Modal } from '../components/UI/modal.jsx';
import {
  Search,
  Download,
  TrendingUp,
  BarChart3,
  FileText,
  Trash2,
  Eye,
  RefreshCw,
  Star,
  Target,
  Activity,
  Zap,
  DollarSign,
  Award,
  Shield,
  Plus
} from 'lucide-react';
import { toast } from 'sonner';

const StockSelectionPage = () => {
  const [loading, setLoading] = useState(false);
  const [selectedStocks, setSelectedStocks] = useState([]);
  const [selectionFiles, setSelectionFiles] = useState([]);
  const [showCriteriaModal, setShowCriteriaModal] = useState(false);
  const [showResultModal, setShowResultModal] = useState(false);
  const [selectedResult, setSelectedResult] = useState(null);
  const [presets, setPresets] = useState({});

  // 异步选股状态
  // eslint-disable-next-line no-unused-vars
  const [currentTaskId, setCurrentTaskId] = useState(null);
  const [taskStatus, setTaskStatus] = useState(null);
  const [showProgressModal, setShowProgressModal] = useState(false);
  
  // 可勾选的选股条件
  const [selectedConditions, setSelectedConditions] = useState({
    // ETF筛选
    only_etf: false,            // 仅选择ETF
    include_etf: false,         // 包含ETF
    exclude_etf: false,         // 排除ETF

    // 技术指标
    rsi_oversold: false,        // RSI超卖（RSI小于30）
    rsi_overbought: false,      // RSI超买（RSI大于70）
    rsi_neutral: false,         // RSI中性（30-70）
    bb_breakout_up: false,      // 布林带向上突破
    bb_breakout_down: false,    // 布林带向下突破
    bb_squeeze: false,          // 布林带收窄
    volume_surge: false,        // 成交量放大（大于1.5倍）
    volume_shrink: false,       // 成交量萎缩（小于0.5倍）
    price_near_high: false,     // 价格接近高点
    price_near_low: false,      // 价格接近低点
    ma_golden_cross: false,     // 均线金叉
    ma_death_cross: false,      // 均线死叉
    high_volatility: false,     // 高波动率
    low_volatility: false,      // 低波动率

    // 价值因子
    low_pe: false,              // 低市盈率（PE<20）
    low_pb: false,              // 低市净率（PB<2.5）
    high_dividend: false,       // 高股息率（>3%）

    // 质量因子
    high_roe: false,            // 高ROE（>15%）
    high_roa: false,            // 高ROA（>8%）
    high_margin: false,         // 高毛利率（>30%）

    // 成长因子
    revenue_growth: false,      // 营收高增长（>20%）
    profit_growth: false,       // 利润高增长（>25%）
    eps_growth: false,          // EPS高增长（>20%）

    // 动量因子
    momentum_1m_strong: false,  // 1月强势动量（>5%）
    momentum_3m_strong: false,  // 3月强势动量（>15%）
    momentum_reversal: false,   // 动量反转（近期下跌）

    // 风险控制
    low_risk: false,            // 低风险（低波动率+低Beta）
    high_liquidity: false,      // 高流动性（大成交额）
    stable_beta: false,         // 稳定Beta（0.8-1.2）
  });

  // 条件组合逻辑
  const [conditionLogic, setConditionLogic] = useState('flexible');

  // 预设策略
  const presetStrategies = [
    {
      name: 'all_etfs',
      displayName: '所有ETF',
      description: '选择所有可交易的ETF基金',
      conditions: { only_etf: true }
    },
    {
      name: 'value_stocks',
      displayName: '价值投资',
      description: '低估值高分红的价值股',
      conditions: { exclude_etf: true, low_pe: true, low_pb: true, high_dividend: true, high_roe: true }
    },
    {
      name: 'growth_stocks',
      displayName: '成长投资',
      description: '高成长性的优质股票',
      conditions: { exclude_etf: true, revenue_growth: true, profit_growth: true, eps_growth: true, high_roe: true }
    },
    {
      name: 'momentum_stocks',
      displayName: '动量投资',
      description: '具有强劲上涨动量的股票',
      conditions: { exclude_etf: true, momentum_1m_strong: true, momentum_3m_strong: true, volume_surge: true }
    },
    {
      name: 'low_risk_stocks',
      displayName: '稳健投资',
      description: '低风险稳健的投资标的',
      conditions: { exclude_etf: true, low_risk: true, high_liquidity: true, stable_beta: true, high_dividend: true }
    },
    {
      name: 'technical_breakout',
      displayName: '技术突破',
      description: '技术面突破的强势股',
      conditions: { exclude_etf: true, ma_golden_cross: true, rsi_neutral: true, bb_breakout_up: true, volume_surge: true }
    }
  ];

  // 应用预设策略
  const applyPresetStrategy = (preset) => {
    setSelectedConditions(prev => ({
      ...Object.keys(prev).reduce((acc, key) => ({ ...acc, [key]: false }), {}),
      ...preset.conditions
    }));
  };

  // 将勾选条件转换为后端API格式
  const convertToApiCriteria = () => {
    const criteria = {
      condition_logic: conditionLogic,
      exclude_st: true,
      exclude_new_stock: true,
      lookback_days: 30,
      min_trading_days: 20
    };

    // ETF筛选条件
    if (selectedConditions.only_etf) {
      criteria.only_etf = true;
    }
    if (selectedConditions.include_etf) {
      criteria.include_etf = true;
    }
    if (selectedConditions.exclude_etf) {
      criteria.exclude_etf = true;
    }

    // RSI条件
    if (selectedConditions.rsi_oversold) {
      criteria.rsi_max = 30;
    }
    if (selectedConditions.rsi_overbought) {
      criteria.rsi_min = 70;
    }
    if (selectedConditions.rsi_neutral) {
      criteria.rsi_min = 30;
      criteria.rsi_max = 70;
    }

    // 布林带条件
    if (selectedConditions.bb_breakout_up) {
      criteria.bb_position = 'upper';
    }
    if (selectedConditions.bb_breakout_down) {
      criteria.bb_position = 'lower';
    }

    // 成交量条件
    if (selectedConditions.volume_surge) {
      criteria.volume_ratio_min = 1.5;
    }
    if (selectedConditions.volume_shrink) {
      criteria.volume_ratio_max = 0.5;
    }

    // 趋势条件
    if (selectedConditions.ma_golden_cross) {
      criteria.ma_arrangement = 'bullish';
    }
    if (selectedConditions.ma_death_cross) {
      criteria.ma_arrangement = 'bearish';
    }

    // 波动率条件
    if (selectedConditions.high_volatility) {
      criteria.volatility_min = 0.25;
    }
    if (selectedConditions.low_volatility) {
      criteria.volatility_max = 0.15;
    }

    // 价值因子条件
    if (selectedConditions.low_pe) {
      criteria.pe_max = 20;
    }
    if (selectedConditions.low_pb) {
      criteria.pb_max = 2.5;
    }
    if (selectedConditions.high_dividend) {
      criteria.dividend_yield_min = 0.03;
    }

    // 质量因子条件
    if (selectedConditions.high_roe) {
      criteria.roe_min = 15;
    }
    if (selectedConditions.high_roa) {
      criteria.roa_min = 8;
    }
    if (selectedConditions.high_margin) {
      criteria.gross_margin_min = 30;
    }

    // 成长因子条件
    if (selectedConditions.revenue_growth) {
      criteria.revenue_growth_min = 20;
    }
    if (selectedConditions.profit_growth) {
      criteria.profit_growth_min = 25;
    }
    if (selectedConditions.eps_growth) {
      criteria.eps_growth_min = 20;
    }

    // 动量因子条件
    if (selectedConditions.momentum_1m_strong) {
      criteria.momentum_1m_min = 0.05;
    }
    if (selectedConditions.momentum_3m_strong) {
      criteria.momentum_3m_min = 0.15;
    }
    if (selectedConditions.momentum_reversal) {
      criteria.momentum_1m_max = -0.05;
    }

    // 风险控制条件
    if (selectedConditions.low_risk) {
      criteria.volatility_max = 0.2;
      criteria.beta_max = 1.1;
    }
    if (selectedConditions.high_liquidity) {
      criteria.avg_amount_min = 10000; // 1亿元
    }
    if (selectedConditions.stable_beta) {
      criteria.beta_min = 0.8;
      criteria.beta_max = 1.2;
    }

    return criteria;
  };
  
  const [customName, setCustomName] = useState('');
  const [maxResults, setMaxResults] = useState(100);

  // 股票池相关状态
  // eslint-disable-next-line no-unused-vars
  const [showAddToPoolModal, setShowAddToPoolModal] = useState(false);
  // eslint-disable-next-line no-unused-vars
  const [selectedResults, setSelectedResults] = useState([]);
  // eslint-disable-next-line no-unused-vars
  const [stockPools, setStockPools] = useState([]);
  // eslint-disable-next-line no-unused-vars
  const [addToPoolData, setAddToPoolData] = useState({
    pool_name: '',
    create_new_pool: true,
    target_pool_id: ''
  });

  // 加载选股文件列表
  const loadSelectionFiles = async () => {
    try {
      const response = await fetch('/api/stock-selection/files');
      const data = await response.json();
      
      if (data.success) {
        setSelectionFiles(data.data.files);
      }
    } catch (error) {
      console.error('加载选股文件失败:', error);
    }
  };

  // 加载预设条件
  const loadPresets = async () => {
    try {
      const response = await fetch('/api/stock-selection/presets');
      const data = await response.json();
      
      if (data.success) {
        setPresets(data.data.presets);
      }
    } catch (error) {
      console.error('加载预设条件失败:', error);
    }
  };

  // 执行选股
  const executeSelection = async () => {
    // 如果没有输入名称，使用默认名称
    const finalName = customName.trim() || `选股_${new Date().toLocaleString().replace(/[/\s:]/g, '_')}`;

    setLoading(true);
    
    try {
      const response = await fetch('/api/stock-selection/select', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          criteria: convertToApiCriteria(),
          custom_name: finalName,
          max_results: maxResults
        })
      });
      
      const data = await response.json();
      
      if (data.success) {
        // 获取任务ID，开始轮询状态
        const taskId = data.data.task_id;
        setCurrentTaskId(taskId);
        setTaskStatus({
          status: 'pending',
          progress: 0,
          message: '选股任务已启动...'
        });

        toast.success('选股任务已启动，正在处理中...');
        setShowCriteriaModal(false);
        setShowProgressModal(true);

        // 开始轮询任务状态
        pollTaskStatus(taskId);
      } else {
        toast.error(data.message || '启动选股任务失败');
      }
    } catch (error) {
      console.error('启动选股任务失败:', error);
      toast.error('启动选股任务失败: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  // 轮询任务状态
  const pollTaskStatus = async (taskId) => {
    const poll = async () => {
      try {
        const response = await fetch(`/api/stock-selection/task/${taskId}`);
        const data = await response.json();

        if (data.success) {
          const status = data.data;
          setTaskStatus(status);

          if (status.status === 'completed') {
            // 任务完成
            toast.success(`选股完成！共选中 ${status.selected_count} 只股票`);
            setShowProgressModal(false);

            // 设置结果并刷新文件列表
            if (status.result) {
              setSelectedStocks(status.result.selected_stocks);
            }
            loadSelectionFiles();

            return; // 停止轮询
          } else if (status.status === 'failed') {
            // 任务失败
            toast.error(`选股失败: ${status.error || '未知错误'}`);
            setShowProgressModal(false);
            return; // 停止轮询
          }

          // 继续轮询
          setTimeout(poll, 2000); // 每2秒轮询一次
        } else {
          toast.error('获取任务状态失败');
          setShowProgressModal(false);
        }
      } catch (error) {
        console.error('轮询任务状态失败:', error);
        toast.error('获取任务状态失败');
        setShowProgressModal(false);
      }
    };

    // 开始轮询
    poll();
  };

  // 应用预设条件
  const applyPreset = (presetKey) => {
    const preset = presets[presetKey];
    if (preset) {
      // 根据预设条件设置勾选状态
      const newConditions = { ...selectedConditions };

      // 重置所有条件
      Object.keys(newConditions).forEach(key => {
        newConditions[key] = false;
      });

      // 根据预设条件设置对应的勾选项
      const presetCriteria = preset.criteria;

      if (presetCriteria.rsi_min && presetCriteria.rsi_min >= 50) {
        newConditions.rsi_overbought = true;
      }
      if (presetCriteria.rsi_max && presetCriteria.rsi_max <= 30) {
        newConditions.rsi_oversold = true;
      }
      if (presetCriteria.volume_min && presetCriteria.volume_min >= 1.5) {
        newConditions.volume_surge = true;
      }
      if (presetCriteria.bb_position === 'upper') {
        newConditions.bb_breakout_up = true;
      }
      if (presetCriteria.bb_position === 'lower') {
        newConditions.bb_breakout_down = true;
      }
      if (presetCriteria.ma_trend === 'bullish') {
        newConditions.ma_golden_cross = true;
      }
      if (presetCriteria.atr_min && presetCriteria.atr_min >= 0.02) {
        newConditions.high_volatility = true;
      }

      setSelectedConditions(newConditions);
      setCustomName(preset.name);
      toast.success(`已应用预设条件: ${preset.name}`);
    }
  };

  // 查看选股结果
  const viewSelectionResult = async (filename) => {
    try {
      const response = await fetch(`/api/stock-selection/files/${filename}`);
      const data = await response.json();
      
      if (data.success) {
        setSelectedResult(data.data);
        setShowResultModal(true);
      } else {
        toast.error('加载选股结果失败');
      }
    } catch (error) {
      console.error('加载选股结果失败:', error);
      toast.error('加载选股结果失败');
    }
  };

  // 删除选股文件
  const deleteSelectionFile = async (filename) => {
    if (!window.confirm('确定要删除这个选股结果吗？')) {
      return;
    }

    try {
      const response = await fetch(`/api/stock-selection/files/${filename}`, {
        method: 'DELETE'
      });
      
      const data = await response.json();
      
      if (data.success) {
        toast.success('删除成功');
        loadSelectionFiles();
      } else {
        toast.error('删除失败');
      }
    } catch (error) {
      console.error('删除失败:', error);
      toast.error('删除失败');
    }
  };

  // 导出股票代码
  const exportStockCodes = async (filename) => {
    try {
      const response = await fetch(`/api/stock-selection/files/${filename}/codes`);
      const data = await response.json();
      
      if (data.success) {
        const codes = data.data.stock_codes.join('\n');
        const blob = new Blob([codes], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `${filename.replace('.json', '')}-codes.txt`;
        a.click();
        URL.revokeObjectURL(url);
        
        toast.success('股票代码导出成功');
      } else {
        toast.error('导出失败');
      }
    } catch (error) {
      console.error('导出失败:', error);
      toast.error('导出失败');
    }
  };

  // 获取股票池列表
  const fetchStockPools = async () => {
    try {
      const response = await fetch('/api/stock-pool/pools');
      const data = await response.json();
      if (data.success) {
        setStockPools(data.data);
      }
    } catch (error) {
      console.error('获取股票池列表失败:', error);
    }
  };

  // 加入股票池
  // eslint-disable-next-line no-unused-vars
  const addToStockPool = async (filename) => {
    try {
      // 先获取选股结果
      const response = await fetch(`/api/stock-selection/result/${filename}`);
      const data = await response.json();

      if (data.success && data.data) {
        setSelectedResults(data.data);
        setAddToPoolData({
          pool_name: filename.replace('.json', ''),
          create_new_pool: true,
          target_pool_id: ''
        });
        await fetchStockPools();
        setShowAddToPoolModal(true);
      }
    } catch (error) {
      console.error('获取选股结果失败:', error);
    }
  };

  // 确认加入股票池
  const confirmAddToPool = async () => {
    try {
      const requestData = {
        selection_results: selectedResults,
        pool_name: addToPoolData.create_new_pool ? addToPoolData.pool_name : null,
        create_new_pool: addToPoolData.create_new_pool
      };

      const response = await fetch('/api/stock-pool/pools/add-selection-results', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData)
      });

      const data = await response.json();

      if (data.success) {
        setShowAddToPoolModal(false);
        toast.success(`成功将${data.data.added_count}只股票加入股票池"${data.data.pool_name}"`);
      } else {
        toast.error('加入股票池失败: ' + data.message);
      }
    } catch (error) {
      console.error('加入股票池失败:', error);
      toast.error('加入股票池失败: ' + error.message);
    }
  };

  useEffect(() => {
    loadSelectionFiles();
    loadPresets();
  }, []);

  // 选股结果表格列定义
  const stockColumns = [
    {
      accessorKey: 'stock_code',
      header: '股票代码',
    },
    {
      accessorKey: 'stock_name',
      header: '股票名称',
    },
    {
      accessorKey: 'score',
      header: '综合评分',
      cell: ({ row }) => (
        <Tag color={row.getValue('score') > 50 ? 'green' : row.getValue('score') > 30 ? 'orange' : 'red'}>
          {row.getValue('score').toFixed(1)}
        </Tag>
      ),
    },
    {
      accessorKey: 'indicators',
      header: 'RSI',
      cell: ({ row }) => {
        const rsi = row.getValue('indicators')?.rsi;
        return rsi ? rsi.toFixed(1) : '-';
      },
    },
    {
      accessorKey: 'indicators',
      header: '1月动量',
      cell: ({ row }) => {
        const momentum = row.getValue('indicators')?.momentum_1m;
        if (momentum === undefined || momentum === null) return '-';
        const percent = (momentum * 100).toFixed(1);
        return (
          <Tag color={momentum > 0.05 ? 'green' : momentum < -0.05 ? 'red' : 'gray'} size="sm">
            {percent}%
          </Tag>
        );
      },
    },
    {
      accessorKey: 'indicators',
      header: '波动率',
      cell: ({ row }) => {
        const volatility = row.getValue('indicators')?.volatility;
        if (volatility === undefined || volatility === null) return '-';
        const percent = (volatility * 100).toFixed(1);
        return (
          <Tag color={volatility < 0.2 ? 'green' : volatility > 0.4 ? 'red' : 'orange'} size="sm">
            {percent}%
          </Tag>
        );
      },
    },
    {
      accessorKey: 'indicators',
      header: '成交额',
      cell: ({ row }) => {
        const amount = row.getValue('indicators')?.avg_amount;
        if (amount === undefined || amount === null) return '-';
        if (amount > 10000) {
          return <Tag color="green" size="sm">{(amount/10000).toFixed(1)}亿</Tag>;
        } else if (amount > 1000) {
          return <Tag color="orange" size="sm">{(amount/1000).toFixed(1)}千万</Tag>;
        } else {
          return <Tag color="gray" size="sm">{amount.toFixed(0)}万</Tag>;
        }
      },
    },
    {
      accessorKey: 'indicators',
      header: '均线排列',
      cell: ({ row }) => {
        const arrangement = row.getValue('indicators')?.ma_arrangement;
        const arrangementColors = {
          'strong_bullish': 'green',
          'bullish': 'green',
          'neutral': 'gray',
          'bearish': 'red',
          'strong_bearish': 'red'
        };
        const arrangementLabels = {
          'strong_bullish': '强多头',
          'bullish': '多头',
          'neutral': '中性',
          'bearish': '空头',
          'strong_bearish': '强空头'
        };
        return arrangement ? (
          <Tag color={arrangementColors[arrangement] || 'gray'} size="sm">
            {arrangementLabels[arrangement] || arrangement}
          </Tag>
        ) : '-';
      },
    },
    {
      accessorKey: 'selection_date',
      header: '选择时间',
      cell: ({ row }) => new Date(row.getValue('selection_date')).toLocaleString(),
    },
  ];

  // 选股文件表格列定义
  const fileColumns = [
    {
      accessorKey: 'custom_name',
      header: '名称',
    },
    {
      accessorKey: 'date',
      header: '日期',
    },
    {
      accessorKey: 'time',
      header: '时间',
    },
    {
      accessorKey: 'total_selected',
      header: '股票数量',
      cell: ({ row }) => (
        <Tag color="blue">
          {row.getValue('total_selected')}
        </Tag>
      ),
    },
    {
      accessorKey: 'actions',
      header: '操作',
      cell: ({ row }) => (
        <div className="flex gap-2">
          <ShadButton
            size="sm"
            variant="outline"
            onClick={() => viewSelectionResult(row.original.filename)}
          >
            <Eye className="w-3 h-3 mr-1" />
            查看
          </ShadButton>
          <ShadButton
            size="sm"
            variant="outline"
            onClick={() => exportStockCodes(row.original.filename)}
          >
            <Download className="w-3 h-3 mr-1" />
            导出
          </ShadButton>
          <ShadButton
            size="sm"
            variant="outline"
            onClick={() => addToStockPool(row.original.filename)}
          >
            <Plus className="w-3 h-3 mr-1" />
            加入股票池
          </ShadButton>
          <ShadButton
            size="sm"
            variant="outline"
            onClick={() => deleteSelectionFile(row.original.filename)}
          >
            <Trash2 className="w-3 h-3 mr-1" />
            删除
          </ShadButton>
        </div>
      ),
    },
  ];

  return (
    <div className="space-y-6 p-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-2">
            <Target className="w-8 h-8 text-blue-600" />
            智能选股
          </h1>
          <p className="text-gray-600 mt-2">
            基于技术指标和Alpha101因子的智能股票筛选系统
          </p>
        </div>
        
        <div className="flex gap-2">
          <ShadButton
            onClick={() => setShowCriteriaModal(true)}
            disabled={loading}
          >
            <Search className="w-4 h-4 mr-2" />
            开始选股
          </ShadButton>
          <ShadButton
            variant="outline"
            onClick={loadSelectionFiles}
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            刷新
          </ShadButton>
        </div>
      </div>

      {/* 功能说明 */}
      <ShadAlert
        title="功能说明"
        description="智能选股系统支持多种技术指标筛选（ATR、布林带、RSI、MACD等）和Alpha101因子分析，帮助您发现符合条件的投资标的。选股结果将保存为JSON文件，可用于策略回测和实盘交易。"
        variant="info"
        icon={<Target className="w-4 h-4" />}
      />

      {/* 预设选股策略 */}
      <ShadCard>
        <ShadCardHeader>
          <ShadCardTitle className="flex items-center gap-2">
            <Star className="w-5 h-5 text-yellow-500" />
            预设选股策略
          </ShadCardTitle>
        </ShadCardHeader>
        <ShadCardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {Object.entries(presets).map(([key, preset]) => (
              <div key={key} className="p-4 border rounded-lg hover:bg-gray-50 cursor-pointer"
                   onClick={() => applyPreset(key)}>
                <div className="flex items-center gap-2 mb-2">
                  <TrendingUp className="w-4 h-4 text-green-500" />
                  <h3 className="font-medium">{preset.name}</h3>
                </div>
                <p className="text-sm text-gray-600">{preset.description}</p>
              </div>
            ))}
          </div>
        </ShadCardContent>
      </ShadCard>

      {/* 当前选股结果 */}
      {selectedStocks.length > 0 && (
        <ShadCard>
          <ShadCardHeader>
            <ShadCardTitle className="flex items-center gap-2">
              <BarChart3 className="w-5 h-5 text-blue-500" />
              当前选股结果 ({selectedStocks.length})
            </ShadCardTitle>
          </ShadCardHeader>
          <ShadCardContent>
            <DataTable
              columns={stockColumns}
              data={selectedStocks}
            />
          </ShadCardContent>
        </ShadCard>
      )}

      {/* 历史选股结果 */}
      <ShadCard>
        <ShadCardHeader>
          <ShadCardTitle className="flex items-center gap-2">
            <FileText className="w-5 h-5 text-gray-500" />
            历史选股结果 ({selectionFiles.length})
          </ShadCardTitle>
        </ShadCardHeader>
        <ShadCardContent>
          {selectionFiles.length > 0 ? (
            <DataTable
              columns={fileColumns}
              data={selectionFiles}
            />
          ) : (
            <div className="text-center py-8 text-gray-500">
              <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>暂无历史选股结果</p>
            </div>
          )}
        </ShadCardContent>
      </ShadCard>

      {/* 选股条件设置模态框 */}
      <Modal
        open={showCriteriaModal}
        onClose={() => setShowCriteriaModal(false)}
        title="设置选股条件"
        size="lg"
      >
        <div className="space-y-6">
          {/* 基本设置 */}
          <div className="grid grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium mb-2">自定义名称</label>
              <input
                type="text"
                value={customName}
                onChange={(e) => setCustomName(e.target.value)}
                className="w-full px-3 py-2 border rounded-md"
                placeholder="留空自动生成"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">最大结果数</label>
              <input
                type="number"
                value={maxResults}
                onChange={(e) => setMaxResults(parseInt(e.target.value) || 100)}
                className="w-full px-3 py-2 border rounded-md"
                min="10"
                max="500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">条件组合模式</label>
              <select
                value={conditionLogic}
                onChange={(e) => setConditionLogic(e.target.value)}
                className="w-full px-3 py-2 border rounded-md"
              >
                <option value="flexible">灵活模式（推荐）</option>
                <option value="strict">严格模式（AND）</option>
                <option value="any">宽松模式（OR）</option>
              </select>
            </div>
          </div>

          {/* 预设策略快速选择 */}
          <div className="p-4 bg-gray-50 rounded-lg">
            <h3 className="text-lg font-medium mb-3 flex items-center">
              <Star className="w-5 h-5 mr-2 text-yellow-500" />
              预设策略
            </h3>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
              {presetStrategies.map((preset) => (
                <button
                  key={preset.name}
                  onClick={() => applyPresetStrategy(preset)}
                  className="p-3 text-left border rounded-lg hover:bg-white hover:shadow-sm transition-all duration-200 group"
                >
                  <div className="font-medium text-gray-900 group-hover:text-blue-600">
                    {preset.displayName}
                  </div>
                  <div className="text-sm text-gray-500 mt-1">
                    {preset.description}
                  </div>
                </button>
              ))}
            </div>
          </div>

          {/* 条件组合说明 */}
          <div className="bg-blue-50 p-4 rounded-lg">
            <h4 className="font-medium text-blue-800 mb-2">条件组合模式说明：</h4>
            <ul className="text-sm text-blue-700 space-y-1">
              <li><strong>灵活模式</strong>：基于评分系统，满足50%以上条件或评分足够高的股票</li>
              <li><strong>严格模式</strong>：必须同时满足所有设置的条件（AND关系）</li>
              <li><strong>宽松模式</strong>：满足任意一个条件即可（OR关系）</li>
            </ul>
          </div>

          {/* 可勾选的选股条件 */}
          <div className="space-y-6">
            {/* ETF筛选条件 */}
            <div>
              <h3 className="text-lg font-medium mb-3 flex items-center">
                <Target className="w-5 h-5 mr-2 text-purple-500" />
                ETF筛选
              </h3>
              <div className="grid grid-cols-3 gap-4">
                <label className="flex items-center space-x-3 p-3 border rounded-lg hover:bg-gray-50 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={selectedConditions.only_etf}
                    onChange={(e) => setSelectedConditions({...selectedConditions, only_etf: e.target.checked})}
                    className="w-4 h-4 text-blue-600"
                  />
                  <div>
                    <div className="font-medium">仅选择ETF</div>
                    <div className="text-sm text-gray-500">只选择ETF基金</div>
                  </div>
                </label>
                <label className="flex items-center space-x-3 p-3 border rounded-lg hover:bg-gray-50 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={selectedConditions.include_etf}
                    onChange={(e) => setSelectedConditions({...selectedConditions, include_etf: e.target.checked})}
                    className="w-4 h-4 text-blue-600"
                  />
                  <div>
                    <div className="font-medium">包含ETF</div>
                    <div className="text-sm text-gray-500">股票+ETF混合选择</div>
                  </div>
                </label>
                <label className="flex items-center space-x-3 p-3 border rounded-lg hover:bg-gray-50 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={selectedConditions.exclude_etf}
                    onChange={(e) => setSelectedConditions({...selectedConditions, exclude_etf: e.target.checked})}
                    className="w-4 h-4 text-blue-600"
                  />
                  <div>
                    <div className="font-medium">排除ETF</div>
                    <div className="text-sm text-gray-500">只选择股票，排除ETF</div>
                  </div>
                </label>
              </div>
            </div>

            {/* RSI条件 */}
            <div>
              <h3 className="text-lg font-medium mb-3 flex items-center">
                <TrendingUp className="w-5 h-5 mr-2 text-blue-500" />
                RSI相对强弱指标
              </h3>
              <div className="grid grid-cols-2 gap-4">
                <label className="flex items-center space-x-3 p-3 border rounded-lg hover:bg-gray-50 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={selectedConditions.rsi_oversold}
                    onChange={(e) => setSelectedConditions({...selectedConditions, rsi_oversold: e.target.checked})}
                    className="w-4 h-4 text-blue-600"
                  />
                  <div>
                    <div className="font-medium">RSI超卖</div>
                    <div className="text-sm text-gray-500">RSI &lt; 30，可能反弹</div>
                  </div>
                </label>
                <label className="flex items-center space-x-3 p-3 border rounded-lg hover:bg-gray-50 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={selectedConditions.rsi_overbought}
                    onChange={(e) => setSelectedConditions({...selectedConditions, rsi_overbought: e.target.checked})}
                    className="w-4 h-4 text-blue-600"
                  />
                  <div>
                    <div className="font-medium">RSI超买</div>
                    <div className="text-sm text-gray-500">RSI &gt; 70，可能回调</div>
                  </div>
                </label>
                <label className="flex items-center space-x-3 p-3 border rounded-lg hover:bg-gray-50 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={selectedConditions.rsi_neutral}
                    onChange={(e) => setSelectedConditions({...selectedConditions, rsi_neutral: e.target.checked})}
                    className="w-4 h-4 text-blue-600"
                  />
                  <div>
                    <div className="font-medium">RSI中性</div>
                    <div className="text-sm text-gray-500">30 ≤ RSI ≤ 70，相对稳定</div>
                  </div>
                </label>
              </div>
            </div>

            {/* 布林带条件 */}
            <div>
              <h3 className="text-lg font-medium mb-3 flex items-center">
                <BarChart3 className="w-5 h-5 mr-2 text-green-500" />
                布林带指标
              </h3>
              <div className="grid grid-cols-2 gap-4">
                <label className="flex items-center space-x-3 p-3 border rounded-lg hover:bg-gray-50 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={selectedConditions.bb_breakout_up}
                    onChange={(e) => setSelectedConditions({...selectedConditions, bb_breakout_up: e.target.checked})}
                    className="w-4 h-4 text-blue-600"
                  />
                  <div>
                    <div className="font-medium">向上突破</div>
                    <div className="text-sm text-gray-500">价格突破上轨，强势信号</div>
                  </div>
                </label>
                <label className="flex items-center space-x-3 p-3 border rounded-lg hover:bg-gray-50 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={selectedConditions.bb_breakout_down}
                    onChange={(e) => setSelectedConditions({...selectedConditions, bb_breakout_down: e.target.checked})}
                    className="w-4 h-4 text-blue-600"
                  />
                  <div>
                    <div className="font-medium">向下突破</div>
                    <div className="text-sm text-gray-500">价格跌破下轨，弱势信号</div>
                  </div>
                </label>
                <label className="flex items-center space-x-3 p-3 border rounded-lg hover:bg-gray-50 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={selectedConditions.bb_squeeze}
                    onChange={(e) => setSelectedConditions({...selectedConditions, bb_squeeze: e.target.checked})}
                    className="w-4 h-4 text-blue-600"
                  />
                  <div>
                    <div className="font-medium">布林带收窄</div>
                    <div className="text-sm text-gray-500">波动率降低，可能变盘</div>
                  </div>
                </label>
              </div>
            </div>

            {/* 成交量条件 */}
            <div>
              <h3 className="text-lg font-medium mb-3 flex items-center">
                <Activity className="w-5 h-5 mr-2 text-purple-500" />
                成交量指标
              </h3>
              <div className="grid grid-cols-2 gap-4">
                <label className="flex items-center space-x-3 p-3 border rounded-lg hover:bg-gray-50 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={selectedConditions.volume_surge}
                    onChange={(e) => setSelectedConditions({...selectedConditions, volume_surge: e.target.checked})}
                    className="w-4 h-4 text-blue-600"
                  />
                  <div>
                    <div className="font-medium">成交量放大</div>
                    <div className="text-sm text-gray-500">成交量 &gt; 1.5倍均量</div>
                  </div>
                </label>
                <label className="flex items-center space-x-3 p-3 border rounded-lg hover:bg-gray-50 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={selectedConditions.volume_shrink}
                    onChange={(e) => setSelectedConditions({...selectedConditions, volume_shrink: e.target.checked})}
                    className="w-4 h-4 text-blue-600"
                  />
                  <div>
                    <div className="font-medium">成交量萎缩</div>
                    <div className="text-sm text-gray-500">成交量 &lt; 0.5倍均量</div>
                  </div>
                </label>
              </div>
            </div>

            {/* 趋势条件 */}
            <div>
              <h3 className="text-lg font-medium mb-3 flex items-center">
                <TrendingUp className="w-5 h-5 mr-2 text-orange-500" />
                趋势指标
              </h3>
              <div className="grid grid-cols-2 gap-4">
                <label className="flex items-center space-x-3 p-3 border rounded-lg hover:bg-gray-50 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={selectedConditions.ma_golden_cross}
                    onChange={(e) => setSelectedConditions({...selectedConditions, ma_golden_cross: e.target.checked})}
                    className="w-4 h-4 text-blue-600"
                  />
                  <div>
                    <div className="font-medium">均线金叉</div>
                    <div className="text-sm text-gray-500">短期均线上穿长期均线</div>
                  </div>
                </label>
                <label className="flex items-center space-x-3 p-3 border rounded-lg hover:bg-gray-50 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={selectedConditions.ma_death_cross}
                    onChange={(e) => setSelectedConditions({...selectedConditions, ma_death_cross: e.target.checked})}
                    className="w-4 h-4 text-blue-600"
                  />
                  <div>
                    <div className="font-medium">均线死叉</div>
                    <div className="text-sm text-gray-500">短期均线下穿长期均线</div>
                  </div>
                </label>
                <label className="flex items-center space-x-3 p-3 border rounded-lg hover:bg-gray-50 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={selectedConditions.momentum_strong}
                    onChange={(e) => setSelectedConditions({...selectedConditions, momentum_strong: e.target.checked})}
                    className="w-4 h-4 text-blue-600"
                  />
                  <div>
                    <div className="font-medium">强势动量</div>
                    <div className="text-sm text-gray-500">价格动量强劲上涨</div>
                  </div>
                </label>
                <label className="flex items-center space-x-3 p-3 border rounded-lg hover:bg-gray-50 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={selectedConditions.momentum_weak}
                    onChange={(e) => setSelectedConditions({...selectedConditions, momentum_weak: e.target.checked})}
                    className="w-4 h-4 text-blue-600"
                  />
                  <div>
                    <div className="font-medium">弱势动量</div>
                    <div className="text-sm text-gray-500">价格动量疲弱下跌</div>
                  </div>
                </label>
              </div>
            </div>

            {/* 波动率条件 */}
            <div>
              <h3 className="text-lg font-medium mb-3 flex items-center">
                <Zap className="w-5 h-5 mr-2 text-red-500" />
                波动率指标
              </h3>
              <div className="grid grid-cols-2 gap-4">
                <label className="flex items-center space-x-3 p-3 border rounded-lg hover:bg-gray-50 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={selectedConditions.high_volatility}
                    onChange={(e) => setSelectedConditions({...selectedConditions, high_volatility: e.target.checked})}
                    className="w-4 h-4 text-blue-600"
                  />
                  <div>
                    <div className="font-medium">高波动率</div>
                    <div className="text-sm text-gray-500">ATR &gt; 3%，活跃交易</div>
                  </div>
                </label>
                <label className="flex items-center space-x-3 p-3 border rounded-lg hover:bg-gray-50 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={selectedConditions.low_volatility}
                    onChange={(e) => setSelectedConditions({...selectedConditions, low_volatility: e.target.checked})}
                    className="w-4 h-4 text-blue-600"
                  />
                  <div>
                    <div className="font-medium">低波动率</div>
                    <div className="text-sm text-gray-500">ATR &lt; 1%，稳定交易</div>
                  </div>
                </label>
              </div>
            </div>

            {/* 价格位置条件 */}
            <div>
              <h3 className="text-lg font-medium mb-3 flex items-center">
                <Target className="w-5 h-5 mr-2 text-indigo-500" />
                价格位置
              </h3>
              <div className="grid grid-cols-2 gap-4">
                <label className="flex items-center space-x-3 p-3 border rounded-lg hover:bg-gray-50 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={selectedConditions.price_near_high}
                    onChange={(e) => setSelectedConditions({...selectedConditions, price_near_high: e.target.checked})}
                    className="w-4 h-4 text-blue-600"
                  />
                  <div>
                    <div className="font-medium">接近高点</div>
                    <div className="text-sm text-gray-500">价格接近近期高点</div>
                  </div>
                </label>
                <label className="flex items-center space-x-3 p-3 border rounded-lg hover:bg-gray-50 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={selectedConditions.price_near_low}
                    onChange={(e) => setSelectedConditions({...selectedConditions, price_near_low: e.target.checked})}
                    className="w-4 h-4 text-blue-600"
                  />
                  <div>
                    <div className="font-medium">接近低点</div>
                    <div className="text-sm text-gray-500">价格接近近期低点</div>
                  </div>
                </label>
              </div>
            </div>

            {/* 价值因子条件 */}
            <div>
              <h3 className="text-lg font-medium mb-3 flex items-center">
                <DollarSign className="w-5 h-5 mr-2 text-green-500" />
                价值因子
              </h3>
              <div className="grid grid-cols-3 gap-4">
                <label className="flex items-center space-x-3 p-3 border rounded-lg hover:bg-gray-50 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={selectedConditions.low_pe}
                    onChange={(e) => setSelectedConditions({...selectedConditions, low_pe: e.target.checked})}
                    className="w-4 h-4 text-blue-600"
                  />
                  <div>
                    <div className="font-medium">低市盈率</div>
                    <div className="text-sm text-gray-500">PE &lt; 20</div>
                  </div>
                </label>
                <label className="flex items-center space-x-3 p-3 border rounded-lg hover:bg-gray-50 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={selectedConditions.low_pb}
                    onChange={(e) => setSelectedConditions({...selectedConditions, low_pb: e.target.checked})}
                    className="w-4 h-4 text-blue-600"
                  />
                  <div>
                    <div className="font-medium">低市净率</div>
                    <div className="text-sm text-gray-500">PB &lt; 2.5</div>
                  </div>
                </label>
                <label className="flex items-center space-x-3 p-3 border rounded-lg hover:bg-gray-50 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={selectedConditions.high_dividend}
                    onChange={(e) => setSelectedConditions({...selectedConditions, high_dividend: e.target.checked})}
                    className="w-4 h-4 text-blue-600"
                  />
                  <div>
                    <div className="font-medium">高股息率</div>
                    <div className="text-sm text-gray-500">股息率 &gt; 3%</div>
                  </div>
                </label>
              </div>
            </div>

            {/* 质量因子条件 */}
            <div>
              <h3 className="text-lg font-medium mb-3 flex items-center">
                <Award className="w-5 h-5 mr-2 text-yellow-500" />
                质量因子
              </h3>
              <div className="grid grid-cols-3 gap-4">
                <label className="flex items-center space-x-3 p-3 border rounded-lg hover:bg-gray-50 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={selectedConditions.high_roe}
                    onChange={(e) => setSelectedConditions({...selectedConditions, high_roe: e.target.checked})}
                    className="w-4 h-4 text-blue-600"
                  />
                  <div>
                    <div className="font-medium">高ROE</div>
                    <div className="text-sm text-gray-500">净资产收益率 &gt; 15%</div>
                  </div>
                </label>
                <label className="flex items-center space-x-3 p-3 border rounded-lg hover:bg-gray-50 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={selectedConditions.high_roa}
                    onChange={(e) => setSelectedConditions({...selectedConditions, high_roa: e.target.checked})}
                    className="w-4 h-4 text-blue-600"
                  />
                  <div>
                    <div className="font-medium">高ROA</div>
                    <div className="text-sm text-gray-500">总资产收益率 &gt; 8%</div>
                  </div>
                </label>
                <label className="flex items-center space-x-3 p-3 border rounded-lg hover:bg-gray-50 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={selectedConditions.high_margin}
                    onChange={(e) => setSelectedConditions({...selectedConditions, high_margin: e.target.checked})}
                    className="w-4 h-4 text-blue-600"
                  />
                  <div>
                    <div className="font-medium">高毛利率</div>
                    <div className="text-sm text-gray-500">毛利率 &gt; 30%</div>
                  </div>
                </label>
              </div>
            </div>

            {/* 成长因子条件 */}
            <div>
              <h3 className="text-lg font-medium mb-3 flex items-center">
                <TrendingUp className="w-5 h-5 mr-2 text-emerald-500" />
                成长因子
              </h3>
              <div className="grid grid-cols-3 gap-4">
                <label className="flex items-center space-x-3 p-3 border rounded-lg hover:bg-gray-50 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={selectedConditions.revenue_growth}
                    onChange={(e) => setSelectedConditions({...selectedConditions, revenue_growth: e.target.checked})}
                    className="w-4 h-4 text-blue-600"
                  />
                  <div>
                    <div className="font-medium">营收高增长</div>
                    <div className="text-sm text-gray-500">营收增长率 &gt; 20%</div>
                  </div>
                </label>
                <label className="flex items-center space-x-3 p-3 border rounded-lg hover:bg-gray-50 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={selectedConditions.profit_growth}
                    onChange={(e) => setSelectedConditions({...selectedConditions, profit_growth: e.target.checked})}
                    className="w-4 h-4 text-blue-600"
                  />
                  <div>
                    <div className="font-medium">利润高增长</div>
                    <div className="text-sm text-gray-500">利润增长率 &gt; 25%</div>
                  </div>
                </label>
                <label className="flex items-center space-x-3 p-3 border rounded-lg hover:bg-gray-50 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={selectedConditions.eps_growth}
                    onChange={(e) => setSelectedConditions({...selectedConditions, eps_growth: e.target.checked})}
                    className="w-4 h-4 text-blue-600"
                  />
                  <div>
                    <div className="font-medium">EPS高增长</div>
                    <div className="text-sm text-gray-500">EPS增长率 &gt; 20%</div>
                  </div>
                </label>
              </div>
            </div>

            {/* 动量因子条件 */}
            <div>
              <h3 className="text-lg font-medium mb-3 flex items-center">
                <Zap className="w-5 h-5 mr-2 text-orange-500" />
                动量因子
              </h3>
              <div className="grid grid-cols-3 gap-4">
                <label className="flex items-center space-x-3 p-3 border rounded-lg hover:bg-gray-50 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={selectedConditions.momentum_1m_strong}
                    onChange={(e) => setSelectedConditions({...selectedConditions, momentum_1m_strong: e.target.checked})}
                    className="w-4 h-4 text-blue-600"
                  />
                  <div>
                    <div className="font-medium">1月强势</div>
                    <div className="text-sm text-gray-500">1月涨幅 &gt; 5%</div>
                  </div>
                </label>
                <label className="flex items-center space-x-3 p-3 border rounded-lg hover:bg-gray-50 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={selectedConditions.momentum_3m_strong}
                    onChange={(e) => setSelectedConditions({...selectedConditions, momentum_3m_strong: e.target.checked})}
                    className="w-4 h-4 text-blue-600"
                  />
                  <div>
                    <div className="font-medium">3月强势</div>
                    <div className="text-sm text-gray-500">3月涨幅 &gt; 15%</div>
                  </div>
                </label>
                <label className="flex items-center space-x-3 p-3 border rounded-lg hover:bg-gray-50 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={selectedConditions.momentum_reversal}
                    onChange={(e) => setSelectedConditions({...selectedConditions, momentum_reversal: e.target.checked})}
                    className="w-4 h-4 text-blue-600"
                  />
                  <div>
                    <div className="font-medium">反转机会</div>
                    <div className="text-sm text-gray-500">1月跌幅 &gt; 5%</div>
                  </div>
                </label>
              </div>
            </div>

            {/* 风险控制条件 */}
            <div>
              <h3 className="text-lg font-medium mb-3 flex items-center">
                <Shield className="w-5 h-5 mr-2 text-red-500" />
                风险控制
              </h3>
              <div className="grid grid-cols-3 gap-4">
                <label className="flex items-center space-x-3 p-3 border rounded-lg hover:bg-gray-50 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={selectedConditions.low_risk}
                    onChange={(e) => setSelectedConditions({...selectedConditions, low_risk: e.target.checked})}
                    className="w-4 h-4 text-blue-600"
                  />
                  <div>
                    <div className="font-medium">低风险</div>
                    <div className="text-sm text-gray-500">低波动率+低Beta</div>
                  </div>
                </label>
                <label className="flex items-center space-x-3 p-3 border rounded-lg hover:bg-gray-50 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={selectedConditions.high_liquidity}
                    onChange={(e) => setSelectedConditions({...selectedConditions, high_liquidity: e.target.checked})}
                    className="w-4 h-4 text-blue-600"
                  />
                  <div>
                    <div className="font-medium">高流动性</div>
                    <div className="text-sm text-gray-500">日均成交额 &gt; 1亿</div>
                  </div>
                </label>
                <label className="flex items-center space-x-3 p-3 border rounded-lg hover:bg-gray-50 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={selectedConditions.stable_beta}
                    onChange={(e) => setSelectedConditions({...selectedConditions, stable_beta: e.target.checked})}
                    className="w-4 h-4 text-blue-600"
                  />
                  <div>
                    <div className="font-medium">稳定Beta</div>
                    <div className="text-sm text-gray-500">Beta 0.8-1.2</div>
                  </div>
                </label>
              </div>
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="flex justify-end gap-2 pt-4 border-t">
            <ShadButton
              variant="outline"
              onClick={() => setShowCriteriaModal(false)}
            >
              取消
            </ShadButton>
            <ShadButton
              onClick={executeSelection}
              disabled={loading}
            >
              {loading ? (
                <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
              ) : (
                <Search className="w-4 h-4 mr-2" />
              )}
              开始选股
            </ShadButton>
          </div>
        </div>
      </Modal>

      {/* 选股结果详情模态框 */}
      <Modal
        open={showResultModal}
        onClose={() => setShowResultModal(false)}
        title={`选股结果详情 - ${selectedResult?.filename}`}
        size="xl"
      >
        {selectedResult && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">
                  共选中 {selectedResult.total_selected} 只股票
                </p>
              </div>
              <ShadButton
                size="sm"
                onClick={() => exportStockCodes(selectedResult.filename)}
              >
                <Download className="w-3 h-3 mr-1" />
                导出代码
              </ShadButton>
            </div>
            
            <DataTable
              columns={stockColumns}
              data={selectedResult.stocks}
            />
          </div>
        )}
      </Modal>

      {/* 选股进度模态框 */}
      {showProgressModal && taskStatus && (
        <Modal
          open={showProgressModal}
          onClose={() => {
            if (taskStatus.status === 'completed' || taskStatus.status === 'failed') {
              setShowProgressModal(false);
            }
          }}
          title="选股进度"
          size="md"
        >
          <div className="space-y-4">
            {/* 进度条 */}
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>进度</span>
                <span>{taskStatus.progress}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${taskStatus.progress}%` }}
                ></div>
              </div>
            </div>

            {/* 状态信息 */}
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span>状态:</span>
                <span className={`font-medium ${
                  taskStatus.status === 'running' ? 'text-blue-600' :
                  taskStatus.status === 'completed' ? 'text-green-600' :
                  taskStatus.status === 'failed' ? 'text-red-600' :
                  'text-gray-600'
                }`}>
                  {taskStatus.status === 'pending' ? '准备中' :
                   taskStatus.status === 'running' ? '运行中' :
                   taskStatus.status === 'completed' ? '已完成' :
                   taskStatus.status === 'failed' ? '失败' : '未知'}
                </span>
              </div>

              {taskStatus.total_count > 0 && (
                <div className="flex justify-between">
                  <span>处理进度:</span>
                  <span>{taskStatus.processed_count} / {taskStatus.total_count}</span>
                </div>
              )}

              {taskStatus.selected_count > 0 && (
                <div className="flex justify-between">
                  <span>已选中:</span>
                  <span className="text-green-600 font-medium">{taskStatus.selected_count} 只股票</span>
                </div>
              )}

              {taskStatus.current_stock && (
                <div className="flex justify-between">
                  <span>当前处理:</span>
                  <span className="font-mono text-xs">{taskStatus.current_stock}</span>
                </div>
              )}
            </div>

            {/* 消息 */}
            {taskStatus.message && (
              <div className="p-3 bg-gray-50 rounded-lg">
                <p className="text-sm text-gray-700">{taskStatus.message}</p>
              </div>
            )}

            {/* 操作按钮 */}
            <div className="flex justify-end pt-4 border-t">
              {(taskStatus.status === 'completed' || taskStatus.status === 'failed') && (
                <ShadButton
                  onClick={() => setShowProgressModal(false)}
                  className="px-4 py-2"
                >
                  关闭
                </ShadButton>
              )}
            </div>
          </div>
        </Modal>
      )}

      {/* 加入股票池模态框 */}
      {showAddToPoolModal && (
        <Modal onClose={() => setShowAddToPoolModal(false)}>
          <div className="p-6">
            <h3 className="text-lg font-semibold mb-4">加入股票池</h3>

            <div className="space-y-4">
              <div>
                <p className="text-sm text-gray-600 mb-2">
                  将选中的 {selectedResults.length} 只股票加入股票池
                </p>
              </div>

              <div>
                <label className="flex items-center space-x-2">
                  <input
                    type="radio"
                    checked={addToPoolData.create_new_pool}
                    onChange={() => setAddToPoolData({...addToPoolData, create_new_pool: true})}
                    className="w-4 h-4 text-blue-600"
                  />
                  <span>创建新股票池</span>
                </label>

                {addToPoolData.create_new_pool && (
                  <div className="mt-2 ml-6">
                    <input
                      type="text"
                      placeholder="输入股票池名称"
                      value={addToPoolData.pool_name}
                      onChange={(e) => setAddToPoolData({...addToPoolData, pool_name: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                )}
              </div>

              <div>
                <label className="flex items-center space-x-2">
                  <input
                    type="radio"
                    checked={!addToPoolData.create_new_pool}
                    onChange={() => setAddToPoolData({...addToPoolData, create_new_pool: false})}
                    className="w-4 h-4 text-blue-600"
                  />
                  <span>添加到现有股票池</span>
                </label>

                {!addToPoolData.create_new_pool && (
                  <div className="mt-2 ml-6">
                    <select
                      value={addToPoolData.target_pool_id}
                      onChange={(e) => setAddToPoolData({...addToPoolData, target_pool_id: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="">选择股票池</option>
                      {stockPools.map((pool) => (
                        <option key={pool.pool_id} value={pool.pool_id}>
                          {pool.pool_name} ({pool.stock_count}只股票)
                        </option>
                      ))}
                    </select>
                  </div>
                )}
              </div>
            </div>

            <div className="flex justify-end space-x-3 mt-6">
              <ShadButton
                variant="outline"
                onClick={() => setShowAddToPoolModal(false)}
              >
                取消
              </ShadButton>
              <ShadButton
                onClick={confirmAddToPool}
                disabled={addToPoolData.create_new_pool ? !addToPoolData.pool_name : !addToPoolData.target_pool_id}
              >
                确认加入
              </ShadButton>
            </div>
          </div>
        </Modal>
      )}
    </div>
  );
};

export default StockSelectionPage;
