#!/usr/bin/env python3
"""
测试修复后的QMT数据源 - 确保只使用真实数据
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from backend.core.logger import get_logger

logger = get_logger(__name__)

async def test_qmt_store_real_data():
    """测试QMT Store只使用真实数据"""
    logger.info("🧪 测试QMT Store真实数据获取...")
    
    try:
        from backend.stores.qmt_store import QMTStore
        from datetime import datetime, timedelta
        
        # 创建QMT Store
        qmt_store = QMTStore()
        logger.info("✅ QMT Store创建成功")
        
        # 测试历史数据获取
        test_stock = '000001.SZ'
        end_date = datetime.now()
        start_date = end_date - timedelta(days=30)
        
        logger.info(f"📊 测试历史数据获取: {test_stock}")
        logger.info(f"   日期范围: {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')}")
        
        # 创建历史数据源
        data = qmt_store.getdata(
            dataname=test_stock,
            historical=True,
            live=False,
            fromdate=start_date,
            todate=end_date
        )
        
        logger.info(f"✅ 历史数据源创建成功: {type(data)}")
        logger.info(f"   股票代码: {getattr(data, 'stock_code', 'NOT_SET')}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ QMT Store测试失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False

async def test_qmt_live_data_real():
    """测试QMT Live Data只使用真实数据"""
    logger.info("🧪 测试QMT Live Data真实数据获取...")
    
    try:
        from backend.stores.qmt_store import QMTStore
        
        # 创建QMT Store
        qmt_store = QMTStore()
        
        # 测试实时数据源创建
        test_stock = '000001.SZ'
        
        logger.info(f"📡 测试实时数据源创建: {test_stock}")
        
        # 创建实时数据源
        data = qmt_store.getdata(
            dataname=test_stock,
            historical=False,
            live=True,
            paper_trading=True  # 纸面交易模式
        )
        
        logger.info(f"✅ 实时数据源创建成功: {type(data)}")
        logger.info(f"   股票代码: {getattr(data, 'stock_code', 'NOT_SET')}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ QMT Live Data测试失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False

async def test_data_manager_direct():
    """直接测试数据管理器"""
    logger.info("🧪 测试数据管理器直接调用...")
    
    try:
        from backend.data.data_manager import data_manager
        from datetime import datetime, timedelta
        
        # 测试股票列表获取
        logger.info("📋 测试股票列表获取...")
        stock_list_response = data_manager.get_stock_list(page=1, page_size=3)
        stocks = stock_list_response['data']
        
        if not stocks:
            logger.error("❌ 未获取到股票列表")
            return False
            
        logger.info(f"✅ 获取到股票列表: {len(stocks)}只股票")
        
        # 测试第一只股票的数据获取
        test_stock = stocks[0]
        stock_code = test_stock['code']
        stock_name = test_stock['name']
        
        logger.info(f"📊 测试股票数据获取: {stock_name}({stock_code})")
        
        # 设置测试日期
        end_date = datetime.now().strftime('%Y-%m-%d')
        start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
        
        # 获取历史数据
        df = data_manager.get_stock_data(
            stock_code=stock_code,
            start_date=start_date,
            end_date=end_date
        )
        
        if df is None or len(df) == 0:
            logger.error(f"❌ 未获取到{stock_code}的数据")
            return False
            
        logger.info(f"✅ 获取到真实数据: {stock_code} {len(df)}条记录")
        logger.info(f"   数据范围: {df.index[0]} 到 {df.index[-1]}")
        logger.info(f"   最新收盘价: {df['close'].iloc[-1]:.2f}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 数据管理器测试失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False

async def main():
    """主测试函数"""
    logger.info("🚀 开始测试修复后的QMT数据源...")
    
    tests = [
        ("数据管理器直接调用", test_data_manager_direct),
        ("QMT Store历史数据", test_qmt_store_real_data),
        ("QMT Live Data实时数据", test_qmt_live_data_real),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"🧪 测试: {test_name}")
        logger.info(f"{'='*50}")
        
        try:
            result = await test_func()
            if result:
                logger.info(f"✅ {test_name} - 通过")
                passed += 1
            else:
                logger.error(f"❌ {test_name} - 失败")
        except Exception as e:
            logger.error(f"❌ {test_name} - 异常: {e}")
    
    logger.info(f"\n{'='*50}")
    logger.info(f"🎯 测试结果: {passed}/{total} 通过")
    logger.info(f"{'='*50}")
    
    if passed == total:
        logger.info("🎉 所有测试通过！QMT数据源修复成功！")
        return True
    else:
        logger.error("❌ 部分测试失败，需要进一步修复")
        return False

if __name__ == "__main__":
    asyncio.run(main())
