#!/usr/bin/env node
/**
 * 测试前端修复
 * 检查是否还有对已删除组件的引用
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 检查前端修复状态...\n');

// 检查文件是否存在
const checkFileExists = (filePath) => {
  const fullPath = path.join(__dirname, filePath);
  return fs.existsSync(fullPath);
};

// 检查文件内容
const checkFileContent = (filePath, searchPattern, description) => {
  const fullPath = path.join(__dirname, filePath);
  if (!fs.existsSync(fullPath)) {
    console.log(`⚠️ 文件不存在: ${filePath}`);
    return false;
  }
  
  const content = fs.readFileSync(fullPath, 'utf8');
  const hasPattern = searchPattern.test(content);
  
  if (hasPattern) {
    console.log(`❌ ${description}: ${filePath}`);
    return false;
  } else {
    console.log(`✅ ${description}: ${filePath}`);
    return true;
  }
};

let allPassed = true;

// 1. 检查LiveTradingPage.js是否已删除
console.log('1. 检查LiveTradingPage.js是否已删除');
if (checkFileExists('frontend/src/pages/LiveTradingPage.js')) {
  console.log('❌ LiveTradingPage.js 仍然存在');
  allPassed = false;
} else {
  console.log('✅ LiveTradingPage.js 已成功删除');
}

console.log('\n2. 检查App.js中的引用');
// 2. 检查App.js中是否还有LiveTradingPage的引用
allPassed &= checkFileContent(
  'frontend/src/App.js',
  /import.*LiveTradingPage/,
  'App.js中不应有LiveTradingPage导入'
);

allPassed &= checkFileContent(
  'frontend/src/App.js',
  /path="\/live-trading"/,
  'App.js中不应有/live-trading路由'
);

console.log('\n3. 检查Sidebar.js中的引用');
// 3. 检查Sidebar.js中是否还有实盘交易菜单
allPassed &= checkFileContent(
  'frontend/src/components/Layout/Sidebar.js',
  /path: '\/live-trading'/,
  'Sidebar.js中不应有/live-trading菜单项'
);

console.log('\n4. 检查LiveTradingConfig.jsx是否修复');
// 4. 检查LiveTradingConfig.jsx是否已修复FormItem问题
allPassed &= checkFileContent(
  'frontend/src/components/LiveTradingConfig.jsx',
  /import.*FormItem/,
  'LiveTradingConfig.jsx中不应导入FormItem'
);

// 检查是否使用了正确的标签结构
const liveTradingConfigPath = path.join(__dirname, 'frontend/src/components/LiveTradingConfig.jsx');
if (fs.existsSync(liveTradingConfigPath)) {
  const content = fs.readFileSync(liveTradingConfigPath, 'utf8');
  if (content.includes('<label className="text-sm font-medium">')) {
    console.log('✅ LiveTradingConfig.jsx 使用了正确的标签结构');
  } else {
    console.log('❌ LiveTradingConfig.jsx 标签结构可能有问题');
    allPassed = false;
  }
} else {
  console.log('⚠️ LiveTradingConfig.jsx 文件不存在');
  allPassed = false;
}

console.log('\n5. 检查MultiStrategyLiveTradingPage.js是否正常');
// 5. 检查MultiStrategyLiveTradingPage.js是否正常
allPassed &= checkFileContent(
  'frontend/src/pages/MultiStrategyLiveTradingPage.js',
  /import.*LiveTradingConfig/,
  'MultiStrategyLiveTradingPage.js应该导入LiveTradingConfig'
) === false; // 这里我们期望找到导入，所以反转结果

// 检查是否有LiveTradingMonitor导入
const multiStrategyPath = path.join(__dirname, 'frontend/src/pages/MultiStrategyLiveTradingPage.js');
if (fs.existsSync(multiStrategyPath)) {
  const content = fs.readFileSync(multiStrategyPath, 'utf8');
  if (content.includes('import LiveTradingConfig')) {
    console.log('✅ MultiStrategyLiveTradingPage.js 正确导入了LiveTradingConfig');
  } else {
    console.log('❌ MultiStrategyLiveTradingPage.js 缺少LiveTradingConfig导入');
    allPassed = false;
  }
  
  if (content.includes('import LiveTradingMonitor')) {
    console.log('✅ MultiStrategyLiveTradingPage.js 正确导入了LiveTradingMonitor');
  } else {
    console.log('❌ MultiStrategyLiveTradingPage.js 缺少LiveTradingMonitor导入');
    allPassed = false;
  }
}

// 总结
console.log('\n' + '='.repeat(50));
if (allPassed) {
  console.log('🎉 所有检查通过！前端修复成功');
  console.log('\n✅ 修复内容：');
  console.log('   - 删除了LiveTradingPage.js文件');
  console.log('   - 移除了App.js中的相关引用');
  console.log('   - 移除了Sidebar.js中的菜单项');
  console.log('   - 修复了LiveTradingConfig.jsx中的FormItem错误');
  console.log('   - 保留了MultiStrategyLiveTradingPage.js的功能');
  
  console.log('\n🚀 现在可以：');
  console.log('   1. 启动前端: npm start');
  console.log('   2. 访问多策略交易页面');
  console.log('   3. 点击"添加策略"应该不会再出现错误');
} else {
  console.log('❌ 部分检查失败，请检查上述问题');
}

process.exit(allPassed ? 0 : 1);
