#!/usr/bin/env python3
"""
系统状态检查脚本
"""

import requests
import subprocess
import time
from datetime import datetime

def check_port(port):
    """检查端口是否被占用"""
    try:
        result = subprocess.run(
            ['netstat', '-ano'], 
            capture_output=True, 
            text=True, 
            timeout=10
        )
        return f':{port}' in result.stdout
    except:
        return False

def check_backend_api():
    """检查后端API状态"""
    try:
        # 检查基本状态
        response = requests.get('http://localhost:8001/api/data/status', timeout=5)
        if response.status_code == 200:
            print("✅ 后端API基本功能正常")
            
            # 检查选股API
            response = requests.get('http://localhost:8001/api/stock-selection/presets', timeout=5)
            if response.status_code == 200:
                data = response.json()
                presets_count = len(data.get('data', {}).get('presets', {}))
                print(f"✅ 选股API正常 (预设策略: {presets_count}个)")
            else:
                print("❌ 选股API异常")
                
            # 检查持仓监控API
            response = requests.get('http://localhost:8001/api/position-monitor/dashboard', timeout=5)
            if response.status_code == 200:
                print("✅ 持仓监控API正常")
            else:
                print("❌ 持仓监控API异常")
                
            return True
        else:
            print(f"❌ 后端API异常 (状态码: {response.status_code})")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ 后端服务器未运行 (端口8001)")
        return False
    except Exception as e:
        print(f"❌ 后端API检查失败: {e}")
        return False

def check_frontend():
    """检查前端状态"""
    try:
        # 检查前端是否可访问
        response = requests.get('http://localhost:3000', timeout=5)
        if response.status_code == 200:
            print("✅ 前端服务器正常 (端口3000)")
            return True
        else:
            print(f"❌ 前端服务器异常 (状态码: {response.status_code})")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ 前端服务器未运行 (端口3000)")
        return False
    except Exception as e:
        print(f"❌ 前端检查失败: {e}")
        return False

def main():
    """主函数"""
    print("🔍 QMT-TRADER系统状态检查")
    print(f"⏰ 检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # 1. 检查端口占用情况
    print("\n📡 端口占用检查:")
    backend_port_used = check_port(8001)
    frontend_port_used = check_port(3000)
    
    print(f"   端口8001 (后端): {'✅ 已占用' if backend_port_used else '❌ 未占用'}")
    print(f"   端口3000 (前端): {'✅ 已占用' if frontend_port_used else '❌ 未占用'}")
    
    # 2. 检查后端API
    print("\n🔧 后端API检查:")
    backend_ok = check_backend_api()
    
    # 3. 检查前端
    print("\n🌐 前端服务检查:")
    frontend_ok = check_frontend()
    
    # 4. 系统整体状态
    print("\n📊 系统整体状态:")
    if backend_ok and frontend_ok:
        print("✅ 系统运行正常")
        print("\n🎯 可用功能:")
        print("   - 智能选股: http://localhost:3000/stock-selection")
        print("   - 持仓监控: http://localhost:3000/position-monitor")
        print("   - 策略回测: http://localhost:3000/backtest")
        print("   - 实盘交易: http://localhost:3000/multi-strategy")
        print("   - 数据管理: http://localhost:3000/data-management")
    elif backend_ok and not frontend_ok:
        print("⚠️ 后端正常，前端异常")
        print("   建议: 运行 python start_frontend.py 启动前端")
    elif not backend_ok and frontend_ok:
        print("⚠️ 前端正常，后端异常")
        print("   建议: 运行 python start_dev_server.py 启动后端")
    else:
        print("❌ 系统异常")
        print("   建议:")
        print("   1. 运行 python start_dev_server.py 启动后端")
        print("   2. 运行 python start_frontend.py 启动前端")
    
    # 5. 启动建议
    print("\n🚀 启动建议:")
    if not backend_ok:
        print("   后端: python start_dev_server.py")
    if not frontend_ok:
        print("   前端: python start_frontend.py")
    
    print("\n📋 功能测试:")
    print("   选股模块: python test_stock_selection.py")
    
    print("\n" + "=" * 60)
    print("✅ 系统状态检查完成")

if __name__ == "__main__":
    main()
