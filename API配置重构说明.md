# API配置重构说明

## 🎯 问题解决

### 原问题
- 前端代码中硬编码了API端点URL (`http://localhost:8001/api/stock-pool/pools`)
- 不同环境需要手动修改代码
- 代码可维护性差，容易出错

### 解决方案
创建了统一的API配置管理系统，支持环境变量和配置文件。

## 📁 新增文件

### 1. `frontend/src/config/api.js` - API配置中心
```javascript
// 主要功能：
- 统一管理所有API端点
- 支持环境变量配置
- 提供通用的API请求函数
- 专门的股票池API函数封装
```

### 2. `frontend/.env.development` - 开发环境配置
```
REACT_APP_API_BASE_URL=http://localhost:8001
REACT_APP_ENV=development
REACT_APP_DEBUG=true
```

### 3. `frontend/.env.production` - 生产环境配置
```
REACT_APP_API_BASE_URL=http://localhost:8000
REACT_APP_ENV=production
REACT_APP_DEBUG=false
```

## 🔧 配置优先级

1. **环境变量** `REACT_APP_API_BASE_URL` (最高优先级)
2. **开发环境默认** `http://localhost:8001`
3. **生产环境默认** 相对路径 (空字符串)

## 📊 使用方式

### 旧方式 (硬编码)
```javascript
const response = await fetch('http://localhost:8001/api/stock-pool/pools');
```

### 新方式 (配置驱动)
```javascript
import { stockPoolApi } from '../config/api.js';

// 方式1: 使用封装的API函数
const data = await stockPoolApi.list();

// 方式2: 使用通用API函数
import { apiRequest, API_ENDPOINTS } from '../config/api.js';
const data = await apiRequest(API_ENDPOINTS.STOCK_POOL.LIST);

// 方式3: 手动构建URL
import { buildApiUrl, API_ENDPOINTS } from '../config/api.js';
const url = buildApiUrl(API_ENDPOINTS.STOCK_POOL.LIST);
const response = await fetch(url);
```

## 🎯 优势

### 1. **环境适配**
- 开发环境自动使用8001端口
- 生产环境自动使用8000端口
- 支持自定义环境变量覆盖

### 2. **代码维护**
- 所有API端点集中管理
- 修改端点只需要改一个地方
- 类型安全的端点定义

### 3. **错误处理**
- 统一的错误处理逻辑
- 自动的HTTP状态码检查
- 详细的错误日志

### 4. **开发体验**
- 智能的默认配置
- 清晰的配置文档
- 易于调试和测试

## 🚀 当前状态

### 服务状态
- ✅ **后端服务**: `http://localhost:8001` - 正常运行
- ✅ **前端服务**: `http://localhost:3001` - 正常运行
- ✅ **API配置**: 自动适配开发环境

### 功能状态
- ✅ **股票池列表**: 正常加载
- ✅ **创建股票池**: 功能正常
- ✅ **股票管理**: 添加/删除正常
- ✅ **数据显示**: 股票数量、时间正常显示

## 🔧 如何切换环境

### 开发环境 (默认)
```bash
cd frontend
npm start
# 自动使用 http://localhost:8001
```

### 生产环境
```bash
cd frontend
npm run build
npm run start:prod
# 自动使用 http://localhost:8000
```

### 自定义端口
```bash
# 设置环境变量
export REACT_APP_API_BASE_URL=http://localhost:9000
cd frontend
npm start
```

## 📝 配置调试

### 查看当前配置
```javascript
import { getApiConfig } from '../config/api.js';
console.log('API配置:', getApiConfig());
```

### 输出示例
```javascript
{
  baseUrl: "http://localhost:8001",
  environment: "development", 
  customBaseUrl: undefined
}
```

## 🎯 最佳实践

### 1. **使用封装的API函数**
```javascript
// 推荐 ✅
import { stockPoolApi } from '../config/api.js';
const data = await stockPoolApi.list();

// 不推荐 ❌
const response = await fetch('http://localhost:8001/api/stock-pool/pools');
```

### 2. **环境变量命名**
- 所有React环境变量必须以 `REACT_APP_` 开头
- 使用大写字母和下划线分隔
- 描述性的变量名

### 3. **错误处理**
```javascript
try {
  const data = await stockPoolApi.list();
  // 处理成功响应
} catch (error) {
  // 统一的错误处理
  console.error('API调用失败:', error);
}
```

现在API配置已经完全重构，不再使用硬编码，支持灵活的环境配置！🎉
