#!/usr/bin/env python3
"""
简单测试数据格式化
"""

import pandas as pd
import backtrader as bt

def test_string_index_issue():
    """测试字符串索引问题"""
    print("=== 测试字符串索引问题 ===")
    
    # 创建有问题的数据（字符串索引）
    df_problem = pd.DataFrame({
        'open': [10.0, 10.1, 10.2],
        'high': [10.5, 10.6, 10.7],
        'low': [9.8, 9.9, 10.0],
        'close': [10.2, 10.3, 10.4],
        'volume': [1000000, 1100000, 1200000]
    }, index=['2025-07-01', '2025-07-02', '2025-07-03'])  # 字符串索引
    
    print(f"问题数据索引类型: {type(df_problem.index)}")
    print(f"索引内容: {df_problem.index.tolist()}")
    
    # 尝试直接用于backtrader（会出错）
    try:
        data_feed = bt.feeds.PandasData(dataname=df_problem)
        cerebro = bt.Cerebro()
        cerebro.adddata(data_feed)
        
        class TestStrategy(bt.Strategy):
            def next(self):
                pass
        
        cerebro.addstrategy(TestStrategy)
        cerebro.run()
        print("❌ 意外成功了？")
    except Exception as e:
        print(f"✅ 预期的错误: {e}")
    
    # 修复数据格式
    df_fixed = df_problem.copy()
    df_fixed.index = pd.to_datetime(df_fixed.index)
    
    print(f"修复后索引类型: {type(df_fixed.index)}")
    print(f"是否为DatetimeIndex: {isinstance(df_fixed.index, pd.DatetimeIndex)}")
    
    # 测试修复后的数据
    try:
        data_feed = bt.feeds.PandasData(dataname=df_fixed)
        cerebro = bt.Cerebro()
        cerebro.adddata(data_feed)
        
        class TestStrategy(bt.Strategy):
            def __init__(self):
                self.count = 0
            
            def next(self):
                self.count += 1
                if self.count <= 2:
                    print(f"数据点{self.count}: 日期={self.data.datetime.date(0)}, 收盘={self.data.close[0]:.2f}")
        
        cerebro.addstrategy(TestStrategy)
        cerebro.run()
        print("✅ 修复后运行成功！")
        return True
    except Exception as e:
        print(f"❌ 修复后仍然失败: {e}")
        return False

if __name__ == "__main__":
    print("🔍 开始简单格式化测试")
    success = test_string_index_issue()
    if success:
        print("🎉 测试通过！数据格式问题已修复")
    else:
        print("❌ 测试失败")
