#!/usr/bin/env python3
"""
独立选股模块
支持多种技术指标和Alpha101因子进行股票筛选
"""

import os
import json
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
# import talib  # 暂时注释掉，使用简化版本的技术指标计算
from pathlib import Path

from backend.core.logger import get_logger
from backend.stock_selection.quantitative_factors import QuantitativeFactors
# from backend.data.data_manager import data_manager  # 延迟导入，避免启动时连接QMT

logger = get_logger(__name__)

@dataclass
class SelectionCriteria:
    """选股条件"""
    # 股票类型筛选
    include_etf: bool = False  # 包含ETF
    only_etf: bool = False  # 仅ETF
    exclude_etf: bool = False  # 排除ETF
    etf_types: Optional[List[str]] = None  # ETF类型：['stock', 'bond', 'commodity', 'money', 'cross_border']

    # 技术指标条件
    atr_min: Optional[float] = None
    atr_max: Optional[float] = None
    atr_period: int = 14

    # 布林带条件
    bb_position: Optional[str] = None  # 'upper', 'lower', 'middle', 'outside', 'inside'
    bb_period: int = 20
    bb_std: float = 2.0

    # RSI条件
    rsi_min: Optional[float] = None
    rsi_max: Optional[float] = None
    rsi_period: int = 14

    # MACD条件
    macd_signal: Optional[str] = None  # 'bullish', 'bearish'
    macd_fast: int = 12
    macd_slow: int = 26
    macd_signal_period: int = 9

    # 移动平均线条件
    ma_trend: Optional[str] = None  # 'above_ma', 'below_ma', 'cross_up', 'cross_down'
    ma_period: int = 20
    ma_arrangement: Optional[str] = None  # 'bullish', 'bearish' - 均线排列

    # 成交量条件
    volume_min: Optional[float] = None
    volume_max: Optional[float] = None
    volume_avg_period: int = 20
    volume_ratio_min: Optional[float] = None  # 量比最小值
    volume_ratio_max: Optional[float] = None  # 量比最大值
    turnover_rate_min: Optional[float] = None  # 换手率最小值（%）
    turnover_rate_max: Optional[float] = None  # 换手率最大值（%）

    # 价格条件
    price_min: Optional[float] = None
    price_max: Optional[float] = None
    price_change_min: Optional[float] = None  # 涨跌幅最小值
    price_change_max: Optional[float] = None  # 涨跌幅最大值

    # 动量因子条件
    momentum_1m_min: Optional[float] = None  # 1月动量最小值
    momentum_1m_max: Optional[float] = None  # 1月动量最大值
    momentum_3m_min: Optional[float] = None  # 3月动量最小值
    momentum_3m_max: Optional[float] = None  # 3月动量最大值
    momentum_12m_min: Optional[float] = None  # 12月动量最小值
    momentum_12m_max: Optional[float] = None  # 12月动量最大值

    # 市值条件
    market_cap_min: Optional[float] = None
    market_cap_max: Optional[float] = None

    # 价值因子条件
    pe_min: Optional[float] = None  # 市盈率最小值
    pe_max: Optional[float] = None  # 市盈率最大值
    pb_min: Optional[float] = None  # 市净率最小值
    pb_max: Optional[float] = None  # 市净率最大值
    ps_min: Optional[float] = None  # 市销率最小值
    ps_max: Optional[float] = None  # 市销率最大值
    dividend_yield_min: Optional[float] = None  # 股息率最小值
    dividend_yield_max: Optional[float] = None  # 股息率最大值

    # 质量因子条件
    roe_min: Optional[float] = None  # ROE最小值（%）
    roe_max: Optional[float] = None  # ROE最大值（%）
    roa_min: Optional[float] = None  # ROA最小值（%）
    roa_max: Optional[float] = None  # ROA最大值（%）
    gross_margin_min: Optional[float] = None  # 毛利率最小值（%）
    gross_margin_max: Optional[float] = None  # 毛利率最大值（%）

    # 成长因子条件
    revenue_growth_min: Optional[float] = None  # 营收增长率最小值（%）
    revenue_growth_max: Optional[float] = None  # 营收增长率最大值（%）
    profit_growth_min: Optional[float] = None  # 净利润增长率最小值（%）
    profit_growth_max: Optional[float] = None  # 净利润增长率最大值（%）
    eps_growth_min: Optional[float] = None  # EPS增长率最小值（%）
    eps_growth_max: Optional[float] = None  # EPS增长率最大值（%）

    # 风险控制因子
    volatility_min: Optional[float] = None  # 波动率最小值
    volatility_max: Optional[float] = None  # 波动率最大值
    beta_min: Optional[float] = None  # Beta最小值
    beta_max: Optional[float] = None  # Beta最大值
    max_drawdown_max: Optional[float] = None  # 最大回撤上限

    # 流动性因子
    avg_amount_min: Optional[float] = None  # 日均成交额最小值（万元）
    avg_amount_max: Optional[float] = None  # 日均成交额最大值（万元）

    # Alpha101因子条件
    alpha_factors: Optional[List[str]] = None
    alpha_min_values: Optional[Dict[str, float]] = None
    alpha_max_values: Optional[Dict[str, float]] = None

    # 时间范围
    lookback_days: int = 30

    # 其他条件
    exclude_st: bool = True  # 排除ST股票
    exclude_new_stock: bool = True  # 排除新股（上市不足60天）
    min_trading_days: int = 20  # 最少交易天数

    # 条件组合逻辑
    condition_logic: str = "flexible"  # "strict" (AND), "flexible" (评分制), "any" (OR)

@dataclass
class StockScore:
    """股票评分"""
    stock_code: str
    stock_name: str
    score: float
    indicators: Dict[str, Any]
    alpha_factors: Dict[str, float]
    selection_date: str
    
class StockSelector:
    """股票选择器"""
    
    def __init__(self):
        self.results_dir = Path("data/stock_selection")
        self.results_dir.mkdir(parents=True, exist_ok=True)

        # 初始化量化因子计算器
        self.quant_factors = QuantitativeFactors()

        # 初始化xttrader
        self.xt_available = False

        try:
            import xtquant.xtdata as xt
            self.xt = xt
            self.xt_available = True
            logger.info("✅ xttrader数据源已连接")
        except ImportError:
            logger.error("❌ xttrader未安装！选股功能将不可用")
            self.xt = None

    def _get_stocks_from_xttrader(self, include_etf=False, only_etf=False):
        """从xttrader获取股票列表"""
        try:
            if not self.xt_available:
                return []

            stock_list = []

            # 如果只要ETF
            if only_etf:
                etf_list = self._get_etf_list()
                logger.info(f"从xttrader获取到 {len(etf_list)} 只ETF")
                return etf_list

            # 获取A股股票列表
            sh_stocks = self.xt.get_stock_list_in_sector('沪深A股')
            if sh_stocks:
                for stock_code in sh_stocks:
                    # 过滤掉指数和其他非股票代码
                    if stock_code.endswith('.SH') or stock_code.endswith('.SZ'):
                        # 判断是否为ETF
                        is_etf = self._is_etf_code(stock_code)

                        # 根据条件决定是否包含
                        if is_etf and not include_etf:
                            continue  # 是ETF但不包含ETF

                        stock_list.append({
                            'code': stock_code,
                            'name': self._get_stock_name(stock_code),
                            'is_etf': is_etf
                        })

            # 如果需要包含ETF，额外获取ETF列表
            if include_etf:
                etf_list = self._get_etf_list()
                # 去重合并
                existing_codes = {stock['code'] for stock in stock_list}
                for etf in etf_list:
                    if etf['code'] not in existing_codes:
                        stock_list.append(etf)

            logger.info(f"从xttrader获取到 {len(stock_list)} 只股票/ETF")
            return stock_list

        except Exception as e:
            logger.error(f"从xttrader获取股票列表失败: {e}")
            return []

    def _get_etf_list(self):
        """获取ETF列表"""
        try:
            etf_list = []

            # 尝试多种方式获取ETF
            etf_sectors = [
                '沪深ETF',
                '上海ETF',
                '深圳ETF',
                'ETF基金'
            ]

            for sector in etf_sectors:
                try:
                    etfs = self.xt.get_stock_list_in_sector(sector)
                    if etfs:
                        for etf_code in etfs:
                            if self._is_etf_code(etf_code):
                                etf_list.append({
                                    'code': etf_code,
                                    'name': self._get_stock_name(etf_code),
                                    'is_etf': True,
                                    'sector': sector
                                })
                except Exception as e:
                    logger.debug(f"从{sector}获取ETF失败: {e}")
                    continue

            # 去重
            unique_etfs = {}
            for etf in etf_list:
                unique_etfs[etf['code']] = etf

            return list(unique_etfs.values())

        except Exception as e:
            logger.error(f"获取ETF列表失败: {e}")
            return []

    def _is_etf_code(self, code: str) -> bool:
        """判断是否为ETF代码"""
        if not code or len(code) < 9:
            return False

        # ETF代码特征
        # 上海：51xxxx.SH, 58xxxx.SH
        # 深圳：15xxxx.SZ, 16xxxx.SZ
        if code.endswith('.SH'):
            stock_num = code[:6]
            return stock_num.startswith('51') or stock_num.startswith('58')
        elif code.endswith('.SZ'):
            stock_num = code[:6]
            return stock_num.startswith('15') or stock_num.startswith('16')

        return False

    def _classify_etf_type(self, etf_name: str) -> str:
        """根据ETF名称分类ETF类型"""
        name_lower = etf_name.lower()

        # 股票型ETF
        if any(keyword in name_lower for keyword in ['300', '500', '50', 'a股', '创业板', '科创', '中证', '上证', '深证', '沪深']):
            return 'stock'

        # 债券型ETF
        if any(keyword in name_lower for keyword in ['债', '国债', '企债', '信用']):
            return 'bond'

        # 商品型ETF
        if any(keyword in name_lower for keyword in ['黄金', '白银', '原油', '商品', '有色', '农业']):
            return 'commodity'

        # 货币型ETF
        if any(keyword in name_lower for keyword in ['货币', '现金', '短债']):
            return 'money'

        # 跨境ETF
        if any(keyword in name_lower for keyword in ['美股', '港股', '德国', '日本', '纳斯达克', '标普', '恒生']):
            return 'cross_border'

        # 默认为股票型
        return 'stock'

    def _get_stock_name(self, stock_code):
        """获取股票名称"""
        try:
            if self.xt_available:
                # 尝试从xttrader获取股票名称
                info = self.xt.get_instrument_detail(stock_code)
                if info and 'InstrumentName' in info:
                    return info['InstrumentName']

            # 如果无法获取，返回代码
            return stock_code.split('.')[0]

        except:
            return stock_code.split('.')[0]

    def _get_stock_data_from_xttrader(self, stock_code, start_date, end_date):
        """从xttrader获取股票数据"""
        try:
            if not self.xt_available:
                return None

            # 获取日线数据
            data = self.xt.get_market_data(
                stock_list=[stock_code],
                period='1d',
                start_time=start_date,
                end_time=end_date,
                fill_data=True
            )

            if data and isinstance(data, dict):
                # xttrader返回的是字典格式，需要转换为DataFrame
                required_fields = ['time', 'open', 'high', 'low', 'close', 'volume']
                if all(field in data for field in required_fields):
                    # 检查股票代码是否在数据中
                    if stock_code not in data['time'].index:
                        logger.debug(f"{stock_code}不在返回的数据中")
                        return None

                    # 获取各字段数据
                    time_data = data['time'].loc[stock_code]
                    open_data = data['open'].loc[stock_code]
                    high_data = data['high'].loc[stock_code]
                    low_data = data['low'].loc[stock_code]
                    close_data = data['close'].loc[stock_code]
                    volume_data = data['volume'].loc[stock_code]

                    # 转换时间戳为日期
                    dates = pd.to_datetime(time_data, unit='ms')

                    # 构建DataFrame
                    df_data = {
                        'open': open_data.values,
                        'high': high_data.values,
                        'low': low_data.values,
                        'close': close_data.values,
                        'volume': volume_data.values
                    }

                    df = pd.DataFrame(df_data, index=dates)

                    # 确保数据类型正确
                    for col in ['open', 'high', 'low', 'close', 'volume']:
                        df[col] = pd.to_numeric(df[col], errors='coerce')

                    # 删除无效数据
                    df = df.dropna()

                    if len(df) > 0:
                        logger.debug(f"成功获取{stock_code}数据: {len(df)}条记录")
                        return df
                    else:
                        logger.debug(f"{stock_code}数据为空或全为无效值")
                        return None
                else:
                    missing_fields = [field for field in required_fields if field not in data]
                    logger.debug(f"{stock_code}数据缺少字段: {missing_fields}")
                    return None

            logger.debug(f"{stock_code}未返回有效数据")
            return None

        except Exception as e:
            logger.error(f"从xttrader获取股票数据失败 {stock_code}: {e}")
            return None
        
    def calculate_technical_indicators(self, df: pd.DataFrame, fundamental_data: Optional[Dict] = None) -> Dict[str, Any]:
        """计算技术指标和量化因子"""
        if len(df) < 20:
            return {}

        try:
            # 使用新的量化因子计算器
            indicators = self.quant_factors.calculate_all_factors(df, fundamental_data)

            # 如果量化因子计算失败，返回空字典
            if not indicators:
                return {}

            return indicators

        except Exception as e:
            logger.error(f"计算技术指标失败: {e}")
            return {}


    
    def _get_ma_trend(self, price: float, ma5: float, ma20: float, ma60: float) -> str:
        """获取均线趋势"""
        if price > ma5 > ma20 > ma60:
            return 'strong_bullish'
        elif price > ma5 > ma20:
            return 'bullish'
        elif price < ma5 < ma20 < ma60:
            return 'strong_bearish'
        elif price < ma5 < ma20:
            return 'bearish'
        else:
            return 'sideways'
    
    def calculate_alpha101_factors(self, df: pd.DataFrame) -> Dict[str, float]:
        """计算Alpha101因子"""
        if len(df) < 30:
            return {}
            
        try:
            factors = {}
            
            # 基础数据
            high = df['high'].values
            low = df['low'].values
            close = df['close'].values
            volume = df['volume'].values
            
            # Alpha001: (rank(Ts_ArgMax(SignedPower(((returns < 0) ? stddev(returns, 20) : close), 2.), 5)) - 0.5)
            returns = np.diff(close) / close[:-1]
            if len(returns) >= 20:
                factors['alpha001'] = self._calculate_alpha001(returns, close)
            
            # Alpha002: (-1 * correlation(rank(delta(log(volume), 2)), rank(((close - open) / open)), 6))
            if 'open' in df.columns:
                open_prices = df['open'].values
                factors['alpha002'] = self._calculate_alpha002(volume, close, open_prices)
            
            # Alpha003: (-1 * correlation(rank(open), rank(volume), 10))
            if 'open' in df.columns:
                open_prices = df['open'].values
                factors['alpha003'] = self._calculate_alpha003(open_prices, volume)
            
            # Alpha004: (-1 * Ts_Rank(rank(low), 9))
            factors['alpha004'] = self._calculate_alpha004(low)
            
            # Alpha005: (rank((open - (sum(vwap, 10) / 10))) * (-1 * abs(rank((close - vwap)))))
            if len(df) >= 10:
                vwap = (high + low + close) / 3  # 简化的VWAP
                factors['alpha005'] = self._calculate_alpha005(df.get('open', close).values, close, vwap)
            
            # 更多因子可以继续添加...
            
            return factors
            
        except Exception as e:
            logger.error(f"计算Alpha101因子失败: {e}")
            return {}
    
    def _calculate_alpha001(self, returns: np.ndarray, close: np.ndarray) -> float:
        """Alpha001因子计算"""
        try:
            if len(returns) < 20:
                return 0.0
            
            # 简化版本的Alpha001
            neg_returns = returns < 0
            stddev_20 = np.std(returns[-20:])
            
            # 使用收盘价的平方作为简化
            signed_power = np.where(neg_returns[-5:], stddev_20, close[-5:]) ** 2
            
            if len(signed_power) > 0:
                return float(np.argmax(signed_power) - 2.5)
            return 0.0
            
        except:
            return 0.0
    
    def _calculate_alpha002(self, volume: np.ndarray, close: np.ndarray, open_prices: np.ndarray) -> float:
        """Alpha002因子计算"""
        try:
            if len(volume) < 8 or len(close) < 2 or len(open_prices) < 2:
                return 0.0
            
            # 计算volume的delta(log(volume), 2)
            log_vol = np.log(volume + 1)  # 加1避免log(0)
            vol_delta = np.diff(log_vol, n=2)
            
            # 计算(close - open) / open
            price_ratio = (close[2:] - open_prices[2:]) / (open_prices[2:] + 1e-8)
            
            if len(vol_delta) >= 6 and len(price_ratio) >= 6:
                # 计算相关性
                corr = np.corrcoef(vol_delta[-6:], price_ratio[-6:])[0, 1]
                return -1 * corr if not np.isnan(corr) else 0.0
            
            return 0.0
            
        except:
            return 0.0
    
    def _calculate_alpha003(self, open_prices: np.ndarray, volume: np.ndarray) -> float:
        """Alpha003因子计算"""
        try:
            if len(open_prices) < 10 or len(volume) < 10:
                return 0.0
            
            # 计算相关性
            corr = np.corrcoef(open_prices[-10:], volume[-10:])[0, 1]
            return -1 * corr if not np.isnan(corr) else 0.0
            
        except:
            return 0.0
    
    def _calculate_alpha004(self, low: np.ndarray) -> float:
        """Alpha004因子计算"""
        try:
            if len(low) < 9:
                return 0.0
            
            # 计算时间序列排名
            low_rank = np.argsort(np.argsort(low[-9:]))
            return -1 * (low_rank[-1] / 8.0)  # 归一化到[-1, 0]
            
        except:
            return 0.0
    
    def _calculate_alpha005(self, open_prices: np.ndarray, close: np.ndarray, vwap: np.ndarray) -> float:
        """Alpha005因子计算"""
        try:
            if len(open_prices) < 10 or len(vwap) < 10:
                return 0.0
            
            # 计算平均VWAP
            avg_vwap = np.mean(vwap[-10:])
            
            # 计算因子
            factor1 = open_prices[-1] - avg_vwap
            factor2 = abs(close[-1] - vwap[-1])
            
            return factor1 * (-1 * factor2)
            
        except:
            return 0.0
    
    def check_selection_criteria(self, indicators: Dict[str, Any], alpha_factors: Dict[str, float],
                                criteria: SelectionCriteria, stock_name: str = "", is_etf: bool = False) -> Tuple[bool, float]:
        """检查选股条件（支持灵活的条件组合）"""
        score = 0.0
        condition_met_count = 0
        total_conditions = 0
        passed = True  # 初始化passed变量

        try:
            # ETF类型筛选
            if criteria.only_etf and not is_etf:
                return False, 0.0

            if criteria.exclude_etf and is_etf:
                return False, 0.0

            if criteria.etf_types and is_etf:
                etf_type = self._classify_etf_type(stock_name)
                if etf_type not in criteria.etf_types:
                    return False, 0.0
            # ATR条件
            if criteria.atr_min is not None or criteria.atr_max is not None:
                total_conditions += 1
                atr_ratio = indicators.get('atr_ratio', 0)
                atr_met = True

                if criteria.atr_min is not None and atr_ratio < criteria.atr_min:
                    atr_met = False
                if criteria.atr_max is not None and atr_ratio > criteria.atr_max:
                    atr_met = False

                if atr_met:
                    condition_met_count += 1
                    score += min(atr_ratio * 10, 10)  # ATR贡献分数
            
            # 布林带条件
            if criteria.bb_position:
                total_conditions += 1
                bb_pos = indicators.get('bb_position', '')
                bb_met = False

                if criteria.bb_position == 'upper' and bb_pos == 'above_upper':
                    bb_met = True
                elif criteria.bb_position == 'lower' and bb_pos == 'below_lower':
                    bb_met = True
                elif criteria.bb_position == 'outside' and bb_pos in ['above_upper', 'below_lower']:
                    bb_met = True
                elif criteria.bb_position == 'inside' and bb_pos not in ['above_upper', 'below_lower']:
                    bb_met = True

                if bb_met:
                    condition_met_count += 1
                    score += 10

            # RSI条件
            if criteria.rsi_min is not None or criteria.rsi_max is not None:
                total_conditions += 1
                rsi = indicators.get('rsi', 50)
                rsi_met = True

                if criteria.rsi_min is not None and rsi < criteria.rsi_min:
                    rsi_met = False
                if criteria.rsi_max is not None and rsi > criteria.rsi_max:
                    rsi_met = False

                if rsi_met:
                    condition_met_count += 1
                    # RSI评分（30-70为正常范围）
                    if 30 <= rsi <= 70:
                        score += 10
                    elif rsi < 30:
                        score += 15  # 超卖，可能反弹
                    elif rsi > 70:
                        score += 5   # 超买，风险较高
            
            # MACD条件
            if criteria.macd_signal:
                total_conditions += 1
                macd_trend = indicators.get('macd_trend', '')
                macd_met = False

                if criteria.macd_signal == 'bullish' and macd_trend == 'bullish':
                    macd_met = True
                elif criteria.macd_signal == 'bearish' and macd_trend == 'bearish':
                    macd_met = True

                if macd_met:
                    condition_met_count += 1
                    score += 10

            # 移动平均线条件
            if criteria.ma_trend:
                total_conditions += 1
                ma_trend = indicators.get('ma_trend', '')
                ma_met = False

                if criteria.ma_trend == 'bullish' and ma_trend in ['bullish', 'strong_bullish']:
                    ma_met = True
                elif criteria.ma_trend == 'bearish' and ma_trend in ['bearish', 'strong_bearish']:
                    ma_met = True

                if ma_met:
                    condition_met_count += 1
                    score += 10
            
            # 趋势评分
            ma_trend = indicators.get('ma_trend', '')
            trend_scores = {
                'strong_bullish': 20,
                'bullish': 15,
                'sideways': 5,
                'bearish': -5,
                'strong_bearish': -10
            }
            score += trend_scores.get(ma_trend, 0)
            
            # 成交量条件
            if criteria.volume_min is not None or criteria.volume_max is not None:
                total_conditions += 1
                vol_ratio = indicators.get('volume_ratio', 0)
                volume_met = True

                if criteria.volume_min is not None and vol_ratio < criteria.volume_min:
                    volume_met = False
                if criteria.volume_max is not None and vol_ratio > criteria.volume_max:
                    volume_met = False

                if volume_met:
                    condition_met_count += 1
                    score += min(vol_ratio * 5, 15)  # 成交量活跃度评分
            
            # 价格变化条件
            if criteria.price_change_min is not None or criteria.price_change_max is not None:
                total_conditions += 1
                price_change = indicators.get('price_change', 0)
                price_met = True

                if criteria.price_change_min is not None and price_change < criteria.price_change_min:
                    price_met = False
                if criteria.price_change_max is not None and price_change > criteria.price_change_max:
                    price_met = False

                if price_met:
                    condition_met_count += 1
                    score += abs(price_change) * 100  # 价格变化评分
            
            # Alpha101因子条件
            if criteria.alpha_factors:
                total_conditions += len(criteria.alpha_factors)
                alpha_met_count = 0

                for factor in criteria.alpha_factors:
                    factor_value = alpha_factors.get(factor, 0)
                    factor_met = True

                    if criteria.alpha_min_values and factor in criteria.alpha_min_values:
                        if factor_value < criteria.alpha_min_values[factor]:
                            factor_met = False

                    if criteria.alpha_max_values and factor in criteria.alpha_max_values:
                        if factor_value > criteria.alpha_max_values[factor]:
                            factor_met = False

                    if factor_met:
                        alpha_met_count += 1
                        score += abs(factor_value) * 2  # Alpha因子评分

                condition_met_count += alpha_met_count

            # 价值因子条件
            value_conditions = [
                ('pe_min', 'pe_max', 'pe_ttm'),
                ('pb_min', 'pb_max', 'pb_ratio'),
                ('ps_min', 'ps_max', 'ps_ttm'),
                ('dividend_yield_min', 'dividend_yield_max', 'dividend_yield')
            ]

            for min_attr, max_attr, indicator_key in value_conditions:
                min_val = getattr(criteria, min_attr, None)
                max_val = getattr(criteria, max_attr, None)

                if min_val is not None or max_val is not None:
                    total_conditions += 1
                    val = indicators.get(indicator_key)
                    met = True

                    if val is None:
                        met = False
                    else:
                        if min_val is not None and val < min_val:
                            met = False
                        if max_val is not None and val > max_val:
                            met = False

                    if met:
                        condition_met_count += 1
                        # 价值因子评分
                        if indicator_key == 'pe_ttm' and val > 0:
                            score += min(10 / val, 10)  # PE越低分越高
                        elif indicator_key == 'pb_ratio' and val > 0:
                            score += min(5 / val, 10)   # PB越低分越高
                        elif indicator_key == 'dividend_yield':
                            score += min(val * 200, 10)  # 股息率越高分越高

            # 质量因子条件
            quality_conditions = [
                ('roe_min', 'roe_max', 'roe'),
                ('roa_min', 'roa_max', 'roa'),
                ('gross_margin_min', 'gross_margin_max', 'gross_margin')
            ]

            for min_attr, max_attr, indicator_key in quality_conditions:
                min_val = getattr(criteria, min_attr, None)
                max_val = getattr(criteria, max_attr, None)

                if min_val is not None or max_val is not None:
                    total_conditions += 1
                    val = indicators.get(indicator_key)
                    met = True

                    if val is None:
                        met = False
                    else:
                        if min_val is not None and val < min_val:
                            met = False
                        if max_val is not None and val > max_val:
                            met = False

                    if met:
                        condition_met_count += 1
                        # 质量因子评分
                        if indicator_key == 'roe' and val > 0:
                            score += min(val / 2, 15)  # ROE/2，最高15分
                        elif indicator_key == 'roa' and val > 0:
                            score += min(val, 10)      # ROA直接计分
                        elif indicator_key == 'gross_margin' and val > 0:
                            score += min(val / 5, 10)  # 毛利率/5

            # 成长因子条件
            growth_conditions = [
                ('revenue_growth_min', 'revenue_growth_max', 'revenue_growth'),
                ('profit_growth_min', 'profit_growth_max', 'profit_growth'),
                ('eps_growth_min', 'eps_growth_max', 'eps_growth')
            ]

            for min_attr, max_attr, indicator_key in growth_conditions:
                min_val = getattr(criteria, min_attr, None)
                max_val = getattr(criteria, max_attr, None)

                if min_val is not None or max_val is not None:
                    total_conditions += 1
                    val = indicators.get(indicator_key)
                    met = True

                    if val is None:
                        met = False
                    else:
                        if min_val is not None and val < min_val:
                            met = False
                        if max_val is not None and val > max_val:
                            met = False

                    if met:
                        condition_met_count += 1
                        # 成长因子评分
                        if val and val > 0:
                            if indicator_key == 'revenue_growth':
                                score += min(val / 2, 10)  # 营收增长率/2
                            elif indicator_key == 'profit_growth':
                                score += min(val / 3, 12)  # 利润增长率/3
                            elif indicator_key == 'eps_growth':
                                score += min(val / 3, 10)  # EPS增长率/3

            # 动量因子条件
            momentum_conditions = [
                ('momentum_1m_min', 'momentum_1m_max', 'momentum_1m'),
                ('momentum_3m_min', 'momentum_3m_max', 'momentum_3m'),
                ('momentum_12m_min', 'momentum_12m_max', 'momentum_12m')
            ]

            for min_attr, max_attr, indicator_key in momentum_conditions:
                min_val = getattr(criteria, min_attr, None)
                max_val = getattr(criteria, max_attr, None)

                if min_val is not None or max_val is not None:
                    total_conditions += 1
                    momentum_val = indicators.get(indicator_key, 0)
                    momentum_met = True

                    if min_val is not None and momentum_val < min_val:
                        momentum_met = False
                    if max_val is not None and momentum_val > max_val:
                        momentum_met = False

                    if momentum_met:
                        condition_met_count += 1
                        score += abs(momentum_val) * 20  # 动量因子评分

            # 成交量相关条件
            volume_conditions = [
                ('volume_ratio_min', 'volume_ratio_max', 'volume_ratio'),
                ('turnover_rate_min', 'turnover_rate_max', 'turnover_rate')
            ]

            for min_attr, max_attr, indicator_key in volume_conditions:
                min_val = getattr(criteria, min_attr, None)
                max_val = getattr(criteria, max_attr, None)

                if min_val is not None or max_val is not None:
                    total_conditions += 1
                    val = indicators.get(indicator_key, 0)
                    met = True

                    if min_val is not None and val < min_val:
                        met = False
                    if max_val is not None and val > max_val:
                        met = False

                    if met:
                        condition_met_count += 1
                        score += min(val * 2, 10)  # 成交量活跃度评分

            # 风险控制因子条件
            risk_conditions = [
                ('volatility_min', 'volatility_max', 'volatility'),
                ('beta_min', 'beta_max', 'beta'),
                ('max_drawdown_max', None, 'max_drawdown')
            ]

            for min_attr, max_attr, indicator_key in risk_conditions:
                min_val = getattr(criteria, min_attr, None) if min_attr else None
                max_val = getattr(criteria, max_attr, None) if max_attr else None

                if min_val is not None or max_val is not None:
                    total_conditions += 1
                    val = indicators.get(indicator_key, 0)
                    met = True

                    if min_val is not None and val < min_val:
                        met = False
                    if max_val is not None and val > max_val:
                        met = False

                    if met:
                        condition_met_count += 1
                        # 风险控制评分（低风险高分）
                        if indicator_key == 'volatility':
                            if 0.1 <= val <= 0.3:  # 适中波动率
                                score += 10
                            elif val < 0.1:
                                score += 5  # 过低波动率
                        elif indicator_key == 'beta':
                            if 0.8 <= val <= 1.2:  # 适中Beta
                                score += 8
                        elif indicator_key == 'max_drawdown':
                            if val < 0.2:  # 低回撤
                                score += 12

            # 流动性因子条件
            if criteria.avg_amount_min is not None or criteria.avg_amount_max is not None:
                total_conditions += 1
                avg_amount = indicators.get('avg_amount', 0)
                amount_met = True

                if criteria.avg_amount_min is not None and avg_amount < criteria.avg_amount_min:
                    amount_met = False
                if criteria.avg_amount_max is not None and avg_amount > criteria.avg_amount_max:
                    amount_met = False

                if amount_met:
                    condition_met_count += 1
                    # 流动性评分
                    if avg_amount > 10000:  # 1亿元以上
                        score += 15
                    elif avg_amount > 1000:  # 1000万元以上
                        score += 10
                    elif avg_amount > 100:   # 100万元以上
                        score += 5

            # 波动率评分（保持原有逻辑）
            volatility = indicators.get('volatility', 0)
            if 0.01 <= volatility <= 0.05:  # 适中波动率
                score += 10
            elif volatility > 0.05:
                score += 5  # 高波动率，机会与风险并存

            # 根据条件组合逻辑决定是否通过
            if criteria.condition_logic == "strict":
                # 严格模式：所有条件都必须满足
                passed = (condition_met_count == total_conditions) if total_conditions > 0 else True
            elif criteria.condition_logic == "any":
                # 任意模式：满足任意一个条件即可
                passed = (condition_met_count > 0) if total_conditions > 0 else True
            else:
                # 灵活模式（默认）：基于评分，满足50%以上条件或评分足够高
                if total_conditions > 0:
                    condition_ratio = condition_met_count / total_conditions
                    passed = condition_ratio >= 0.5 or score >= 20
                else:
                    passed = score >= 10  # 没有特定条件时，基于基础评分

            # 调试日志（仅在有条件时输出）
            if total_conditions > 0:
                logger.debug(f"条件检查: 满足{condition_met_count}/{total_conditions}, 评分:{score:.1f}, 通过:{passed}")

            return passed, max(score, 0)

        except Exception as e:
            logger.error(f"检查选股条件失败: {e}")
            return False, 0.0

    def select_stocks(self, criteria: SelectionCriteria, custom_name: str = "default") -> List[StockScore]:
        """选股（同步版本，保持兼容性）"""
        return self.select_stocks_with_progress(criteria, custom_name, None)

    def select_stocks_with_progress(self, criteria: SelectionCriteria, custom_name: str = "default",
                                  progress_callback=None) -> List[StockScore]:
        """执行选股"""
        logger.info(f"开始执行选股，条件: {custom_name}")

        selected_stocks = []

        try:
            # 使用xttrader获取股票数据
            if not self.xt_available:
                logger.error("xttrader不可用，无法执行选股")
                return []

            # 根据条件获取股票列表
            include_etf = criteria.include_etf or criteria.only_etf
            all_stocks = self._get_stocks_from_xttrader(include_etf=include_etf, only_etf=criteria.only_etf)

            stock_type = "ETF" if criteria.only_etf else ("股票/ETF" if include_etf else "股票")
            logger.info(f"从xttrader获取到 {len(all_stocks)} 只{stock_type}")

            if not all_stocks:
                logger.error("未从xttrader获取到股票列表")
                return []

            logger.info(f"开始筛选 {len(all_stocks)} 只股票")

            # 计算日期范围
            end_date = datetime.now()
            # 确保获取足够的数据，特别是当需要排除新股时
            extra_days = 60 if criteria.exclude_new_stock else 30
            start_date = end_date - timedelta(days=criteria.lookback_days + extra_days)

            processed_count = 0
            selected_count = 0

            for stock in all_stocks:
                try:
                    stock_code = stock['code']
                    stock_name = stock.get('name', stock_code)

                    processed_count += 1

                    # 调用进度回调
                    if progress_callback:
                        progress_callback(processed_count, len(all_stocks), stock_code, selected_count)

                    # 每处理100只股票输出一次进度
                    if processed_count % 100 == 0:
                        logger.info(f"选股进度: {processed_count}/{len(all_stocks)} ({processed_count/len(all_stocks)*100:.1f}%), 已选中: {selected_count}")

                    # 排除ST股票
                    if criteria.exclude_st and ('ST' in stock_name or '*ST' in stock_name):
                        continue

                    # 获取股票数据（仅使用xttrader）
                    try:
                        df = self._get_stock_data_from_xttrader(
                            stock_code=stock_code,
                            start_date=start_date.strftime('%Y%m%d'),
                            end_date=end_date.strftime('%Y%m%d')
                        )
                    except Exception as e:
                        logger.debug(f"获取{stock_code}数据失败: {e}")
                        continue

                    if df is None or len(df) < criteria.min_trading_days:
                        continue

                    # 排除新股
                    if criteria.exclude_new_stock and len(df) < 60:
                        continue

                    # 计算技术指标和量化因子
                    # TODO: 获取基本面数据，目前使用None
                    fundamental_data = None  # 将来可以从数据源获取基本面数据
                    indicators = self.calculate_technical_indicators(df, fundamental_data)
                    if not indicators:
                        continue

                    # 计算Alpha101因子
                    alpha_factors = self.calculate_alpha101_factors(df)

                    # 检查选股条件
                    is_etf = stock.get('is_etf', False)
                    passed, score = self.check_selection_criteria(indicators, alpha_factors, criteria, stock_name, is_etf)

                    if passed and score > 0:
                        stock_score = StockScore(
                            stock_code=stock_code,
                            stock_name=stock_name,
                            score=score,
                            indicators=indicators,
                            alpha_factors=alpha_factors,
                            selection_date=datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                        )
                        selected_stocks.append(stock_score)
                        selected_count += 1

                except Exception as e:
                    logger.error(f"处理股票 {stock.get('code', 'unknown')} 失败: {e}")
                    continue

            # 按评分排序
            selected_stocks.sort(key=lambda x: x.score, reverse=True)

            logger.info(f"选股完成，共选中 {len(selected_stocks)} 只股票")

            # 保存结果
            if selected_stocks:
                self.save_selection_results(selected_stocks, custom_name)

            return selected_stocks

        except Exception as e:
            logger.error(f"选股执行失败: {e}")
            return []

    def save_selection_results(self, selected_stocks: List[StockScore], custom_name: str):
        """保存选股结果"""
        try:
            # 生成文件名
            date_str = datetime.now().strftime('%Y-%m-%d')
            filename = f"{date_str}-{custom_name}.json"
            filepath = self.results_dir / filename

            # 准备保存数据
            save_data = {
                'selection_info': {
                    'date': date_str,
                    'time': datetime.now().strftime('%H:%M:%S'),
                    'custom_name': custom_name,
                    'total_selected': len(selected_stocks),
                    'selection_criteria': 'Multiple technical indicators and Alpha101 factors'
                },
                'stocks': [asdict(stock) for stock in selected_stocks]
            }

            # 保存到文件
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(save_data, f, ensure_ascii=False, indent=2, default=str)

            logger.info(f"选股结果已保存到: {filepath}")

            # 同时保存一个最新结果的副本
            latest_filepath = self.results_dir / f"latest-{custom_name}.json"
            with open(latest_filepath, 'w', encoding='utf-8') as f:
                json.dump(save_data, f, ensure_ascii=False, indent=2, default=str)

        except Exception as e:
            logger.error(f"保存选股结果失败: {e}")

    def load_selection_results(self, filename: str) -> Optional[List[StockScore]]:
        """加载选股结果"""
        try:
            filepath = self.results_dir / filename

            if not filepath.exists():
                logger.error(f"选股结果文件不存在: {filepath}")
                return None

            with open(filepath, 'r', encoding='utf-8') as f:
                data = json.load(f)

            stocks_data = data.get('stocks', [])
            selected_stocks = []

            for stock_data in stocks_data:
                stock_score = StockScore(
                    stock_code=stock_data['stock_code'],
                    stock_name=stock_data['stock_name'],
                    score=stock_data['score'],
                    indicators=stock_data['indicators'],
                    alpha_factors=stock_data['alpha_factors'],
                    selection_date=stock_data['selection_date']
                )
                selected_stocks.append(stock_score)

            logger.info(f"成功加载选股结果: {len(selected_stocks)} 只股票")
            return selected_stocks

        except Exception as e:
            logger.error(f"加载选股结果失败: {e}")
            return None

    def list_selection_files(self) -> List[Dict[str, Any]]:
        """列出所有选股结果文件"""
        try:
            files = []

            for filepath in self.results_dir.glob("*.json"):
                if filepath.name.startswith("latest-"):
                    continue

                try:
                    with open(filepath, 'r', encoding='utf-8') as f:
                        data = json.load(f)

                    selection_info = data.get('selection_info', {})

                    files.append({
                        'filename': filepath.name,
                        'date': selection_info.get('date', ''),
                        'time': selection_info.get('time', ''),
                        'custom_name': selection_info.get('custom_name', ''),
                        'total_selected': selection_info.get('total_selected', 0),
                        'file_size': filepath.stat().st_size,
                        'modified_time': datetime.fromtimestamp(filepath.stat().st_mtime).strftime('%Y-%m-%d %H:%M:%S')
                    })

                except Exception as e:
                    logger.error(f"读取文件信息失败 {filepath}: {e}")
                    continue

            # 按日期排序
            files.sort(key=lambda x: x['date'], reverse=True)

            return files

        except Exception as e:
            logger.error(f"列出选股文件失败: {e}")
            return []

    def get_stock_codes_from_file(self, filename: str) -> List[str]:
        """从选股结果文件中提取股票代码列表"""
        try:
            selected_stocks = self.load_selection_results(filename)
            if selected_stocks:
                return [stock.stock_code for stock in selected_stocks]
            return []

        except Exception as e:
            logger.error(f"提取股票代码失败: {e}")
            return []

# 选股器类定义完成
# 注意：不再创建全局实例，避免导入时的初始化问题
