#!/usr/bin/env python3
"""
详细调试选股功能 - 找出为什么总是返回0个结果
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from backend.stock_selection.stock_selector import StockSelector, SelectionCriteria
from backend.core.logger import get_logger
from datetime import datetime, timedelta
import time

logger = get_logger(__name__)

def debug_xttrader_connection():
    """调试xttrader连接"""
    print("🔍 检查xttrader连接状态...")
    
    try:
        import xtquant.xtdata as xt
        print("✅ xtquant模块导入成功")
        
        # 测试获取股票列表
        try:
            print("   正在获取股票列表...")
            start_time = time.time()
            stock_list = xt.get_stock_list_in_sector('沪深A股')
            end_time = time.time()
            
            if stock_list:
                print(f"✅ 成功获取股票列表，共 {len(stock_list)} 只股票")
                print(f"   获取耗时: {end_time - start_time:.2f}秒")
                print(f"   前5只股票: {stock_list[:5]}")
                return True, stock_list
            else:
                print("❌ 获取股票列表为空")
                return False, []
        except Exception as e:
            print(f"❌ 获取股票列表失败: {e}")
            import traceback
            traceback.print_exc()
            return False, []
            
    except ImportError as e:
        print(f"❌ xtquant模块导入失败: {e}")
        return False, []

def debug_single_stock_data(stock_code):
    """调试单只股票数据获取"""
    print(f"\n🔍 调试股票 {stock_code} 数据获取...")

    try:
        # 使用StockSelector的方法来获取数据
        from backend.stock_selection.stock_selector import StockSelector
        selector = StockSelector()

        # 计算日期范围
        end_date = datetime.now()
        start_date = end_date - timedelta(days=60)

        print(f"   时间范围: {start_date.strftime('%Y%m%d')} 到 {end_date.strftime('%Y%m%d')}")

        # 获取数据
        print("   正在获取股票数据...")
        start_time = time.time()

        df = selector._get_stock_data_from_xttrader(
            stock_code=stock_code,
            start_date=start_date.strftime('%Y%m%d'),
            end_date=end_date.strftime('%Y%m%d')
        )

        end_time = time.time()
        print(f"   数据获取耗时: {end_time - start_time:.2f}秒")

        if df is not None and not df.empty:
            print(f"✅ 成功获取数据，共 {len(df)} 个交易日")
            print(f"   数据列: {list(df.columns)}")
            print(f"   索引类型: {type(df.index)}")

            print(f"   最新数据:")
            latest = df.iloc[-1]
            for col in ['open', 'high', 'low', 'close', 'volume']:
                if col in df.columns:
                    print(f"     {col}: {latest[col]}")

            # 检查数据质量
            print(f"   数据质量检查:")
            print(f"     空值数量: {df.isnull().sum().sum()}")
            print(f"     零值数量: {(df == 0).sum().sum()}")
            print(f"     价格范围: {df['close'].min():.2f} - {df['close'].max():.2f}")
            print(f"     成交量范围: {df['volume'].min():.0f} - {df['volume'].max():.0f}")

            return df
        else:
            print(f"❌ 未获取到 {stock_code} 的数据")
            return None

    except Exception as e:
        print(f"❌ 获取 {stock_code} 数据失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def debug_technical_indicators(df, stock_code):
    """调试技术指标计算"""
    print(f"\n🔍 调试 {stock_code} 技术指标计算...")
    
    try:
        selector = StockSelector()
        
        print("   正在计算技术指标...")
        start_time = time.time()
        indicators = selector.calculate_technical_indicators(df)
        end_time = time.time()
        
        print(f"   指标计算耗时: {end_time - start_time:.2f}秒")
        
        if indicators:
            print("✅ 技术指标计算成功:")
            for key, value in indicators.items():
                if isinstance(value, (int, float)):
                    print(f"   {key}: {value:.4f}")
                else:
                    print(f"   {key}: {value}")
            return indicators
        else:
            print("❌ 技术指标计算失败")
            return None
            
    except Exception as e:
        print(f"❌ 技术指标计算异常: {e}")
        import traceback
        traceback.print_exc()
        return None

def debug_condition_check(indicators, alpha_factors, criteria, stock_code):
    """调试条件检查"""
    print(f"\n🔍 调试 {stock_code} 条件检查...")
    
    try:
        selector = StockSelector()
        
        print("   正在检查选股条件...")
        start_time = time.time()
        passed, score = selector.check_selection_criteria(indicators, alpha_factors, criteria)
        end_time = time.time()
        
        print(f"   条件检查耗时: {end_time - start_time:.4f}秒")
        print(f"   条件检查结果: {'通过' if passed else '不通过'}")
        print(f"   评分: {score:.2f}")
        
        # 详细检查每个条件
        print("   详细条件检查:")
        
        # 成交量条件
        if criteria.volume_min is not None:
            volume_ratio = indicators.get('volume_ratio', 0)
            volume_passed = volume_ratio >= criteria.volume_min
            print(f"     成交量比例: {volume_ratio:.4f} >= {criteria.volume_min} -> {'通过' if volume_passed else '不通过'}")
        
        # RSI条件
        if criteria.rsi_min is not None or criteria.rsi_max is not None:
            rsi = indicators.get('rsi', 0)
            rsi_passed = True
            if criteria.rsi_min is not None:
                rsi_passed = rsi_passed and (rsi >= criteria.rsi_min)
            if criteria.rsi_max is not None:
                rsi_passed = rsi_passed and (rsi <= criteria.rsi_max)
            print(f"     RSI: {rsi:.2f} -> {'通过' if rsi_passed else '不通过'}")
        
        # 价格条件
        if criteria.price_min is not None or criteria.price_max is not None:
            current_price = indicators.get('current_price', 0)
            price_passed = True
            if criteria.price_min is not None:
                price_passed = price_passed and (current_price >= criteria.price_min)
            if criteria.price_max is not None:
                price_passed = price_passed and (current_price <= criteria.price_max)
            print(f"     价格: {current_price:.2f} -> {'通过' if price_passed else '不通过'}")
        
        return passed, score
        
    except Exception as e:
        print(f"❌ 条件检查异常: {e}")
        import traceback
        traceback.print_exc()
        return False, 0

def debug_full_selection_process():
    """调试完整选股流程"""
    print(f"\n🚀 调试完整选股流程...")
    
    # 1. 检查xttrader连接
    xt_available, stock_list = debug_xttrader_connection()
    
    if not xt_available:
        print("❌ xttrader不可用，无法继续调试")
        return
    
    if not stock_list:
        print("❌ 股票列表为空，无法继续调试")
        return
    
    # 2. 测试前几只股票的完整流程
    print(f"\n🎯 测试前3只股票的完整流程")
    
    criteria = SelectionCriteria(
        volume_min=0.1,  # 降低条件
        condition_logic="flexible"
    )
    
    successful_stocks = 0
    failed_stocks = 0
    
    for i, test_stock in enumerate(stock_list[:3]):
        print(f"\n--- 测试股票 {i+1}: {test_stock} ---")
        
        # 获取数据
        df = debug_single_stock_data(test_stock)
        
        if df is not None and not df.empty and len(df) >= 20:
            # 计算技术指标
            indicators = debug_technical_indicators(df, test_stock)
            
            if indicators:
                # 计算Alpha因子
                try:
                    selector = StockSelector()
                    alpha_factors = selector.calculate_alpha101_factors(df)
                    print(f"✅ Alpha因子计算成功，共 {len(alpha_factors)} 个因子")
                except Exception as e:
                    print(f"❌ Alpha因子计算失败: {e}")
                    alpha_factors = {}
                
                # 测试条件检查
                passed, score = debug_condition_check(indicators, alpha_factors, criteria, test_stock)
                
                if passed:
                    successful_stocks += 1
                    print(f"✅ {test_stock} 通过选股条件，评分: {score:.2f}")
                else:
                    print(f"❌ {test_stock} 未通过选股条件，评分: {score:.2f}")
            else:
                failed_stocks += 1
                print(f"❌ {test_stock} 技术指标计算失败")
        else:
            failed_stocks += 1
            print(f"❌ {test_stock} 数据获取失败或数据不足")
    
    print(f"\n📊 测试结果汇总:")
    print(f"   成功通过: {successful_stocks} 只")
    print(f"   失败: {failed_stocks} 只")
    print(f"   通过率: {successful_stocks/(successful_stocks+failed_stocks)*100:.1f}%")

def main():
    """主函数"""
    print("🎯 QMT-TRADER 选股功能详细调试")
    print("=" * 60)
    
    # 执行详细调试
    debug_full_selection_process()
    
    print("\n" + "=" * 60)
    print("✅ 详细调试完成")

if __name__ == "__main__":
    main()
