"""
回测结果文件存储管理器
使用JSON文件存储回测结果，支持查询、导出等功能
"""

import os
import json
import shutil
from datetime import datetime
from typing import List, Dict, Any, Optional
from dataclasses import asdict
from pathlib import Path

from backend.core.logger import get_logger
from backend.backtest.backtest_engine import BacktestResult

logger = get_logger(__name__)

class BacktestStorage:
    """回测结果文件存储管理器"""
    
    def __init__(self, storage_dir: str = "data/backtest_results"):
        """
        初始化存储管理器
        
        Args:
            storage_dir: 存储目录路径
        """
        self.storage_dir = Path(storage_dir)
        self.storage_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建子目录
        self.results_dir = self.storage_dir / "results"
        self.index_dir = self.storage_dir / "index"
        self.exports_dir = self.storage_dir / "exports"
        
        for dir_path in [self.results_dir, self.index_dir, self.exports_dir]:
            dir_path.mkdir(exist_ok=True)
        
        # 索引文件路径
        self.index_file = self.index_dir / "backtest_index.json"
        
        logger.info(f"📁 回测存储管理器初始化完成，存储目录: {self.storage_dir}")
    
    def save_result(self, result: BacktestResult) -> bool:
        """
        保存回测结果到文件
        
        Args:
            result: 回测结果对象
            
        Returns:
            bool: 保存是否成功
        """
        try:
            logger.info(f"💾 开始保存回测结果: {result.task_id}")
            
            # 转换为字典
            result_dict = asdict(result)
            
            # 保存详细结果文件
            result_file = self.results_dir / f"{result.task_id}.json"
            with open(result_file, 'w', encoding='utf-8') as f:
                json.dump(result_dict, f, ensure_ascii=False, indent=2)
            
            # 更新索引
            self._update_index(result)
            
            logger.info(f"✅ 回测结果保存成功: {result.task_id}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 保存回测结果失败: {result.task_id}, 错误: {e}")
            return False
    
    def get_result(self, task_id: str) -> Optional[Dict[str, Any]]:
        """
        获取指定的回测结果
        
        Args:
            task_id: 任务ID
            
        Returns:
            Dict: 回测结果字典，如果不存在返回None
        """
        try:
            result_file = self.results_dir / f"{task_id}.json"
            
            if not result_file.exists():
                logger.warning(f"⚠️ 回测结果文件不存在: {task_id}")
                return None
            
            with open(result_file, 'r', encoding='utf-8') as f:
                result = json.load(f)
            
            logger.debug(f"📖 成功读取回测结果: {task_id}")
            return result
            
        except Exception as e:
            logger.error(f"❌ 读取回测结果失败: {task_id}, 错误: {e}")
            return None
    
    def get_results_list(self, 
                        strategy_name: Optional[str] = None,
                        start_date: Optional[str] = None,
                        end_date: Optional[str] = None,
                        limit: int = 50,
                        offset: int = 0) -> Dict[str, Any]:
        """
        获取回测结果列表
        
        Args:
            strategy_name: 策略名称筛选
            start_date: 开始日期筛选
            end_date: 结束日期筛选
            limit: 返回数量限制
            offset: 偏移量
            
        Returns:
            Dict: 包含结果列表和总数的字典
        """
        try:
            logger.info(f"📋 查询回测结果列表，策略: {strategy_name}, 限制: {limit}")
            
            index = self._load_index()
            
            # 筛选结果
            filtered_results = []
            for item in index:
                # 策略名称筛选
                if strategy_name and item.get('strategy_name') != strategy_name:
                    continue
                
                # 日期筛选
                if start_date and item.get('created_at', '') < start_date:
                    continue
                if end_date and item.get('created_at', '') > end_date:
                    continue
                
                filtered_results.append(item)
            
            # 按创建时间倒序排列
            filtered_results.sort(key=lambda x: x.get('created_at', ''), reverse=True)
            
            # 分页
            total = len(filtered_results)
            results = filtered_results[offset:offset + limit]
            
            logger.info(f"✅ 查询完成，总数: {total}, 返回: {len(results)}")
            
            return {
                'results': results,
                'total': total,
                'limit': limit,
                'offset': offset
            }
            
        except Exception as e:
            logger.error(f"❌ 查询回测结果列表失败: {e}")
            return {'results': [], 'total': 0, 'limit': limit, 'offset': offset}
    
    def delete_result(self, task_id: str) -> bool:
        """
        删除回测结果
        
        Args:
            task_id: 任务ID
            
        Returns:
            bool: 删除是否成功
        """
        try:
            logger.info(f"🗑️ 开始删除回测结果: {task_id}")
            
            # 删除结果文件
            result_file = self.results_dir / f"{task_id}.json"
            if result_file.exists():
                result_file.unlink()
            
            # 从索引中移除
            self._remove_from_index(task_id)
            
            logger.info(f"✅ 回测结果删除成功: {task_id}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 删除回测结果失败: {task_id}, 错误: {e}")
            return False
    
    def export_result(self, task_id: str, format: str = 'json') -> Optional[str]:
        """
        导出回测结果
        
        Args:
            task_id: 任务ID
            format: 导出格式 ('json', 'csv', 'excel')
            
        Returns:
            str: 导出文件路径，失败返回None
        """
        try:
            logger.info(f"📤 开始导出回测结果: {task_id}, 格式: {format}")
            
            result = self.get_result(task_id)
            if not result:
                logger.error(f"❌ 回测结果不存在: {task_id}")
                return None
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            if format == 'json':
                export_file = self.exports_dir / f"{task_id}_{timestamp}.json"
                with open(export_file, 'w', encoding='utf-8') as f:
                    json.dump(result, f, ensure_ascii=False, indent=2)
            
            elif format == 'csv':
                export_file = self.exports_dir / f"{task_id}_{timestamp}.csv"
                self._export_to_csv(result, export_file)
            
            elif format == 'excel':
                export_file = self.exports_dir / f"{task_id}_{timestamp}.xlsx"
                self._export_to_excel(result, export_file)
            
            else:
                logger.error(f"❌ 不支持的导出格式: {format}")
                return None
            
            logger.info(f"✅ 回测结果导出成功: {export_file}")
            return str(export_file)
            
        except Exception as e:
            logger.error(f"❌ 导出回测结果失败: {task_id}, 错误: {e}")
            return None
    
    def _update_index(self, result: BacktestResult):
        """更新索引文件"""
        try:
            index = self._load_index()
            
            # 创建索引项
            index_item = {
                'task_id': result.task_id,
                'strategy_name': result.strategy_name,
                'start_date': result.start_date,
                'end_date': result.end_date,
                'initial_capital': result.initial_capital,
                'final_capital': result.final_capital,
                'total_return': result.total_return,
                'annual_return': result.annual_return,
                'sharpe_ratio': result.sharpe_ratio,
                'max_drawdown': result.max_drawdown,
                'win_rate': result.win_rate,
                'total_trades': result.total_trades,
                'created_at': result.created_at,
                'completed_at': result.completed_at,
                'status': result.status
            }
            
            # 移除已存在的项（如果有）
            index = [item for item in index if item.get('task_id') != result.task_id]
            
            # 添加新项
            index.append(index_item)
            
            # 保存索引
            with open(self.index_file, 'w', encoding='utf-8') as f:
                json.dump(index, f, ensure_ascii=False, indent=2)
            
            logger.debug(f"📝 索引更新成功: {result.task_id}")
            
        except Exception as e:
            logger.error(f"❌ 更新索引失败: {e}")
    
    def _load_index(self) -> List[Dict[str, Any]]:
        """加载索引文件"""
        try:
            if not self.index_file.exists():
                return []
            
            with open(self.index_file, 'r', encoding='utf-8') as f:
                return json.load(f)
                
        except Exception as e:
            logger.error(f"❌ 加载索引失败: {e}")
            return []
    
    def _remove_from_index(self, task_id: str):
        """从索引中移除项"""
        try:
            index = self._load_index()
            index = [item for item in index if item.get('task_id') != task_id]
            
            with open(self.index_file, 'w', encoding='utf-8') as f:
                json.dump(index, f, ensure_ascii=False, indent=2)
            
            logger.debug(f"📝 从索引中移除: {task_id}")
            
        except Exception as e:
            logger.error(f"❌ 从索引中移除失败: {e}")
    
    def _export_to_csv(self, result: Dict[str, Any], file_path: Path):
        """导出为CSV格式"""
        import csv
        
        # 导出交易记录
        trades = result.get('trades', [])
        if trades:
            with open(file_path, 'w', newline='', encoding='utf-8') as f:
                writer = csv.DictWriter(f, fieldnames=trades[0].keys())
                writer.writeheader()
                writer.writerows(trades)
    
    def _export_to_excel(self, result: Dict[str, Any], file_path: Path):
        """导出为Excel格式"""
        try:
            import pandas as pd
            
            with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                # 基本信息
                basic_info = {
                    '项目': ['任务ID', '策略名称', '回测期间', '初始资金', '最终资金', 
                            '总收益率', '年化收益', '夏普比率', '最大回撤', '胜率', '总交易次数'],
                    '值': [
                        result.get('task_id'),
                        result.get('strategy_name'),
                        f"{result.get('start_date')} ~ {result.get('end_date')}",
                        result.get('initial_capital'),
                        result.get('final_capital'),
                        f"{result.get('total_return', 0) * 100:.2f}%",
                        f"{result.get('annual_return', 0) * 100:.2f}%",
                        result.get('sharpe_ratio'),
                        f"{result.get('max_drawdown', 0) * 100:.2f}%",
                        f"{result.get('win_rate', 0) * 100:.2f}%",
                        result.get('total_trades')
                    ]
                }
                pd.DataFrame(basic_info).to_excel(writer, sheet_name='基本信息', index=False)
                
                # 交易记录
                trades = result.get('trades', [])
                if trades:
                    pd.DataFrame(trades).to_excel(writer, sheet_name='交易记录', index=False)
                
                # 每日收益
                daily_returns = result.get('daily_returns', [])
                if daily_returns:
                    pd.DataFrame(daily_returns).to_excel(writer, sheet_name='每日收益', index=False)
                    
        except ImportError:
            logger.warning("⚠️ pandas或openpyxl未安装，无法导出Excel格式")
            raise

# 全局存储管理器实例
backtest_storage = BacktestStorage()
