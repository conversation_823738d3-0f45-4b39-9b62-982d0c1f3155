# -*- coding: utf-8 -*-
"""
配置驱动系统 - 核心配置管理器
支持策略配置、回测配置、实盘配置的统一管理
"""

import json
import os
import yaml
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

@dataclass
class StrategyConfig:
    """策略配置基类"""
    name: str
    description: str = ""
    parameters: Dict[str, Any] = None
    display_name: str = ""
    category: str = ""
    # 可选字段，提供默认值
    version: str = "1.0.0"
    author: str = "System"
    created_at: str = ""
    class_path: str = ""
    config_file: str = ""
    enabled: bool = True
    risk_level: str = "medium"
    updated_at: str = ""

    def __post_init__(self):
        """初始化后处理"""
        if self.parameters is None:
            self.parameters = {}
        if not self.created_at:
            from datetime import datetime
            self.created_at = datetime.now().isoformat()
        if not self.updated_at:
            self.updated_at = self.created_at

    def to_dict(self):
        return asdict(self)

    @classmethod
    def from_dict(cls, data: Dict[str, Any]):
        # 只保留StrategyConfig支持的字段
        valid_fields = {
            'name', 'description', 'version', 'author', 'created_at', 'parameters',
            'display_name', 'class_path', 'config_file', 'enabled', 'category',
            'risk_level', 'updated_at'
        }
        filtered_data = {k: v for k, v in data.items() if k in valid_fields}

        # 确保必需字段存在
        if 'name' not in filtered_data:
            raise ValueError("Strategy config must have 'name' field")

        return cls(**filtered_data)

@dataclass
class BacktestConfig:
    """回测配置 - 不提供默认值"""
    start_date: str
    end_date: str
    initial_capital: float
    commission: float
    slippage: float
    benchmark: str
    data_frequency: str  # 移除默认值

    def to_dict(self):
        return asdict(self)

@dataclass
class LiveTradingConfig:
    """实盘交易配置 - 不提供默认值"""
    account_id: str
    broker: str
    max_positions: int
    risk_limit: float
    enable_trading: bool
    paper_trading: bool
    min_path: str
    session_id_range: List[int]  # 改为List[int]以匹配JSON格式

    def to_dict(self):
        return asdict(self)

@dataclass
class DataConfig:
    """数据配置 - 不提供默认值"""
    data_source: str
    min_path: str
    session_id_range: List[int]  # 改为List[int]以匹配JSON格式

    def to_dict(self):
        return asdict(self)

class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_dir: str = None):
        # 自动检测配置目录
        if config_dir is None:
            # 检测可能的配置目录路径
            possible_paths = [
                "config",  # 从backend目录启动
                "backend/config",  # 从根目录启动
                os.path.join(os.path.dirname(__file__), "..", "config"),  # 相对于当前文件
            ]

            config_dir = "backend/config"  # 默认值
            for path in possible_paths:
                abs_path = os.path.abspath(path)
                if os.path.exists(abs_path) and os.path.isdir(abs_path):
                    # 检查是否包含必要的配置文件
                    required_files = ["strategies.json", "backtest.json", "live_trading.json"]
                    if all(os.path.exists(os.path.join(abs_path, f)) for f in required_files):
                        config_dir = path
                        break
        self.config_dir = config_dir
        self.ensure_config_dir()
        
        # 配置文件路径
        self.strategy_configs_file = os.path.join(config_dir, "strategies.json")
        self.backtest_configs_file = os.path.join(config_dir, "backtest.json")
        self.live_configs_file = os.path.join(config_dir, "live_trading.json")
        self.data_configs_file = os.path.join(config_dir, "data.json")
        self.system_config_file = os.path.join(config_dir, "system.yaml")
        
        # 内存缓存
        self._strategy_configs = {}
        self._backtest_configs = {}
        self._live_configs = {}
        self._data_config = None
        self._system_config = None
        
        # 加载配置
        self.load_all_configs()
    
    def ensure_config_dir(self):
        """确保配置目录存在"""
        os.makedirs(self.config_dir, exist_ok=True)
    
    def load_all_configs(self):
        """加载所有配置"""
        self.load_strategy_configs()
        self.load_backtest_configs()
        self.load_live_configs()
        self.load_data_config()
        self.load_system_config()
    
    def load_strategy_configs(self):
        """加载策略配置 - 不创建默认配置"""
        if not os.path.exists(self.strategy_configs_file):
            raise FileNotFoundError(f"策略配置文件不存在: {self.strategy_configs_file}")

        with open(self.strategy_configs_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
            # 处理strategies.json的嵌套结构
            strategies_data = data.get('strategies', data)

            self._strategy_configs = {}
            for name, config in strategies_data.items():
                # 从策略的配置文件中加载parameters
                config_file = config.get('config_file')
                if config_file and os.path.exists(config_file):
                    with open(config_file, 'r', encoding='utf-8') as param_file:
                        param_data = json.load(param_file)
                        config['parameters'] = param_data.get('parameters', {})
                else:
                    config['parameters'] = {}

                self._strategy_configs[name] = StrategyConfig.from_dict(config)

        logger.info(f"加载了 {len(self._strategy_configs)} 个策略配置")
    
    def load_backtest_configs(self):
        """加载回测配置 - 不创建默认配置"""
        if not os.path.exists(self.backtest_configs_file):
            raise FileNotFoundError(f"回测配置文件不存在: {self.backtest_configs_file}")

        with open(self.backtest_configs_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
            self._backtest_configs = {
                name: BacktestConfig(**config)
                for name, config in data.items()
            }
        logger.info(f"加载了 {len(self._backtest_configs)} 个回测配置")
    
    def load_live_configs(self):
        """加载实盘配置"""
        if os.path.exists(self.live_configs_file):
            with open(self.live_configs_file, 'r', encoding='utf-8') as f:
                
                data = json.load(f)
                print(f"===============  data: {data}")
                self._live_configs = {
                    name: LiveTradingConfig(**config) 
                    for name, config in data.items()
                }
            logger.info(f"加载了 {len(self._live_configs)} 个实盘配置")
        else:
            raise FileNotFoundError(f"实盘配置文件不存在: {self.live_configs_file}")
    
    def load_data_config(self):
        """加载数据配置 - 不创建默认配置"""
        if not os.path.exists(self.data_configs_file):
            raise FileNotFoundError(f"数据配置文件不存在: {self.data_configs_file}")

        with open(self.data_configs_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
            # 只保留DataConfig支持的字段
            valid_fields = {'data_source', 'min_path', 'session_id_range'}
            filtered_data = {k: v for k, v in data.items() if k in valid_fields}
            self._data_config = DataConfig(**filtered_data)
        logger.info("加载数据配置成功")
    
    def load_system_config(self):
        """加载系统配置 - 不创建默认配置"""
        if not os.path.exists(self.system_config_file):
            raise FileNotFoundError(f"系统配置文件不存在: {self.system_config_file}")

        with open(self.system_config_file, 'r', encoding='utf-8') as f:
            self._system_config = yaml.safe_load(f)
        logger.info("加载系统配置成功")
    

    

    

    

    
    # 保存配置方法
    def save_strategy_configs(self):
        """保存策略配置"""
        data = {name: config.to_dict() for name, config in self._strategy_configs.items()}
        with open(self.strategy_configs_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
    
    def save_backtest_configs(self):
        """保存回测配置"""
        data = {name: config.to_dict() for name, config in self._backtest_configs.items()}
        with open(self.backtest_configs_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
    
    def save_live_configs(self):
        """保存实盘配置"""
        data = {name: config.to_dict() for name, config in self._live_configs.items()}
        with open(self.live_configs_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
    
    def save_data_config(self):
        """保存数据配置"""
        with open(self.data_configs_file, 'w', encoding='utf-8') as f:
            json.dump(self._data_config.to_dict(), f, ensure_ascii=False, indent=2)
    
    def save_system_config(self):
        """保存系统配置"""
        with open(self.system_config_file, 'w', encoding='utf-8') as f:
            yaml.dump(self._system_config, f, default_flow_style=False, allow_unicode=True)
    
    # 获取配置方法
    def get_strategy_config(self, name: str) -> Optional[StrategyConfig]:
        """获取策略配置"""
        return self._strategy_configs.get(name)
    
    def get_all_strategy_configs(self) -> Dict[str, StrategyConfig]:
        """获取所有策略配置"""
        return self._strategy_configs.copy()
    
    def get_backtest_config(self, name: str) -> Optional[BacktestConfig]:
        """获取回测配置"""
        return self._backtest_configs.get(name)
    
    def get_all_backtest_configs(self) -> Dict[str, BacktestConfig]:
        """获取所有回测配置"""
        return self._backtest_configs.copy()
    
    def get_live_config(self, name: str) -> Optional[LiveTradingConfig]:
        """获取实盘配置"""
        return self._live_configs.get(name)
    
    def get_all_live_configs(self) -> Dict[str, LiveTradingConfig]:
        """获取所有实盘配置"""
        return self._live_configs.copy()
    
    def get_data_config(self) -> DataConfig:
        """获取数据配置"""
        return self._data_config
    
    def get_system_config(self) -> Dict[str, Any]:
        """获取系统配置"""
        return self._system_config.copy()
    
    # 更新配置方法
    def update_strategy_config(self, name: str, config: StrategyConfig):
        """更新策略配置"""
        self._strategy_configs[name] = config
        self.save_strategy_configs()
    
    def update_backtest_config(self, name: str, config: BacktestConfig):
        """更新回测配置"""
        self._backtest_configs[name] = config
        self.save_backtest_configs()
    
    def update_live_config(self, name: str, config: LiveTradingConfig):
        """更新实盘配置"""
        self._live_configs[name] = config
        self.save_live_configs()
    
    def update_data_config(self, config: DataConfig):
        """更新数据配置"""
        self._data_config = config
        self.save_data_config()
    
    def update_system_config(self, config: Dict[str, Any]):
        """更新系统配置"""
        self._system_config.update(config)
        self.save_system_config()

# 全局配置管理器实例
config_manager = ConfigManager()
