#!/usr/bin/env python3
"""
开发模式API服务器
不依赖QMT连接，用于前端开发和测试
"""

import os
import sys
import time
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import logging

# 导入选股API
from backend.api.stock_selection_api import router as stock_selection_router

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建FastAPI应用
app = FastAPI(
    title="QMT Trader API (开发模式)",
    description="股票交易系统API - 开发模式，不依赖QMT连接",
    version="1.0.0"
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000", "http://localhost:3001", "http://127.0.0.1:3001"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册选股API路由
app.include_router(stock_selection_router)

# 模拟数据
MOCK_POSITIONS = [
    {
        "stock_code": "000001.SZ",
        "stock_name": "平安银行",
        "quantity": 1000,
        "avg_price": 12.50,
        "current_price": 13.20,
        "pnl_ratio": 0.056,
        "market_value": 13200,
        "pnl": 700
    },
    {
        "stock_code": "000002.SZ", 
        "stock_name": "万科A",
        "quantity": 500,
        "avg_price": 18.80,
        "current_price": 17.90,
        "pnl_ratio": -0.048,
        "market_value": 8950,
        "pnl": -450
    },
    {
        "stock_code": "600000.SH",
        "stock_name": "浦发银行",
        "quantity": 800,
        "avg_price": 9.20,
        "current_price": 9.85,
        "pnl_ratio": 0.071,
        "market_value": 7880,
        "pnl": 520
    }
]

MOCK_SIGNALS = [
    {
        "stock_code": "000002.SZ",
        "signal_type": "loss_stop",
        "current_price": 17.90,
        "loss_ratio": -0.048,
        "message": "000002.SZ 亏损4.8%，触发亏损止损",
        "timestamp": (datetime.now() - timedelta(minutes=5)).isoformat()
    },
    {
        "stock_code": "600519.SH",
        "signal_type": "trailing_stop", 
        "current_price": 1680.00,
        "high_price": 1750.00,
        "drop_ratio": 0.04,
        "message": "600519.SH 从最高点1750.00下跌4.0%，触发移动止损",
        "timestamp": (datetime.now() - timedelta(minutes=15)).isoformat()
    }
]

MOCK_PENDING_ORDERS = [
    {
        "order_id": 12345,
        "stock_code": "000002.SZ",
        "quantity": 500,
        "price": 17.90,
        "order_type": "sell",
        "status": "pending",
        "created_time": (datetime.now() - timedelta(minutes=3)).strftime("%Y-%m-%d %H:%M:%S"),
        "last_status": "pending",
        "last_check": datetime.now().isoformat()
    }
]

# 全局状态
monitor_status = {
    "is_running": False,
    "last_check": None,
    "config": {
        "loss_stop_enabled": True,
        "loss_stop_percent": 5.0,
        "trailing_stop_enabled": True,
        "trailing_stop_percent": 5.0,
        "auto_sell": False,
        "monitor_interval": 60
    }
}

# API路由

@app.get("/")
async def root():
    return {
        "message": "QMT Trader API - 开发模式",
        "version": "1.0.0",
        "mode": "development",
        "timestamp": datetime.now().isoformat()
    }

@app.get("/api/data/status")
async def get_data_status():
    """获取数据状态"""
    return {
        "success": True,
        "data": {
            "qmt_connected": False,
            "mode": "development",
            "last_update": datetime.now().isoformat(),
            "message": "开发模式 - 使用模拟数据"
        }
    }

@app.get("/api/data/account")
async def get_account_info():
    """获取账户信息"""
    return {
        "success": True,
        "data": {
            "account_id": "DEV_ACCOUNT",
            "total_value": 100000.00,
            "available_cash": 70000.00,
            "market_value": 30000.00,
            "positions": MOCK_POSITIONS,
            "last_update": datetime.now().isoformat()
        }
    }

@app.get("/api/position-monitor/status")
async def get_monitor_status():
    """获取监控状态"""
    return {
        "success": True,
        "data": monitor_status
    }

@app.post("/api/position-monitor/start")
async def start_monitor():
    """启动监控"""
    monitor_status["is_running"] = True
    monitor_status["last_check"] = datetime.now().isoformat()
    return {
        "success": True,
        "message": "监控已启动"
    }

@app.post("/api/position-monitor/stop")
async def stop_monitor():
    """停止监控"""
    monitor_status["is_running"] = False
    return {
        "success": True,
        "message": "监控已停止"
    }

@app.get("/api/position-monitor/signals")
async def get_signals(limit: int = 100):
    """获取止损信号"""
    return MOCK_SIGNALS[-limit:] if limit > 0 else MOCK_SIGNALS

@app.post("/api/position-monitor/check")
async def manual_check():
    """手动检查"""
    return {
        "signals": MOCK_SIGNALS,
        "count": len(MOCK_SIGNALS),
        "message": f"检查完成，发现{len(MOCK_SIGNALS)}个止损信号"
    }

@app.get("/api/position-monitor/pending-orders")
async def get_pending_orders():
    """获取待处理订单"""
    return {
        "success": True,
        "pending_orders": MOCK_PENDING_ORDERS,
        "count": len(MOCK_PENDING_ORDERS)
    }

@app.post("/api/position-monitor/check-orders")
async def check_orders():
    """检查订单状态"""
    return {
        "success": True,
        "message": "订单状态检查完成",
        "pending_count": len(MOCK_PENDING_ORDERS),
        "pending_orders": MOCK_PENDING_ORDERS
    }

@app.post("/api/position-monitor/cancel-order/{order_id}")
async def cancel_order(order_id: int):
    """取消订单"""
    # 从待处理订单中移除
    global MOCK_PENDING_ORDERS
    MOCK_PENDING_ORDERS = [order for order in MOCK_PENDING_ORDERS if order["order_id"] != order_id]
    
    return {
        "success": True,
        "message": f"订单 {order_id} 取消成功",
        "order_id": order_id
    }

@app.get("/api/position-monitor/dashboard")
async def get_dashboard():
    """获取仪表板数据"""
    return {
        "success": True,
        "data": {
            "positions": MOCK_POSITIONS,
            "recent_signals": list(reversed(MOCK_SIGNALS)),
            "pending_orders": MOCK_PENDING_ORDERS,
            "monitor_status": monitor_status,
            "statistics": {
                "total_positions": len(MOCK_POSITIONS),
                "profit_positions": sum(1 for p in MOCK_POSITIONS if p["pnl_ratio"] > 0),
                "loss_positions": sum(1 for p in MOCK_POSITIONS if p["pnl_ratio"] < 0),
                "total_signals": len(MOCK_SIGNALS),
                "total_pending": len(MOCK_PENDING_ORDERS),
                "is_monitoring": monitor_status["is_running"]
            },
            "last_update": time.time()
        }
    }

@app.get("/api/position-monitor/alerts/latest")
async def get_latest_alerts(since: float = None):
    """获取最新警告"""
    return {
        "success": True,
        "data": {
            "signals": list(reversed(MOCK_SIGNALS)),
            "count": len(MOCK_SIGNALS),
            "has_new": True
        }
    }

# 股票池管理API (简化版)
@app.get("/api/data/universes")
async def get_stock_universes():
    """获取股票池"""
    return {
        "success": True,
        "data": [
            {
                "name": "default_small",
                "display_name": "默认小股票池",
                "description": "适合新手的小型股票池",
                "universe_type": "custom",
                "stock_count": 10
            }
        ]
    }

@app.get("/api/data/universes/{universe_name}")
async def get_stock_universe(universe_name: str):
    """获取指定股票池"""
    return {
        "success": True,
        "data": {
            "name": universe_name,
            "display_name": "默认小股票池",
            "description": "适合新手的小型股票池",
            "universe_type": "custom",
            "stock_codes": ["000001.SZ", "000002.SZ", "600000.SH"],
            "stock_count": 3
        }
    }

if __name__ == "__main__":
    import uvicorn
    print("🚀 启动开发模式API服务器...")
    print("📋 功能:")
    print("  - 不依赖QMT连接")
    print("  - 使用模拟数据")
    print("  - 支持前端开发和测试")
    print("  - 访问地址: http://localhost:8000")

    uvicorn.run(app, host="0.0.0.0", port=8000)
