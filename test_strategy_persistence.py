#!/usr/bin/env python3
"""
测试多策略持久化功能
"""

import requests
import json
import time

BASE_URL = "http://localhost:8000"

def test_api_endpoint(endpoint, method="GET", data=None):
    """测试API接口"""
    url = f"{BASE_URL}{endpoint}"
    print(f"\n测试 {method} {endpoint}")
    
    try:
        if method == "GET":
            response = requests.get(url, timeout=10)
        elif method == "POST":
            response = requests.post(url, json=data, timeout=10)
        elif method == "DELETE":
            response = requests.delete(url, timeout=10)
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
            return result
        else:
            print(f"错误: {response.text}")
            return None
            
    except Exception as e:
        print(f"请求失败: {e}")
        return None

def main():
    """主测试函数"""
    print("=== 多策略持久化功能测试 ===")
    
    # 1. 查看当前运行的策略
    print("\n1. 查看当前运行的策略")
    strategies = test_api_endpoint("/api/live/strategies")
    
    # 2. 启动一个测试策略
    print("\n2. 启动测试策略")
    test_strategy = {
        "name": "测试持久化策略",
        "strategy_type": "bollinger_bands",
        "config": {
            "initial_capital": 100000,
            "max_positions": 3,
            "risk_limit": 0.02,
            "bb_period": 20,
            "bb_std": 2.0
        }
    }
    
    start_result = test_api_endpoint("/api/live/strategies", "POST", test_strategy)
    strategy_id = None
    if start_result and start_result.get('success'):
        strategy_id = start_result.get('strategy_id')
        print(f"策略ID: {strategy_id}")
    
    # 3. 再次查看运行的策略
    print("\n3. 查看启动后的策略列表")
    strategies = test_api_endpoint("/api/live/strategies")
    
    # 4. 检查持久化文件是否创建
    print("\n4. 检查持久化文件")
    import os
    tasks_file = "data/multi_strategy_tasks.json"
    if os.path.exists(tasks_file):
        print(f"✅ 持久化文件存在: {tasks_file}")
        with open(tasks_file, 'r', encoding='utf-8') as f:
            tasks_data = json.load(f)
        print("文件内容:")
        print(json.dumps(tasks_data, indent=2, ensure_ascii=False))
    else:
        print(f"❌ 持久化文件不存在: {tasks_file}")
    
    # 5. 测试手动恢复功能
    print("\n5. 测试手动恢复功能")
    recover_result = test_api_endpoint("/api/live/strategies/recover", "POST")
    
    # 6. 停止策略（如果启动成功）
    if strategy_id:
        print(f"\n6. 停止测试策略: {strategy_id}")
        stop_result = test_api_endpoint(f"/api/live/strategies/{strategy_id}", "DELETE")
        
        # 7. 再次检查持久化文件
        print("\n7. 检查停止后的持久化文件")
        if os.path.exists(tasks_file):
            with open(tasks_file, 'r', encoding='utf-8') as f:
                tasks_data = json.load(f)
            print("停止后的文件内容:")
            print(json.dumps(tasks_data, indent=2, ensure_ascii=False))
    
    print("\n=== 测试完成 ===")
    print("\n💡 重启系统测试说明:")
    print("1. 启动一个策略后，重启后端服务")
    print("2. 查看策略是否自动恢复")
    print("3. 或者调用 POST /api/live/strategies/recover 手动恢复")

if __name__ == "__main__":
    main()
