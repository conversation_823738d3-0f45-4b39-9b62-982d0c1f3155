#!/usr/bin/env python3
"""
测试QMT数据获取 - 只使用真实数据
"""

import sys
import os
import asyncio
import logging
from datetime import datetime, timedelta

# 添加项目路径
sys.path.append(os.path.dirname(__file__))

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

async def test_qmt_data_only():
    """测试QMT数据获取 - 只使用真实数据"""
    logger.info("=== 测试QMT真实数据获取 ===")
    
    try:
        from backend.backtest.simple_backtest_engine import SimpleBacktestEngine
        
        # 创建回测引擎
        engine = SimpleBacktestEngine()
        
        # 设置测试参数 - 使用较短的时间范围
        end_date = datetime.now().strftime('%Y-%m-%d')
        start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
        task_id = "test_qmt_only"
        
        # 初始化任务状态
        engine.running_tasks[task_id] = {
            'status': 'running',
            'progress': 0,
            'message': '开始测试',
            'error': None
        }
        
        logger.info(f"测试日期范围: {start_date} 到 {end_date}")
        
        # 测试QMT数据获取
        logger.info("开始测试QMT真实数据获取...")
        stock_data = await engine._get_stock_data(start_date, end_date, task_id)
        
        if stock_data:
            logger.info(f"✅ QMT数据获取成功: {len(stock_data)}只股票")
            
            # 显示获取到的股票信息
            for i, (stock_code, df) in enumerate(stock_data.items()):
                logger.info(f"  {i+1}. {stock_code}: {len(df)}条记录")
                if len(df) > 0:
                    logger.info(f"     数据范围: {df.index[0]} 到 {df.index[-1]}")
                    logger.info(f"     最新收盘价: {df['close'].iloc[-1]:.2f}")
                    logger.info(f"     数据列: {list(df.columns)}")
            
            logger.info("🎉 QMT真实数据测试成功！")
            return True
        else:
            logger.error("❌ QMT数据获取失败")
            return False
            
    except Exception as e:
        logger.error(f"❌ QMT数据测试失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False

async def test_single_stock():
    """测试单只股票数据获取"""
    logger.info("\n=== 测试单只股票数据获取 ===")
    
    try:
        from backend.data.data_manager import data_manager
        
        # 测试知名股票
        test_stocks = ['000001.SZ', '600000.SH', '600519.SH']
        
        end_date = datetime.now().strftime('%Y-%m-%d')
        start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
        
        for stock_code in test_stocks:
            logger.info(f"\n--- 测试 {stock_code} ---")
            
            try:
                df = data_manager.get_stock_data(
                    stock_code=stock_code,
                    start_date=start_date,
                    end_date=end_date
                )
                
                if df is not None and len(df) > 0:
                    logger.info(f"✅ {stock_code} 数据获取成功: {len(df)}条记录")
                    logger.info(f"   数据范围: {df.index[0]} 到 {df.index[-1]}")
                    logger.info(f"   收盘价范围: {df['close'].min():.2f} - {df['close'].max():.2f}")
                    
                    # 显示前3条数据
                    logger.info("   前3条数据:")
                    for i in range(min(3, len(df))):
                        row = df.iloc[i]
                        logger.info(f"     {df.index[i]}: O={row['open']:.2f}, H={row['high']:.2f}, L={row['low']:.2f}, C={row['close']:.2f}")
                else:
                    logger.warning(f"⚠️ {stock_code} 数据为空")
                    
            except Exception as e:
                logger.error(f"❌ {stock_code} 数据获取失败: {e}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 单只股票测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    logger.info("🔍 开始QMT真实数据测试")
    
    # 测试1: 单只股票数据获取
    single_ok = await test_single_stock()
    
    if single_ok:
        # 测试2: 回测引擎数据获取
        engine_ok = await test_qmt_data_only()
        
        if engine_ok:
            logger.info("\n🎉 所有QMT测试通过！")
            logger.info("✅ QMT连接正常")
            logger.info("✅ 真实数据获取成功")
            logger.info("✅ 回测引擎数据获取正常")
            logger.info("✅ 现在可以运行布林带策略了")
        else:
            logger.error("\n❌ 回测引擎数据获取失败")
    else:
        logger.error("\n❌ 单只股票数据获取失败")

if __name__ == "__main__":
    asyncio.run(main())
