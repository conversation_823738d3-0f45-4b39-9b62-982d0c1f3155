#!/usr/bin/env python3
"""
股票池管理器
统一管理所有策略的股票池配置
"""

from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from enum import Enum
import json
import os
from datetime import datetime, timedelta

from backend.core.logger import get_logger
from backend.data.data_manager import data_manager

logger = get_logger(__name__)

class UniverseType(Enum):
    """股票池类型"""
    CUSTOM = "custom"           # 自定义股票池
    INDEX_HS300 = "hs300"      # 沪深300
    INDEX_ZZ500 = "zz500"      # 中证500
    INDEX_ZZ1000 = "zz1000"    # 中证1000
    INDEX_CYB = "cyb"          # 创业板指
    INDEX_KECHUANG = "kc50"    # 科创50
    SECTOR_TECH = "tech"       # 科技板块
    SECTOR_FINANCE = "finance" # 金融板块
    SECTOR_CONSUME = "consume" # 消费板块
    SECTOR_MEDICAL = "medical" # 医药板块
    SECTOR_ENERGY = "energy"   # 新能源板块
    TOP_LIQUID = "top_liquid"  # 流动性最好的股票
    ALL_A = "all_a"           # 全A股

@dataclass
class StockUniverse:
    """股票池定义"""
    name: str                    # 股票池名称
    display_name: str           # 显示名称
    description: str            # 描述
    universe_type: UniverseType # 股票池类型
    stock_codes: List[str]      # 股票代码列表
    created_time: datetime      # 创建时间
    updated_time: datetime      # 更新时间
    is_active: bool = True      # 是否激活
    auto_update: bool = False   # 是否自动更新
    update_frequency: str = "weekly"  # 更新频率
    filters: Dict[str, Any] = None    # 筛选条件

class StockUniverseManager:
    """股票池管理器"""
    
    def __init__(self):
        self.universes: Dict[str, StockUniverse] = {}
        self.config_file = "data/stock_universes.json"
        self._load_universes()
        self._init_default_universes()
    
    def _load_universes(self):
        """加载股票池配置"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    
                for universe_data in data.get('universes', []):
                    universe = StockUniverse(
                        name=universe_data['name'],
                        display_name=universe_data['display_name'],
                        description=universe_data['description'],
                        universe_type=UniverseType(universe_data['universe_type']),
                        stock_codes=universe_data['stock_codes'],
                        created_time=datetime.fromisoformat(universe_data['created_time']),
                        updated_time=datetime.fromisoformat(universe_data['updated_time']),
                        is_active=universe_data.get('is_active', True),
                        auto_update=universe_data.get('auto_update', False),
                        update_frequency=universe_data.get('update_frequency', 'weekly'),
                        filters=universe_data.get('filters')
                    )
                    self.universes[universe.name] = universe
                    
                logger.info(f"✅ 加载了{len(self.universes)}个股票池")
            else:
                logger.info("📋 股票池配置文件不存在，将创建默认配置")
                
        except Exception as e:
            logger.error(f"❌ 加载股票池配置失败: {e}")
    
    def _save_universes(self):
        """保存股票池配置"""
        try:
            os.makedirs(os.path.dirname(self.config_file), exist_ok=True)
            
            data = {
                'universes': [
                    {
                        'name': universe.name,
                        'display_name': universe.display_name,
                        'description': universe.description,
                        'universe_type': universe.universe_type.value,
                        'stock_codes': universe.stock_codes,
                        'created_time': universe.created_time.isoformat(),
                        'updated_time': universe.updated_time.isoformat(),
                        'is_active': universe.is_active,
                        'auto_update': universe.auto_update,
                        'update_frequency': universe.update_frequency,
                        'filters': universe.filters
                    }
                    for universe in self.universes.values()
                ]
            }
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
                
            logger.info(f"✅ 保存了{len(self.universes)}个股票池配置")
            
        except Exception as e:
            logger.error(f"❌ 保存股票池配置失败: {e}")
    
    def _init_default_universes(self):
        """初始化默认股票池"""
        if not self.universes:
            logger.info("📋 创建默认股票池...")
            
            # 创建默认股票池
            default_universes = [
                {
                    'name': 'default_small',
                    'display_name': '默认小股票池',
                    'description': '适合新手的小型股票池，包含10只优质蓝筹股',
                    'universe_type': UniverseType.CUSTOM,
                    'stock_codes': [
                        '000001.SZ', '000002.SZ', '600000.SH', '600036.SH', '600519.SH',
                        '000858.SZ', '002415.SZ', '600276.SH', '000725.SZ', '000166.SZ'
                    ]
                },
                {
                    'name': 'blue_chips',
                    'display_name': '蓝筹股票池',
                    'description': '大盘蓝筹股，适合稳健投资',
                    'universe_type': UniverseType.CUSTOM,
                    'stock_codes': [
                        '000001.SZ', '000002.SZ', '600000.SH', '600036.SH', '601166.SH',
                        '600519.SH', '000858.SZ', '600276.SH', '000725.SZ', '600887.SH',
                        '601318.SH', '000568.SZ', '002304.SZ', '600196.SH', '000895.SZ'
                    ]
                },
                {
                    'name': 'tech_stocks',
                    'display_name': '科技股票池',
                    'description': '科技板块股票，适合成长投资',
                    'universe_type': UniverseType.SECTOR_TECH,
                    'stock_codes': [
                        '000858.SZ', '002415.SZ', '300059.SZ', '002821.SZ', '300015.SZ',
                        '000063.SZ', '002230.SZ', '300274.SZ', '688599.SH', '688981.SH'
                    ]
                }
            ]
            
            now = datetime.now()
            for universe_data in default_universes:
                universe = StockUniverse(
                    name=universe_data['name'],
                    display_name=universe_data['display_name'],
                    description=universe_data['description'],
                    universe_type=universe_data['universe_type'],
                    stock_codes=universe_data['stock_codes'],
                    created_time=now,
                    updated_time=now
                )
                self.universes[universe.name] = universe
            
            self._save_universes()
            logger.info(f"✅ 创建了{len(default_universes)}个默认股票池")
    
    def get_universe(self, name: str) -> Optional[StockUniverse]:
        """获取股票池"""
        return self.universes.get(name)
    
    def get_universe_stocks(self, name: str) -> List[str]:
        """获取股票池的股票代码列表"""
        universe = self.get_universe(name)
        return universe.stock_codes if universe else []
    
    def list_universes(self, active_only: bool = True) -> List[Dict[str, Any]]:
        """列出所有股票池"""
        universes = []
        for universe in self.universes.values():
            if active_only and not universe.is_active:
                continue
                
            universes.append({
                'name': universe.name,
                'display_name': universe.display_name,
                'description': universe.description,
                'universe_type': universe.universe_type.value,
                'stock_count': len(universe.stock_codes),
                'created_time': universe.created_time.isoformat(),
                'updated_time': universe.updated_time.isoformat(),
                'is_active': universe.is_active,
                'auto_update': universe.auto_update
            })
        
        return sorted(universes, key=lambda x: x['display_name'])
    
    def create_universe(self, name: str, display_name: str, description: str,
                       universe_type: UniverseType, stock_codes: List[str],
                       **kwargs) -> bool:
        """创建新股票池"""
        try:
            if name in self.universes:
                logger.error(f"❌ 股票池 {name} 已存在")
                return False
            
            now = datetime.now()
            universe = StockUniverse(
                name=name,
                display_name=display_name,
                description=description,
                universe_type=universe_type,
                stock_codes=stock_codes,
                created_time=now,
                updated_time=now,
                **kwargs
            )
            
            self.universes[name] = universe
            self._save_universes()
            
            logger.info(f"✅ 创建股票池: {display_name} ({len(stock_codes)}只股票)")
            return True
            
        except Exception as e:
            logger.error(f"❌ 创建股票池失败: {e}")
            return False
    
    def update_universe_stocks(self, name: str, stock_codes: List[str]) -> bool:
        """更新股票池的股票列表"""
        try:
            universe = self.get_universe(name)
            if not universe:
                logger.error(f"❌ 股票池 {name} 不存在")
                return False
            
            universe.stock_codes = stock_codes
            universe.updated_time = datetime.now()
            
            self._save_universes()
            
            logger.info(f"✅ 更新股票池 {universe.display_name}: {len(stock_codes)}只股票")
            return True
            
        except Exception as e:
            logger.error(f"❌ 更新股票池失败: {e}")
            return False
    
    def get_recommended_universe(self, strategy_type: str = "default") -> str:
        """根据策略类型推荐股票池"""
        recommendations = {
            "bollinger_bands": "blue_chips",
            "ma_cross": "default_small", 
            "rsi": "tech_stocks",
            "default": "default_small"
        }
        
        return recommendations.get(strategy_type, "default_small")

# 全局实例
stock_universe_manager = StockUniverseManager()
