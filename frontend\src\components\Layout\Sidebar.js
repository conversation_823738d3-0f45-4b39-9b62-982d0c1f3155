import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { LayoutDashboard, BarChart3, Settings, Database, LineChart, Shield, Target } from 'lucide-react';

const menuItems = [
  {
    key: '/',
    icon: <LayoutDashboard className="w-5 h-5" />,
    label: '仪表板',
    path: '/'
  },
  {
    key: '/backtest',
    icon: <BarChart3 className="w-5 h-5" />,
    label: '策略回测',
    path: '/backtest'
  },
  {
    key: '/backtest/history',
    icon: <BarChart3 className="w-5 h-5" />,
    label: '回测历史',
    path: '/backtest/history'
  },
  {
    key: '/multi-strategy',
    icon: <LineChart className="w-5 h-5" />,
    label: '多策略交易',
    path: '/multi-strategy'
  },

  {
    key: '/position-monitor',
    icon: <Shield className="w-5 h-5" />,
    label: '持仓监控',
    path: '/position-monitor'
  },
  {
    key: '/stock-selection',
    icon: <Target className="w-5 h-5" />,
    label: '智能选股',
    path: '/stock-selection'
  },
  {
    key: '/stock-pools',
    icon: <Database className="w-5 h-5" />,
    label: '股票池管理',
    path: '/stock-pools'
  },
  {
    key: '/data-management',
    icon: <Database className="w-5 h-5" />,
    label: '数据管理',
    path: '/data-management'
  },
  {
    key: '/system-config',
    icon: <Settings className="w-5 h-5" />,
    label: '系统配置',
    path: '/system-config'
  },
];

const Sidebar = ({ collapsed, onToggle }) => {
  const location = useLocation();

  return (
    <div className={`bg-gray-900 text-white transition-all duration-300 ${collapsed ? 'w-16' : 'w-64'} min-h-screen`}>
      {/* Logo */}
      <div className="p-4 border-b border-gray-700">
        <div className="flex items-center justify-center">
          {collapsed ? (
            <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-sm">CS</span>
            </div>
          ) : (
            <div className="text-xl font-bold text-blue-400">CN-Stock</div>
          )}
        </div>
      </div>

      {/* Toggle Button */}
      <div className="p-2 border-b border-gray-700">
        <button
          onClick={onToggle}
          className="w-full p-2 text-gray-400 hover:text-white hover:bg-gray-800 rounded-lg transition-colors"
        >
          {collapsed ? '→' : '←'}
        </button>
      </div>

      {/* Menu Items */}
      <nav className="p-2">
        {menuItems.map((item) => {
          const isActive = location.pathname === item.path;
          return (
            <Link
              key={item.key}
              to={item.path}
              className={`flex items-center p-3 mb-1 rounded-lg transition-colors ${
                isActive
                  ? 'bg-blue-600 text-white'
                  : 'text-gray-300 hover:bg-gray-800 hover:text-white'
              }`}
            >
              <span className="flex-shrink-0">{item.icon}</span>
              {!collapsed && (
                <span className="ml-3 text-sm font-medium">{item.label}</span>
              )}
            </Link>
          );
        })}
      </nav>
    </div>
  );
};

export default Sidebar;
