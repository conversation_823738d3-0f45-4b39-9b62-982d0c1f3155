#!/usr/bin/env python3
"""
测试API响应格式
"""

import sys
import os
import requests
import json

# 添加项目路径
sys.path.append(os.path.dirname(__file__))

def test_api_response_format():
    """测试API响应格式"""
    try:
        print("🔍 测试API响应格式...")
        
        # 测试数据
        test_data = {
            "strategy_name": "bollinger_bands",
            "initial_capital": 100000,
            "commission": 0.001,
            "stock_codes": ["000001.SZ", "000002.SZ"],
            "paper_trading": True,
            "strategy_params": {
                "period": 20,
                "std_dev": 2.0,
                "max_positions": 5,
                "position_size": 0.2
            }
        }
        
        print("📤 发送请求...")
        response = requests.post(
            'http://localhost:8000/api/live/start',
            json=test_data,
            headers={'Content-Type': 'application/json'}
        )
        
        print(f"📥 响应状态码: {response.status_code}")
        print(f"📥 响应头: {response.headers.get('content-type')}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"📥 响应数据类型: {type(data)}")
            print(f"📥 响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
            
            # 检查响应结构
            print("\n🔍 响应结构分析:")
            print(f"  - 是否有success字段: {'success' in data}")
            print(f"  - success值: {data.get('success')}")
            print(f"  - 是否有task_id字段: {'task_id' in data}")
            print(f"  - task_id值: {data.get('task_id')}")
            print(f"  - 是否有data字段: {'data' in data}")
            print(f"  - 是否有message字段: {'message' in data}")
            print(f"  - message值: {data.get('message')}")
            
            # 模拟前端处理
            print("\n🎯 前端处理模拟:")
            
            # 检查success
            if data.get('success') == True:
                print("  ✅ apiUtils.isSuccess(response) = True")
                
                # 尝试获取task_id
                task_id_from_data = data.get('data', {}).get('task_id') if isinstance(data.get('data'), dict) else None
                task_id_direct = data.get('task_id')
                
                print(f"  - response.data.task_id = {task_id_from_data}")
                print(f"  - response.task_id = {task_id_direct}")
                
                if task_id_direct:
                    print(f"  ✅ 应该使用: response.task_id = {task_id_direct}")
                elif task_id_from_data:
                    print(f"  ✅ 应该使用: response.data.task_id = {task_id_from_data}")
                else:
                    print("  ❌ 没有找到task_id")
            else:
                print("  ❌ apiUtils.isSuccess(response) = False")
                
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            try:
                error_data = response.json()
                print(f"错误详情: {json.dumps(error_data, indent=2, ensure_ascii=False)}")
            except:
                print(f"错误文本: {response.text}")
                
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def test_other_apis():
    """测试其他API的响应格式"""
    try:
        print("\n🔍 测试其他API响应格式...")
        
        # 测试策略列表API
        print("\n📋 策略列表API:")
        response = requests.get('http://localhost:8000/api/backtest/strategies')
        if response.status_code == 200:
            data = response.json()
            print(f"  - 响应结构: {list(data.keys())}")
            print(f"  - success: {data.get('success')}")
            print(f"  - data类型: {type(data.get('data'))}")
            if isinstance(data.get('data'), list):
                print(f"  - data长度: {len(data.get('data'))}")
        
        # 测试获取运行中策略API
        print("\n🏃 运行中策略API:")
        try:
            response = requests.get('http://localhost:8000/api/live/results')
            if response.status_code == 200:
                data = response.json()
                print(f"  - 响应结构: {list(data.keys())}")
                print(f"  - success: {data.get('success')}")
                print(f"  - data类型: {type(data.get('data'))}")
            else:
                print(f"  - HTTP状态码: {response.status_code}")
        except Exception as e:
            print(f"  - 请求失败: {e}")
            
    except Exception as e:
        print(f"❌ 其他API测试失败: {e}")

if __name__ == "__main__":
    test_api_response_format()
    test_other_apis()
