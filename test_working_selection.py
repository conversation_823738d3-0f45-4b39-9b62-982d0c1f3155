#!/usr/bin/env python3
"""
测试能够工作的选股条件
"""

import requests
import json

def test_working_conditions():
    """测试能够选中股票的条件"""
    print("🧪 测试能够工作的选股条件...")
    
    # 测试1: 非常宽松的成交量条件
    test_cases = [
        {
            "name": "超宽松成交量条件",
            "criteria": {
                "volume_min": 0.1,  # 成交量比例大于0.1
                "condition_logic": "flexible"
            }
        },
        {
            "name": "无特定条件（基于评分）",
            "criteria": {
                "condition_logic": "flexible"
            }
        },
        {
            "name": "RSI中性区间",
            "criteria": {
                "rsi_min": 30,
                "rsi_max": 70,
                "condition_logic": "flexible"
            }
        },
        {
            "name": "任意条件模式",
            "criteria": {
                "volume_min": 0.1,
                "rsi_min": 20,
                "condition_logic": "any"  # 满足任意条件即可
            }
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📋 测试 {i}: {test_case['name']}")
        
        payload = {
            "criteria": test_case["criteria"],
            "custom_name": f"test_{i}",
            "max_results": 10
        }
        
        try:
            response = requests.post(
                'http://localhost:8000/api/stock-selection/select',
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    results = data.get('data', {})
                    selected_count = len(results.get('selected_stocks', []))
                    
                    if selected_count > 0:
                        print(f"✅ 成功选中 {selected_count} 只股票!")
                        
                        # 显示前3只股票
                        stocks = results.get('selected_stocks', [])
                        for j, stock in enumerate(stocks[:3]):
                            print(f"   {j+1}. {stock.get('stock_code')} - {stock.get('stock_name')} (评分: {stock.get('score', 0):.2f})")
                        
                        return True  # 找到了能工作的条件
                    else:
                        print(f"❌ 没有选中股票")
                else:
                    print(f"❌ 选股失败: {data.get('message', '未知错误')}")
            else:
                print(f"❌ API请求失败: {response.status_code}")
                
        except requests.exceptions.Timeout:
            print("⏰ 请求超时")
        except Exception as e:
            print(f"❌ 请求异常: {e}")
    
    return False

def main():
    """主函数"""
    print("🎯 测试能够工作的选股条件")
    print("=" * 50)
    
    success = test_working_conditions()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 找到了能够工作的选股条件!")
        print("\n💡 建议:")
        print("   1. 在前端界面中使用更宽松的默认条件")
        print("   2. 成交量条件设置为 0.1 而不是 1.0")
        print("   3. 使用'任意条件'模式增加选中概率")
    else:
        print("⚠️ 所有测试条件都没有选中股票")
        print("   可能需要进一步调试选股逻辑")

if __name__ == "__main__":
    main()
