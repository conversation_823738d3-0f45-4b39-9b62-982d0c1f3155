#!/usr/bin/env python3
"""
ETF选股API接口
"""

from fastapi import APIRouter, HTTPException, BackgroundTasks
from pydantic import BaseModel
from typing import List, Dict, Any, Optional
from datetime import datetime
import json
import uuid
import asyncio

from backend.stock_selection.etf_selector import ETFSelector, ETFCriteria
from backend.core.logger import get_logger

logger = get_logger(__name__)

router = APIRouter(prefix="/api/etf-selection", tags=["ETF选择"])

# 创建ETF选股器实例
_etf_selector = None
_etf_selection_tasks = {}

def get_etf_selector():
    """获取ETF选股器实例"""
    global _etf_selector
    if _etf_selector is None:
        _etf_selector = ETFSelector()
    return _etf_selector

class ETFCriteriaRequest(BaseModel):
    """ETF选股条件请求"""
    # 基础筛选条件
    min_market_cap: Optional[float] = None
    max_market_cap: Optional[float] = None
    
    # 成交量条件
    min_avg_volume: Optional[float] = None
    min_volume_ratio: Optional[float] = None
    
    # 价格条件
    min_price: Optional[float] = None
    max_price: Optional[float] = None
    
    # 涨跌幅条件
    min_return_1d: Optional[float] = None
    max_return_1d: Optional[float] = None
    min_return_5d: Optional[float] = None
    max_return_5d: Optional[float] = None
    min_return_20d: Optional[float] = None
    max_return_20d: Optional[float] = None
    
    # 波动率条件
    min_volatility: Optional[float] = None
    max_volatility: Optional[float] = None
    
    # ETF类型筛选
    etf_types: Optional[List[str]] = None
    exclude_types: Optional[List[str]] = None
    
    # 跟踪指数筛选
    track_indexes: Optional[List[str]] = None
    exclude_indexes: Optional[List[str]] = None
    
    # 时间范围
    lookback_days: int = 30
    min_trading_days: int = 20
    
    # 其他条件
    exclude_new_etf: bool = True
    only_main_board: bool = False
    
    # 条件组合逻辑
    condition_logic: str = "flexible"

class ETFSelectionRequest(BaseModel):
    """ETF选股请求"""
    criteria: ETFCriteriaRequest
    custom_name: str = "ETF选股"
    max_results: int = 100

class ETFTaskStatus:
    """ETF选股任务状态"""
    def __init__(self, task_id: str, custom_name: str):
        self.task_id = task_id
        self.custom_name = custom_name
        self.status = "pending"  # pending, running, completed, failed
        self.progress = 0
        self.message = "任务已创建"
        self.result = []
        self.error = None
        self.start_time = datetime.now()
        self.end_time = None

async def run_etf_selection_async(task_id: str, criteria: ETFCriteria, custom_name: str):
    """异步执行ETF选股"""
    task_status = _etf_selection_tasks.get(task_id)
    if not task_status:
        return
    
    try:
        task_status.status = "running"
        task_status.message = "正在执行ETF选股..."
        
        # 获取ETF选股器
        selector = get_etf_selector()
        
        # 执行选股
        def progress_callback(processed, total, current_etf, selected):
            task_status.progress = int((processed / total) * 100)
            task_status.message = f"正在处理 {current_etf}... ({processed}/{total})"
        
        # 由于ETF选股器暂时不支持进度回调，我们直接执行
        selected_etfs = selector.select_etfs(criteria, custom_name)
        
        # 转换结果格式
        result = []
        for etf in selected_etfs:
            result.append({
                'stock_code': etf.stock_code,
                'stock_name': etf.stock_name,
                'score': etf.score,
                'indicators': etf.indicators,
                'selection_date': etf.selection_date
            })
        
        task_status.status = "completed"
        task_status.progress = 100
        task_status.message = f"ETF选股完成，共选中 {len(result)} 只ETF"
        task_status.result = result
        task_status.end_time = datetime.now()
        
        logger.info(f"ETF选股任务完成: {task_id}, 选中 {len(result)} 只ETF")
        
    except Exception as e:
        task_status.status = "failed"
        task_status.error = str(e)
        task_status.message = f"ETF选股失败: {str(e)}"
        task_status.end_time = datetime.now()
        logger.error(f"ETF选股任务失败: {task_id}, 错误: {e}")

@router.post("/select")
async def start_etf_selection(request: ETFSelectionRequest, background_tasks: BackgroundTasks):
    """启动ETF选股任务"""
    try:
        # 转换请求为ETF选股条件
        criteria = ETFCriteria(
            min_market_cap=request.criteria.min_market_cap,
            max_market_cap=request.criteria.max_market_cap,
            min_avg_volume=request.criteria.min_avg_volume,
            min_volume_ratio=request.criteria.min_volume_ratio,
            min_price=request.criteria.min_price,
            max_price=request.criteria.max_price,
            min_return_1d=request.criteria.min_return_1d,
            max_return_1d=request.criteria.max_return_1d,
            min_return_5d=request.criteria.min_return_5d,
            max_return_5d=request.criteria.max_return_5d,
            min_return_20d=request.criteria.min_return_20d,
            max_return_20d=request.criteria.max_return_20d,
            min_volatility=request.criteria.min_volatility,
            max_volatility=request.criteria.max_volatility,
            etf_types=request.criteria.etf_types,
            exclude_types=request.criteria.exclude_types,
            track_indexes=request.criteria.track_indexes,
            exclude_indexes=request.criteria.exclude_indexes,
            lookback_days=request.criteria.lookback_days,
            min_trading_days=request.criteria.min_trading_days,
            exclude_new_etf=request.criteria.exclude_new_etf,
            only_main_board=request.criteria.only_main_board,
            condition_logic=request.criteria.condition_logic
        )
        
        # 生成任务ID
        task_id = str(uuid.uuid4())
        
        # 创建任务状态
        task_status = ETFTaskStatus(task_id, request.custom_name)
        _etf_selection_tasks[task_id] = task_status
        
        # 启动后台任务
        background_tasks.add_task(run_etf_selection_async, task_id, criteria, request.custom_name)
        
        return {
            'success': True,
            'data': {
                'task_id': task_id,
                'message': 'ETF选股任务已启动，请使用task_id查询进度',
                'custom_name': request.custom_name
            }
        }
        
    except Exception as e:
        logger.error(f"启动ETF选股任务失败: {e}")
        raise HTTPException(status_code=500, detail=f"启动ETF选股任务失败: {str(e)}")

@router.get("/status/{task_id}")
async def get_etf_selection_status(task_id: str):
    """获取ETF选股任务状态"""
    try:
        task_status = _etf_selection_tasks.get(task_id)
        if not task_status:
            raise HTTPException(status_code=404, detail="任务不存在")
        
        return {
            'success': True,
            'data': {
                'task_id': task_status.task_id,
                'custom_name': task_status.custom_name,
                'status': task_status.status,
                'progress': task_status.progress,
                'message': task_status.message,
                'result': task_status.result if task_status.status == 'completed' else [],
                'error': task_status.error,
                'start_time': task_status.start_time.isoformat(),
                'end_time': task_status.end_time.isoformat() if task_status.end_time else None
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取ETF选股任务状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取任务状态失败: {str(e)}")

@router.get("/presets")
async def get_etf_selection_presets():
    """获取ETF选股预设条件"""
    try:
        presets = [
            {
                "name": "all_etfs",
                "display_name": "所有ETF",
                "description": "选择所有可交易的ETF基金",
                "criteria": {
                    "min_volume_ratio": 0.1,
                    "condition_logic": "flexible"
                }
            },
            {
                "name": "active_etfs",
                "display_name": "活跃ETF",
                "description": "选择成交活跃的ETF基金",
                "criteria": {
                    "min_volume_ratio": 1.0,
                    "min_avg_volume": 1000000,
                    "condition_logic": "flexible"
                }
            },
            {
                "name": "stock_etfs",
                "display_name": "股票型ETF",
                "description": "选择股票型ETF基金",
                "criteria": {
                    "etf_types": ["stock"],
                    "min_volume_ratio": 0.5,
                    "condition_logic": "flexible"
                }
            },
            {
                "name": "bond_etfs",
                "display_name": "债券型ETF",
                "description": "选择债券型ETF基金",
                "criteria": {
                    "etf_types": ["bond"],
                    "min_volume_ratio": 0.3,
                    "condition_logic": "flexible"
                }
            },
            {
                "name": "index_etfs",
                "display_name": "宽基指数ETF",
                "description": "选择跟踪主要指数的ETF",
                "criteria": {
                    "track_indexes": ["300", "500", "50", "创业板", "科创"],
                    "min_volume_ratio": 0.8,
                    "condition_logic": "flexible"
                }
            },
            {
                "name": "low_volatility_etfs",
                "display_name": "低波动ETF",
                "description": "选择波动率较低的稳健型ETF",
                "criteria": {
                    "max_volatility": 0.2,
                    "min_volume_ratio": 0.5,
                    "condition_logic": "flexible"
                }
            }
        ]
        
        return {
            'success': True,
            'data': presets,
            'message': f'获取到 {len(presets)} 个ETF选股预设'
        }
        
    except Exception as e:
        logger.error(f"获取ETF选股预设失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取预设失败: {str(e)}")

@router.get("/types")
async def get_etf_types():
    """获取ETF类型列表"""
    try:
        etf_types = [
            {"value": "stock", "label": "股票型ETF", "description": "跟踪股票指数的ETF"},
            {"value": "bond", "label": "债券型ETF", "description": "投资债券市场的ETF"},
            {"value": "commodity", "label": "商品型ETF", "description": "投资商品市场的ETF"},
            {"value": "money", "label": "货币型ETF", "description": "货币市场基金ETF"},
            {"value": "cross_border", "label": "跨境ETF", "description": "投资海外市场的ETF"}
        ]
        
        return {
            'success': True,
            'data': etf_types,
            'message': f'获取到 {len(etf_types)} 种ETF类型'
        }
        
    except Exception as e:
        logger.error(f"获取ETF类型失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取ETF类型失败: {str(e)}")

@router.delete("/tasks/{task_id}")
async def delete_etf_selection_task(task_id: str):
    """删除ETF选股任务"""
    try:
        if task_id in _etf_selection_tasks:
            del _etf_selection_tasks[task_id]
            return {
                'success': True,
                'message': f'任务 {task_id} 已删除'
            }
        else:
            raise HTTPException(status_code=404, detail="任务不存在")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除ETF选股任务失败: {e}")
        raise HTTPException(status_code=500, detail=f"删除任务失败: {str(e)}")
