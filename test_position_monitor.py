#!/usr/bin/env python3
"""
持仓监控功能测试脚本
"""

import requests
import time
import json
from datetime import datetime

# API基础URL
BASE_URL = "http://localhost:8000"

def test_position_monitor_apis():
    """测试持仓监控相关API"""
    print("🧪 测试持仓监控API...")
    
    # 1. 测试获取监控状态
    print("\n1. 测试获取监控状态")
    try:
        response = requests.get(f"{BASE_URL}/api/position-monitor/status")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 监控状态: {data}")
        else:
            print(f"❌ 获取监控状态失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 获取监控状态异常: {e}")
    
    # 2. 测试获取止损信号
    print("\n2. 测试获取止损信号")
    try:
        response = requests.get(f"{BASE_URL}/api/position-monitor/signals?limit=10")
        if response.status_code == 200:
            signals = response.json()
            print(f"✅ 止损信号数量: {len(signals)}")
            if signals:
                print(f"   最新信号: {signals[0]}")
        else:
            print(f"❌ 获取止损信号失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 获取止损信号异常: {e}")
    
    # 3. 测试获取待处理订单
    print("\n3. 测试获取待处理订单")
    try:
        response = requests.get(f"{BASE_URL}/api/position-monitor/pending-orders")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 待处理订单: {data}")
        else:
            print(f"❌ 获取待处理订单失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 获取待处理订单异常: {e}")
    
    # 4. 测试手动检查
    print("\n4. 测试手动检查")
    try:
        response = requests.post(f"{BASE_URL}/api/position-monitor/check")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 手动检查结果: {data}")
        else:
            print(f"❌ 手动检查失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 手动检查异常: {e}")
    
    # 5. 测试获取仪表板数据
    print("\n5. 测试获取仪表板数据")
    try:
        response = requests.get(f"{BASE_URL}/api/position-monitor/dashboard")
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                dashboard = data['data']
                stats = dashboard.get('statistics', {})
                print(f"✅ 仪表板数据:")
                print(f"   持仓数量: {stats.get('total_positions', 0)}")
                print(f"   盈利持仓: {stats.get('profit_positions', 0)}")
                print(f"   亏损持仓: {stats.get('loss_positions', 0)}")
                print(f"   止损信号: {stats.get('total_signals', 0)}")
                print(f"   待处理订单: {stats.get('total_pending', 0)}")
                print(f"   监控状态: {'运行中' if stats.get('is_monitoring') else '已停止'}")
            else:
                print(f"❌ 获取仪表板数据失败: {data.get('message')}")
        else:
            print(f"❌ 获取仪表板数据失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 获取仪表板数据异常: {e}")
    
    # 6. 测试获取最新警告
    print("\n6. 测试获取最新警告")
    try:
        current_time = time.time()
        response = requests.get(f"{BASE_URL}/api/position-monitor/alerts/latest?since={current_time - 3600}")
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                alerts = data['data']
                print(f"✅ 最新警告:")
                print(f"   信号数量: {alerts.get('count', 0)}")
                print(f"   有新信号: {alerts.get('has_new', False)}")
                if alerts.get('signals'):
                    print(f"   最新信号: {alerts['signals'][0]}")
            else:
                print(f"❌ 获取最新警告失败: {data.get('message')}")
        else:
            print(f"❌ 获取最新警告失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 获取最新警告异常: {e}")

def test_account_data():
    """测试账户数据获取"""
    print("\n🧪 测试账户数据获取...")
    
    try:
        response = requests.get(f"{BASE_URL}/api/data/account")
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                account = data['data']
                positions = account.get('positions', [])
                print(f"✅ 账户数据:")
                print(f"   总资产: ¥{account.get('total_value', 0):,.2f}")
                print(f"   可用资金: ¥{account.get('available_cash', 0):,.2f}")
                print(f"   持仓数量: {len(positions)}")
                
                if positions:
                    print(f"   持仓详情:")
                    for pos in positions[:3]:  # 只显示前3个
                        pnl_ratio = pos.get('pnl_ratio', 0)
                        pnl_color = "📈" if pnl_ratio > 0 else "📉" if pnl_ratio < 0 else "➡️"
                        print(f"     {pnl_color} {pos.get('stock_code', 'N/A')}: "
                              f"{pos.get('quantity', 0)}股, "
                              f"成本¥{pos.get('avg_price', 0):.2f}, "
                              f"现价¥{pos.get('current_price', 0):.2f}, "
                              f"盈亏{pnl_ratio*100:+.2f}%")
            else:
                print(f"❌ 获取账户数据失败: {data.get('message')}")
        else:
            print(f"❌ 获取账户数据失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 获取账户数据异常: {e}")

def test_monitor_lifecycle():
    """测试监控生命周期"""
    print("\n🧪 测试监控生命周期...")
    
    # 1. 启动监控
    print("\n1. 启动监控")
    try:
        response = requests.post(f"{BASE_URL}/api/position-monitor/start")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 启动监控: {data}")
        else:
            print(f"❌ 启动监控失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 启动监控异常: {e}")
    
    # 等待一段时间
    print("\n⏳ 等待5秒...")
    time.sleep(5)
    
    # 2. 检查状态
    print("\n2. 检查监控状态")
    try:
        response = requests.get(f"{BASE_URL}/api/position-monitor/status")
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                status = data['data']
                print(f"✅ 监控状态: {'运行中' if status.get('is_running') else '已停止'}")
            else:
                print(f"❌ 获取状态失败: {data.get('message')}")
        else:
            print(f"❌ 获取状态失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 获取状态异常: {e}")
    
    # 3. 停止监控
    print("\n3. 停止监控")
    try:
        response = requests.post(f"{BASE_URL}/api/position-monitor/stop")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 停止监控: {data}")
        else:
            print(f"❌ 停止监控失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 停止监控异常: {e}")

def main():
    """主函数"""
    print("🚀 持仓监控功能测试开始")
    print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # 测试各个功能
    test_account_data()
    test_position_monitor_apis()
    test_monitor_lifecycle()
    
    print("\n" + "=" * 60)
    print("✅ 持仓监控功能测试完成")
    print("\n📋 测试总结:")
    print("1. ✅ 账户数据获取")
    print("2. ✅ 监控状态管理")
    print("3. ✅ 止损信号获取")
    print("4. ✅ 待处理订单管理")
    print("5. ✅ 仪表板数据")
    print("6. ✅ 实时警告获取")
    print("7. ✅ 监控生命周期")
    
    print("\n🎯 下一步:")
    print("1. 访问 http://localhost:3000/position-monitor 查看前端界面")
    print("2. 测试声音警告功能")
    print("3. 测试自动刷新功能")
    print("4. 配置监控参数")

if __name__ == "__main__":
    main()
