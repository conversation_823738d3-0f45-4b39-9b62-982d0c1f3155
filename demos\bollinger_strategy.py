# -*- coding: utf-8 -*-
"""
布林线交易策略 - VS Code版本
基于QMT的xtquant库实现

功能：
1. 布林线技术指标计算
2. 复杂趋势判断
3. 自动交易执行
4. 风险控制
"""

import numpy as np
import pandas as pd
import time
import random
from datetime import datetime, timedelta
from xtquant import xttrader, xtdata
from xtquant.xttype import StockAccount
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('strategy.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class BollingerStrategy:
    """布林线交易策略类"""
    
    def __init__(self, config):
        """初始化策略"""
        self.config = config
        self.xt_trader = None
        self.account = None
        self.holdings = {}  # 持仓记录
        self.buy_prices = {}  # 买入价格记录
        self.max_profits = {}  # 最高盈利记录
        
        # 策略参数
        self.bollinger_period = config.get('bollinger_period', 20)
        self.bollinger_std = config.get('bollinger_std', 2.0)
        self.trend_period = config.get('trend_period', 20)
        self.max_positions = config.get('max_positions', 10)
        self.position_weight = config.get('position_weight', 0.1)
        self.stop_loss = config.get('stop_loss', 0.03)
        self.profit_retracement = config.get('profit_retracement', 0.20)
        self.buy_zone_ratio = config.get('buy_zone_ratio', 0.3)
        self.min_trend_slope = config.get('min_trend_slope', 0.0005)
        self.relative_strength_threshold = config.get('relative_strength_threshold', 1.1)
        
        # 股票池
        self.stock_pool = config.get('stock_pool', [
            '000001.SZ', '000002.SZ', '600000.SH', '600036.SH', '600519.SH',
            '000858.SZ', '002415.SZ', '600276.SH', '000725.SZ', '000166.SZ'
        ])
        
        logger.info("策略初始化完成")
        logger.info(f"股票池: {len(self.stock_pool)}只股票")
        logger.info(f"布林线周期: {self.bollinger_period}天")
        logger.info(f"最大持仓: {self.max_positions}只")
    
    def connect_trader(self, min_path, account_id, account_type='live'):
        """连接QMT交易端"""
        try:
            # 检查账户类型
            if account_type == 'backtest':
                logger.info('回测模式：使用虚拟账户，不连接真实交易端')
                self.account = {'account_id': account_id, 'type': 'backtest'}
                return True

            elif account_type == 'paper':
                logger.info('模拟交易模式：使用虚拟账户，不连接真实交易端')
                self.account = {'account_id': account_id, 'type': 'paper'}
                return True

            # 实盘交易模式
            logger.info('实盘交易模式：连接QMT交易端')

            # 使用固定的session_id避免随机性
            session_id = 123456  # 固定session_id用于demo
            self.xt_trader = xttrader.XtQuantTrader(min_path, session_id)
            self.xt_trader.start()

            # 连接交易端
            connect_result = self.xt_trader.connect()
            if connect_result == 0:
                logger.info('QMT连接成功')
            else:
                logger.error('QMT连接失败')
                return False

            # 设置账户
            self.account = StockAccount(account_id)
            res = self.xt_trader.subscribe(self.account)
            if res == 0:
                logger.info(f'实盘账户订阅成功: {account_id}')
                return True
            else:
                logger.error('实盘账户订阅失败')
                return False

        except Exception as e:
            logger.error(f"连接交易端失败: {str(e)}")
            return False
    
    def get_stock_data(self, stock_code, period=100):
        """获取股票历史数据"""
        try:
            # 检查账户类型，如果是回测模式，不在这里获取数据
            if hasattr(self, 'account') and isinstance(self.account, dict):
                if self.account.get('type') == 'backtest':
                    logger.info(f"回测模式：数据由回测引擎提供")
                    return None

            # 获取历史数据
            end_time = datetime.now().strftime('%Y%m%d')
            start_time = (datetime.now() - timedelta(days=period*2)).strftime('%Y%m%d')

            logger.info(f"获取{stock_code}实时数据: {start_time} 到 {end_time}")

            # 使用xtdata获取数据
            data = xtdata.get_market_data(
                stock_list=[stock_code],
                period='1d',
                start_time=start_time,
                end_time=end_time,
                count=-1
            )

            if stock_code in data:
                df = data[stock_code]
                if len(df) >= period:
                    logger.info(f"✅ 获取到{stock_code}实时数据: {len(df)}个交易日")
                    return df['close'].values

            logger.warning(f"获取{stock_code}数据失败或数据不足")
            return None

        except ImportError:
            logger.warning("xtdata模块未找到，无法获取实时数据")
            return None
        except Exception as e:
            logger.error(f"获取{stock_code}数据异常: {str(e)}")
            return None
    
    def calculate_bollinger_bands(self, prices):
        """计算布林线"""
        if len(prices) < self.bollinger_period:
            return None, None, None
        
        # 计算移动平均线
        sma = np.mean(prices[-self.bollinger_period:])
        
        # 计算标准差
        std = np.std(prices[-self.bollinger_period:])
        
        # 计算布林线
        upper_band = sma + (self.bollinger_std * std)
        lower_band = sma - (self.bollinger_std * std)
        
        return sma, upper_band, lower_band
    
    def calculate_trend_slope(self, prices, ma_period, slope_period):
        """计算移动平均线斜率"""
        if len(prices) < ma_period + slope_period:
            return 0
        
        current_ma = np.mean(prices[-ma_period:])
        past_ma = np.mean(prices[-ma_period-slope_period:-slope_period])
        
        slope = (current_ma - past_ma) / past_ma if past_ma > 0 else 0
        return slope
    
    def is_uptrend_advanced(self, stock_code, prices):
        """复杂趋势判断"""
        if len(prices) < max(60, self.trend_period + 10):
            return False
        
        # 计算多周期均线
        ma5 = np.mean(prices[-5:])
        ma10 = np.mean(prices[-10:])
        ma20 = np.mean(prices[-20:])
        ma60 = np.mean(prices[-min(60, len(prices)-5):])
        
        # 放宽的均线排列判断
        ma_bullish = (ma5 > ma20) and (ma20 > ma60) and (ma5 > ma10)
        
        # 价格位置
        current_price = prices[-1]
        price_above_ma20 = current_price > ma20
        
        # 均线斜率
        ma20_slope = self.calculate_trend_slope(prices, 20, 5)
        ma60_slope = self.calculate_trend_slope(prices, min(60, len(prices)-10), 10)
        slope_positive = ma20_slope > self.min_trend_slope and ma60_slope > 0
        
        # 价格动量
        momentum_5 = (prices[-1] - prices[-6]) / prices[-6] if len(prices) > 6 else 0
        momentum_10 = (prices[-1] - prices[-11]) / prices[-11] if len(prices) > 11 else 0
        momentum_positive = momentum_5 > 0 and momentum_10 > 0
        
        # 波动率
        volatility = np.std(prices[-20:]) / np.mean(prices[-20:])
        volatility_reasonable = volatility < 0.05
        
        # 相对强度（简化处理）
        relative_strength_ok = True  # 暂时默认通过
        
        # 综合判断
        core_conditions = ma_bullish and price_above_ma20 and slope_positive
        bonus_score = sum([momentum_positive, volatility_reasonable, relative_strength_ok])
        
        trend_up = core_conditions and bonus_score >= 2
        
        # 输出分析结果
        if trend_up:
            logger.info(f"{stock_code} 趋势分析: ✅ 强势上涨趋势")
            logger.info(f"  均线排列: {'✅' if ma_bullish else '❌'}")
            logger.info(f"  价格位置: {'✅' if price_above_ma20 else '❌'}")
            logger.info(f"  均线斜率: {'✅' if slope_positive else '❌'}")
            logger.info(f"  加分条件: {bonus_score}/3")
        
        return trend_up
    
    def check_buy_conditions(self, stock_code, current_price, middle_line, upper_line):
        """检查买入条件"""
        # 计算买入区间
        buy_threshold = middle_line + (upper_line - middle_line) * self.buy_zone_ratio
        
        # 价格条件
        price_ok = middle_line <= current_price <= buy_threshold
        
        # 持仓限制
        current_positions = len([h for h in self.holdings.values() if h > 0])
        position_ok = current_positions < self.max_positions
        
        # 资金条件（简化处理）
        fund_ok = True  # 实际应该检查账户资金
        
        return price_ok and position_ok and fund_ok
    
    def check_sell_conditions(self, stock_code, current_price, middle_line):
        """检查卖出条件"""
        if stock_code not in self.holdings or self.holdings[stock_code] <= 0:
            return False
        
        buy_price = self.buy_prices.get(stock_code, current_price)
        
        # 止损条件
        loss_ratio = (current_price - buy_price) / buy_price
        stop_loss_triggered = loss_ratio <= -self.stop_loss
        
        # 跌破中线
        below_middle = current_price < middle_line
        
        # 盈利回撤
        max_profit = self.max_profits.get(stock_code, 0)
        current_profit = (current_price - buy_price) / buy_price
        if current_profit > max_profit:
            self.max_profits[stock_code] = current_profit
        
        profit_retracement = (max_profit > 0.05 and 
                            current_profit < max_profit * (1 - self.profit_retracement))
        
        return stop_loss_triggered or below_middle or profit_retracement
    
    def execute_buy(self, stock_code, current_price):
        """执行买入"""
        try:
            # 计算买入数量（简化处理）
            shares = 1000  # 固定买入1000股，实际应该根据资金计算

            # 记录买入信息
            self.holdings[stock_code] = shares
            self.buy_prices[stock_code] = current_price
            self.max_profits[stock_code] = 0

            # 检查账户类型
            account_type = getattr(self.account, 'type', 'live') if isinstance(self.account, dict) else 'live'

            if account_type in ['backtest', 'paper']:
                logger.info(f"🛒 [{account_type.upper()}] 买入: {stock_code} {shares}股@{current_price:.2f}元")
            else:
                logger.info(f"🛒 [实盘] 买入: {stock_code} {shares}股@{current_price:.2f}元")
                # 实际下单（仅在实盘模式下执行）
                # order_result = self.xt_trader.order_stock(...)

            return True

        except Exception as e:
            logger.error(f"买入{stock_code}失败: {str(e)}")
            return False
    
    def execute_sell(self, stock_code, current_price):
        """执行卖出"""
        try:
            shares = self.holdings.get(stock_code, 0)
            if shares <= 0:
                return False

            buy_price = self.buy_prices.get(stock_code, current_price)
            profit = (current_price - buy_price) * shares
            profit_rate = (current_price - buy_price) / buy_price * 100

            # 清除持仓记录
            self.holdings[stock_code] = 0
            self.buy_prices.pop(stock_code, None)
            self.max_profits.pop(stock_code, None)

            # 检查账户类型
            account_type = getattr(self.account, 'type', 'live') if isinstance(self.account, dict) else 'live'

            if account_type in ['backtest', 'paper']:
                logger.info(f"💰 [{account_type.upper()}] 卖出: {stock_code} {shares}股@{current_price:.2f}元")
                logger.info(f"  盈亏: {profit:.2f}元 ({profit_rate:+.2f}%)")
            else:
                logger.info(f"💰 [实盘] 卖出: {stock_code} {shares}股@{current_price:.2f}元")
                logger.info(f"  盈亏: {profit:.2f}元 ({profit_rate:+.2f}%)")
                # 实际下单（仅在实盘模式下执行）
                # order_result = self.xt_trader.order_stock(...)

            return True

        except Exception as e:
            logger.error(f"卖出{stock_code}失败: {str(e)}")
            return False
    
    def run_strategy(self):
        """运行策略主循环"""
        logger.info("开始运行布林线策略...")
        
        while True:
            try:
                logger.info("=" * 50)
                logger.info(f"策略运行时间: {datetime.now()}")
                
                buy_signals = 0
                sell_signals = 0
                
                for stock_code in self.stock_pool:
                    # 获取股票数据
                    prices = self.get_stock_data(stock_code)
                    if prices is None or len(prices) < 30:
                        continue
                    
                    current_price = prices[-1]
                    
                    # 计算布林线
                    middle_line, upper_line, lower_line = self.calculate_bollinger_bands(prices)
                    if middle_line is None:
                        continue
                    
                    # 当前持仓
                    current_holding = self.holdings.get(stock_code, 0)
                    
                    if current_holding > 0:
                        # 检查卖出条件
                        if self.check_sell_conditions(stock_code, current_price, middle_line):
                            if self.execute_sell(stock_code, current_price):
                                sell_signals += 1
                    else:
                        # 检查买入条件
                        if self.is_uptrend_advanced(stock_code, prices):
                            if self.check_buy_conditions(stock_code, current_price, middle_line, upper_line):
                                if self.execute_buy(stock_code, current_price):
                                    buy_signals += 1
                
                logger.info(f"本轮信号: 买入{buy_signals}个, 卖出{sell_signals}个")
                logger.info(f"当前持仓: {len([h for h in self.holdings.values() if h > 0])}只")
                
                # 等待下次运行
                time.sleep(300)  # 5分钟运行一次
                
            except KeyboardInterrupt:
                logger.info("策略停止运行")
                break
            except Exception as e:
                logger.error(f"策略运行异常: {str(e)}")
                time.sleep(60)  # 异常后等待1分钟再继续

def main():
    """主函数"""
    # 策略配置
    config = {
        'bollinger_period': 20,
        'bollinger_std': 2.0,
        'trend_period': 20,
        'max_positions': 5,
        'position_weight': 0.15,
        'stop_loss': 0.03,
        'profit_retracement': 0.20,
        'buy_zone_ratio': 0.3,
        'min_trend_slope': 0.0005,
        'relative_strength_threshold': 1.1,
        'stock_pool': [
            '000001.SZ', '000002.SZ', '600000.SH', '600036.SH', '600519.SH',
            '000858.SZ', '002415.SZ', '600276.SH', '000725.SZ', '000166.SZ'
        ]
    }
    
    # 创建策略实例
    strategy = BollingerStrategy(config)
    
    # 连接交易端（需要修改为实际路径和账户）
    min_path = r"D:\国金证券QMT交易端\userdata_mini"
    account_id = "**********"
    
    if strategy.connect_trader(min_path, account_id):
        # 运行策略
        strategy.run_strategy()
    else:
        logger.error("无法连接交易端，策略退出")

if __name__ == "__main__":
    main()
