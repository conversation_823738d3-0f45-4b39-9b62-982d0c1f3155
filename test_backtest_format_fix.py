#!/usr/bin/env python3
"""
测试回测引擎格式化修复
"""

import logging

# 配置详细日志格式
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - [%(filename)s:%(lineno)d:%(funcName)s] - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

logger = logging.getLogger(__name__)

def test_format_scenarios():
    """测试各种格式化场景"""
    logger.info("=== 测试回测结果格式化场景 ===")
    
    # 测试场景1: 正常值
    test_cases = [
        {
            'name': '正常值',
            'total_return': 0.1234,
            'sharpe_ratio': 1.567,
            'annual_return': 0.0987,
            'max_drawdown': 0.0543
        },
        {
            'name': 'None值',
            'total_return': None,
            'sharpe_ratio': None,
            'annual_return': None,
            'max_drawdown': None
        },
        {
            'name': '零值',
            'total_return': 0,
            'sharpe_ratio': 0,
            'annual_return': 0,
            'max_drawdown': 0
        },
        {
            'name': '负值',
            'total_return': -0.1234,
            'sharpe_ratio': -0.567,
            'annual_return': -0.0987,
            'max_drawdown': -0.0543
        }
    ]
    
    for case in test_cases:
        logger.info(f"\n--- 测试 {case['name']} ---")
        
        total_return = case['total_return']
        sharpe_ratio = case['sharpe_ratio']
        annual_return = case['annual_return']
        max_drawdown = case['max_drawdown']
        
        logger.debug(f"原始值 - total_return: {total_return}, sharpe_ratio: {sharpe_ratio}")
        
        # 测试修复前的格式化（会出错）
        try:
            result = f"修复前: 总收益率={total_return:.2%}, 夏普比率={sharpe_ratio:.2f}"
            logger.info(f"✅ 修复前成功: {result}")
        except Exception as e:
            logger.error(f"❌ 修复前失败: {e}")
        
        # 测试修复后的格式化（安全）
        try:
            safe_total_return = total_return if total_return is not None else 0
            safe_sharpe_ratio = sharpe_ratio if sharpe_ratio is not None else 0
            safe_annual_return = annual_return if annual_return is not None else 0
            safe_max_drawdown = max_drawdown if max_drawdown is not None else 0
            
            result = f"修复后: 总收益率={safe_total_return:.2%}, 夏普比率={safe_sharpe_ratio:.2f}, 年化收益率={safe_annual_return:.2%}, 最大回撤={safe_max_drawdown:.2%}"
            logger.info(f"✅ 修复后成功: {result}")
        except Exception as e:
            logger.error(f"❌ 修复后失败: {e}")

def test_annual_return_calculation():
    """测试年化收益率计算"""
    logger.info("\n=== 测试年化收益率计算 ===")
    
    test_cases = [
        {'total_return': 0.1, 'years': 1, 'expected': 0.1},
        {'total_return': 0.1, 'years': 0.5, 'expected': 0.21},  # 约21%
        {'total_return': None, 'years': 1, 'expected': 0},
        {'total_return': 0.1, 'years': 0, 'expected': 0.1},
        {'total_return': -0.5, 'years': 1, 'expected': -0.5},
    ]
    
    for case in test_cases:
        total_return = case['total_return']
        years = case['years']
        
        logger.debug(f"计算年化收益率 - total_return: {total_return}, years: {years}")
        
        # 安全计算年化收益率
        if years > 0 and total_return is not None:
            try:
                annual_return = (1 + total_return) ** (1 / years) - 1
                logger.info(f"✅ 年化收益率计算成功: {annual_return:.4f}")
            except (ValueError, OverflowError, ZeroDivisionError) as e:
                annual_return = total_return
                logger.warning(f"⚠️ 年化收益率计算异常，使用总收益率: {annual_return}, 错误: {e}")
        else:
            annual_return = total_return if total_return is not None else 0
            logger.info(f"✅ 使用默认年化收益率: {annual_return}")

def test_division_safety():
    """测试除法安全性"""
    logger.info("\n=== 测试除法安全性 ===")
    
    test_cases = [
        {'initial_capital': 1000000, 'final_value': 1100000},
        {'initial_capital': 0, 'final_value': 1100000},
        {'initial_capital': None, 'final_value': 1100000},
        {'initial_capital': 1000000, 'final_value': None},
    ]
    
    for case in test_cases:
        initial_capital = case['initial_capital']
        final_value = case['final_value']
        
        logger.debug(f"计算总收益率 - initial_capital: {initial_capital}, final_value: {final_value}")
        
        # 安全计算总收益率
        if initial_capital and initial_capital > 0 and final_value is not None:
            total_return = (final_value - initial_capital) / initial_capital
            logger.info(f"✅ 总收益率计算成功: {total_return:.4f}")
        else:
            total_return = 0
            logger.warning(f"⚠️ 使用默认总收益率: {total_return}")

if __name__ == "__main__":
    logger.info("🔍 开始回测格式化修复测试")
    
    test_format_scenarios()
    test_annual_return_calculation()
    test_division_safety()
    
    logger.info("✅ 回测格式化修复测试完成")
