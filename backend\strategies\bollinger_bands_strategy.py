"""
布林带策略
基于布林带指标的交易策略
"""

import backtrader as bt
import logging
from .base_strategy_new import ConfigurableStrategy, register_strategy

logger = logging.getLogger(__name__)


class BollingerBandsStrategy(ConfigurableStrategy):
    """布林带策略"""
    
    def __init__(self):
        super().__init__()

        # 策略基本信息
        self.strategy_name = "bollinger_bands"
        self.display_name = "布林带策略"
        self.description = "基于布林带指标的均值回归策略，价格触及下轨时买入，触及上轨时卖出"

        # 策略参数
        self.period = self.get_param_value('period', 20)
        self.std_dev = self.get_param_value('std_dev', 2.0)
        self.max_positions = self.get_param_value('max_positions', 5)
        self.position_size = self.get_param_value('position_size', 0.2)

        # 为每个数据源创建布林带指标
        self.bollinger_bands = {}
        for i, data in enumerate(self.datas):
            stock_code = getattr(data, '_name', f'data_{i}')

            # 创建布林带指标
            bb = bt.indicators.BollingerBands(
                data.close,
                period=self.period,
                devfactor=self.std_dev
            )

            self.bollinger_bands[stock_code] = {
                'bb': bb,
                'data': data
            }

            logger.info(f"为 {stock_code} 创建布林带指标")

    
    def generate_signals(self, data):
        """生成交易信号"""
        stock_code = getattr(data, '_name', 'Unknown')
        
        # 获取对应的布林带指标
        if stock_code not in self.bollinger_bands:
            return {'buy': False, 'sell': False}
        
        bb_info = self.bollinger_bands[stock_code]
        bb = bb_info['bb']
        
        # 更严格的数据检查
        min_required_data = max(self.period + 5, 30)  # 至少需要30个数据点
        if len(data) < min_required_data:
            logger.debug(f"{stock_code}: 数据不足，需要{min_required_data}个，当前{len(data)}个")
            return {'buy': False, 'sell': False}

        # 确保布林带指标有足够的数据并且已经计算完成
        try:
            if (not hasattr(bb, 'top') or not hasattr(bb, 'bot') or not hasattr(bb, 'mid') or
                len(bb.top) == 0 or len(bb.bot) == 0 or len(bb.mid) == 0):
                logger.debug(f"{stock_code}: 布林带指标未准备就绪")
                return {'buy': False, 'sell': False}

            # 检查指标是否有有效值
            if (bb.top[0] is None or bb.bot[0] is None or bb.mid[0] is None or
                not isinstance(bb.top[0], (int, float)) or
                not isinstance(bb.bot[0], (int, float)) or
                not isinstance(bb.mid[0], (int, float))):
                logger.debug(f"{stock_code}: 布林带指标值无效")
                return {'buy': False, 'sell': False}

        except (IndexError, TypeError, AttributeError) as e:
            logger.warning(f"{stock_code}: 布林带指标检查错误: {e}")
            return {'buy': False, 'sell': False}

        try:
            # 安全获取价格和指标值
            current_price = data.close[0] if len(data.close) > 0 and data.close[0] is not None else 0
            upper_band = bb.top[0]
            lower_band = bb.bot[0]
            middle_band = bb.mid[0]

            # 验证数据的合理性
            if (current_price <= 0 or upper_band <= 0 or lower_band <= 0 or middle_band <= 0 or
                upper_band <= lower_band or middle_band <= lower_band or middle_band >= upper_band):
                logger.debug(f"{stock_code}: 数据不合理 - 价格:{current_price}, 上轨:{upper_band}, 中轨:{middle_band}, 下轨:{lower_band}")
                return {'buy': False, 'sell': False}

        except (IndexError, TypeError, AttributeError) as e:
            logger.warning(f"{stock_code}: 数据访问错误: {e}")
            return {'buy': False, 'sell': False}

        # 检查数据有效性
        if current_price <= 0 or upper_band <= 0 or lower_band <= 0:
            return {'buy': False, 'sell': False}

        # 生成信号
        signals = {'buy': False, 'sell': False}

        # 买入信号：价格跌破下轨
        if current_price <= lower_band:
            signals['buy'] = True
            signals['reason'] = f'价格({current_price:.2f})跌破下轨({lower_band:.2f})'

        # 卖出信号：价格突破上轨
        elif current_price >= upper_band:
            signals['sell'] = True
            signals['reason'] = f'价格({current_price:.2f})突破上轨({upper_band:.2f})'

        # 记录调试信息
        if signals['buy'] or signals['sell']:
            logger.debug(f"{stock_code}: 价格={current_price:.2f}, 上轨={upper_band:.2f}, "
                        f"中轨={middle_band:.2f}, 下轨={lower_band:.2f}")
        
        return signals
    
    def process_buy_signal(self, data, signals):
        """处理买入信号"""
        stock_code = getattr(data, '_name', 'Unknown')
        
        # 检查是否已持仓
        position = self.getposition(data)
        if position.size > 0:
            logger.debug(f"{stock_code} 已持仓，跳过买入信号")
            return
        
        # 检查持仓数量限制
        if self.positions_count >= self.max_positions:
            logger.debug(f"已达最大持仓数限制: {self.positions_count}/{self.max_positions}")
            return
        
        # 计算买入数量
        cash = self.broker.get_cash()
        value = cash * self.position_size
        size = int(value / data.close[0] / 100) * 100  # 按手买入
        
        if size >= 100:  # 至少买入1手
            # 安全获取价格
            logger.debug(f"Buy signal processing - data.close[0]: {data.close[0]}")
            current_price = data.close[0] if data.close[0] is not None else 0
            logger.debug(f"Processed current_price: {current_price}")

            order = self.buy(data=data, size=size)
            logger.debug(f"Buy order created: {order}")
            logger.info(f"布林带买入: {stock_code} {size}股 @{current_price:.2f} - {signals['reason']}")

            # 记录详细交易信息
            self.record_trade(
                date=data.datetime.date(0).strftime('%Y-%m-%d'),
                time=data.datetime.time(0).strftime('%H:%M:%S'),
                stock_code=stock_code,
                action='buy',
                price=current_price,
                quantity=size,
                reason=f"布林带策略-{signals['reason']}",
                order_ref=order.ref if order else None
            )
        else:
            logger.debug(f"{stock_code} 资金不足，无法买入")
    
    def process_sell_signal(self, data, signals):
        """处理卖出信号"""
        stock_code = getattr(data, '_name', 'Unknown')
        
        # 检查是否持仓
        position = self.getposition(data)
        if position.size <= 0:
            logger.debug(f"{stock_code} 无持仓，跳过卖出信号")
            return
        
        # 安全获取价格和持仓数量
        logger.debug(f"Sell signal processing - data.close[0]: {data.close[0]}, position.size: {position.size}")
        current_price = data.close[0] if data.close[0] is not None else 0
        position_size = position.size if position.size is not None else 0
        logger.debug(f"Processed sell data - current_price: {current_price}, position_size: {position_size}")

        # 卖出全部持仓
        order = self.sell(data=data, size=position_size)
        logger.debug(f"Sell order created: {order}")
        logger.info(f"布林带卖出: {stock_code} {position_size}股 @{current_price:.2f} - {signals['reason']}")

        # 记录详细交易信息
        self.record_trade(
            date=data.datetime.date(0).strftime('%Y-%m-%d'),
            time=data.datetime.time(0).strftime('%H:%M:%S'),
            stock_code=stock_code,
            action='sell',
            price=current_price,
            quantity=position_size,
            reason=f"布林带策略-{signals['reason']}",
            order_ref=order.ref if order else None
        )


class MovingAverageStrategy(ConfigurableStrategy):
    """移动平均线策略"""
    
    def __init__(self):
        super().__init__()

        # 策略基本信息
        self.strategy_name = "moving_average"
        self.display_name = "移动平均线策略"
        self.description = "基于双移动平均线的趋势跟踪策略，短期均线上穿长期均线时买入，下穿时卖出"

        # 策略参数
        self.short_period = self.get_param_value('short_period', 10)
        self.long_period = self.get_param_value('long_period', 30)
        self.max_positions = self.get_param_value('max_positions', 3)
        self.position_size = self.get_param_value('position_size', 0.3)

        # 为每个数据源创建移动平均线指标
        self.ma_indicators = {}
        for i, data in enumerate(self.datas):
            stock_code = getattr(data, '_name', f'data_{i}')

            # 创建移动平均线指标
            short_ma = bt.indicators.SimpleMovingAverage(data.close, period=self.short_period)
            long_ma = bt.indicators.SimpleMovingAverage(data.close, period=self.long_period)

            self.ma_indicators[stock_code] = {
                'short_ma': short_ma,
                'long_ma': long_ma,
                'data': data
            }

            logger.info(f"为 {stock_code} 创建移动平均线指标")

    
    def generate_signals(self, data):
        """生成交易信号"""
        stock_code = getattr(data, '_name', 'Unknown')
        
        # 获取对应的移动平均线指标
        if stock_code not in self.ma_indicators:
            return {'buy': False, 'sell': False}
        
        ma_info = self.ma_indicators[stock_code]
        short_ma = ma_info['short_ma']
        long_ma = ma_info['long_ma']
        
        # 确保有足够的数据
        if len(data) < self.long_period + 1:
            return {'buy': False, 'sell': False}
        
        # 安全获取均线值
        current_short = short_ma[0] if short_ma[0] is not None else 0
        current_long = long_ma[0] if long_ma[0] is not None else 0
        prev_short = short_ma[-1] if short_ma[-1] is not None else 0
        prev_long = long_ma[-1] if long_ma[-1] is not None else 0

        # 检查数据有效性
        if current_short <= 0 or current_long <= 0 or prev_short <= 0 or prev_long <= 0:
            return {'buy': False, 'sell': False}

        # 生成信号
        signals = {'buy': False, 'sell': False}

        # 买入信号：短期均线上穿长期均线（金叉）
        if prev_short <= prev_long and current_short > current_long:
            signals['buy'] = True
            signals['reason'] = f'金叉信号: 短期均线({current_short:.2f})上穿长期均线({current_long:.2f})'

        # 卖出信号：短期均线下穿长期均线（死叉）
        elif prev_short >= prev_long and current_short < current_long:
            signals['sell'] = True
            signals['reason'] = f'死叉信号: 短期均线({current_short:.2f})下穿长期均线({current_long:.2f})'
        
        return signals


class BuyHoldStrategy(ConfigurableStrategy):
    """买入持有策略"""
    
    def __init__(self):
        super().__init__()

        # 策略基本信息
        self.strategy_name = "buy_hold"
        self.display_name = "买入持有策略"
        self.description = "在回测开始时买入选定股票，持有到回测结束"

        # 策略参数
        self.max_stocks = self.get_param_value('max_stocks', 3)
        self.position_size = self.get_param_value('position_size', 0.33)

        # 标记是否已经买入
        self.has_bought = False

        logger.info(f"买入持有策略参数: max_stocks={self.max_stocks}, position_size={self.position_size}")
    
    def generate_signals(self, data):
        """生成交易信号"""
        stock_code = getattr(data, '_name', 'Unknown')

        # 检查是否已持仓
        position = self.getposition(data)
        if position.size > 0:
            # 已持仓，不再买入
            return {'buy': False, 'sell': False}

        # 检查已买入的股票数量
        bought_count = sum(1 for d in self.datas if self.getposition(d).size > 0)
        if bought_count >= self.max_stocks:
            # 已达到最大持仓数量
            return {'buy': False, 'sell': False}

        # 检查是否有足够的数据（至少需要1个数据点）
        if len(data) >= 1:
            logger.info(f"🎯 buy_hold策略生成买入信号: {stock_code} (已买入{bought_count}/{self.max_stocks}只)")
            return {'buy': True, 'reason': f'买入持有策略-初始建仓({bought_count+1}/{self.max_stocks})'}

        return {'buy': False, 'sell': False}

    def process_buy_signal(self, data, signals):
        """处理买入信号"""
        stock_code = getattr(data, '_name', 'Unknown')

        # 检查是否已持仓
        position = self.getposition(data)
        if position.size > 0:
            return

        # 计算买入数量
        cash = self.broker.get_cash()
        if cash <= 0:
            logger.warning(f"现金不足，无法买入 {stock_code}")
            return

        value = cash * self.position_size
        current_price = data.close[0] if data.close[0] is not None else 0

        if value <= 0 or current_price <= 0:
            logger.warning(f"计算买入金额异常: value={value}, price={current_price}")
            return

        size = int(value / current_price / 100) * 100  # 按手买入

        if size >= 100:  # 至少买入1手
            order = self.buy(data=data, size=size)
            logger.info(f"买入持有: {stock_code} {size}股 @{current_price:.2f}")

            # 记录详细交易信息
            self.record_trade(
                date=data.datetime.date(0).strftime('%Y-%m-%d'),
                time=data.datetime.time(0).strftime('%H:%M:%S'),
                stock_code=stock_code,
                action='buy',
                price=current_price,
                quantity=size,
                reason="买入持有策略-建仓",
                order_ref=order.ref if order else None
            )

            # 检查是否已买入足够的股票
            bought_count = sum(1 for d in self.datas if self.getposition(d).size > 0)
            logger.info(f"✅ 买入持有策略执行成功: {stock_code} {size}股 @{current_price:.2f} (已买入{bought_count}/{self.max_stocks}只)")
        else:
            logger.debug(f"{stock_code} 计算买入数量不足100股: {size}")


class RSIStrategy(ConfigurableStrategy):
    """RSI策略"""

    def __init__(self):
        super().__init__()

        # 策略基本信息
        self.strategy_name = "rsi"
        self.display_name = "RSI策略"
        self.description = "基于RSI指标的超买超卖策略，RSI低于30时买入，高于70时卖出"

        # 策略参数
        self.period = self.get_param_value('period', 14)
        self.oversold = self.get_param_value('oversold', 30)
        self.overbought = self.get_param_value('overbought', 70)
        self.max_positions = self.get_param_value('max_positions', 3)
        self.position_size = self.get_param_value('position_size', 0.3)

        # 为每个数据源创建RSI指标
        self.rsi_indicators = {}
        for i, data in enumerate(self.datas):
            stock_code = getattr(data, '_name', f'data_{i}')

            # 创建RSI指标
            rsi = bt.indicators.RSI(data.close, period=self.period)

            self.rsi_indicators[stock_code] = {
                'rsi': rsi,
                'data': data
            }

            logger.info(f"为 {stock_code} 创建RSI指标")

    def generate_signals(self, data):
        """生成交易信号"""
        stock_code = getattr(data, '_name', 'Unknown')

        # 获取对应的RSI指标
        if stock_code not in self.rsi_indicators:
            return {'buy': False, 'sell': False}

        rsi_info = self.rsi_indicators[stock_code]
        rsi = rsi_info['rsi']

        # 确保有足够的数据
        if len(data) < self.period + 1:
            return {'buy': False, 'sell': False}

        # 安全获取RSI值
        current_rsi = rsi[0] if rsi[0] is not None else 50

        # 检查数据有效性
        if current_rsi <= 0 or current_rsi > 100:
            return {'buy': False, 'sell': False}

        # 生成信号
        signals = {'buy': False, 'sell': False}

        # 买入信号：RSI低于超卖线
        if current_rsi <= self.oversold:
            signals['buy'] = True
            signals['reason'] = f'RSI超卖信号: RSI({current_rsi:.1f}) <= {self.oversold}'

        # 卖出信号：RSI高于超买线
        elif current_rsi >= self.overbought:
            signals['sell'] = True
            signals['reason'] = f'RSI超买信号: RSI({current_rsi:.1f}) >= {self.overbought}'

        return signals


# 注册所有策略
register_strategy(BollingerBandsStrategy, 'bollinger_bands', '布林带策略',
                 '基于布林带指标的均值回归策略，价格触及下轨时买入，触及上轨时卖出')
register_strategy(MovingAverageStrategy, 'moving_average', '移动平均线策略',
                 '基于双移动平均线的趋势跟踪策略，短期均线上穿长期均线时买入，下穿时卖出')
register_strategy(BuyHoldStrategy, 'buy_hold', '买入持有策略',
                 '在回测开始时买入选定股票，持有到回测结束')
register_strategy(RSIStrategy, 'rsi', 'RSI策略',
                 '基于RSI指标的超买超卖策略，RSI低于30时买入，高于70时卖出')
