#!/usr/bin/env python3
"""
测试新的回测系统
基于backtrader，使用真实数据和策略注册机制
"""

import asyncio
import time
import json
from backend.backtest.simple_backtest_engine import simple_backtest_engine
from backend.strategies.base_strategy_new import list_strategies


async def test_strategy_registration():
    """测试策略注册机制"""
    print("=== 测试策略注册机制 ===")
    
    strategies = list_strategies()
    print(f"已注册的策略数量: {len(strategies)}")
    
    for strategy in strategies:
        print(f"策略名称: {strategy['name']}")
        print(f"显示名称: {strategy['display_name']}")
        print(f"描述: {strategy['description']}")
        print(f"类名: {strategy['class_name']}")
        print("-" * 50)


async def test_backtest_execution(strategy_name: str, strategy_config: dict = None):
    """测试回测执行"""
    print(f"\n=== 测试回测执行: {strategy_name} ===")
    
    if strategy_config is None:
        strategy_config = {}
    
    # 启动回测
    config = {
        'strategy_name': strategy_name,
        'strategy_config': strategy_config,
        'start_date': '2024-01-01',
        'end_date': '2024-12-31',
        'initial_capital': 1000000,
        'commission': 0.0003
    }
    
    task_id = await simple_backtest_engine.start_backtest(config)
    print(f"回测任务已启动: {task_id}")
    
    # 等待完成
    print("等待回测完成...")
    for i in range(30):  # 最多等待30秒
        await asyncio.sleep(1)
        
        task_status = simple_backtest_engine.get_task_status(task_id)
        if task_status:
            status = task_status['status']
            progress = task_status['progress']
            message = task_status['message']
            
            print(f"进度: {progress}% - {message}")
            
            if status in ['completed', 'failed']:
                break
    
    # 获取结果
    result = simple_backtest_engine.get_result(task_id)
    if result:
        print(f"\n=== 回测结果 ===")
        print(f"策略: {result.strategy_name}")
        print(f"时间范围: {result.start_date} 到 {result.end_date}")
        print(f"初始资金: ¥{result.initial_capital:,.2f}")
        print(f"最终资金: ¥{result.final_capital:,.2f}")
        print(f"总收益率: {result.total_return:.2%}")
        print(f"年化收益率: {result.annual_return:.2%}")
        print(f"最大回撤: {result.max_drawdown:.2%}")
        print(f"夏普比率: {result.sharpe_ratio:.2f}")
        print(f"胜率: {result.win_rate:.2%}")
        print(f"总交易次数: {result.total_trades}")
        print(f"盈利交易: {result.profit_trades}")
        print(f"亏损交易: {result.loss_trades}")
        print(f"平均盈利: ¥{result.avg_profit:.2f}")
        print(f"平均亏损: ¥{result.avg_loss:.2f}")
        print(f"盈亏比: {result.profit_factor:.2f}")
        
        print(f"\n=== 交易记录 (前5笔) ===")
        for i, trade in enumerate(result.trades[:5]):
            action_symbol = "📈" if trade['action'] == 'buy' else "📉"
            print(f"{action_symbol} 交易#{trade['id']}: {trade['date']} {trade['time']}")
            print(f"   {trade['stock_code']} ({trade['stock_name']}) - {trade['action_name']}")
            print(f"   价格: ¥{trade['price']}, 数量: {trade['quantity']:,}股")
            print(f"   金额: ¥{trade['amount']:,.2f}, 手续费: ¥{trade['commission']:.2f}")
            if trade['action'] == 'sell' and 'profit_loss' in trade:
                print(f"   盈亏: ¥{trade['profit_loss']:,.2f} ({trade.get('profit_loss_pct', 0):.2f}%)")
            print(f"   原因: {trade['reason']}")
            print()
        
        if len(result.trades) > 5:
            print(f"... 还有 {len(result.trades) - 5} 笔交易")
        
        print(f"\n=== 每日收益 (前5天) ===")
        for dr in result.daily_returns[:5]:
            print(f"{dr['date']}: 组合价值=¥{dr['value']:,.2f}, 日收益={dr['return']:.4%}")
        
        if len(result.daily_returns) > 5:
            print(f"... 还有 {len(result.daily_returns) - 5} 天数据")
            
        return True
    else:
        print("获取回测结果失败")
        return False


async def test_multiple_strategies():
    """测试多个策略"""
    print("\n" + "="*60)
    print("测试多个策略")
    print("="*60)
    
    # 测试不同的策略配置
    test_cases = [
        {
            'name': 'buy_hold',
            'config': {
                'max_stocks': 3,
                'position_size': 0.33
            }
        },
        {
            'name': 'bollinger_bands',
            'config': {
                'period': 20,
                'std_dev': 2.0,
                'max_positions': 5,
                'position_size': 0.2
            }
        },
        {
            'name': 'moving_average',
            'config': {
                'short_period': 10,
                'long_period': 30,
                'max_positions': 3,
                'position_size': 0.3
            }
        }
    ]
    
    results = []
    
    for test_case in test_cases:
        print(f"\n{'='*20} 测试策略: {test_case['name']} {'='*20}")
        success = await test_backtest_execution(test_case['name'], test_case['config'])
        results.append({
            'strategy': test_case['name'],
            'success': success
        })
        
        # 等待一下再测试下一个策略
        await asyncio.sleep(2)
    
    # 总结
    print(f"\n{'='*20} 测试总结 {'='*20}")
    for result in results:
        status = "✅ 成功" if result['success'] else "❌ 失败"
        print(f"{result['strategy']}: {status}")


async def main():
    """主函数"""
    print("新回测系统测试")
    print("="*60)
    
    # 测试策略注册
    await test_strategy_registration()
    
    # 测试多个策略
    await test_multiple_strategies()
    
    print("\n测试完成！")


if __name__ == "__main__":
    asyncio.run(main())
