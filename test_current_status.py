#!/usr/bin/env python3
"""
测试当前系统状态
"""

import sys
import os
import requests
import json
import time

# 添加项目路径
sys.path.append(os.path.dirname(__file__))

def test_current_strategy_startup():
    """测试当前策略启动状态"""
    try:
        print("🔍 测试当前策略启动状态...")
        
        # 启动一个策略
        test_data = {
            "strategy_name": "bollinger_bands",
            "initial_capital": 100000,
            "commission": 0.001,
            "stock_codes": ["000001.SZ"],
            "paper_trading": True,
            "strategy_params": {
                "period": 20,
                "std_dev": 2.0
            }
        }
        
        print("📤 启动策略...")
        response = requests.post(
            'http://localhost:8000/api/live/start',
            json=test_data,
            headers={'Content-Type': 'application/json'}
        )
        
        print(f"📋 响应状态: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"📋 响应: {data}")
            
            if data.get('success'):
                task_id = data.get('task_id')
                print(f"✅ 策略启动成功: {task_id[:8]}...")
                
                # 等待策略初始化
                print("⏳ 等待策略初始化...")
                time.sleep(8)
                
                # 检查策略状态
                print("📋 检查策略状态...")
                response = requests.get('http://localhost:8000/api/live/results')
                
                if response.status_code == 200:
                    data = response.json()
                    if data.get('success'):
                        results = data.get('data', [])
                        strategy = next((r for r in results if r['task_id'] == task_id), None)
                        
                        if strategy:
                            status = strategy.get('status', 'unknown')
                            print(f"📊 策略状态: {status}")
                            
                            if status == 'running':
                                print("🎉 策略运行正常！")
                                print("✅ 所有问题已修复")
                                
                                # 清理
                                print(f"🧹 清理测试策略...")
                                requests.post(f'http://localhost:8000/api/live/stop/{task_id}')
                                time.sleep(1)
                                requests.delete(f'http://localhost:8000/api/live/delete/{task_id}')
                                print("✅ 清理完成")
                                
                                return True
                            elif status == 'error':
                                print("❌ 策略状态为错误")
                                print("   请检查最新的错误日志")
                                return False
                            else:
                                print(f"⚠️ 策略状态: {status}")
                                return False
                        else:
                            print("❌ 未找到策略")
                            return False
                    else:
                        print(f"❌ 获取策略状态失败: {data}")
                        return False
                else:
                    print(f"❌ API错误: {response.status_code}")
                    return False
            else:
                print(f"❌ 策略启动失败: {data.get('message', 'Unknown error')}")
                return False
        else:
            print(f"❌ HTTP错误: {response.text}")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def check_error_logs():
    """检查最新的错误日志"""
    try:
        print("\n🔍 检查最新错误日志...")
        
        with open('backend/logs/errors.log', 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # 获取最后10行
        recent_lines = lines[-10:] if len(lines) >= 10 else lines
        
        print("📋 最近的错误日志:")
        for line in recent_lines:
            if line.strip():
                print(f"   {line.strip()}")
        
        # 检查是否有新的错误
        current_time = time.strftime("%H:%M")
        has_recent_errors = any(current_time[:4] in line for line in recent_lines[-5:])
        
        if has_recent_errors:
            print("⚠️ 发现最近的错误")
            return False
        else:
            print("✅ 没有发现最近的错误")
            return True
        
    except Exception as e:
        print(f"❌ 检查日志失败: {e}")
        return False

def provide_status_summary():
    """提供状态总结"""
    print("\n🎯 当前系统状态:")
    
    print("🔧 已修复的问题:")
    print("  ✅ QMTTrader缺失方法")
    print("  ✅ 股票代码传递逻辑")
    print("  ✅ backtrader属性问题")
    print("  ✅ PandasData继承")
    print("  ✅ WebSocket订阅机制")
    
    print("\n💡 如果仍有问题:")
    print("  1. 重启后端服务器")
    print("  2. 清除浏览器缓存")
    print("  3. 检查Python模块是否重新加载")
    print("  4. 查看最新的错误日志")
    
    print("\n🚀 使用建议:")
    print("  1. 访问: http://localhost:3000/multi-strategy")
    print("  2. 点击'添加策略'")
    print("  3. 配置策略参数")
    print("  4. 选择纸上交易模式")
    print("  5. 启动策略并观察状态")

if __name__ == "__main__":
    print("🧪 当前系统状态测试")
    print("=" * 50)
    
    # 检查错误日志
    log_ok = check_error_logs()
    
    # 测试策略启动
    startup_ok = test_current_strategy_startup()
    
    print("\n" + "=" * 50)
    print("🎯 测试结果:")
    print(f"  错误日志: {'✅ 无最近错误' if log_ok else '⚠️ 有最近错误'}")
    print(f"  策略启动: {'✅ 成功' if startup_ok else '❌ 失败'}")
    
    if startup_ok:
        print("\n🎉 系统完全正常！")
        print("✅ 可以正常使用策略交易功能")
    else:
        print("\n⚠️ 系统仍有问题")
        print("   建议重启后端服务器并重试")
    
    # 提供状态总结
    provide_status_summary()
    
    print("\n" + "=" * 50)
    print("🎯 测试完成")
