#!/usr/bin/env python3
"""
量化因子计算模块 - 实现完整的量化因子体系
"""

import numpy as np
import pandas as pd
from typing import Dict, Any, Optional, Tuple
from backend.core.logger import get_logger

logger = get_logger(__name__)

class QuantitativeFactors:
    """量化因子计算器"""
    
    def __init__(self):
        self.logger = get_logger(self.__class__.__name__)
    
    def calculate_all_factors(self, df: pd.DataFrame, fundamental_data: Optional[Dict] = None) -> Dict[str, Any]:
        """计算所有量化因子"""
        try:
            if df is None or len(df) < 20:
                return {}
            
            factors = {}
            
            # 基础数据
            close = df['close'].values
            high = df['high'].values
            low = df['low'].values
            volume = df['volume'].values
            
            # 如果有开盘价，使用开盘价，否则用收盘价
            open_price = df.get('open', df['close']).values
            
            # 如果有成交额数据
            amount = df.get('amount', close * volume).values
            
            # 1. 价值因子 (Value Factors)
            value_factors = self.calculate_value_factors(close, fundamental_data)
            factors.update(value_factors)
            
            # 2. 质量因子 (Quality Factors)
            quality_factors = self.calculate_quality_factors(fundamental_data)
            factors.update(quality_factors)
            
            # 3. 成长因子 (Growth Factors)
            growth_factors = self.calculate_growth_factors(fundamental_data)
            factors.update(growth_factors)
            
            # 4. 动量因子 (Momentum Factors)
            momentum_factors = self.calculate_momentum_factors(close, volume, amount)
            factors.update(momentum_factors)
            
            # 5. 风险控制因子
            risk_factors = self.calculate_risk_factors(close, high, low, volume)
            factors.update(risk_factors)
            
            # 6. 技术面量化因子
            technical_factors = self.calculate_technical_factors(close, high, low, volume, open_price)
            factors.update(technical_factors)
            
            # 7. 流动性因子
            liquidity_factors = self.calculate_liquidity_factors(volume, amount, close)
            factors.update(liquidity_factors)
            
            return factors
            
        except Exception as e:
            self.logger.error(f"计算量化因子失败: {e}")
            return {}
    
    def calculate_value_factors(self, close: np.ndarray, fundamental_data: Optional[Dict] = None) -> Dict[str, Any]:
        """计算价值因子"""
        factors = {}
        
        try:
            current_price = close[-1]
            
            if fundamental_data:
                # EP (市盈率倒数) = 1 / PE_TTM
                pe_ttm = fundamental_data.get('pe_ttm')
                if pe_ttm and pe_ttm > 0:
                    factors['ep_ratio'] = 1.0 / pe_ttm
                    factors['pe_ttm'] = pe_ttm
                else:
                    factors['ep_ratio'] = 0
                    factors['pe_ttm'] = None
                
                # BP (市净率倒数) = 1 / PB
                pb_ratio = fundamental_data.get('pb_ratio')
                if pb_ratio and pb_ratio > 0:
                    factors['bp_ratio'] = 1.0 / pb_ratio
                    factors['pb_ratio'] = pb_ratio
                else:
                    factors['bp_ratio'] = 0
                    factors['pb_ratio'] = None
                
                # SP (市销率倒数) = 1 / PS_TTM
                ps_ttm = fundamental_data.get('ps_ttm')
                if ps_ttm and ps_ttm > 0:
                    factors['sp_ratio'] = 1.0 / ps_ttm
                    factors['ps_ttm'] = ps_ttm
                else:
                    factors['sp_ratio'] = 0
                    factors['ps_ttm'] = None
                
                # 股息率
                dividend_yield = fundamental_data.get('dividend_yield', 0)
                factors['dividend_yield'] = dividend_yield
                
                # EV/EBITDA
                ev_ebitda = fundamental_data.get('ev_ebitda')
                factors['ev_ebitda'] = ev_ebitda
                
                # 市值
                market_cap = fundamental_data.get('market_cap')
                factors['market_cap'] = market_cap
                
                # 价值因子综合得分 (标准化后)
                value_score = 0
                count = 0
                
                if factors['ep_ratio'] > 0:
                    value_score += min(factors['ep_ratio'] * 100, 10)  # 限制最大值
                    count += 1
                if factors['bp_ratio'] > 0:
                    value_score += min(factors['bp_ratio'] * 10, 10)
                    count += 1
                if dividend_yield > 0:
                    value_score += min(dividend_yield * 200, 10)  # 股息率*200
                    count += 1
                
                factors['value_score'] = value_score / count if count > 0 else 0
            
            else:
                # 没有基本面数据时，设置默认值
                factors.update({
                    'ep_ratio': 0, 'pe_ttm': None,
                    'bp_ratio': 0, 'pb_ratio': None,
                    'sp_ratio': 0, 'ps_ttm': None,
                    'dividend_yield': 0,
                    'ev_ebitda': None,
                    'market_cap': None,
                    'value_score': 0
                })
            
        except Exception as e:
            self.logger.error(f"计算价值因子失败: {e}")
        
        return factors
    
    def calculate_quality_factors(self, fundamental_data: Optional[Dict] = None) -> Dict[str, Any]:
        """计算质量因子"""
        factors = {}
        
        try:
            if fundamental_data:
                # ROE (净资产收益率)
                roe = fundamental_data.get('roe')
                factors['roe'] = roe
                
                # ROA (总资产收益率)
                roa = fundamental_data.get('roa')
                factors['roa'] = roa
                
                # 毛利率
                gross_margin = fundamental_data.get('gross_margin')
                factors['gross_margin'] = gross_margin
                
                # 净利率
                net_margin = fundamental_data.get('net_margin')
                factors['net_margin'] = net_margin
                
                # 资产周转率
                asset_turnover = fundamental_data.get('asset_turnover')
                factors['asset_turnover'] = asset_turnover
                
                # 权益乘数
                equity_multiplier = fundamental_data.get('equity_multiplier')
                factors['equity_multiplier'] = equity_multiplier
                
                # 质量因子综合得分
                quality_score = 0
                count = 0
                
                if roe and roe > 0:
                    quality_score += min(roe / 2, 10)  # ROE/2，最大10分
                    count += 1
                if roa and roa > 0:
                    quality_score += min(roa, 10)  # ROA直接计分
                    count += 1
                if gross_margin and gross_margin > 0:
                    quality_score += min(gross_margin / 5, 10)  # 毛利率/5
                    count += 1
                
                factors['quality_score'] = quality_score / count if count > 0 else 0
            
            else:
                # 没有基本面数据时，设置默认值
                factors.update({
                    'roe': None, 'roa': None,
                    'gross_margin': None, 'net_margin': None,
                    'asset_turnover': None, 'equity_multiplier': None,
                    'quality_score': 0
                })
            
        except Exception as e:
            self.logger.error(f"计算质量因子失败: {e}")
        
        return factors
    
    def calculate_growth_factors(self, fundamental_data: Optional[Dict] = None) -> Dict[str, Any]:
        """计算成长因子"""
        factors = {}
        
        try:
            if fundamental_data:
                # 营收增长率
                revenue_growth = fundamental_data.get('revenue_growth')
                factors['revenue_growth'] = revenue_growth
                
                # 净利润增长率
                profit_growth = fundamental_data.get('profit_growth')
                factors['profit_growth'] = profit_growth
                
                # EPS增长率
                eps_growth = fundamental_data.get('eps_growth')
                factors['eps_growth'] = eps_growth
                
                # 研发费用增长率
                rd_growth = fundamental_data.get('rd_growth')
                factors['rd_growth'] = rd_growth
                
                # 营收增长率（3年CAGR）
                revenue_cagr_3y = fundamental_data.get('revenue_cagr_3y')
                factors['revenue_cagr_3y'] = revenue_cagr_3y
                
                # 净利润增长率（3年CAGR）
                profit_cagr_3y = fundamental_data.get('profit_cagr_3y')
                factors['profit_cagr_3y'] = profit_cagr_3y
                
                # 成长因子综合得分
                growth_score = 0
                count = 0
                
                if revenue_growth and revenue_growth > 0:
                    growth_score += min(revenue_growth / 2, 10)  # 营收增长率/2
                    count += 1
                if profit_growth and profit_growth > 0:
                    growth_score += min(profit_growth / 3, 10)  # 利润增长率/3
                    count += 1
                if eps_growth and eps_growth > 0:
                    growth_score += min(eps_growth / 3, 10)  # EPS增长率/3
                    count += 1
                
                factors['growth_score'] = growth_score / count if count > 0 else 0
            
            else:
                # 没有基本面数据时，设置默认值
                factors.update({
                    'revenue_growth': None, 'profit_growth': None,
                    'eps_growth': None, 'rd_growth': None,
                    'revenue_cagr_3y': None, 'profit_cagr_3y': None,
                    'growth_score': 0
                })
            
        except Exception as e:
            self.logger.error(f"计算成长因子失败: {e}")
        
        return factors
    
    def calculate_momentum_factors(self, close: np.ndarray, volume: np.ndarray, amount: np.ndarray) -> Dict[str, Any]:
        """计算动量因子"""
        factors = {}
        
        try:
            # 短期动量（1M = 22个交易日）
            if len(close) >= 22:
                factors['momentum_1m'] = (close[-1] - close[-22]) / close[-22]
            else:
                factors['momentum_1m'] = 0
            
            # 中期动量（3M = 66个交易日）
            if len(close) >= 66:
                factors['momentum_3m'] = (close[-1] - close[-66]) / close[-66]
            else:
                factors['momentum_3m'] = 0
            
            # 长期动量（12M = 252个交易日）
            if len(close) >= 252:
                factors['momentum_12m'] = (close[-1] - close[-252]) / close[-252]
            else:
                factors['momentum_12m'] = 0
            
            # 动量加速度 (1M动量 - 3M动量)
            factors['momentum_acceleration'] = factors['momentum_1m'] - factors['momentum_3m']
            
            # 成交量动量
            if len(volume) >= 20:
                volume_ma5 = np.mean(volume[-5:])
                volume_ma20 = np.mean(volume[-20:])
                factors['volume_momentum'] = volume_ma5 / volume_ma20 if volume_ma20 > 0 else 1
            else:
                factors['volume_momentum'] = 1
            
            # 资金流动量 (简化版)
            if len(amount) >= 20:
                amount_ma5 = np.mean(amount[-5:])
                amount_ma20 = np.mean(amount[-20:])
                factors['money_flow_momentum'] = amount_ma5 / amount_ma20 if amount_ma20 > 0 else 1
            else:
                factors['money_flow_momentum'] = 1
            
            # 动量因子综合得分
            momentum_score = 0
            
            # 1月动量评分
            if factors['momentum_1m'] > 0.05:  # 1月涨幅>5%
                momentum_score += 15
            elif factors['momentum_1m'] > 0:
                momentum_score += 5
            
            # 3月动量评分
            if factors['momentum_3m'] > 0.15:  # 3月涨幅>15%
                momentum_score += 10
            elif factors['momentum_3m'] > 0:
                momentum_score += 3
            
            # 成交量动量评分
            if factors['volume_momentum'] > 1.5:
                momentum_score += 10
            elif factors['volume_momentum'] > 1.0:
                momentum_score += 5
            
            factors['momentum_score'] = momentum_score
            
        except Exception as e:
            self.logger.error(f"计算动量因子失败: {e}")

        return factors

    def calculate_risk_factors(self, close: np.ndarray, high: np.ndarray, low: np.ndarray, volume: np.ndarray) -> Dict[str, Any]:
        """计算风险控制因子"""
        factors = {}

        try:
            # 历史波动率（20日年化）
            if len(close) >= 20:
                returns = np.diff(close[-21:]) / close[-21:-1]  # 20个收益率
                volatility = np.std(returns) * np.sqrt(252)  # 年化波动率
                factors['volatility'] = volatility
                factors['volatility_20d'] = volatility
            else:
                factors['volatility'] = 0
                factors['volatility_20d'] = 0

            # Beta系数（简化计算，相对于自身历史）
            if len(close) >= 60:
                returns = np.diff(close[-61:]) / close[-61:-1]
                market_returns = np.mean(returns)  # 简化的市场收益
                if len(returns) > 1:
                    covariance = np.cov(returns, [market_returns] * len(returns))[0, 1]
                    market_variance = np.var([market_returns] * len(returns))
                    factors['beta'] = covariance / market_variance if market_variance != 0 else 1.0
                else:
                    factors['beta'] = 1.0
            else:
                factors['beta'] = 1.0

            # 最大回撤
            if len(close) >= 60:
                peak = np.maximum.accumulate(close[-60:])
                drawdown = (close[-60:] - peak) / peak
                factors['max_drawdown'] = abs(np.min(drawdown))
            else:
                factors['max_drawdown'] = 0

            # VaR (Value at Risk) 简化计算
            if len(close) >= 20:
                returns = np.diff(close[-21:]) / close[-21:-1]
                factors['var_95'] = abs(np.percentile(returns, 5))  # 95% VaR
                factors['var_99'] = abs(np.percentile(returns, 1))  # 99% VaR
            else:
                factors['var_95'] = 0
                factors['var_99'] = 0

            # 下行波动率
            if len(close) >= 20:
                returns = np.diff(close[-21:]) / close[-21:-1]
                negative_returns = returns[returns < 0]
                if len(negative_returns) > 0:
                    factors['downside_volatility'] = np.std(negative_returns) * np.sqrt(252)
                else:
                    factors['downside_volatility'] = 0
            else:
                factors['downside_volatility'] = 0

            # 风险因子综合得分（低风险高分）
            risk_score = 0

            # 波动率评分（适中波动率得分高）
            vol = factors['volatility']
            if 0.1 <= vol <= 0.25:  # 适中波动率
                risk_score += 15
            elif vol < 0.1:  # 过低波动率
                risk_score += 8
            elif vol <= 0.4:  # 可接受波动率
                risk_score += 5

            # Beta评分
            beta = factors['beta']
            if 0.8 <= beta <= 1.2:  # 适中Beta
                risk_score += 10
            elif beta < 0.8:  # 低Beta
                risk_score += 8

            # 回撤评分
            max_dd = factors['max_drawdown']
            if max_dd < 0.1:  # 低回撤
                risk_score += 15
            elif max_dd < 0.2:
                risk_score += 10
            elif max_dd < 0.3:
                risk_score += 5

            factors['risk_score'] = risk_score

        except Exception as e:
            self.logger.error(f"计算风险因子失败: {e}")

        return factors

    def calculate_technical_factors(self, close: np.ndarray, high: np.ndarray, low: np.ndarray,
                                  volume: np.ndarray, open_price: np.ndarray) -> Dict[str, Any]:
        """计算技术面量化因子"""
        factors = {}

        try:
            # 均线系统因子
            if len(close) >= 60:
                ma5 = np.mean(close[-5:])
                ma10 = np.mean(close[-10:])
                ma20 = np.mean(close[-20:])
                ma60 = np.mean(close[-60:])

                factors['ma5'] = ma5
                factors['ma10'] = ma10
                factors['ma20'] = ma20
                factors['ma60'] = ma60

                # 均线排列
                if ma5 > ma10 > ma20 > ma60:
                    factors['ma_arrangement'] = 'strong_bullish'
                    factors['ma_arrangement_score'] = 20
                elif ma5 > ma10 > ma20:
                    factors['ma_arrangement'] = 'bullish'
                    factors['ma_arrangement_score'] = 15
                elif ma5 < ma10 < ma20 < ma60:
                    factors['ma_arrangement'] = 'strong_bearish'
                    factors['ma_arrangement_score'] = -10
                elif ma5 < ma10 < ma20:
                    factors['ma_arrangement'] = 'bearish'
                    factors['ma_arrangement_score'] = -5
                else:
                    factors['ma_arrangement'] = 'neutral'
                    factors['ma_arrangement_score'] = 0

                # 价格位置（相对于均线）
                factors['price_vs_ma20'] = (close[-1] - ma20) / ma20
                factors['price_vs_ma60'] = (close[-1] - ma60) / ma60

                # 均线斜率
                if len(close) >= 25:
                    ma20_5d_ago = np.mean(close[-25:-5])
                    factors['ma20_slope'] = (ma20 - ma20_5d_ago) / ma20_5d_ago
                else:
                    factors['ma20_slope'] = 0

            # RSI
            factors['rsi'] = self._calculate_rsi(close, 14)

            # MACD
            macd_line, macd_signal, macd_histogram = self._calculate_macd(close)
            factors['macd'] = macd_line
            factors['macd_signal'] = macd_signal
            factors['macd_histogram'] = macd_histogram
            factors['macd_trend'] = 'bullish' if macd_line > macd_signal else 'bearish'

            # 布林带位置
            if len(close) >= 20:
                ma20 = np.mean(close[-20:])
                std20 = np.std(close[-20:])
                bb_upper = ma20 + 2 * std20
                bb_lower = ma20 - 2 * std20

                factors['bb_upper'] = bb_upper
                factors['bb_middle'] = ma20
                factors['bb_lower'] = bb_lower

                if bb_upper > bb_lower:
                    factors['bb_position'] = (close[-1] - bb_lower) / (bb_upper - bb_lower)
                else:
                    factors['bb_position'] = 0.5

                factors['bb_width'] = (bb_upper - bb_lower) / ma20 if ma20 > 0 else 0

            # 价格位置（相对于近期高低点）
            if len(high) >= 20 and len(low) >= 20:
                high_20d = np.max(high[-20:])
                low_20d = np.min(low[-20:])

                factors['high_20d'] = high_20d
                factors['low_20d'] = low_20d

                if high_20d > low_20d:
                    factors['price_position'] = (close[-1] - low_20d) / (high_20d - low_20d)
                else:
                    factors['price_position'] = 0.5

            # ATR (Average True Range)
            if len(close) >= 15:
                tr1 = high[1:] - low[1:]
                tr2 = np.abs(high[1:] - close[:-1])
                tr3 = np.abs(low[1:] - close[:-1])
                tr = np.maximum(tr1, np.maximum(tr2, tr3))
                factors['atr'] = np.mean(tr[-14:])
                factors['atr_ratio'] = factors['atr'] / close[-1] if close[-1] > 0 else 0

            # 技术面综合评分
            tech_score = 0

            # RSI评分
            rsi = factors.get('rsi', 50)
            if 30 <= rsi <= 70:
                tech_score += 10
            elif rsi < 30:
                tech_score += 15  # 超卖
            elif rsi > 70:
                tech_score += 5   # 超买

            # MACD评分
            if factors.get('macd_trend') == 'bullish':
                tech_score += 10

            # 均线排列评分
            tech_score += factors.get('ma_arrangement_score', 0)

            # 价格位置评分
            price_pos = factors.get('price_position', 0.5)
            if 0.3 <= price_pos <= 0.8:  # 适中位置
                tech_score += 8
            elif price_pos > 0.8:  # 高位
                tech_score += 3

            factors['technical_score'] = max(tech_score, 0)

        except Exception as e:
            self.logger.error(f"计算技术因子失败: {e}")

        return factors

    def calculate_liquidity_factors(self, volume: np.ndarray, amount: np.ndarray, close: np.ndarray) -> Dict[str, Any]:
        """计算流动性因子"""
        factors = {}

        try:
            # 日均成交额（万元）
            if len(amount) >= 20:
                factors['avg_amount'] = np.mean(amount[-20:]) / 10000  # 转换为万元
                factors['avg_amount_60d'] = np.mean(amount[-60:]) / 10000 if len(amount) >= 60 else factors['avg_amount']
            else:
                factors['avg_amount'] = amount[-1] / 10000 if len(amount) > 0 else 0
                factors['avg_amount_60d'] = factors['avg_amount']

            # 量比（当日成交量/近期平均成交量）
            if len(volume) >= 20:
                volume_avg_20 = np.mean(volume[-20:])
                factors['volume_ratio'] = volume[-1] / volume_avg_20 if volume_avg_20 > 0 else 1

                # 5日量比
                if len(volume) >= 5:
                    volume_avg_5 = np.mean(volume[-5:])
                    factors['volume_ratio_5d'] = volume_avg_5 / volume_avg_20 if volume_avg_20 > 0 else 1
                else:
                    factors['volume_ratio_5d'] = factors['volume_ratio']
            else:
                factors['volume_ratio'] = 1
                factors['volume_ratio_5d'] = 1

            # 换手率估算（简化计算）
            # 实际换手率 = 成交量 / 流通股本，这里用成交量/平均成交量 * 基准换手率估算
            base_turnover = 2.0  # 假设基准换手率2%
            factors['turnover_rate'] = factors['volume_ratio'] * base_turnover

            # Amihud非流动性指标 = |日收益率| / 成交额
            if len(close) >= 2 and len(amount) >= 2:
                daily_return = abs((close[-1] - close[-2]) / close[-2])
                daily_amount = amount[-1]
                factors['amihud_illiquidity'] = daily_return / (daily_amount / 1e8) if daily_amount > 0 else 0  # 标准化
            else:
                factors['amihud_illiquidity'] = 0

            # 成交额比率
            if len(amount) >= 20:
                amount_avg = np.mean(amount[-20:])
                factors['amount_ratio'] = amount[-1] / amount_avg if amount_avg > 0 else 1
            else:
                factors['amount_ratio'] = 1

            # 流动性因子综合得分
            liquidity_score = 0

            # 成交额评分
            avg_amount = factors['avg_amount']
            if avg_amount > 10000:  # 1亿元以上
                liquidity_score += 20
            elif avg_amount > 5000:  # 5000万元以上
                liquidity_score += 15
            elif avg_amount > 1000:  # 1000万元以上
                liquidity_score += 10
            elif avg_amount > 100:   # 100万元以上
                liquidity_score += 5

            # 量比评分
            vol_ratio = factors['volume_ratio']
            if vol_ratio > 2.0:  # 放量
                liquidity_score += 10
            elif vol_ratio > 1.5:
                liquidity_score += 8
            elif vol_ratio > 1.0:
                liquidity_score += 5
            elif vol_ratio < 0.5:  # 缩量
                liquidity_score -= 5

            # 换手率评分
            turnover = factors['turnover_rate']
            if 1.0 <= turnover <= 10.0:  # 适中换手率
                liquidity_score += 8
            elif turnover > 15.0:  # 过度活跃
                liquidity_score += 3
            elif turnover < 0.5:  # 过度冷清
                liquidity_score -= 3

            factors['liquidity_score'] = max(liquidity_score, 0)

        except Exception as e:
            self.logger.error(f"计算流动性因子失败: {e}")

        return factors

    # 辅助计算方法
    def _calculate_rsi(self, close: np.ndarray, period: int = 14) -> float:
        """计算RSI指标"""
        try:
            if len(close) < period + 1:
                return 50.0

            delta = np.diff(close)
            gain = np.where(delta > 0, delta, 0)
            loss = np.where(delta < 0, -delta, 0)

            avg_gain = np.mean(gain[-period:])
            avg_loss = np.mean(loss[-period:])

            if avg_loss == 0:
                return 100.0

            rs = avg_gain / avg_loss
            rsi = 100 - (100 / (1 + rs))

            return float(rsi)

        except Exception:
            return 50.0

    def _calculate_macd(self, close: np.ndarray, fast: int = 12, slow: int = 26, signal: int = 9) -> Tuple[float, float, float]:
        """计算MACD指标"""
        try:
            if len(close) < slow:
                return 0.0, 0.0, 0.0

            # 计算EMA
            ema_fast = self._calculate_ema(close, fast)
            ema_slow = self._calculate_ema(close, slow)

            macd_line = ema_fast - ema_slow

            # 简化信号线计算
            if len(close) >= slow + signal:
                # 取最近signal个MACD值计算信号线
                recent_macd = [macd_line] * min(signal, len(close))
                macd_signal = np.mean(recent_macd)
            else:
                macd_signal = macd_line

            macd_histogram = macd_line - macd_signal

            return float(macd_line), float(macd_signal), float(macd_histogram)

        except Exception:
            return 0.0, 0.0, 0.0

    def _calculate_ema(self, data: np.ndarray, period: int) -> float:
        """计算指数移动平均"""
        try:
            if len(data) < period:
                return float(np.mean(data))

            alpha = 2.0 / (period + 1)
            ema = data[0]

            for price in data[1:]:
                ema = alpha * price + (1 - alpha) * ema

            return float(ema)

        except Exception:
            return float(np.mean(data)) if len(data) > 0 else 0.0
