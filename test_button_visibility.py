#!/usr/bin/env python3
"""
测试按钮可见性 - 启动一个策略来验证按钮显示
"""

import sys
import os
import requests
import json
import time

# 添加项目路径
sys.path.append(os.path.dirname(__file__))

def test_button_visibility():
    """测试按钮可见性"""
    try:
        print("🔍 测试停止按钮可见性...")
        
        # 启动一个策略
        test_data = {
            "strategy_name": "bollinger_bands",
            "initial_capital": 100000,
            "commission": 0.001,
            "stock_codes": ["000001.SZ"],
            "paper_trading": True,
            "strategy_params": {
                "period": 20,
                "std_dev": 2.0
            }
        }
        
        print("📤 启动测试策略...")
        response = requests.post(
            'http://localhost:8000/api/live/start',
            json=test_data,
            headers={'Content-Type': 'application/json'}
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                task_id = data.get('task_id')
                print(f"✅ 策略启动成功: {task_id[:8]}...")
                
                # 等待策略启动
                time.sleep(2)
                
                # 获取策略列表
                print("📋 获取策略列表...")
                response = requests.get('http://localhost:8000/api/live/results')
                
                if response.status_code == 200:
                    data = response.json()
                    if data.get('success'):
                        results = data.get('data', [])
                        
                        print(f"✅ 获取到 {len(results)} 个策略")
                        print("\n📊 策略详情（用于前端按钮显示）:")
                        print("=" * 80)
                        
                        for result in results:
                            task_id_short = result.get('task_id', '')[:8]
                            strategy_name = result.get('strategy_name', '')
                            status = result.get('status', '')
                            paper_trading = result.get('paper_trading', True)
                            
                            # 按钮状态判断
                            stop_button_enabled = status == 'running'
                            stop_button_color = "🔴 红色" if stop_button_enabled else "⚪ 灰色"
                            stop_button_tooltip = "停止策略" if stop_button_enabled else "策略已停止"
                            
                            trading_mode = "🟢 纸上交易" if paper_trading else "🔴 实盘交易"
                            status_display = "🟢 运行中" if status == 'running' else "🔴 已停止"
                            
                            print(f"策略ID: {task_id_short}...")
                            print(f"  策略名称: {strategy_name}")
                            print(f"  交易模式: {trading_mode}")
                            print(f"  运行状态: {status_display}")
                            print(f"  查看按钮: 🔵 蓝色 (始终启用)")
                            print(f"  停止按钮: {stop_button_color} ({'启用' if stop_button_enabled else '禁用'})")
                            print(f"  按钮提示: {stop_button_tooltip}")
                            print("-" * 40)
                        
                        print("\n🎯 前端显示说明:")
                        print("1. 访问: http://localhost:3000/multi-strategy")
                        print("2. 在策略表格的'操作'列中应该看到:")
                        print("   - 👁️ 蓝色查看按钮（悬停时背景变蓝）")
                        print("   - ⏹️ 红色停止按钮（悬停时背景变红）")
                        print("3. 悬停在按钮上会显示tooltip提示")
                        print("4. 点击停止按钮后，按钮会变为灰色禁用状态")
                        
                        # 清理测试数据
                        print(f"\n🧹 清理测试策略: {task_id[:8]}...")
                        try:
                            requests.post(f'http://localhost:8000/api/live/stop/{task_id}')
                            print("✅ 测试策略已停止")
                        except:
                            print("⚠️ 清理失败，请手动停止")
                            
                    else:
                        print(f"❌ 获取策略列表失败: {data}")
                else:
                    print(f"❌ 获取策略列表API错误: {response.status_code}")
                    
            else:
                print(f"❌ 策略启动失败: {data}")
        else:
            print(f"❌ 启动策略API错误: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    test_button_visibility()
