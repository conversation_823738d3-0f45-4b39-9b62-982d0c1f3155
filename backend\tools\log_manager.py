#!/usr/bin/env python3
"""
日志管理工具
用于查看、分析和管理日志文件
"""

import os
import sys
import argparse
from datetime import datetime, timedelta
import glob
from typing import List, Dict, Optional

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from backend.core.logger import logger_manager

class LogManager:
    """日志管理器"""
    
    def __init__(self):
        self.log_dir = logger_manager.log_dir
    
    def list_log_files(self, module: Optional[str] = None) -> List[str]:
        """列出日志文件"""
        if module:
            pattern = os.path.join(self.log_dir, module, "*.log")
        else:
            pattern = os.path.join(self.log_dir, "**", "*.log")
        
        return glob.glob(pattern, recursive=True)
    
    def get_log_size(self, file_path: str) -> str:
        """获取日志文件大小"""
        try:
            size = os.path.getsize(file_path)
            if size < 1024:
                return f"{size} B"
            elif size < 1024 * 1024:
                return f"{size / 1024:.1f} KB"
            else:
                return f"{size / (1024 * 1024):.1f} MB"
        except:
            return "Unknown"
    
    def show_log_info(self):
        """显示日志信息"""
        print("=" * 60)
        print("QMT交易系统 - 日志文件信息")
        print("=" * 60)
        
        modules = ['api', 'data', 'backtest', 'strategies', 'trading', 'core', 'services']
        
        for module in modules:
            module_dir = os.path.join(self.log_dir, module)
            if os.path.exists(module_dir):
                print(f"\n📁 {module.upper()} 模块:")
                files = self.list_log_files(module)
                if files:
                    for file_path in sorted(files):
                        file_name = os.path.basename(file_path)
                        size = self.get_log_size(file_path)
                        mtime = datetime.fromtimestamp(os.path.getmtime(file_path))
                        print(f"  📄 {file_name:<25} {size:<10} {mtime.strftime('%Y-%m-%d %H:%M:%S')}")
                else:
                    print("  (无日志文件)")
        
        # 错误日志
        error_log = os.path.join(self.log_dir, "errors.log")
        if os.path.exists(error_log):
            print(f"\n🚨 错误日志:")
            size = self.get_log_size(error_log)
            mtime = datetime.fromtimestamp(os.path.getmtime(error_log))
            print(f"  📄 errors.log{' ' * 16} {size:<10} {mtime.strftime('%Y-%m-%d %H:%M:%S')}")
    
    def tail_log(self, file_path: str, lines: int = 50):
        """显示日志文件末尾内容"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                all_lines = f.readlines()
                tail_lines = all_lines[-lines:] if len(all_lines) > lines else all_lines
                
                print(f"\n📄 {os.path.basename(file_path)} (最后 {len(tail_lines)} 行):")
                print("-" * 80)
                for line in tail_lines:
                    print(line.rstrip())
                print("-" * 80)
        except Exception as e:
            print(f"读取日志文件失败: {e}")
    
    def search_logs(self, keyword: str, module: Optional[str] = None, days: int = 7):
        """搜索日志内容"""
        files = self.list_log_files(module)
        cutoff_time = datetime.now() - timedelta(days=days)
        
        print(f"\n🔍 搜索关键词: '{keyword}' (最近{days}天)")
        print("=" * 60)
        
        found_count = 0
        for file_path in files:
            # 检查文件修改时间
            mtime = datetime.fromtimestamp(os.path.getmtime(file_path))
            if mtime < cutoff_time:
                continue
            
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    
                file_matches = []
                for i, line in enumerate(lines, 1):
                    if keyword.lower() in line.lower():
                        file_matches.append((i, line.strip()))
                
                if file_matches:
                    print(f"\n📄 {os.path.relpath(file_path, self.log_dir)}:")
                    for line_num, line_content in file_matches[-10:]:  # 只显示最后10个匹配
                        print(f"  {line_num:4d}: {line_content}")
                    found_count += len(file_matches)
                    
            except Exception as e:
                print(f"搜索文件 {file_path} 失败: {e}")
        
        print(f"\n总共找到 {found_count} 个匹配项")
    
    def cleanup_logs(self, days: int = 30, dry_run: bool = True):
        """清理旧日志"""
        cutoff_time = datetime.now() - timedelta(days=days)
        
        print(f"\n🧹 清理 {days} 天前的日志文件")
        if dry_run:
            print("(预览模式，不会实际删除)")
        print("=" * 60)
        
        files = self.list_log_files()
        deleted_count = 0
        deleted_size = 0
        
        for file_path in files:
            try:
                mtime = datetime.fromtimestamp(os.path.getmtime(file_path))
                if mtime < cutoff_time:
                    size = os.path.getsize(file_path)
                    print(f"{'[预览]' if dry_run else '[删除]'} {os.path.relpath(file_path, self.log_dir)} ({self.get_log_size(file_path)})")
                    
                    if not dry_run:
                        os.remove(file_path)
                    
                    deleted_count += 1
                    deleted_size += size
            except Exception as e:
                print(f"处理文件 {file_path} 失败: {e}")
        
        print(f"\n{'预计' if dry_run else '实际'}删除 {deleted_count} 个文件，释放 {deleted_size / (1024*1024):.1f} MB 空间")
    
    def monitor_errors(self, follow: bool = False):
        """监控错误日志"""
        error_log = os.path.join(self.log_dir, "errors.log")
        
        if not os.path.exists(error_log):
            print("错误日志文件不存在")
            return
        
        print("🚨 监控错误日志 (Ctrl+C 退出)")
        print("=" * 60)
        
        if follow:
            # 实时监控模式
            import time
            with open(error_log, 'r', encoding='utf-8') as f:
                f.seek(0, 2)  # 移到文件末尾
                while True:
                    line = f.readline()
                    if line:
                        print(line.rstrip())
                    else:
                        time.sleep(0.1)
        else:
            # 显示最近的错误
            self.tail_log(error_log, 100)

def main():
    parser = argparse.ArgumentParser(description='QMT交易系统日志管理工具')
    parser.add_argument('command', choices=['info', 'tail', 'search', 'cleanup', 'errors'], 
                       help='操作命令')
    parser.add_argument('--module', '-m', help='指定模块 (api, data, backtest, strategies, trading, core, services)')
    parser.add_argument('--file', '-f', help='指定日志文件')
    parser.add_argument('--lines', '-n', type=int, default=50, help='显示行数 (默认50)')
    parser.add_argument('--keyword', '-k', help='搜索关键词')
    parser.add_argument('--days', '-d', type=int, default=7, help='天数 (默认7)')
    parser.add_argument('--follow', action='store_true', help='实时监控')
    parser.add_argument('--execute', action='store_true', help='执行清理 (非预览模式)')
    
    args = parser.parse_args()
    
    log_manager = LogManager()
    
    if args.command == 'info':
        log_manager.show_log_info()
    
    elif args.command == 'tail':
        if args.file:
            file_path = os.path.join(log_manager.log_dir, args.file)
            if not os.path.exists(file_path):
                # 尝试在模块目录中查找
                if args.module:
                    file_path = os.path.join(log_manager.log_dir, args.module, args.file)
            log_manager.tail_log(file_path, args.lines)
        else:
            print("请指定日志文件: --file filename.log")
    
    elif args.command == 'search':
        if args.keyword:
            log_manager.search_logs(args.keyword, args.module, args.days)
        else:
            print("请指定搜索关键词: --keyword 关键词")
    
    elif args.command == 'cleanup':
        log_manager.cleanup_logs(args.days, not args.execute)
    
    elif args.command == 'errors':
        log_manager.monitor_errors(args.follow)

if __name__ == "__main__":
    main()
