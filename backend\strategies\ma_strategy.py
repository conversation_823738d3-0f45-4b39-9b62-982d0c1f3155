#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
均线策略 - Moving Average Strategy
"""

import pandas as pd
import numpy as np
from typing import Dict, Any, List, Optional
from .base_strategy import ConfigurableStrategy


class MovingAverageStrategy(ConfigurableStrategy):
    """
    均线策略
    
    策略逻辑:
    1. 计算短期和长期移动平均线
    2. 当短期均线上穿长期均线时买入
    3. 当短期均线下穿长期均线时卖出
    4. 支持止损和止盈
    """
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.name = "moving_average"
        self.display_name = "均线策略"
        self.description = "基于双均线交叉的趋势跟踪策略"
        self.version = "1.0.0"
        self.author = "System"
        
        # 策略状态
        self.positions = {}
        self.last_prices = {}
        
    @classmethod
    def get_default_config(cls) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            "name": "moving_average",
            "display_name": "均线策略",
            "description": "基于双均线交叉的趋势跟踪策略",
            "version": "1.0.0",
            "author": "System",
            "parameters": {
                "short_period": {
                    "value": 5,
                    "type": "int",
                    "min": 3,
                    "max": 50,
                    "description": "短期均线周期"
                },
                "long_period": {
                    "value": 20,
                    "type": "int", 
                    "min": 10,
                    "max": 200,
                    "description": "长期均线周期"
                },
                "stop_loss": {
                    "value": 0.05,
                    "type": "float",
                    "min": 0.01,
                    "max": 0.2,
                    "description": "止损比例"
                },
                "take_profit": {
                    "value": 0.1,
                    "type": "float",
                    "min": 0.02,
                    "max": 0.5,
                    "description": "止盈比例"
                },
                "max_positions": {
                    "value": 5,
                    "type": "int",
                    "min": 1,
                    "max": 20,
                    "description": "最大持仓数量"
                },
                "position_size": {
                    "value": 0.2,
                    "type": "float",
                    "min": 0.1,
                    "max": 1.0,
                    "description": "单次开仓比例"
                },
                "min_volume": {
                    "value": 1000000,
                    "type": "int",
                    "min": 100000,
                    "max": 10000000,
                    "description": "最小成交量要求"
                }
            }
        }
    
    def calculate_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """计算技术指标"""
        df = data.copy()
        
        short_period = self.get_parameter('short_period')
        long_period = self.get_parameter('long_period')
        
        # 计算移动平均线
        df['ma_short'] = df['close'].rolling(window=short_period).mean()
        df['ma_long'] = df['close'].rolling(window=long_period).mean()
        
        # 计算均线差值和比率
        df['ma_diff'] = df['ma_short'] - df['ma_long']
        df['ma_ratio'] = df['ma_short'] / df['ma_long'] - 1
        
        # 计算交叉信号
        df['ma_cross_up'] = (
            (df['ma_short'] > df['ma_long']) & 
            (df['ma_short'].shift(1) <= df['ma_long'].shift(1))
        )
        df['ma_cross_down'] = (
            (df['ma_short'] < df['ma_long']) & 
            (df['ma_short'].shift(1) >= df['ma_long'].shift(1))
        )
        
        # 计算成交量均线
        df['volume_ma'] = df['volume'].rolling(window=20).mean()
        df['volume_ratio'] = df['volume'] / df['volume_ma']
        
        return df
    
    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """生成交易信号"""
        df = self.calculate_indicators(data)
        
        # 初始化信号列
        df['signal'] = 0
        df['signal_strength'] = 0.0
        df['signal_reason'] = ''
        
        min_volume = self.get_parameter('min_volume')
        
        for i in range(len(df)):
            if pd.isna(df.iloc[i]['ma_short']) or pd.isna(df.iloc[i]['ma_long']):
                continue
                
            current = df.iloc[i]
            
            # 买入信号
            if (current['ma_cross_up'] and 
                current['volume'] >= min_volume and
                current['volume_ratio'] > 1.2):
                
                df.iloc[i, df.columns.get_loc('signal')] = 1
                df.iloc[i, df.columns.get_loc('signal_strength')] = min(
                    abs(current['ma_ratio']) * 10, 1.0
                )
                df.iloc[i, df.columns.get_loc('signal_reason')] = '短期均线上穿长期均线'
            
            # 卖出信号
            elif (current['ma_cross_down'] or
                  current['volume_ratio'] > 3.0):  # 异常放量
                
                df.iloc[i, df.columns.get_loc('signal')] = -1
                df.iloc[i, df.columns.get_loc('signal_strength')] = min(
                    abs(current['ma_ratio']) * 10, 1.0
                )
                df.iloc[i, df.columns.get_loc('signal_reason')] = '短期均线下穿长期均线'
        
        return df
    
    def should_buy(self, symbol: str, data: pd.DataFrame, current_positions: Dict) -> tuple:
        """判断是否应该买入"""
        if len(current_positions) >= self.get_parameter('max_positions'):
            return False, "已达到最大持仓数量"
        
        if symbol in current_positions:
            return False, "已持有该股票"
        
        signals = self.generate_signals(data)
        if len(signals) == 0:
            return False, "无数据"
        
        latest = signals.iloc[-1]
        
        if latest['signal'] == 1:
            return True, latest['signal_reason']
        
        return False, "无买入信号"
    
    def should_sell(self, symbol: str, data: pd.DataFrame, position: Dict) -> tuple:
        """判断是否应该卖出"""
        signals = self.generate_signals(data)
        if len(signals) == 0:
            return False, "无数据"
        
        latest = signals.iloc[-1]
        current_price = latest['close']
        entry_price = position.get('avg_price', current_price)
        
        # 计算收益率
        return_rate = (current_price - entry_price) / entry_price
        
        # 止损
        stop_loss = self.get_parameter('stop_loss')
        if return_rate <= -stop_loss:
            return True, f"止损 ({return_rate:.2%})"
        
        # 止盈
        take_profit = self.get_parameter('take_profit')
        if return_rate >= take_profit:
            return True, f"止盈 ({return_rate:.2%})"
        
        # 技术信号
        if latest['signal'] == -1:
            return True, latest['signal_reason']
        
        return False, "持有"
    
    def get_position_size(self, symbol: str, current_price: float, available_cash: float) -> int:
        """计算开仓数量"""
        position_ratio = self.get_parameter('position_size')
        target_value = available_cash * position_ratio
        
        # 计算股数（100股为一手）
        shares = int(target_value / current_price / 100) * 100
        
        return max(shares, 100)  # 至少买一手
    
    def validate_parameters(self) -> List[str]:
        """验证参数"""
        errors = []
        
        short_period = self.get_parameter('short_period')
        long_period = self.get_parameter('long_period')
        
        if short_period >= long_period:
            errors.append("短期均线周期必须小于长期均线周期")
        
        stop_loss = self.get_parameter('stop_loss')
        take_profit = self.get_parameter('take_profit')
        
        if stop_loss >= take_profit:
            errors.append("止损比例应该小于止盈比例")
        
        return errors
    
    def get_strategy_info(self) -> Dict[str, Any]:
        """获取策略信息"""
        return {
            "name": self.name,
            "display_name": self.display_name,
            "description": self.description,
            "version": self.version,
            "author": self.author,
            "type": "trend_following",
            "indicators": ["MA", "Volume"],
            "timeframes": ["1d", "1h", "30m"],
            "risk_level": "medium"
        }
