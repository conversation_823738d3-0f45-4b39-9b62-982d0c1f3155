#!/usr/bin/env python3
"""
交易监控工具
查看自动交易执行情况
"""

import sys
import os
from datetime import datetime, timedelta

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from backend.core.logger import logger_manager

def monitor_auto_trades():
    """监控自动交易"""
    print("🔍 自动交易监控")
    print("=" * 50)
    
    # 查看交易日志
    trade_log_file = os.path.join(logger_manager.log_dir, 'trading', 'auto_sell.log')
    
    if os.path.exists(trade_log_file):
        print(f"📄 交易日志文件: {trade_log_file}")
        
        # 读取最近的交易记录
        with open(trade_log_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        if lines:
            print(f"\n📋 最近的交易记录 (最后10条):")
            print("-" * 80)
            for line in lines[-10:]:
                print(line.strip())
            print("-" * 80)
        else:
            print("📭 暂无交易记录")
    else:
        print("📭 交易日志文件不存在")
    
    # 查看持仓监控日志中的卖出信息
    monitor_log_file = os.path.join(logger_manager.log_dir, 'services', 'position_monitor.log')
    
    if os.path.exists(monitor_log_file):
        print(f"\n📄 持仓监控日志: {monitor_log_file}")
        
        # 搜索卖出相关的日志
        with open(monitor_log_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        sell_lines = [line for line in lines if '卖出' in line or 'sell' in line.lower()]
        
        if sell_lines:
            print(f"\n📋 卖出相关日志 (最后10条):")
            print("-" * 80)
            for line in sell_lines[-10:]:
                print(line.strip())
            print("-" * 80)
        else:
            print("📭 暂无卖出相关日志")

def check_qmt_trader_status():
    """检查QMT交易器状态"""
    print("\n🔧 QMT交易器状态检查")
    print("=" * 30)
    
    try:
        from backend.trading.qmt_trader import qmt_trader
        
        print(f"连接状态: {'✅ 已连接' if qmt_trader.is_connected else '❌ 未连接'}")
        print(f"账户ID: {qmt_trader.account_id or '未获取'}")
        
        if qmt_trader.is_connected:
            print("✅ QMT交易器可用，可以执行真实交易")
        else:
            print("❌ QMT交易器不可用，将执行模拟交易")
            
    except Exception as e:
        print(f"❌ QMT交易器检查失败: {e}")

def show_recent_signals():
    """显示最近的止损信号"""
    print("\n🚨 最近的止损信号")
    print("=" * 30)
    
    try:
        from backend.strategies.position_monitor import position_monitor
        
        signals = position_monitor.signals_history[-10:] if position_monitor.signals_history else []
        
        if signals:
            for i, signal in enumerate(signals, 1):
                print(f"{i}. {signal.get('stock_code')} - {signal.get('signal_type')}")
                print(f"   消息: {signal.get('message')}")
                print(f"   时间: {signal.get('timestamp')}")
                print()
        else:
            print("📭 暂无止损信号")
            
    except Exception as e:
        print(f"❌ 获取信号失败: {e}")

if __name__ == "__main__":
    monitor_auto_trades()
    check_qmt_trader_status()
    show_recent_signals()
