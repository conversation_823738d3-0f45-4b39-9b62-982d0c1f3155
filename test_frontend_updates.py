#!/usr/bin/env python3
"""
测试前端更新 - 验证新的量化因子是否正确显示
"""

import requests
import json
import time

def test_api_endpoints():
    """测试API端点是否支持新的量化因子"""
    print("🔍 测试API端点")
    print("=" * 60)
    
    base_url = "http://localhost:8000"
    
    # 1. 测试健康检查
    try:
        response = requests.get(f"{base_url}/health", timeout=5)
        if response.status_code == 200:
            print("✅ 后端服务正常运行")
        else:
            print(f"❌ 后端服务异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 无法连接后端服务: {e}")
        print("请确保后端服务正在运行")
        return False
    
    # 2. 测试增强预设策略API
    print(f"\n📊 测试增强预设策略API...")
    try:
        response = requests.get(f"{base_url}/api/stock-selection/presets/enhanced", timeout=10)
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                presets = data.get('data', [])
                print(f"✅ 获取到 {len(presets)} 个增强预设策略")
                
                # 显示前5个预设策略
                for i, preset in enumerate(presets[:5]):
                    print(f"   {i+1}. {preset.get('display_name')} ({preset.get('category')})")
                    print(f"      描述: {preset.get('description')}")
                    print(f"      风险等级: {preset.get('risk_level')}")
                    
                    # 显示主要条件
                    criteria = preset.get('criteria', {})
                    conditions = []
                    if criteria.get('only_etf'):
                        conditions.append("仅ETF")
                    if criteria.get('pe_max'):
                        conditions.append(f"PE<{criteria['pe_max']}")
                    if criteria.get('roe_min'):
                        conditions.append(f"ROE>{criteria['roe_min']}%")
                    if criteria.get('momentum_1m_min'):
                        conditions.append(f"1月动量>{criteria['momentum_1m_min']*100}%")
                    
                    if conditions:
                        print(f"      主要条件: {', '.join(conditions[:3])}")
                    print()
            else:
                print(f"❌ API返回失败: {data.get('message')}")
                return False
        else:
            print(f"❌ 增强预设策略API失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 增强预设策略API异常: {e}")
        return False
    
    # 3. 测试ETF类型API
    print(f"🎯 测试ETF类型API...")
    try:
        response = requests.get(f"{base_url}/api/stock-selection/etf-types", timeout=10)
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                etf_types = data.get('data', [])
                print(f"✅ 获取到 {len(etf_types)} 种ETF类型")
                
                for etf_type in etf_types:
                    print(f"   • {etf_type.get('label')} ({etf_type.get('value')})")
                    print(f"     {etf_type.get('description')}")
            else:
                print(f"❌ ETF类型API返回失败: {data.get('message')}")
                return False
        else:
            print(f"❌ ETF类型API失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ ETF类型API异常: {e}")
        return False
    
    # 4. 测试策略类别API
    print(f"\n📂 测试策略类别API...")
    try:
        response = requests.get(f"{base_url}/api/stock-selection/presets/categories", timeout=10)
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                categories = data.get('data', [])
                print(f"✅ 获取到 {len(categories)} 个策略类别")
                
                for category in categories:
                    print(f"   • {category}")
            else:
                print(f"❌ 策略类别API返回失败: {data.get('message')}")
                return False
        else:
            print(f"❌ 策略类别API失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 策略类别API异常: {e}")
        return False
    
    return True

def test_new_selection_criteria():
    """测试新的选股条件"""
    print(f"\n🎯 测试新的选股条件")
    print("=" * 60)
    
    base_url = "http://localhost:8000"
    
    # 测试包含新量化因子的选股请求
    test_criteria = {
        "criteria": {
            # ETF筛选
            "only_etf": True,
            "volume_ratio_min": 0.8,
            
            # 价值因子
            "pe_max": 20,
            "pb_max": 2.5,
            "dividend_yield_min": 0.02,
            
            # 质量因子
            "roe_min": 15,
            "roa_min": 8,
            "gross_margin_min": 30,
            
            # 成长因子
            "revenue_growth_min": 20,
            "profit_growth_min": 25,
            "eps_growth_min": 20,
            
            # 动量因子
            "momentum_1m_min": 0.05,
            "momentum_3m_min": 0.15,
            
            # 风险控制因子
            "volatility_max": 0.3,
            "beta_max": 1.2,
            "avg_amount_min": 1000,
            
            # 其他条件
            "condition_logic": "flexible",
            "lookback_days": 60,
            "min_trading_days": 40
        },
        "custom_name": "新量化因子测试",
        "max_results": 50
    }
    
    print(f"📋 测试选股条件:")
    print(f"   ETF筛选: 仅ETF")
    print(f"   价值因子: PE<20, PB<2.5, 股息率>2%")
    print(f"   质量因子: ROE>15%, ROA>8%, 毛利率>30%")
    print(f"   成长因子: 营收增长>20%, 利润增长>25%")
    print(f"   动量因子: 1月动量>5%, 3月动量>15%")
    print(f"   风险控制: 波动率<30%, Beta<1.2, 成交额>1000万")
    
    try:
        print(f"\n🚀 启动选股任务...")
        response = requests.post(
            f"{base_url}/api/stock-selection/select",
            json=test_criteria,
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                task_id = data.get('data', {}).get('task_id')
                print(f"✅ 选股任务已启动: {task_id}")
                print(f"   任务名称: {data.get('data', {}).get('custom_name')}")
                
                # 检查任务状态
                print(f"\n⏳ 检查任务状态...")
                for i in range(5):  # 检查5次
                    time.sleep(2)
                    status_response = requests.get(f"{base_url}/api/stock-selection/status/{task_id}", timeout=5)
                    
                    if status_response.status_code == 200:
                        status_data = status_response.json()
                        if status_data.get('success'):
                            task_status = status_data.get('data', {})
                            status = task_status.get('status')
                            progress = task_status.get('progress', 0)
                            message = task_status.get('message', '')
                            
                            print(f"   状态: {status}, 进度: {progress}%, 消息: {message}")
                            
                            if status == 'completed':
                                results = task_status.get('result', [])
                                print(f"✅ 选股完成，共选中 {len(results)} 只股票/ETF")
                                
                                if results:
                                    print(f"\n📊 前3只结果:")
                                    for j, stock in enumerate(results[:3]):
                                        print(f"   {j+1}. {stock.get('stock_code')} - {stock.get('stock_name')}")
                                        print(f"      评分: {stock.get('score', 0):.1f}")
                                        
                                        indicators = stock.get('indicators', {})
                                        info = []
                                        if 'momentum_1m' in indicators:
                                            info.append(f"1月动量: {indicators['momentum_1m']*100:.1f}%")
                                        if 'volatility' in indicators:
                                            info.append(f"波动率: {indicators['volatility']*100:.1f}%")
                                        if 'avg_amount' in indicators:
                                            info.append(f"成交额: {indicators['avg_amount']:.0f}万")
                                        
                                        if info:
                                            print(f"      {', '.join(info)}")
                                        print()
                                
                                return True
                            elif status == 'failed':
                                error = task_status.get('error', '未知错误')
                                print(f"❌ 选股失败: {error}")
                                return False
                        else:
                            print(f"❌ 获取任务状态失败: {status_data.get('message')}")
                            return False
                    else:
                        print(f"❌ 任务状态API失败: {status_response.status_code}")
                        return False
                
                print(f"⏰ 任务仍在运行中...")
                return True
                
            else:
                print(f"❌ 启动选股任务失败: {data.get('message')}")
                return False
        else:
            print(f"❌ 选股API失败: {response.status_code}")
            print(f"   响应: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 选股测试异常: {e}")
        return False

def main():
    """主函数"""
    print("🎯 前端更新测试")
    print("=" * 80)
    
    try:
        # 测试API端点
        api_success = test_api_endpoints()
        
        if api_success:
            # 测试新的选股条件
            selection_success = test_new_selection_criteria()
            
            print(f"\n" + "=" * 80)
            if api_success and selection_success:
                print(f"✅ 前端更新测试完成！")
                
                print(f"\n💡 前端更新内容:")
                print(f"   ✅ ETF筛选条件 - 仅ETF、包含ETF、排除ETF")
                print(f"   ✅ 价值因子条件 - 低PE、低PB、高股息率")
                print(f"   ✅ 质量因子条件 - 高ROE、高ROA、高毛利率")
                print(f"   ✅ 成长因子条件 - 营收增长、利润增长、EPS增长")
                print(f"   ✅ 动量因子条件 - 1月强势、3月强势、反转机会")
                print(f"   ✅ 风险控制条件 - 低风险、高流动性、稳定Beta")
                print(f"   ✅ 预设策略选择 - 6种常用投资策略")
                print(f"   ✅ 增强结果显示 - 更多量化指标列")
                
                print(f"\n🎯 使用建议:")
                print(f"   • 重启前端服务查看新界面")
                print(f"   • 使用预设策略快速选择条件")
                print(f"   • 查看增强的选股结果表格")
                print(f"   • 尝试不同的量化因子组合")
            else:
                print(f"❌ 部分测试失败")
        else:
            print(f"❌ API测试失败，请检查后端服务")
            
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
