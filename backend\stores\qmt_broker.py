#!/usr/bin/env python3
"""
QMT Broker - 交易适配器
将backtrader的交易指令转换为QMT实际交易
"""

import threading
from collections import defaultdict
from datetime import datetime
from typing import Dict, Any, Optional

import backtrader as bt
from backend.core.logger import get_logger

logger = get_logger(__name__)

class QMTBroker(bt.broker.BrokerBase):
    """
    QMT交易适配器
    
    将backtrader的交易指令转换为QMT的实际交易操作
    支持：
    1. 市价单、限价单
    2. 买入、卖出
    3. 持仓管理
    4. 资金管理
    5. 订单状态跟踪
    """
    
    params = (
        ('cash', 100000.0),         # 初始资金
        ('commission', 0.001),      # 手续费率
        ('margin', None),           # 保证金（股票不需要）
        ('mult', 1.0),             # 合约乘数
        ('interest', 0.0),         # 利息
        ('interest_long', False),   # 多头利息
        ('leverage', 1.0),         # 杠杆
        ('trade_history', True),    # 是否记录交易历史
    )
    
    def __init__(self, store=None, **kwargs):
        """
        初始化QMT Broker
        
        Args:
            store: QMTStore实例
            **kwargs: 其他参数
        """
        self.store = store
        
        # 订单管理
        self.orders = {}  # 订单字典 {order_id: order}
        self.order_counter = 0  # 订单计数器
        self.pending_orders = {}  # 待处理订单
        
        # 持仓管理
        self.positions = defaultdict(float)  # {stock_code: quantity}
        self.position_values = defaultdict(float)  # {stock_code: value}
        self.avg_prices = defaultdict(float)  # {stock_code: avg_price}
        
        # 资金管理
        self.cash = kwargs.get('cash', self.params.cash)
        self.startingcash = self.cash  # backtrader需要的属性
        self.value = self.cash
        
        # 线程锁
        self.lock = threading.Lock()

        # 风险控制参数
        self.max_position_ratio = 0.3  # 单只股票最大持仓比例
        self.max_order_ratio = 0.1     # 单笔订单最大比例
        self.min_cash_ratio = 0.1      # 最小现金保留比例

        # 交易模式
        self.live_mode = False
        self.paper_trading = True

        logger.info("💼 初始化QMT Broker")
        logger.info(f"   初始资金: {self.cash:,.2f}")
        logger.info(f"   风险控制: 最大持仓{self.max_position_ratio*100:.0f}%, 最大订单{self.max_order_ratio*100:.0f}%")

        # 调用父类初始化
        super().__init__(**kwargs)

    def set_live_mode(self, live_mode: bool, paper_trading: bool = True):
        """设置实时交易模式"""
        self.live_mode = live_mode
        self.paper_trading = paper_trading

        mode_str = "实时" if live_mode else "回测"
        trading_str = "纸面交易" if paper_trading else "实盘交易"

        logger.info(f"🔄 设置交易模式: {mode_str} + {trading_str}")

        if live_mode and not paper_trading:
            # 实盘交易模式，初始化真实交易连接
            self._init_live_trading()

    def _init_live_trading(self):
        """初始化实盘交易连接"""
        try:
            logger.info("🔌 初始化实盘交易连接...")

            # 这里可以添加真实的交易连接逻辑
            # 例如连接到xtquant或其他交易接口

            logger.info("✅ 实盘交易连接成功")

        except Exception as e:
            logger.error(f"❌ 实盘交易连接失败: {e}")
            logger.info("📝 回退到纸面交易模式")
            self.paper_trading = True
    
    def start(self):
        """启动broker"""
        logger.info("🔄 启动QMT Broker")
        
        # 如果连接到QMT，同步账户信息
        if self.store.is_connected():
            self._sync_account_info()
    
    def _sync_account_info(self):
        """同步QMT账户信息"""
        try:
            trader = self.store.get_trader()
            if trader and trader.is_connected:
                # 获取账户资金
                account_info = trader.get_account_info()
                if account_info:
                    self.cash = account_info.get('available_cash', self.cash)
                    logger.info(f"✅ 同步账户资金: {self.cash:,.2f}")
                
                # 获取持仓信息
                positions = trader.get_positions()
                if positions:
                    for pos in positions:
                        stock_code = pos['stock_code']
                        quantity = pos['quantity']
                        self.positions[stock_code] = quantity
                    logger.info(f"✅ 同步持仓信息: {len(positions)}个持仓")
                    
        except Exception as e:
            logger.warning(f"⚠️ 同步账户信息失败: {e}")
    
    def submit(self, order):
        """
        提交订单
        
        Args:
            order: backtrader订单对象
        
        Returns:
            订单对象
        """
        with self.lock:
            # 生成订单ID
            self.order_counter += 1
            order_id = self.order_counter
            
            # 设置订单属性
            order.submit(self)
            order.addinfo(name='order_id', value=order_id)
            
            # 存储订单
            self.orders[order_id] = order
            
            logger.info(f"📝 提交订单: ID={order_id}, "
                       f"{'买入' if order.isbuy() else '卖出'} "
                       f"{order.data._name} {order.size}股")
            
            # 异步处理订单
            self._process_order_async(order, order_id)
            
            return order
    
    def _process_order_async(self, order, order_id):
        """异步处理订单"""
        try:
            # 检查订单有效性
            if not self._validate_order(order):
                self._reject_order(order, "订单验证失败")
                return
            
            # 接受订单
            order.accept(self)
            logger.info(f"✅ 订单已接受: ID={order_id}")
            
            # 如果连接到QMT，执行实际交易
            if self.store.is_connected():
                self._execute_real_order(order, order_id)
            else:
                # 模拟执行
                self._execute_simulated_order(order, order_id)
                
        except Exception as e:
            logger.error(f"❌ 处理订单失败: {e}")
            self._reject_order(order, str(e))
    
    def _validate_order(self, order):
        """验证订单"""
        try:
            # 检查股票代码
            if not order.data._name:
                logger.error("❌ 股票代码为空")
                return False
            
            # 检查数量
            if order.size <= 0:
                logger.error("❌ 订单数量必须大于0")
                return False
            
            # 检查买入资金
            if order.isbuy():
                required_cash = order.size * order.price if order.price else order.size * order.data.close[0]
                if required_cash > self.cash:
                    logger.error(f"❌ 资金不足: 需要{required_cash:,.2f}, 可用{self.cash:,.2f}")
                    return False
            
            # 检查卖出持仓
            if order.issell():
                current_position = self.positions.get(order.data._name, 0)
                if order.size > current_position:
                    logger.error(f"❌ 持仓不足: 需要{order.size}, 持有{current_position}")
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 订单验证异常: {e}")
            return False
    
    def _execute_real_order(self, order, order_id):
        """执行真实订单（通过QMT）"""
        try:
            trader = self.store.get_trader()
            stock_code = order.data._name
            quantity = order.size
            price = order.price if order.price else None
            
            if order.isbuy():
                # 执行买入
                qmt_order_id = trader.buy(
                    stock_code=stock_code,
                    quantity=quantity,
                    price=price
                )
                logger.info(f"📈 QMT买入订单: {qmt_order_id}")
                
            else:
                # 执行卖出
                qmt_order_id = trader.sell(
                    stock_code=stock_code,
                    quantity=quantity,
                    price=price
                )
                logger.info(f"📉 QMT卖出订单: {qmt_order_id}")
            
            # 模拟立即成交（实际应该等待QMT回报）
            self._complete_order(order, order_id)
            
        except Exception as e:
            logger.error(f"❌ 执行真实订单失败: {e}")
            self._reject_order(order, str(e))
    
    def _execute_simulated_order(self, order, order_id):
        """执行模拟订单"""
        try:
            # 模拟立即成交
            self._complete_order(order, order_id)
            logger.info(f"🎭 模拟订单执行完成: ID={order_id}")
            
        except Exception as e:
            logger.error(f"❌ 执行模拟订单失败: {e}")
            self._reject_order(order, str(e))
    
    def _complete_order(self, order, order_id):
        """完成订单"""
        try:
            stock_code = order.data._name
            quantity = order.size
            price = order.price if order.price else order.data.close[0]
            
            # 更新持仓
            if order.isbuy():
                self.positions[stock_code] += quantity
                self.cash -= quantity * price
            else:
                self.positions[stock_code] -= quantity
                self.cash += quantity * price
            
            # 扣除手续费
            commission = quantity * price * self.params.commission
            self.cash -= commission
            
            # 完成订单
            order.completed()
            
            logger.info(f"✅ 订单完成: ID={order_id}, "
                       f"价格={price:.2f}, 手续费={commission:.2f}")
            logger.info(f"   持仓: {stock_code}={self.positions[stock_code]}")
            logger.info(f"   资金: {self.cash:,.2f}")
            
        except Exception as e:
            logger.error(f"❌ 完成订单失败: {e}")
            self._reject_order(order, str(e))
    
    def _reject_order(self, order, reason):
        """拒绝订单"""
        order.reject()
        logger.warning(f"❌ 订单被拒绝: {reason}")
    
    def cancel(self, order):
        """取消订单"""
        order.cancel()
        logger.info(f"🚫 订单已取消: {order}")
        return order
    
    def get_cash(self):
        """获取可用资金"""
        return self.cash

    def getcash(self):
        """获取可用资金（backtrader标准方法）"""
        return self.cash

    def getvalue(self, datas=None):
        """获取总资产价值（backtrader标准方法）"""
        return self.get_value(datas)

    def getposition(self, data, clone=True):
        """获取持仓信息（backtrader标准方法）"""
        # 获取股票代码
        stock_code = getattr(data, 'stock_code', None)
        if not stock_code:
            # 如果没有stock_code属性，尝试从_name获取
            stock_code = getattr(data, '_name', 'UNKNOWN')

        # 获取持仓数量
        quantity = self.positions.get(stock_code, 0)

        # 创建Position对象（简化版本）
        from collections import namedtuple
        Position = namedtuple('Position', ['size', 'price', 'adjbase'])

        # 获取平均成本价格
        avg_price = self.avg_prices.get(stock_code, 0.0)

        return Position(size=quantity, price=avg_price, adjbase=None)

    def buy(self, owner, data, size, price=None, plimit=None, exectype=None, valid=None, tradeid=0, oco=None, trailamount=None, trailpercent=None, parent=None, transmit=True, **kwargs):
        """买入订单（backtrader标准方法）"""
        # 创建买入订单
        order_id = self._create_order(
            owner=owner,
            data=data,
            size=abs(size),  # 确保是正数
            price=price,
            exectype=exectype,
            is_buy=True
        )

        # 返回订单对象
        return self._get_order(order_id)

    def sell(self, owner, data, size, price=None, plimit=None, exectype=None, valid=None, tradeid=0, oco=None, trailamount=None, trailpercent=None, parent=None, transmit=True, **kwargs):
        """卖出订单（backtrader标准方法）"""
        # 创建卖出订单
        order_id = self._create_order(
            owner=owner,
            data=data,
            size=abs(size),  # 确保是正数
            price=price,
            exectype=exectype,
            is_buy=False
        )

        # 返回订单对象
        return self._get_order(order_id)

    def _create_order(self, owner, data, size, price, exectype, is_buy):
        """创建订单"""
        with self.lock:
            self.order_counter += 1
            order_id = self.order_counter

            # 获取股票代码
            stock_code = getattr(data, 'stock_code', 'UNKNOWN')

            # 获取当前价格（如果没有指定价格）
            if price is None:
                # 使用当前收盘价作为市价
                current_price = getattr(data, 'close', [100.0])
                if hasattr(current_price, '__getitem__'):
                    price = float(current_price[0])
                else:
                    price = float(current_price)

            # 计算订单总价值
            order_value = abs(size) * price

            # 风险控制检查
            if is_buy:
                # 检查单笔订单限制
                total_value = self.get_value()
                max_order_value = total_value * self.max_order_ratio
                if order_value > max_order_value:
                    logger.warning(f"⚠️ 订单超过限制: {order_value:.2f} > {max_order_value:.2f}")
                    # 调整订单大小
                    size = int(max_order_value / price)
                    order_value = size * price

                # 检查现金保留限制
                min_cash = total_value * self.min_cash_ratio
                available_cash = self.cash - min_cash
                if order_value > available_cash:
                    logger.warning(f"⚠️ 现金不足（保留{min_cash:.2f}）: 需要{order_value:.2f}, 可用{available_cash:.2f}")
                    # 调整订单大小
                    size = int(available_cash / price)
                    order_value = size * price

                # 检查持仓比例限制
                current_position_value = self.positions[stock_code] * self.avg_prices.get(stock_code, price)
                new_position_value = current_position_value + order_value
                max_position_value = total_value * self.max_position_ratio
                if new_position_value > max_position_value:
                    logger.warning(f"⚠️ 持仓超过限制: {new_position_value:.2f} > {max_position_value:.2f}")
                    # 调整订单大小
                    allowed_value = max_position_value - current_position_value
                    if allowed_value > 0:
                        size = int(allowed_value / price)
                        order_value = size * price
                    else:
                        size = 0

                if size <= 0:
                    logger.error(f"❌ 风险控制：无法买入")
                    return None

            # 创建订单对象
            order = {
                'ref': order_id,
                'data': data,
                'size': size if is_buy else -size,  # 买入为正，卖出为负
                'price': price,
                'exectype': exectype,
                'status': 'Submitted',  # Submitted -> Accepted -> Completed
                'alive': True,
                'stock_code': stock_code,
                'is_buy': is_buy,
                'order_value': order_value,
                'created_time': datetime.now()
            }

            # 保存订单
            self.orders[order_id] = order

            logger.info(f"📋 创建订单: {order_id}")
            logger.info(f"   股票: {stock_code}")
            logger.info(f"   方向: {'买入' if is_buy else '卖出'}")
            logger.info(f"   数量: {size}")
            logger.info(f"   价格: {price:.2f}")
            logger.info(f"   总价值: {order_value:.2f}")

            # 立即执行订单（模拟）
            self._execute_order(order_id)

            return order_id

    def _get_order(self, order_id):
        """获取订单对象"""
        order = self.orders.get(order_id)
        if order:
            # 转换为backtrader兼容的对象
            from collections import namedtuple
            Order = namedtuple('Order', ['ref', 'data', 'size', 'price', 'exectype', 'status', 'alive'])
            return Order(
                ref=order['ref'],
                data=order['data'],
                size=order['size'],
                price=order['price'],
                exectype=order['exectype'],
                status=order['status'],
                alive=lambda: order['alive']
            )
        return None

    def _execute_order(self, order_id):
        """执行订单（模拟）"""
        order = self.orders.get(order_id)
        if not order:
            return False

        try:
            stock_code = order['stock_code']
            size = order['size']
            price = order['price']
            is_buy = order['is_buy']
            order_value = order['order_value']

            logger.info(f"🔄 执行订单: {order_id}")

            if is_buy:
                # 买入：扣除现金，增加持仓
                if self.cash >= order_value:
                    self.cash -= order_value
                    self.positions[stock_code] += size
                    self.avg_prices[stock_code] = price  # 简化：使用当前价格作为平均价格

                    logger.info(f"✅ 买入成功: {stock_code}")
                    logger.info(f"   数量: {size}")
                    logger.info(f"   价格: {price:.2f}")
                    logger.info(f"   剩余现金: {self.cash:.2f}")
                    logger.info(f"   持仓: {self.positions[stock_code]}")

                    order['status'] = 'Completed'
                    return True
                else:
                    logger.error(f"❌ 买入失败: 资金不足")
                    order['status'] = 'Rejected'
                    return False
            else:
                # 卖出：增加现金，减少持仓
                abs_size = abs(size)
                if self.positions[stock_code] >= abs_size:
                    self.cash += order_value
                    self.positions[stock_code] -= abs_size

                    logger.info(f"✅ 卖出成功: {stock_code}")
                    logger.info(f"   数量: {abs_size}")
                    logger.info(f"   价格: {price:.2f}")
                    logger.info(f"   现金: {self.cash:.2f}")
                    logger.info(f"   持仓: {self.positions[stock_code]}")

                    order['status'] = 'Completed'
                    return True
                else:
                    logger.error(f"❌ 卖出失败: 持仓不足")
                    order['status'] = 'Rejected'
                    return False

        except Exception as e:
            logger.error(f"❌ 订单执行失败: {e}")
            order['status'] = 'Rejected'
            return False
    
    def get_value(self, datas=None):
        """获取总资产价值"""
        total_value = self.cash

        # 计算持仓价值
        for stock_code, quantity in self.positions.items():
            if quantity > 0:
                # 尝试获取当前市价
                current_price = self._get_current_price(stock_code, datas)
                position_value = quantity * current_price
                total_value += position_value

                logger.debug(f"持仓价值: {stock_code} = {quantity} × {current_price:.2f} = {position_value:.2f}")

        logger.debug(f"总资产: 现金{self.cash:.2f} + 持仓{total_value - self.cash:.2f} = {total_value:.2f}")
        return total_value

    def _get_current_price(self, stock_code, datas=None):
        """获取股票当前价格"""
        # 如果有数据源，尝试从数据源获取当前价格
        if datas:
            for data in datas:
                if hasattr(data, 'stock_code') and data.stock_code == stock_code:
                    if hasattr(data, 'close') and len(data.close) > 0:
                        return float(data.close[0])

        # 否则使用平均成本价格
        return self.avg_prices.get(stock_code, 100.0)

    def get_notification(self):
        """获取通知（backtrader标准方法）"""
        # backtrader broker标准方法
        # 返回None表示没有通知
        return None

    def next(self):
        """处理下一个数据点（backtrader标准方法）"""
        # backtrader broker标准方法
        # 在这里可以处理订单状态更新等
        pass
