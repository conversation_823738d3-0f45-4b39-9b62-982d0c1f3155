#!/usr/bin/env python3
"""
测试策略修复
"""

import requests
import json
import time

BASE_URL = "http://localhost:8000"

def test_strategy_with_valid_stocks():
    """测试使用有效股票代码的策略"""
    print("=== 测试使用有效股票代码的策略 ===")
    
    # 清理现有策略
    response = requests.get(f"{BASE_URL}/api/live/strategies")
    if response.status_code == 200:
        strategies = response.json()
        for strategy in strategies:
            if 'test' in strategy['name'].lower() or 'fix' in strategy['name'].lower():
                print(f"清理现有策略: {strategy['name']}")
                requests.delete(f"{BASE_URL}/api/live/strategies/{strategy['id']}")
    
    # 启动buy_hold策略，使用明确的股票代码
    strategy_data = {
        'name': '修复测试buy_hold',
        'strategy_type': 'buy_hold',
        'config': {
            'initial_capital': 100000,
            'paper_trading': True,
            'stock_codes': ['000001.SZ', '000002.SZ'],  # 明确指定有效的股票代码
            'max_stocks': 2,
            'position_size': 0.5
        }
    }
    
    print("启动buy_hold策略...")
    print(f"配置: {json.dumps(strategy_data, indent=2, ensure_ascii=False)}")
    
    response = requests.post(f"{BASE_URL}/api/live/strategies", json=strategy_data)
    
    if response.status_code == 200:
        result = response.json()
        if result.get('success'):
            strategy_id = result['strategy_id']
            print(f"✅ 策略启动成功: {strategy_id}")
            
            # 监控策略状态
            print(f"\n监控策略状态 (30秒)...")
            for i in range(10):
                time.sleep(3)
                
                response = requests.get(f"{BASE_URL}/api/live/strategies/{strategy_id}")
                if response.status_code == 200:
                    strategy = response.json()
                    print(f"[{i+1:2d}/10] 状态:{strategy['status']} 持仓:{strategy['positions']} 交易:{strategy['trade_count']} 盈亏:¥{strategy['pnl']:.2f}")
                    
                    error_message = strategy.get('error_message')
                    if error_message:
                        print(f"    ❌ 错误: {error_message}")
                        break
                    
                    if strategy['trade_count'] > 0:
                        print(f"    ✅ 策略开始交易！")
                        recent_trades = strategy.get('recent_trades', [])
                        for trade in recent_trades[:3]:
                            print(f"      交易: {trade}")
                        break
                    elif strategy['positions'] > 0:
                        print(f"    ✅ 策略建立持仓！")
                        break
                else:
                    print(f"    ❌ 获取策略状态失败")
                    break
            
            # 检查实盘引擎状态
            print(f"\n检查实盘引擎状态...")
            response = requests.get(f"{BASE_URL}/api/live/results")
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    results = data.get('data', [])
                    print(f"实盘引擎任务数量: {len(results)}")
                    
                    for result in results:
                        # 处理不同类型的result对象
                        if hasattr(result, 'strategy_name'):
                            strategy_name = result.strategy_name
                            task_id = getattr(result, 'task_id', 'N/A')
                            status = getattr(result, 'status', 'N/A')
                            stock_codes = getattr(result, 'stock_codes', [])
                        elif isinstance(result, dict):
                            strategy_name = result.get('strategy_name')
                            task_id = result.get('task_id', 'N/A')
                            status = result.get('status', 'N/A')
                            stock_codes = result.get('stock_codes', [])
                        else:
                            continue
                        
                        if strategy_name == 'buy_hold':
                            print(f"找到buy_hold任务:")
                            print(f"  task_id: {task_id}")
                            print(f"  状态: {status}")
                            print(f"  股票池: {stock_codes}")
                            break
                    else:
                        print(f"❌ 未找到buy_hold任务")
            
            # 清理
            print(f"\n清理测试策略...")
            requests.delete(f"{BASE_URL}/api/live/strategies/{strategy_id}")
            print(f"✅ 策略已清理")
            
            return True
        else:
            print(f"❌ 策略启动失败: {result.get('message')}")
            return False
    else:
        print(f"❌ 策略启动请求失败: {response.status_code}")
        print(f"响应: {response.text}")
        return False

def test_strategy_with_invalid_stocks():
    """测试使用无效股票代码的策略（应该被拒绝）"""
    print("\n=== 测试使用无效股票代码的策略 ===")
    
    # 启动策略，使用无效的股票代码
    strategy_data = {
        'name': '无效股票代码测试',
        'strategy_type': 'buy_hold',
        'config': {
            'initial_capital': 100000,
            'paper_trading': True,
            'stock_codes': ['', 'invalid', '123'],  # 无效的股票代码
            'max_stocks': 2,
            'position_size': 0.5
        }
    }
    
    print("尝试启动使用无效股票代码的策略...")
    print(f"配置: {json.dumps(strategy_data, indent=2, ensure_ascii=False)}")
    
    response = requests.post(f"{BASE_URL}/api/live/strategies", json=strategy_data)
    
    if response.status_code == 200:
        result = response.json()
        if result.get('success'):
            print(f"❌ 策略不应该启动成功，但实际启动了: {result['strategy_id']}")
            # 清理
            requests.delete(f"{BASE_URL}/api/live/strategies/{result['strategy_id']}")
            return False
        else:
            print(f"✅ 策略正确被拒绝: {result.get('message')}")
            return True
    else:
        print(f"✅ 策略启动请求被拒绝: {response.status_code}")
        return True

def test_strategy_with_mixed_stocks():
    """测试使用混合股票代码的策略（有效+无效）"""
    print("\n=== 测试使用混合股票代码的策略 ===")
    
    # 启动策略，使用混合的股票代码
    strategy_data = {
        'name': '混合股票代码测试',
        'strategy_type': 'bollinger_bands',
        'config': {
            'initial_capital': 100000,
            'paper_trading': True,
            'stock_codes': ['000001.SZ', '', '000002.SZ', 'invalid', '000858.SZ'],  # 混合代码
            'bb_period': 10,
            'bb_std': 1.5
        }
    }
    
    print("启动使用混合股票代码的策略...")
    print(f"配置: {json.dumps(strategy_data, indent=2, ensure_ascii=False)}")
    
    response = requests.post(f"{BASE_URL}/api/live/strategies", json=strategy_data)
    
    if response.status_code == 200:
        result = response.json()
        if result.get('success'):
            strategy_id = result['strategy_id']
            print(f"✅ 策略启动成功: {strategy_id}")
            
            # 检查策略配置是否过滤了无效代码
            response = requests.get(f"{BASE_URL}/api/live/strategies/{strategy_id}")
            if response.status_code == 200:
                strategy = response.json()
                stock_codes = strategy.get('config', {}).get('stock_codes', [])
                print(f"✅ 过滤后的股票代码: {stock_codes}")
                
                # 应该只包含有效的代码
                expected_codes = ['000001.SZ', '000002.SZ', '000858.SZ']
                if set(stock_codes) == set(expected_codes):
                    print(f"✅ 股票代码过滤正确")
                    success = True
                else:
                    print(f"❌ 股票代码过滤不正确，期望: {expected_codes}")
                    success = False
            else:
                print(f"❌ 获取策略详情失败")
                success = False
            
            # 清理
            requests.delete(f"{BASE_URL}/api/live/strategies/{strategy_id}")
            return success
        else:
            print(f"❌ 策略启动失败: {result.get('message')}")
            return False
    else:
        print(f"❌ 策略启动请求失败: {response.status_code}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试策略修复")
    
    tests = [
        ("有效股票代码策略", test_strategy_with_valid_stocks),
        ("无效股票代码策略", test_strategy_with_invalid_stocks),
        ("混合股票代码策略", test_strategy_with_mixed_stocks),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        print(f"🧪 测试: {test_name}")
        print(f"{'='*60}")
        
        try:
            result = test_func()
            if result:
                print(f"✅ {test_name} - 通过")
                passed += 1
            else:
                print(f"❌ {test_name} - 失败")
        except Exception as e:
            print(f"❌ {test_name} - 异常: {e}")
    
    print(f"\n🎯 测试结果总结:")
    print(f"✅ 通过: {passed}/{total} 项测试")
    
    if passed == total:
        print(f"\n🎉 所有修复测试通过！")
        print(f"✅ 股票代码格式验证正常工作")
        print(f"✅ LiveTradingResult对象属性访问修复")
        print(f"✅ 策略配置验证增强")
        print(f"✅ 错误处理机制完善")
    else:
        print(f"\n⚠️ 部分测试失败，需要进一步调试")

if __name__ == "__main__":
    main()
