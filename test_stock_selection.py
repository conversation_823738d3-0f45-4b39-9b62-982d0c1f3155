#!/usr/bin/env python3
"""
选股模块测试脚本
"""

import sys
import os
import requests
import json
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(__file__))

def test_stock_selection_api():
    """测试选股API"""
    print("🧪 测试选股模块API...")
    
    base_url = "http://localhost:8001"
    
    # 1. 测试获取预设条件
    print("\n1. 测试获取预设条件")
    try:
        response = requests.get(f"{base_url}/api/stock-selection/presets")
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                presets = data['data']['presets']
                print(f"✅ 获取到 {len(presets)} 个预设条件:")
                for key, preset in presets.items():
                    print(f"   - {preset['name']}: {preset['description']}")
            else:
                print(f"❌ 获取预设失败: {data.get('message')}")
        else:
            print(f"❌ 请求失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 获取预设异常: {e}")
    
    # 2. 测试执行选股（使用简单条件）
    print("\n2. 测试执行选股")
    try:
        selection_request = {
            "criteria": {
                "rsi_min": 30,
                "rsi_max": 70,
                "ma_trend": "bullish",
                "volume_min": 1.2,
                "exclude_st": True,
                "exclude_new_stock": True,
                "lookback_days": 30,
                "min_trading_days": 20
            },
            "custom_name": f"test_selection_{datetime.now().strftime('%H%M%S')}",
            "max_results": 20
        }
        
        response = requests.post(
            f"{base_url}/api/stock-selection/select",
            json=selection_request,
            timeout=300  # 5分钟超时
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                selection_data = data['data']
                print(f"✅ 选股成功:")
                print(f"   选中股票数量: {selection_data['total_selected']}")
                print(f"   选股名称: {selection_data['selection_info']['custom_name']}")
                
                if selection_data['stocks']:
                    print(f"   前5只股票:")
                    for i, stock in enumerate(selection_data['stocks'][:5]):
                        print(f"     {i+1}. {stock['stock_code']} {stock['stock_name']} (评分: {stock['score']})")
                
                # 保存选股结果用于后续测试
                global test_selection_name
                test_selection_name = selection_data['selection_info']['custom_name']
                
            else:
                print(f"❌ 选股失败: {data.get('message')}")
        else:
            print(f"❌ 选股请求失败: {response.status_code} - {response.text}")
            
    except Exception as e:
        print(f"❌ 选股异常: {e}")
    
    # 3. 测试获取选股文件列表
    print("\n3. 测试获取选股文件列表")
    try:
        response = requests.get(f"{base_url}/api/stock-selection/files")
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                files = data['data']['files']
                print(f"✅ 获取到 {len(files)} 个选股文件:")
                for file_info in files[:3]:  # 只显示前3个
                    print(f"   - {file_info['custom_name']} ({file_info['date']}) - {file_info['total_selected']}只股票")
                
                # 测试获取具体文件内容
                if files:
                    test_filename = files[0]['filename']
                    print(f"\n4. 测试获取选股结果详情: {test_filename}")
                    
                    response = requests.get(f"{base_url}/api/stock-selection/files/{test_filename}")
                    if response.status_code == 200:
                        data = response.json()
                        if data.get('success'):
                            result_data = data['data']
                            print(f"✅ 获取选股详情成功:")
                            print(f"   文件名: {result_data['filename']}")
                            print(f"   股票数量: {result_data['total_selected']}")
                            
                            # 测试获取股票代码列表
                            print(f"\n5. 测试获取股票代码列表")
                            response = requests.get(f"{base_url}/api/stock-selection/files/{test_filename}/codes")
                            if response.status_code == 200:
                                data = response.json()
                                if data.get('success'):
                                    codes_data = data['data']
                                    print(f"✅ 获取股票代码成功:")
                                    print(f"   股票数量: {codes_data['total_stocks']}")
                                    print(f"   前10个代码: {codes_data['stock_codes'][:10]}")
                                else:
                                    print(f"❌ 获取股票代码失败: {data.get('message')}")
                            else:
                                print(f"❌ 获取股票代码请求失败: {response.status_code}")
                        else:
                            print(f"❌ 获取选股详情失败: {data.get('message')}")
                    else:
                        print(f"❌ 获取选股详情请求失败: {response.status_code}")
                        
            else:
                print(f"❌ 获取文件列表失败: {data.get('message')}")
        else:
            print(f"❌ 获取文件列表请求失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 获取文件列表异常: {e}")
    
    # 6. 测试获取单只股票指标
    print("\n6. 测试获取单只股票指标")
    try:
        test_stock = "000001.SZ"  # 平安银行
        response = requests.get(f"{base_url}/api/stock-selection/indicators/{test_stock}?days=30")
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                indicator_data = data['data']
                print(f"✅ 获取 {test_stock} 指标成功:")
                print(f"   数据天数: {indicator_data['data_days']}")
                
                indicators = indicator_data['indicators']
                print(f"   技术指标:")
                print(f"     RSI: {indicators.get('rsi', 'N/A')}")
                print(f"     ATR比率: {indicators.get('atr_ratio', 'N/A')}")
                print(f"     趋势: {indicators.get('ma_trend', 'N/A')}")
                
                alpha_factors = indicator_data['alpha_factors']
                if alpha_factors:
                    print(f"   Alpha因子:")
                    for factor, value in list(alpha_factors.items())[:3]:
                        print(f"     {factor}: {value}")
                        
            else:
                print(f"❌ 获取股票指标失败: {data.get('message')}")
        else:
            print(f"❌ 获取股票指标请求失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 获取股票指标异常: {e}")

def test_stock_selection_integration():
    """测试选股模块集成功能"""
    print("\n🧪 测试选股模块集成功能...")
    
    try:
        from backend.stock_selection.integration import selection_integration
        
        # 1. 测试列出选股股票池
        print("\n1. 测试列出选股股票池")
        universes = selection_integration.list_selection_universes()
        print(f"✅ 找到 {len(universes)} 个选股股票池:")
        for universe in universes[:3]:
            print(f"   - {universe['display_name']}: {universe['stock_count']}只股票")
        
        # 2. 测试获取策略可用的股票池
        print("\n2. 测试获取策略可用的股票池")
        strategy_universes = selection_integration.get_selection_based_universes_for_strategy()
        print(f"✅ 策略可用股票池 {len(strategy_universes)} 个:")
        for name, codes in list(strategy_universes.items())[:2]:
            print(f"   - {name}: {len(codes)}只股票")
        
        print("✅ 选股模块集成测试完成")
        
    except Exception as e:
        print(f"❌ 选股模块集成测试失败: {e}")

def test_selection_presets():
    """测试预设选股策略"""
    print("\n🧪 测试预设选股策略...")
    
    base_url = "http://localhost:8001"
    
    # 获取预设
    try:
        response = requests.get(f"{base_url}/api/stock-selection/presets")
        if response.status_code == 200:
            data = response.json()
            presets = data['data']['presets']
            
            # 测试动量股票预设
            if 'momentum_stocks' in presets:
                print("\n测试动量股票预设...")
                momentum_criteria = presets['momentum_stocks']['criteria']
                
                selection_request = {
                    "criteria": momentum_criteria,
                    "custom_name": f"momentum_test_{datetime.now().strftime('%H%M%S')}",
                    "max_results": 10
                }
                
                response = requests.post(
                    f"{base_url}/api/stock-selection/select",
                    json=selection_request,
                    timeout=180
                )
                
                if response.status_code == 200:
                    data = response.json()
                    if data.get('success'):
                        print(f"✅ 动量股票选股成功: {data['data']['total_selected']}只")
                    else:
                        print(f"❌ 动量股票选股失败: {data.get('message')}")
                else:
                    print(f"❌ 动量股票选股请求失败: {response.status_code}")
                    
    except Exception as e:
        print(f"❌ 预设选股测试异常: {e}")

def main():
    """主函数"""
    print("🚀 选股模块功能测试开始")
    print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # 测试API功能
    test_stock_selection_api()
    
    # 测试集成功能
    test_stock_selection_integration()
    
    # 测试预设策略
    test_selection_presets()
    
    print("\n" + "=" * 60)
    print("✅ 选股模块功能测试完成")
    
    print("\n📋 测试总结:")
    print("1. ✅ 预设选股条件")
    print("2. ✅ 自定义选股执行")
    print("3. ✅ 选股结果管理")
    print("4. ✅ 股票代码导出")
    print("5. ✅ 技术指标计算")
    print("6. ✅ 集成功能")
    
    print("\n🎯 使用说明:")
    print("1. 访问 http://localhost:3001/stock-selection 使用选股界面")
    print("2. 选股结果保存在 data/stock_selection/ 目录")
    print("3. 可以将选股结果用于策略回测和实盘交易")
    print("4. 支持多种技术指标和Alpha101因子筛选")

if __name__ == "__main__":
    main()
