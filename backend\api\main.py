# -*- coding: utf-8 -*-
"""
FastAPI主应用
提供REST API接口
"""

from fastapi import FastAPI, HTTPException, BackgroundTasks, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel
from typing import Dict, Any, List, Optional
import logging
import asyncio
import time
from datetime import datetime
import os
import sys

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from backend.core.config_manager import config_manager, StrategyConfig, BacktestConfig, LiveTradingConfig
from backend.core.strategy_manager import strategy_manager
from backend.data.data_manager import data_manager
from backend.backtest.backtest_engine import backtest_engine
from backend.backtest.simple_backtest_engine import simple_backtest_engine
from backend.live.simple_live_engine import live_engine
from backend.risk.risk_manager import risk_manager
from backend.websocket.live_trading_ws import ws_manager
from backend.storage.backtest_storage import backtest_storage

# 导入选股API
try:
    from backend.api.stock_selection_api import router as stock_selection_router
    STOCK_SELECTION_AVAILABLE = True
except ImportError as e:
    print(f"选股模块导入失败: {e}")
    STOCK_SELECTION_AVAILABLE = False

# 导入股票池API
try:
    from backend.api.stock_pool_routes import router as stock_pool_router
    STOCK_POOL_AVAILABLE = True
except ImportError as e:
    print(f"股票池模块导入失败: {e}")
    STOCK_POOL_AVAILABLE = False
from backend.storage.live_trading_storage import live_trading_storage
from backend.services.multi_strategy_service import multi_strategy_service
from backend.strategies.config_parser import config_parser
from backend.strategies.config_driven_engine import multi_signal_engine
from backend.core.strategy_manager import strategy_manager
# from backend.strategies.base_strategy import strategy_runner  # 暂时注释掉
from backend.trading.live_trader import get_live_trader
from backend.services.position_monitor_service import position_monitor_service
from backend.config.position_monitor_config import position_monitor_config

# 初始化日志系统
from backend.init_logging import init_logging
init_logging()

# 使用统一日志系统
from backend.core.logger import get_api_logger

logger = get_api_logger('main')

# 创建FastAPI应用
app = FastAPI(
    title="CN-Stock Trading Platform",
    description="基于配置驱动的量化交易平台",
    version="1.0.0"
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],  # React开发服务器
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册选股API路由
if STOCK_SELECTION_AVAILABLE:
    app.include_router(stock_selection_router)
    logger.info("✅ 选股模块API已加载")
else:
    logger.warning("⚠️ 选股模块API未加载")

# 注册股票池API路由
if STOCK_POOL_AVAILABLE:
    app.include_router(stock_pool_router)
    logger.info("✅ 股票池模块API已加载")
else:
    logger.warning("⚠️ 股票池模块API未加载")

# 请求模型
class BacktestRequest(BaseModel):
    strategy_name: str
    strategy_config: str
    backtest_config: str
    stock_codes: List[str]

class LiveTradingRequest(BaseModel):
    strategy_name: str
    strategy_config: str
    live_config: str

class DataDownloadRequest(BaseModel):
    stock_codes: List[str]
    start_date: str
    end_date: str

class ConfigUpdateRequest(BaseModel):
    config_data: Dict[str, Any]

# 全局状态
current_backtest_task = None
current_live_trading_task = None

@app.get("/")
async def root():
    """根路径"""
    return {"message": "CN-Stock Trading Platform API"}

# 配置管理API
@app.get("/api/config/strategies")
async def get_strategy_configs():
    """获取所有策略配置 - 不做异常处理"""
    # 直接从 config_manager 获取所有策略配置
    strategies = config_manager.get_all_strategy_configs()

    # 转换为字典格式
    strategies_dict = {}
    for name, config in strategies.items():
        strategies_dict[name] = config.to_dict()

    return {
        "success": True,
        "data": strategies_dict
    }

@app.get("/api/config/strategies/{strategy_name}")
async def get_strategy_config(strategy_name: str):
    """获取指定策略配置"""
    try:
        config = config_manager.get_strategy_config(strategy_name)
        if not config:
            raise HTTPException(status_code=404, detail="策略配置不存在")
        
        return {
            "success": True,
            "data": config.to_dict()
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取策略配置失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/config/strategies/{strategy_name}")
async def update_strategy_config(strategy_name: str, request: ConfigUpdateRequest):
    """更新策略配置"""
    try:
        config = StrategyConfig.from_dict(request.config_data)
        config_manager.update_strategy_config(strategy_name, config)
        
        return {
            "success": True,
            "message": "策略配置更新成功"
        }
    except Exception as e:
        logger.error(f"更新策略配置失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/config/backtest")
async def get_backtest_configs():
    """获取所有回测配置"""
    try:
        configs = config_manager.get_all_backtest_configs()
        return {
            "success": True,
            "data": {name: config.to_dict() for name, config in configs.items()}
        }
    except Exception as e:
        logger.error(f"获取回测配置失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/config/live")
async def get_live_configs():
    """获取所有实盘配置"""
    try:
        configs = config_manager.get_all_live_configs()
        return {
            "success": True,
            "data": {name: config.to_dict() for name, config in configs.items()}
        }
    except Exception as e:
        logger.error(f"获取实盘配置失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# 数据管理API
@app.get("/api/data/stocks")
async def get_stock_list(page: int = 1, page_size: int = 100, limit: int = None):
    """获取股票列表 - 支持分页，不做异常处理

    Args:
        page: 页码，从1开始，默认第1页
        page_size: 每页数量，默认100，最大500
        limit: 总数限制，None表示获取所有股票
    """
    # 限制每页最大数量，避免单次请求过大
    page_size = min(page_size, 500)

    result = data_manager.get_stock_list(page=page, page_size=page_size, limit=limit)
    return {
        "success": True,
        **result  # 包含 data, total, page, page_size, total_pages
    }

# 股票池管理API
@app.get("/api/data/universes")
async def get_stock_universes():
    """获取所有股票池"""
    from backend.core.stock_universe_manager import stock_universe_manager

    universes = stock_universe_manager.list_universes()
    return {
        "success": True,
        "data": universes
    }

@app.get("/api/data/universes/{universe_name}")
async def get_stock_universe(universe_name: str):
    """获取指定股票池详情"""
    from backend.core.stock_universe_manager import stock_universe_manager

    universe = stock_universe_manager.get_universe(universe_name)
    if not universe:
        return {
            "success": False,
            "message": f"股票池 {universe_name} 不存在"
        }

    return {
        "success": True,
        "data": {
            "name": universe.name,
            "display_name": universe.display_name,
            "description": universe.description,
            "universe_type": universe.universe_type.value,
            "stock_codes": universe.stock_codes,
            "stock_count": len(universe.stock_codes),
            "created_time": universe.created_time.isoformat(),
            "updated_time": universe.updated_time.isoformat(),
            "is_active": universe.is_active,
            "auto_update": universe.auto_update
        }
    }

@app.post("/api/data/universes")
async def create_stock_universe(request: dict):
    """创建新股票池"""
    from backend.core.stock_universe_manager import stock_universe_manager, UniverseType

    try:
        name = request.get('name')
        display_name = request.get('display_name')
        description = request.get('description', '')
        universe_type = UniverseType(request.get('universe_type', 'custom'))
        stock_codes = request.get('stock_codes', [])

        if not name or not display_name or not stock_codes:
            return {
                "success": False,
                "message": "缺少必要参数: name, display_name, stock_codes"
            }

        success = stock_universe_manager.create_universe(
            name=name,
            display_name=display_name,
            description=description,
            universe_type=universe_type,
            stock_codes=stock_codes
        )

        if success:
            return {
                "success": True,
                "message": f"股票池 {display_name} 创建成功"
            }
        else:
            return {
                "success": False,
                "message": "股票池创建失败"
            }

    except Exception as e:
        return {
            "success": False,
            "message": f"创建股票池失败: {str(e)}"
        }

@app.get("/api/data/status")
async def get_data_status():
    """获取数据状态 - 不做异常处理"""
    status = data_manager.get_data_status()
    return {
        "success": True,
        "data": status
    }

@app.get("/api/data/account")
async def get_data_account_info():
    """获取账户信息 - 兼容前端调用"""
    # 直接从数据管理器获取账户信息
    account_info = data_manager.get_account_info()
    return {
        "success": True,
        "data": account_info
    }

@app.post("/api/data/download")
async def download_data(request: DataDownloadRequest, background_tasks: BackgroundTasks):
    """下载股票数据"""
    try:
        # 在后台任务中执行下载
        background_tasks.add_task(
            data_manager.download_stock_data,
            request.stock_codes,
            request.start_date,
            request.end_date
        )

        return {
            "success": True,
            "message": "数据下载任务已启动"
        }
    except Exception as e:
        logger.error(f"启动数据下载失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/data/{stock_code}")
async def get_stock_data(stock_code: str, period: str, count: int):
    """获取股票数据 - 不提供默认参数，不做异常处理"""
    df = data_manager.get_stock_data(stock_code, period=period, count=count)

    if df.empty:
        return {
            "success": True,
            "data": [],
            "message": "无数据"
        }

    # 转换为JSON格式
    data = []
    for date, row in df.iterrows():
        data.append({
            "date": date.strftime('%Y-%m-%d') if hasattr(date, 'strftime') else str(date),
            "open": float(row['open']),
            "high": float(row['high']),
            "low": float(row['low']),
            "close": float(row['close']),
            "volume": int(row['volume']),
            "amount": float(row['amount'])
        })

    return {
        "success": True,
        "data": data
    }

# 回测API
@app.post("/api/backtest/start")
async def start_backtest(config: Dict[str, Any]):
    """启动回测 - 支持传统策略和配置驱动策略"""
    try:
        strategy_name = config.get('strategy_name', '')

        # 检查是否是多信号策略
        if strategy_name == 'multi_signal_strategy':
            # 多信号策略回测
            config_id = config.get('config_id', 'default')
            config_name = config.get('config_name', '未命名配置')

            # 使用多信号策略引擎执行回测
            logger.info(f"🚀 启动多信号策略回测: {config_name} (ID: {config_id})")

            # 这里应该调用多信号策略引擎，暂时使用传统引擎
            task_id = await simple_backtest_engine.start_backtest(config)

            return {
                "success": True,
                "data": {
                    "task_id": task_id,
                    "message": f"多信号策略回测已启动: {config_name}",
                    "strategy_type": "multi_signal"
                }
            }
        else:
            # 传统策略回测
            task_id = await simple_backtest_engine.start_backtest(config)

            return {
                "success": True,
                "data": {
                    "task_id": task_id,
                    "message": "传统策略回测已启动",
                    "strategy_type": "traditional"
                }
            }
    except Exception as e:
        logger.error(f"❌ 启动回测失败: {e}")
        raise HTTPException(status_code=500, detail=f"启动回测失败: {str(e)}")

@app.get("/api/backtest/status")
async def get_backtest_status():
    """获取回测状态 - 使用新的回测引擎"""
    tasks = simple_backtest_engine.get_all_tasks()
    running_tasks = tasks.get('running', [])
    completed_tasks = tasks.get('completed', [])

    if running_tasks:
        # 返回最新的运行任务状态
        latest_task = running_tasks[-1]
        return {
            "success": True,
            "data": {
                "status": latest_task.get('status', 'unknown'),
                "progress": latest_task.get('progress', 0),
                "message": f"回测进行中 ({latest_task.get('progress', 0)}%)",
                "task_id": latest_task.get('task_id')
            }
        }
    elif completed_tasks:
        # 返回最新的完成任务状态
        latest_task = completed_tasks[-1]
        status = latest_task.get('status', 'completed')
        progress = latest_task.get('progress', 100)

        if status == 'completed':
            message = f"回测已完成 ({progress}%)"
        elif status == 'failed':
            message = f"回测失败: {latest_task.get('error', '未知错误')}"
        else:
            message = f"回测状态: {status}"

        return {
            "success": True,
            "data": {
                "status": status,
                "progress": progress,
                "message": message,
                "task_id": latest_task.get('task_id')
            }
        }
    else:
        return {
            "success": True,
            "data": {
                "status": "idle",
                "message": "无回测任务"
            }
        }
@app.post("/api/backtest/stop")
async def stop_backtest():
    """停止回测"""
    try:
        # 这里需要传入task_id，暂时停止所有运行中的任务
        tasks = backtest_engine.get_all_tasks()
        running_tasks = tasks.get('running', [])

        if running_tasks:
            for task in running_tasks:
                backtest_engine.stop_backtest(task.get('task_id'))

            return {
                "success": True,
                "message": "回测已停止"
            }
        else:
            return {
                "success": True,
                "message": "无运行中的回测任务"
            }
    except Exception as e:
        logger.error(f"停止回测失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

def safe_json_convert(obj):
    """安全的JSON转换，处理NaN和无穷大值"""
    import math
    import numpy as np
    from dataclasses import asdict, is_dataclass

    if is_dataclass(obj):
        obj = asdict(obj)

    if isinstance(obj, dict):
        return {k: safe_json_convert(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [safe_json_convert(item) for item in obj]
    elif isinstance(obj, (float, np.floating)):
        if math.isnan(obj) or math.isinf(obj):
            return None
        return float(obj)
    elif isinstance(obj, (np.integer, np.int64, np.int32)):
        return int(obj)
    elif isinstance(obj, np.ndarray):
        return safe_json_convert(obj.tolist())
    else:
        return obj

@app.get("/api/backtest/results/{task_id}")
async def get_backtest_results(task_id: str):
    """获取回测结果 - 使用新的回测引擎"""
    try:
        result = simple_backtest_engine.get_result(task_id)
        if result:
            # 安全地将dataclass转换为字典，处理NaN和无穷大值
            safe_data = safe_json_convert(result)
            return {
                "success": True,
                "data": safe_data
            }
        else:
            raise HTTPException(status_code=404, detail="回测结果不存在")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取回测结果失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/backtest/strategies")
async def get_available_strategies():
    """获取可用的回测策略列表（混合模式：代码注册 + 配置文件参数）"""
    try:
        # 确保所有策略都被导入和注册
        from backend.strategies import bollinger_bands_strategy
        from backend.strategies import multi_signal_strategy_bt

        # 从代码注册获取策略类信息
        from backend.strategies.base_strategy_new import list_strategies
        strategies_list = list_strategies()

        # 从配置文件获取参数信息
        strategies_config = strategy_manager.get_all_strategies_info()

        all_strategies = []
        for strategy_info in strategies_list:
            strategy_name = strategy_info["name"]

            # 从配置文件获取参数（如果存在）
            config_info = strategies_config.get(strategy_name, {})

            strategy_data = {
                "name": strategy_name,
                "display_name": strategy_info["display_name"],
                "description": strategy_info["description"],
                "category": config_info.get("category", "traditional"),
                "type": "multi_signal" if "multi_signal" in strategy_name else "traditional",
                "parameters": config_info.get("parameters", {})
            }
            all_strategies.append(strategy_data)

            # 记录参数来源
            param_source = "配置文件" if config_info.get("parameters") else "无参数"
            logger.info(f"✅ {strategy_info['display_name']} ({strategy_name}) - 参数来源: {param_source}")

        logger.info(f"📊 返回 {len(all_strategies)} 个策略")

        return {
            "success": True,
            "data": all_strategies
        }
    except Exception as e:
        logger.error(f"获取策略列表失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# 旧的实盘交易API已移除，使用新的QMT Store模式API

@app.get("/api/live/account")
async def get_account_info():
    """获取账户信息 - 不做异常处理"""
    # 直接从数据管理器获取账户信息
    account_info = data_manager.get_account_info()
    return {
        "success": True,
        "data": account_info
    }

# ==================== 持仓监控相关接口 ====================

@app.get("/api/position-monitor/status")
async def get_position_monitor_status():
    """获取持仓监控状态"""
    status = position_monitor_service.get_status()
    return {
        "success": True,
        "data": status
    }

@app.post("/api/position-monitor/start")
async def start_position_monitor():
    """启动持仓监控"""
    await position_monitor_service.start_monitoring()
    return {
        "success": True,
        "message": "持仓监控已启动"
    }

@app.post("/api/position-monitor/stop")
async def stop_position_monitor():
    """停止持仓监控"""
    await position_monitor_service.stop_monitoring()
    return {
        "success": True,
        "message": "持仓监控已停止"
    }

@app.get("/api/position-monitor/config")
async def get_position_monitor_config():
    """获取持仓监控配置"""
    config = position_monitor_config.config.get('position_monitor', {})
    return {
        "success": True,
        "config": config
    }

@app.post("/api/position-monitor/config")
async def update_position_monitor_config(config_data: Dict[str, Any]):
    """更新持仓监控配置"""
    position_monitor_config.update({'position_monitor': config_data})
    return {
        "success": True,
        "message": "配置已更新"
    }

@app.get("/api/position-monitor/signals")
async def get_position_monitor_signals(limit: int = 100):
    """获取止损信号历史"""
    from backend.strategies.position_monitor import position_monitor
    signals = position_monitor.signals_history[-limit:] if limit > 0 else position_monitor.signals_history
    # 按时间倒序排列，最新的在最上面
    return list(reversed(signals))

@app.post("/api/position-monitor/check")
async def manual_position_check():
    """手动检查持仓"""
    signals = await position_monitor_service.manual_check()
    return {
        "signals": signals,
        "count": len(signals),
        "message": f"检查完成，发现{len(signals)}个止损信号"
    }

@app.get("/api/position-monitor/trade-logs")
async def get_trade_logs(limit: int = 20):
    """获取交易日志"""
    return position_monitor_service.get_trade_logs(limit)

@app.get("/api/position-monitor/pending-orders")
async def get_pending_orders():
    """获取待处理订单"""
    return {
        "success": True,
        "pending_orders": list(position_monitor_service.pending_orders.values()),
        "count": len(position_monitor_service.pending_orders)
    }

@app.post("/api/position-monitor/check-orders")
async def check_orders():
    """手动检查订单状态"""
    position_monitor_service._check_pending_orders()
    return {
        "success": True,
        "message": "订单状态检查完成",
        "pending_count": len(position_monitor_service.pending_orders),
        "pending_orders": list(position_monitor_service.pending_orders.values())
    }

@app.post("/api/position-monitor/cancel-order/{order_id}")
async def cancel_order(order_id: int):
    """取消订单"""
    result = position_monitor_service.cancel_pending_order(order_id)
    return result

@app.get("/api/position-monitor/dashboard")
async def get_monitor_dashboard():
    """获取监控仪表板数据"""
    try:
        # 获取持仓数据
        account_info = data_manager.get_account_info()
        positions = account_info.get('positions', [])

        # 获取止损信号
        from backend.strategies.position_monitor import position_monitor
        signals = position_monitor.signals_history[-10:] if position_monitor.signals_history else []

        # 获取待处理订单
        pending_orders = list(position_monitor_service.pending_orders.values())

        # 获取监控状态
        monitor_status = position_monitor_service.get_status()

        # 计算统计数据
        total_positions = len(positions)
        total_signals = len(position_monitor.signals_history)
        total_pending = len(pending_orders)

        # 计算盈亏统计
        profit_positions = sum(1 for p in positions if p.get('pnl_ratio', 0) > 0)
        loss_positions = sum(1 for p in positions if p.get('pnl_ratio', 0) < 0)

        return {
            "success": True,
            "data": {
                "positions": positions,
                "recent_signals": list(reversed(signals)),  # 最新的在前
                "pending_orders": pending_orders,
                "monitor_status": monitor_status,
                "statistics": {
                    "total_positions": total_positions,
                    "profit_positions": profit_positions,
                    "loss_positions": loss_positions,
                    "total_signals": total_signals,
                    "total_pending": total_pending,
                    "is_monitoring": monitor_status.get('is_running', False)
                },
                "last_update": time.time()
            }
        }

    except Exception as e:
        logger.error(f"获取监控仪表板数据失败: {e}")
        return {
            "success": False,
            "message": f"获取数据失败: {str(e)}"
        }

@app.get("/api/position-monitor/alerts/latest")
async def get_latest_alerts(since: float = None):
    """获取最新的警告信号"""
    try:
        from backend.strategies.position_monitor import position_monitor

        all_signals = position_monitor.signals_history

        if since:
            # 只返回指定时间之后的信号
            import time
            filtered_signals = []
            for signal in all_signals:
                try:
                    signal_time = time.mktime(time.strptime(signal['timestamp'], '%Y-%m-%dT%H:%M:%S.%f'))
                    if signal_time > since:
                        filtered_signals.append(signal)
                except:
                    # 如果时间解析失败，包含这个信号
                    filtered_signals.append(signal)

            return {
                "success": True,
                "data": {
                    "signals": list(reversed(filtered_signals)),  # 最新的在前
                    "count": len(filtered_signals),
                    "has_new": len(filtered_signals) > 0
                }
            }
        else:
            # 返回最近10个信号
            recent_signals = all_signals[-10:] if all_signals else []
            return {
                "success": True,
                "data": {
                    "signals": list(reversed(recent_signals)),
                    "count": len(recent_signals),
                    "has_new": True
                }
            }

    except Exception as e:
        logger.error(f"获取最新警告失败: {e}")
        return {
            "success": False,
            "message": f"获取警告失败: {str(e)}"
        }

# 回测历史管理API
@app.get("/api/backtest/history")
async def get_backtest_history(
    strategy_name: Optional[str] = None,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    limit: int = 20,
    offset: int = 0
):
    """获取回测历史记录"""
    try:
        result = backtest_storage.get_results_list(
            strategy_name=strategy_name,
            start_date=start_date,
            end_date=end_date,
            limit=limit,
            offset=offset
        )
        # 安全处理可能的NaN值
        safe_result = safe_json_convert(result)
        return safe_result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取回测历史失败: {str(e)}")

@app.get("/api/backtest/result/{task_id}")
async def get_backtest_result_detail(task_id: str):
    """获取回测结果详情"""
    try:
        result = backtest_storage.get_result(task_id)
        if not result:
            raise HTTPException(status_code=404, detail="回测结果不存在")
        # 安全处理可能的NaN值
        safe_result = safe_json_convert(result)
        return safe_result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取回测结果失败: {str(e)}")

@app.delete("/api/backtest/result/{task_id}")
async def delete_backtest_result(task_id: str):
    """删除回测结果"""
    try:
        success = backtest_storage.delete_result(task_id)
        if not success:
            raise HTTPException(status_code=404, detail="回测结果不存在或删除失败")
        return {"message": "回测结果删除成功"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除回测结果失败: {str(e)}")

@app.post("/api/backtest/export/{task_id}")
async def export_backtest_result(task_id: str, format: str = "json"):
    """导出回测结果"""
    try:
        if format not in ["json", "csv", "excel"]:
            raise HTTPException(status_code=400, detail="不支持的导出格式")

        file_path = backtest_storage.export_result(task_id, format)
        if not file_path:
            raise HTTPException(status_code=404, detail="回测结果不存在或导出失败")

        return {"message": "导出成功", "file_path": file_path}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"导出回测结果失败: {str(e)}")

@app.get("/api/backtest/compare")
async def compare_backtest_results(task_ids: str):
    """对比多个回测结果"""
    try:
        task_id_list = task_ids.split(',')
        if len(task_id_list) < 2:
            raise HTTPException(status_code=400, detail="至少需要2个回测结果进行对比")

        results = []
        for task_id in task_id_list:
            result = backtest_storage.get_result(task_id.strip())
            if result:
                results.append(result)

        if len(results) < 2:
            raise HTTPException(status_code=404, detail="找不到足够的回测结果进行对比")

        # 安全处理可能的NaN值
        safe_results = safe_json_convert({"results": results, "count": len(results)})
        return safe_results
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"对比回测结果失败: {str(e)}")

# 多策略实盘交易API
@app.get("/api/live/strategies")
async def get_running_strategies():
    """获取运行中的策略列表"""
    try:
        strategies = multi_strategy_service.get_running_strategies()
        return strategies
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取策略列表失败: {str(e)}")

@app.post("/api/live/strategies")
async def start_strategy(strategy_data: dict):
    """启动新策略"""
    try:
        # 验证策略数据
        required_fields = ['name', 'strategy_type']
        for field in required_fields:
            if field not in strategy_data:
                raise HTTPException(status_code=400, detail=f"缺少必需字段: {field}")

        result = await multi_strategy_service.start_strategy(strategy_data)

        if result['success']:
            return result
        else:
            raise HTTPException(status_code=400, detail=result['message'])

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"启动策略失败: {str(e)}")

@app.delete("/api/live/strategies/{strategy_id}")
async def stop_strategy(strategy_id: str):
    """停止策略"""
    try:
        result = await multi_strategy_service.stop_strategy(strategy_id)

        if result['success']:
            return result
        else:
            raise HTTPException(status_code=404, detail=result['message'])

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"停止策略失败: {str(e)}")

@app.post("/api/live/strategies/recover")
async def recover_strategies():
    """恢复之前运行的策略"""
    try:
        result = await multi_strategy_service.recover_strategies()

        if result['success']:
            return result
        else:
            raise HTTPException(status_code=500, detail=result['message'])

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"恢复策略失败: {str(e)}")

@app.get("/api/live/strategies/{strategy_id}")
async def get_strategy_detail(strategy_id: str):
    """获取策略详情"""
    try:
        strategy_detail = multi_strategy_service.get_strategy_detail(strategy_id)

        if strategy_detail:
            return strategy_detail
        else:
            raise HTTPException(status_code=404, detail="策略不存在")

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取策略详情失败: {str(e)}")

# 配置驱动策略管理API
@app.get("/api/config/strategies")
async def get_strategy_configs():
    """获取所有策略配置"""
    try:
        # 这里应该从数据库或文件系统获取策略配置列表
        # 暂时返回模拟数据
        strategies = [
            {
                "id": "ma_bollinger_001",
                "name": "ma_bollinger_strategy",
                "display_name": "均线布林线策略",
                "description": "结合移动平均线和布林线的趋势跟踪策略",
                "category": "trend_following",
                "version": "1.0.0",
                "author": "System",
                "valid": True,
                "updated_at": "2024-01-15T10:30:00",
                "config": {
                    "buy_signals": 2,
                    "sell_signals": 2,
                    "risk_management": True
                }
            },
            {
                "id": "momentum_002",
                "name": "simple_momentum_strategy",
                "display_name": "简单动量策略",
                "description": "基于价格动量和成交量的简单策略",
                "category": "momentum",
                "version": "1.0.0",
                "author": "System",
                "valid": True,
                "updated_at": "2024-01-15T11:00:00",
                "config": {
                    "buy_signals": 1,
                    "sell_signals": 1,
                    "risk_management": True
                }
            }
        ]
        return strategies
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取策略配置失败: {str(e)}")

@app.get("/api/config/strategy-templates")
async def get_strategy_templates():
    """获取策略模板列表"""
    try:
        templates = config_parser.list_template_configs()
        return templates
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取策略模板失败: {str(e)}")

@app.post("/api/config/strategies")
async def create_strategy_config(strategy_data: dict):
    """创建新的策略配置"""
    try:
        # 验证必需字段
        required_fields = ['name', 'display_name', 'description']
        for field in required_fields:
            if field not in strategy_data:
                raise HTTPException(status_code=400, detail=f"缺少必需字段: {field}")

        # 创建基础策略配置
        config_template = {
            "name": strategy_data['name'],
            "display_name": strategy_data['display_name'],
            "description": strategy_data['description'],
            "version": strategy_data.get('version', '1.0.0'),
            "author": strategy_data.get('author', 'User'),
            "category": strategy_data.get('category', 'custom'),
            "buy_signals": [],
            "sell_signals": [],
            "risk_management": {
                "stop_loss": 0.05,
                "take_profit": 0.15,
                "max_positions": 5,
                "position_size": 0.2,
                "risk_per_trade": 0.02
            },
            "universe": [],
            "rebalance_frequency": "daily",
            "benchmark": "000300.SH",
            "indicators": {},
            "custom_params": {}
        }

        # 这里应该保存到数据库或文件系统
        logger.info(f"📝 创建策略配置: {strategy_data['name']}")

        return {
            "success": True,
            "message": f"策略配置 {strategy_data['display_name']} 创建成功",
            "strategy_id": f"{strategy_data['name']}_{int(time.time())}"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建策略配置失败: {str(e)}")

@app.delete("/api/config/strategies/{strategy_id}")
async def delete_strategy_config(strategy_id: str):
    """删除策略配置"""
    try:
        # 这里应该从数据库或文件系统删除策略配置
        logger.info(f"🗑️ 删除策略配置: {strategy_id}")

        return {
            "success": True,
            "message": f"策略配置 {strategy_id} 删除成功"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除策略配置失败: {str(e)}")

@app.get("/api/config/strategies/{strategy_id}/validate")
async def validate_strategy_config(strategy_id: str):
    """验证策略配置"""
    try:
        # 这里应该验证策略配置的有效性
        # 暂时返回成功
        logger.info(f"✅ 验证策略配置: {strategy_id}")

        return {
            "valid": True,
            "message": "策略配置验证通过",
            "details": {
                "buy_signals": "有效",
                "sell_signals": "有效",
                "risk_management": "有效",
                "indicators": "有效"
            }
        }
    except Exception as e:
        return {
            "valid": False,
            "message": f"策略配置验证失败: {str(e)}",
            "details": {}
        }

@app.post("/api/config/strategies/{strategy_id}/backtest")
async def run_config_strategy_backtest(strategy_id: str, backtest_params: dict):
    """运行配置驱动策略回测"""
    try:
        # 这里应该使用配置驱动引擎执行回测
        task_id = f"config_backtest_{strategy_id}_{int(time.time())}"

        logger.info(f"🚀 启动配置驱动策略回测: {strategy_id}")

        # 暂时返回任务ID
        return {
            "success": True,
            "task_id": task_id,
            "message": f"配置驱动策略回测启动成功"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"启动策略回测失败: {str(e)}")

@app.get("/api/config/strategies/{strategy_id}/export")
async def export_strategy_config(strategy_id: str):
    """导出策略配置"""
    try:
        # 这里应该从数据库或文件系统获取完整的策略配置
        # 暂时返回模拟配置
        config_data = {
            "name": "exported_strategy",
            "display_name": "导出的策略",
            "description": "从系统导出的策略配置",
            "version": "1.0.0",
            "author": "System",
            "category": "custom",
            "buy_signals": [],
            "sell_signals": [],
            "risk_management": {
                "stop_loss": 0.05,
                "take_profit": 0.15,
                "max_positions": 5,
                "position_size": 0.2
            }
        }

        logger.info(f"📤 导出策略配置: {strategy_id}")
        return config_data
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"导出策略配置失败: {str(e)}")

@app.post("/api/config/strategies/from-template")
async def create_strategy_from_template(template_data: dict):
    """从模板创建策略"""
    try:
        template_path = template_data.get('template_path')
        if not template_path:
            raise HTTPException(status_code=400, detail="缺少模板路径")

        # 使用配置解析器从模板创建策略
        strategy = config_parser.create_strategy_instance(template_path)

        # 更新策略信息
        if 'name' in template_data:
            strategy.config_schema.name = template_data['name']
        if 'display_name' in template_data:
            strategy.config_schema.display_name = template_data['display_name']

        logger.info(f"📋 从模板创建策略: {template_data.get('name', 'unknown')}")

        return {
            "success": True,
            "message": "从模板创建策略成功",
            "strategy_id": f"{template_data.get('name', 'template')}_{int(time.time())}"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"从模板创建策略失败: {str(e)}")

# ==================== 实盘交易接口 ====================

class LiveTradingRequest(BaseModel):
    """实盘交易请求"""
    strategy_name: str
    initial_capital: float = 100000.0
    commission: float = 0.001
    stock_codes: List[str] = []
    paper_trading: bool = True  # 默认纸上交易
    # 策略参数（动态）
    strategy_params: Dict[str, Any] = {}

@app.post("/api/live/start")
async def start_live_trading(request: LiveTradingRequest):
    """启动实盘交易"""
    try:
        logger.info(f"🚀 启动实盘交易: {request.strategy_name}")
        logger.info(f"📋 请求参数: stock_codes={request.stock_codes}, paper_trading={request.paper_trading}")

        # 构建配置（复用回测配置格式）
        config = {
            'strategy_name': request.strategy_name,
            'initial_capital': request.initial_capital,
            'commission': request.commission,
            'stock_codes': request.stock_codes,
            'paper_trading': request.paper_trading,
            **request.strategy_params  # 合并策略参数
        }

        logger.info(f"📋 配置参数: {config}")

        # 启动实盘交易
        task_id = await live_engine.start_live_trading(config)

        return {
            "success": True,
            "task_id": task_id,
            "message": "实盘交易启动成功"
        }

    except Exception as e:
        logger.error(f"❌ 启动实盘交易失败: {e}")
        raise HTTPException(status_code=500, detail=f"启动实盘交易失败: {str(e)}")

@app.post("/api/live/stop/{task_id}")
async def stop_live_trading(task_id: str):
    """暂停实盘交易（原停止接口改为暂停）"""
    try:
        logger.info(f"⏸️ 暂停实盘交易: {task_id}")

        success = await live_engine.pause_live_trading(task_id)

        if success:
            return {
                "success": True,
                "message": "实盘交易暂停成功"
            }
        else:
            raise HTTPException(status_code=404, detail="任务不存在")

    except Exception as e:
        logger.error(f"❌ 暂停实盘交易失败: {e}")
        raise HTTPException(status_code=500, detail=f"暂停实盘交易失败: {str(e)}")

@app.post("/api/live/pause/{task_id}")
async def pause_live_trading_api(task_id: str):
    """暂停实盘交易"""
    try:
        logger.info(f"⏸️ 暂停实盘交易: {task_id}")

        success = await live_engine.pause_live_trading(task_id)

        if success:
            return {
                "success": True,
                "message": "实盘交易暂停成功"
            }
        else:
            raise HTTPException(status_code=404, detail="任务不存在")

    except Exception as e:
        logger.error(f"❌ 暂停实盘交易失败: {e}")
        raise HTTPException(status_code=500, detail=f"暂停实盘交易失败: {str(e)}")

@app.post("/api/live/resume/{task_id}")
async def resume_live_trading_api(task_id: str):
    """恢复实盘交易"""
    try:
        logger.info(f"▶️ 恢复实盘交易: {task_id}")

        success = await live_engine.resume_live_trading(task_id)

        if success:
            return {
                "success": True,
                "message": "实盘交易恢复成功"
            }
        else:
            raise HTTPException(status_code=404, detail="任务不存在")

    except Exception as e:
        logger.error(f"❌ 恢复实盘交易失败: {e}")
        raise HTTPException(status_code=500, detail=f"恢复实盘交易失败: {str(e)}")

@app.delete("/api/live/delete/{task_id}")
async def delete_live_trading_api(task_id: str):
    """删除实盘交易策略"""
    try:
        logger.info(f"🗑️ 删除实盘交易: {task_id}")

        success = await live_engine.delete_live_trading(task_id)

        if success:
            return {
                "success": True,
                "message": "实盘交易删除成功"
            }
        else:
            raise HTTPException(status_code=404, detail="任务不存在")

    except Exception as e:
        logger.error(f"❌ 删除实盘交易失败: {e}")
        raise HTTPException(status_code=500, detail=f"删除实盘交易失败: {str(e)}")

@app.post("/api/live/restart/{task_id}")
async def restart_live_trading(task_id: str):
    """重启实盘交易"""
    try:
        success = live_engine.restart_live_trading(task_id)

        if success:
            return {
                "success": True,
                "message": "实盘交易已重启"
            }
        else:
            raise HTTPException(status_code=404, detail="任务不存在或无法重启")

    except Exception as e:
        logger.error(f"❌ 重启实盘交易失败: {e}")
        raise HTTPException(status_code=500, detail=f"重启实盘交易失败: {str(e)}")

@app.delete("/api/live/delete/{task_id}")
async def delete_live_trading(task_id: str):
    """删除实盘交易记录"""
    try:
        success = live_engine.delete_live_trading(task_id)

        if success:
            return {
                "success": True,
                "message": "实盘交易记录已删除"
            }
        else:
            raise HTTPException(status_code=404, detail="任务不存在")

    except Exception as e:
        logger.error(f"❌ 删除实盘交易失败: {e}")
        raise HTTPException(status_code=500, detail=f"删除实盘交易失败: {str(e)}")

@app.get("/api/live/result/{task_id}")
async def get_live_trading_result(task_id: str):
    """获取实盘交易结果"""
    try:
        result = live_engine.get_live_trading_result(task_id)

        if result:
            return {
                "success": True,
                "data": {
                    "task_id": result.task_id,
                    "strategy_name": result.strategy_name,
                    "start_time": result.start_time,
                    "current_time": result.current_time,
                    "initial_capital": result.initial_capital,
                    "current_capital": result.current_capital,
                    "total_return": result.total_return,
                    "positions": result.positions,
                    "orders": result.orders,
                    "status": result.status,
                    "created_at": result.created_at,
                    "paper_trading": result.paper_trading
                }
            }
        else:
            raise HTTPException(status_code=404, detail="任务不存在")

    except Exception as e:
        logger.error(f"❌ 获取实盘交易结果失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取实盘交易结果失败: {str(e)}")

@app.get("/api/live/results")
async def get_all_live_trading_results():
    """获取所有实盘交易结果"""
    try:
        results = live_engine.get_all_live_trading_results()

        return {
            "success": True,
            "data": [
                {
                    "task_id": result.task_id,
                    "strategy_name": result.strategy_name,
                    "start_time": result.start_time,
                    "current_time": result.current_time,
                    "initial_capital": result.initial_capital,
                    "current_capital": result.current_capital,
                    "total_return": result.total_return,
                    "positions": result.positions,
                    "orders": result.orders,
                    "status": result.status,
                    "created_at": result.created_at,
                    "paper_trading": result.paper_trading
                }
                for result in results
            ]
        }

    except Exception as e:
        logger.error(f"❌ 获取实盘交易结果列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取实盘交易结果列表失败: {str(e)}")

@app.get("/api/live/running")
async def get_running_strategies():
    """获取正在运行的策略列表"""
    try:
        running_strategies = live_engine.get_running_strategies()

        return {
            "success": True,
            "data": running_strategies
        }

    except Exception as e:
        logger.error(f"❌ 获取运行中策略失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取运行中策略失败: {str(e)}")

# ==================== WebSocket接口 ====================

@app.websocket("/ws/live-trading/{client_id}")
async def websocket_endpoint(websocket: WebSocket, client_id: str):
    """实盘交易WebSocket端点"""
    await ws_manager.connect(websocket, client_id)
    try:
        while True:
            data = await websocket.receive_text()
            await ws_manager.handle_message(client_id, data)
    except WebSocketDisconnect:
        await ws_manager.disconnect(client_id)

# ==================== 风险管理接口 ====================

@app.get("/api/risk/metrics")
async def get_risk_metrics(strategy_id: str = None):
    """获取风险指标"""
    try:
        metrics = risk_manager.get_risk_metrics(strategy_id)
        return {
            "success": True,
            "data": metrics
        }
    except Exception as e:
        logger.error(f"❌ 获取风险指标失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取风险指标失败: {str(e)}")

@app.get("/api/risk/alerts")
async def get_risk_alerts(strategy_id: str = None, level: str = None):
    """获取风险告警"""
    try:
        from backend.risk.risk_manager import RiskLevel

        risk_level = None
        if level:
            try:
                risk_level = RiskLevel(level)
            except ValueError:
                raise HTTPException(status_code=400, detail=f"无效的风险级别: {level}")

        alerts = risk_manager.get_risk_alerts(strategy_id, risk_level)
        return {
            "success": True,
            "data": alerts
        }
    except Exception as e:
        logger.error(f"❌ 获取风险告警失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取风险告警失败: {str(e)}")

@app.post("/api/risk/clear-alerts")
async def clear_risk_alerts(hours: int = 24):
    """清理旧的风险告警"""
    try:
        risk_manager.clear_old_alerts(hours)
        return {
            "success": True,
            "message": f"已清理{hours}小时前的告警"
        }
    except Exception as e:
        logger.error(f"❌ 清理风险告警失败: {e}")
        raise HTTPException(status_code=500, detail=f"清理风险告警失败: {str(e)}")

# ==================== 实盘交易历史相关接口 ====================

@app.get("/api/live/history")
async def get_live_trading_history(limit: int = 50):
    """获取实盘交易历史"""
    try:
        histories = await live_trading_storage.load_strategy_history(limit=limit)
        return {
            "success": True,
            "data": histories,
            "count": len(histories)
        }
    except Exception as e:
        logger.error(f"❌ 获取实盘交易历史失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取实盘交易历史失败: {str(e)}")

@app.get("/api/live/statistics")
async def get_live_trading_statistics():
    """获取实盘交易统计信息"""
    try:
        stats = await live_trading_storage.get_strategy_statistics()
        return {
            "success": True,
            "data": stats
        }
    except Exception as e:
        logger.error(f"❌ 获取实盘交易统计失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取实盘交易统计失败: {str(e)}")

@app.get("/api/live/history/{task_id}")
async def get_strategy_detail(task_id: str):
    """获取特定策略的详细历史"""
    try:
        # 从文件加载策略详情
        import json
        from pathlib import Path

        history_file = Path("data/live_trading/history") / f"{task_id}.json"
        if not history_file.exists():
            raise HTTPException(status_code=404, detail="策略历史不存在")

        with open(history_file, 'r', encoding='utf-8') as f:
            history_data = json.load(f)

        return {
            "success": True,
            "data": history_data
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ 获取策略详情失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取策略详情失败: {str(e)}")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
