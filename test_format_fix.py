#!/usr/bin/env python3
"""
测试格式化错误修复
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

def test_none_format():
    """测试None值格式化问题"""
    
    print("=== 测试None值格式化问题 ===")
    
    # 测试1: 直接格式化None值
    try:
        none_value = None
        result = f"值: {none_value:.2f}"
        print(f"❌ 测试1失败: 应该抛出异常，但得到: {result}")
    except Exception as e:
        print(f"✅ 测试1通过: 正确抛出异常 - {e}")
    
    # 测试2: 安全格式化
    try:
        none_value = None
        safe_value = none_value if none_value is not None else 0
        result = f"值: {safe_value:.2f}"
        print(f"✅ 测试2通过: 安全格式化 - {result}")
    except Exception as e:
        print(f"❌ 测试2失败: {e}")
    
    # 测试3: 模拟backtrader对象
    class MockOrder:
        def __init__(self, size=None, price=None):
            self.executed = MockExecuted(size, price) if size is not None else None
    
    class MockExecuted:
        def __init__(self, size=None, price=None):
            self.size = size
            self.price = price
    
    class MockTrade:
        def __init__(self, pnl=None, pnlcomm=None, value=None):
            self.pnl = pnl
            self.pnlcomm = pnlcomm
            self.value = value
            self.isclosed = True
    
    # 测试订单格式化
    print("\n=== 测试订单格式化 ===")
    
    # 正常订单
    order1 = MockOrder(100, 50.25)
    size = order1.executed.size if (order1.executed and order1.executed.size is not None) else 0
    price = order1.executed.price if (order1.executed and order1.executed.price is not None) else 0
    print(f"✅ 正常订单: {size}股 @{price:.2f}")
    
    # None订单
    order2 = MockOrder(None, None)
    size = order2.executed.size if (order2.executed and order2.executed.size is not None) else 0
    price = order2.executed.price if (order2.executed and order2.executed.price is not None) else 0
    print(f"✅ None订单: {size}股 @{price:.2f}")
    
    # 无executed的订单
    order3 = MockOrder()
    order3.executed = None
    size = order3.executed.size if (order3.executed and order3.executed.size is not None) else 0
    price = order3.executed.price if (order3.executed and order3.executed.price is not None) else 0
    print(f"✅ 无executed订单: {size}股 @{price:.2f}")
    
    # 测试交易格式化
    print("\n=== 测试交易格式化 ===")
    
    # 正常交易
    trade1 = MockTrade(1000.50, 995.25, 50000)
    profit = trade1.pnl if trade1.pnl is not None else 0
    pnlcomm = trade1.pnlcomm if trade1.pnlcomm is not None else 0
    value = trade1.value if trade1.value is not None else 0
    profit_rate = (pnlcomm / value * 100) if value > 0 else 0
    print(f"✅ 正常交易: 盈亏 {profit:.2f} ({profit_rate:.2f}%)")
    
    # None交易
    trade2 = MockTrade(None, None, None)
    profit = trade2.pnl if trade2.pnl is not None else 0
    pnlcomm = trade2.pnlcomm if trade2.pnlcomm is not None else 0
    value = trade2.value if trade2.value is not None else 0
    profit_rate = (pnlcomm / value * 100) if value > 0 else 0
    print(f"✅ None交易: 盈亏 {profit:.2f} ({profit_rate:.2f}%)")
    
    # 零值交易
    trade3 = MockTrade(0, 0, 0)
    profit = trade3.pnl if trade3.pnl is not None else 0
    pnlcomm = trade3.pnlcomm if trade3.pnlcomm is not None else 0
    value = trade3.value if trade3.value is not None else 0
    profit_rate = (pnlcomm / value * 100) if value > 0 else 0
    print(f"✅ 零值交易: 盈亏 {profit:.2f} ({profit_rate:.2f}%)")

def test_price_format():
    """测试价格格式化"""
    
    print("\n=== 测试价格格式化 ===")
    
    class MockData:
        def __init__(self, close_price=None):
            self.close = [close_price]
    
    # 正常价格
    data1 = MockData(45.67)
    current_price = data1.close[0] if data1.close[0] is not None else 0
    print(f"✅ 正常价格: @{current_price:.2f}")
    
    # None价格
    data2 = MockData(None)
    current_price = data2.close[0] if data2.close[0] is not None else 0
    print(f"✅ None价格: @{current_price:.2f}")
    
    # 零价格
    data3 = MockData(0)
    current_price = data3.close[0] if data3.close[0] is not None else 0
    print(f"✅ 零价格: @{current_price:.2f}")

if __name__ == "__main__":
    test_none_format()
    test_price_format()
    print("\n🎉 所有测试完成！")
