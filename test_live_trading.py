#!/usr/bin/env python3
"""
测试实时交易系统
"""

import asyncio
import requests
import json
import time
from datetime import datetime

def test_paper_trading():
    """测试纸面交易"""
    print("🧪 测试纸面交易模式")
    print("=" * 50)
    
    # 配置纸面交易
    config = {
        "strategy_name": "bollinger_bands",
        "initial_capital": 100000,
        "commission": 0.001,
        "stock_codes": ["000001.SZ"],
        "paper_trading": True,  # 纸面交易
        "strategy_params": {
            "period": 20,
            "std_dev": 2.0
        }
    }
    
    try:
        # 启动策略
        response = requests.post(
            "http://localhost:8000/api/live/start",
            json=config,
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            task_id = result.get('task_id')
            print(f"✅ 纸面交易启动成功")
            print(f"   任务ID: {task_id}")
            print(f"   模式: 纸面交易")
            print(f"   股票: {config['stock_codes']}")
            
            # 等待一段时间观察运行
            print(f"\n⏳ 等待30秒观察策略运行...")
            time.sleep(30)
            
            # 检查状态
            status_response = requests.get("http://localhost:8000/api/live/status")
            if status_response.status_code == 200:
                status_data = status_response.json()
                print(f"\n📊 当前状态:")
                print(f"   运行中策略: {len(status_data.get('running_strategies', []))}")
                
                for strategy in status_data.get('running_strategies', []):
                    print(f"   - {strategy.get('strategy_name')}: {strategy.get('status')}")
                    print(f"     资金: {strategy.get('current_capital', 0):,.2f}")
                    print(f"     收益率: {strategy.get('total_return', 0)*100:.2f}%")
            
            # 停止策略
            if task_id:
                print(f"\n🛑 停止策略...")
                stop_response = requests.delete(f"http://localhost:8000/api/live/stop/{task_id}")
                if stop_response.status_code == 200:
                    print(f"✅ 策略停止成功")
                else:
                    print(f"❌ 策略停止失败: {stop_response.text}")
            
            return True
            
        else:
            print(f"❌ 纸面交易启动失败: {response.status_code}")
            print(f"   响应: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 纸面交易测试失败: {e}")
        return False

def test_live_trading():
    """测试实盘交易（谨慎使用）"""
    print("\n🧪 测试实盘交易模式")
    print("=" * 50)
    print("⚠️ 警告：这将执行真实交易！")
    
    # 询问用户确认
    confirm = input("是否继续实盘交易测试？(输入 'YES' 确认): ")
    if confirm != 'YES':
        print("❌ 用户取消实盘交易测试")
        return False
    
    # 配置实盘交易
    config = {
        "strategy_name": "bollinger_bands",
        "initial_capital": 10000,  # 较小的资金用于测试
        "commission": 0.001,
        "stock_codes": ["000001.SZ"],
        "paper_trading": False,  # 实盘交易
        "strategy_params": {
            "period": 20,
            "std_dev": 2.0
        }
    }
    
    try:
        # 启动策略
        response = requests.post(
            "http://localhost:8000/api/live/start",
            json=config,
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            task_id = result.get('task_id')
            print(f"✅ 实盘交易启动成功")
            print(f"   任务ID: {task_id}")
            print(f"   模式: 实盘交易")
            print(f"   股票: {config['stock_codes']}")
            print(f"   ⚠️ 注意：这是真实交易！")
            
            # 等待较短时间
            print(f"\n⏳ 等待10秒观察策略运行...")
            time.sleep(10)
            
            # 立即停止（安全起见）
            print(f"\n🛑 安全起见，立即停止策略...")
            stop_response = requests.delete(f"http://localhost:8000/api/live/stop/{task_id}")
            if stop_response.status_code == 200:
                print(f"✅ 策略停止成功")
            else:
                print(f"❌ 策略停止失败: {stop_response.text}")
            
            return True
            
        else:
            print(f"❌ 实盘交易启动失败: {response.status_code}")
            print(f"   响应: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 实盘交易测试失败: {e}")
        return False

def test_multiple_strategies():
    """测试多策略并行运行"""
    print("\n🧪 测试多策略并行运行")
    print("=" * 50)
    
    strategies = [
        {
            "name": "bollinger_bands",
            "config": {
                "strategy_name": "bollinger_bands",
                "initial_capital": 50000,
                "commission": 0.001,
                "stock_codes": ["000001.SZ"],
                "paper_trading": True,
                "strategy_params": {"period": 20, "std_dev": 2.0}
            }
        },
        {
            "name": "rsi",
            "config": {
                "strategy_name": "rsi",
                "initial_capital": 50000,
                "commission": 0.001,
                "stock_codes": ["000002.SZ"],
                "paper_trading": True,
                "strategy_params": {"period": 14, "oversold": 30, "overbought": 70}
            }
        }
    ]
    
    task_ids = []
    
    try:
        # 启动多个策略
        for strategy in strategies:
            response = requests.post(
                "http://localhost:8000/api/live/start",
                json=strategy["config"],
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                task_id = result.get('task_id')
                task_ids.append(task_id)
                print(f"✅ 策略 {strategy['name']} 启动成功: {task_id}")
            else:
                print(f"❌ 策略 {strategy['name']} 启动失败: {response.text}")
        
        print(f"\n📊 成功启动 {len(task_ids)} 个策略")
        
        # 等待观察
        print(f"⏳ 等待20秒观察多策略运行...")
        time.sleep(20)
        
        # 检查状态
        status_response = requests.get("http://localhost:8000/api/live/status")
        if status_response.status_code == 200:
            status_data = status_response.json()
            print(f"\n📊 多策略运行状态:")
            for strategy in status_data.get('running_strategies', []):
                print(f"   - {strategy.get('strategy_name')}: {strategy.get('status')}")
        
        # 停止所有策略
        print(f"\n🛑 停止所有策略...")
        for task_id in task_ids:
            stop_response = requests.delete(f"http://localhost:8000/api/live/stop/{task_id}")
            if stop_response.status_code == 200:
                print(f"✅ 策略 {task_id} 停止成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 多策略测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 实时交易系统测试")
    print("=" * 60)
    
    test_results = []
    
    # 测试纸面交易
    test_results.append(("纸面交易", test_paper_trading()))
    
    # 测试多策略（可选）
    test_multi = input("\n是否测试多策略并行运行？(y/n): ").lower() == 'y'
    if test_multi:
        test_results.append(("多策略并行", test_multiple_strategies()))
    
    # 测试实盘交易（可选，谨慎）
    test_live = input("\n是否测试实盘交易？(y/n): ").lower() == 'y'
    if test_live:
        test_results.append(("实盘交易", test_live_trading()))
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("🎯 测试结果汇总:")
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📊 总体结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！实时交易系统工作正常。")
    else:
        print("⚠️ 部分测试失败，请检查系统配置。")
    
    print(f"\n💡 使用说明:")
    print(f"   1. 纸面交易：使用模拟资金和数据，安全测试策略")
    print(f"   2. 实盘交易：使用真实资金和数据，执行真实交易")
    print(f"   3. 实时数据：系统会持续运行，接收实时数据")
    print(f"   4. 风险控制：内置风险控制机制保护资金")

if __name__ == "__main__":
    main()
