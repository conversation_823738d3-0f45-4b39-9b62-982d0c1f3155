"""
配置驱动策略执行引擎
支持基于配置的策略逻辑执行、状态管理和性能监控
"""

import asyncio
import time
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
from pathlib import Path
import json

from backend.core.logger import get_logger
from backend.strategies.config_driven_strategy import MultiSignalStrategy
from backend.strategies.config_parser import config_parser
from backend.backtest.backtest_engine import BacktestResult
from backend.storage.backtest_storage import backtest_storage

logger = get_logger(__name__)

@dataclass
class StrategyExecutionContext:
    """策略执行上下文"""
    strategy_id: str
    strategy_name: str
    start_time: datetime
    end_time: Optional[datetime] = None
    initial_capital: float = 100000.0
    current_capital: float = 100000.0
    total_trades: int = 0
    successful_trades: int = 0
    failed_trades: int = 0
    max_drawdown: float = 0.0
    peak_capital: float = 100000.0
    execution_status: str = "running"  # running, completed, failed, stopped
    error_message: Optional[str] = None

@dataclass
class StrategyPerformance:
    """策略性能指标"""
    total_return: float = 0.0
    annual_return: float = 0.0
    sharpe_ratio: float = 0.0
    max_drawdown: float = 0.0
    win_rate: float = 0.0
    profit_factor: float = 0.0
    avg_trade_return: float = 0.0
    volatility: float = 0.0
    calmar_ratio: float = 0.0

class MultiSignalEngine:
    """多信号策略执行引擎"""
    
    def __init__(self):
        self.running_strategies: Dict[str, MultiSignalStrategy] = {}
        self.execution_contexts: Dict[str, StrategyExecutionContext] = {}
        self.performance_cache: Dict[str, StrategyPerformance] = {}
        
        # 执行状态
        self.is_running = False
        self.execution_tasks: Dict[str, asyncio.Task] = {}
        
        logger.info("🚀 配置驱动策略执行引擎初始化完成")
    
    async def load_strategy_from_config(self, config_path: str, strategy_id: Optional[str] = None) -> str:
        """从配置文件加载策略"""
        try:
            # 创建策略实例
            strategy = config_parser.create_strategy_instance(config_path)
            
            # 生成策略ID
            if not strategy_id:
                strategy_id = f"{strategy.strategy_name}_{int(time.time())}"
            
            # 注册策略
            self.running_strategies[strategy_id] = strategy
            
            # 创建执行上下文
            context = StrategyExecutionContext(
                strategy_id=strategy_id,
                strategy_name=strategy.strategy_name,
                start_time=datetime.now()
            )
            self.execution_contexts[strategy_id] = context
            
            logger.info(f"✅ 策略加载成功: {strategy_id} ({strategy.display_name})")
            return strategy_id
            
        except Exception as e:
            logger.error(f"❌ 策略加载失败: {e}")
            raise
    
    async def load_strategy_from_dict(self, config_dict: Dict[str, Any], strategy_id: Optional[str] = None) -> str:
        """从配置字典加载策略"""
        try:
            # 创建策略实例
            strategy = config_parser.create_strategy_from_dict(config_dict)
            
            # 生成策略ID
            if not strategy_id:
                strategy_id = f"{strategy.strategy_name}_{int(time.time())}"
            
            # 注册策略
            self.running_strategies[strategy_id] = strategy
            
            # 创建执行上下文
            context = StrategyExecutionContext(
                strategy_id=strategy_id,
                strategy_name=strategy.strategy_name,
                start_time=datetime.now()
            )
            self.execution_contexts[strategy_id] = context
            
            logger.info(f"✅ 策略加载成功: {strategy_id} ({strategy.display_name})")
            return strategy_id
            
        except Exception as e:
            logger.error(f"❌ 策略加载失败: {e}")
            raise
    
    async def execute_strategy_backtest(self, strategy_id: str, stock_data: Dict[str, pd.DataFrame],
                                      initial_capital: float = 100000.0,
                                      progress_callback: Optional[Callable] = None) -> BacktestResult:
        """执行策略回测"""
        if strategy_id not in self.running_strategies:
            raise ValueError(f"策略不存在: {strategy_id}")
        
        strategy = self.running_strategies[strategy_id]
        context = self.execution_contexts[strategy_id]
        
        try:
            # 更新执行上下文
            context.initial_capital = initial_capital
            context.current_capital = initial_capital
            context.execution_status = "running"
            
            logger.info(f"🚀 开始执行策略回测: {strategy_id}")
            
            # 创建回测上下文
            from backend.strategies.base_strategy import BacktestContext
            backtest_context = BacktestContext(
                initial_capital=initial_capital,
                start_date=min(data.index[0] for data in stock_data.values() if not data.empty),
                end_date=max(data.index[-1] for data in stock_data.values() if not data.empty),
                stock_codes=list(stock_data.keys())
            )
            
            # 初始化策略
            if not strategy.initialize(backtest_context):
                raise RuntimeError("策略初始化失败")
            
            # 执行策略
            success = await strategy.run(stock_data, progress_callback)
            
            if not success:
                raise RuntimeError("策略执行失败")
            
            # 更新执行上下文
            context.end_time = datetime.now()
            context.current_capital = strategy.current_capital
            context.total_trades = len(strategy.trades)
            context.execution_status = "completed"
            
            # 计算性能指标
            performance = self._calculate_performance(strategy, context)
            self.performance_cache[strategy_id] = performance
            
            # 创建回测结果
            result = self._create_backtest_result(strategy, context, performance)
            
            # 保存回测结果
            backtest_storage.save_result(result)
            
            logger.info(f"✅ 策略回测完成: {strategy_id}")
            return result
            
        except Exception as e:
            context.execution_status = "failed"
            context.error_message = str(e)
            logger.error(f"❌ 策略回测失败: {strategy_id} - {e}")
            raise
    
    async def execute_strategy_live(self, strategy_id: str, market_data_callback: Callable,
                                  initial_capital: float = 100000.0) -> bool:
        """执行策略实盘交易"""
        if strategy_id not in self.running_strategies:
            raise ValueError(f"策略不存在: {strategy_id}")
        
        strategy = self.running_strategies[strategy_id]
        context = self.execution_contexts[strategy_id]
        
        try:
            # 更新执行上下文
            context.initial_capital = initial_capital
            context.current_capital = initial_capital
            context.execution_status = "running"
            
            logger.info(f"🚀 开始执行策略实盘交易: {strategy_id}")
            
            # 创建实盘执行任务
            task = asyncio.create_task(self._live_execution_loop(strategy_id, market_data_callback))
            self.execution_tasks[strategy_id] = task
            
            return True
            
        except Exception as e:
            context.execution_status = "failed"
            context.error_message = str(e)
            logger.error(f"❌ 策略实盘交易启动失败: {strategy_id} - {e}")
            return False
    
    async def _live_execution_loop(self, strategy_id: str, market_data_callback: Callable):
        """实盘执行循环"""
        strategy = self.running_strategies[strategy_id]
        context = self.execution_contexts[strategy_id]
        
        try:
            while context.execution_status == "running":
                # 获取市场数据
                market_data = await market_data_callback()
                
                if not market_data:
                    await asyncio.sleep(1)
                    continue
                
                # 执行策略逻辑
                await strategy.run(market_data)
                
                # 更新执行上下文
                context.current_capital = strategy.current_capital
                context.total_trades = len(strategy.trades)
                
                # 计算性能指标
                performance = self._calculate_performance(strategy, context)
                self.performance_cache[strategy_id] = performance
                
                # 等待下次执行
                await asyncio.sleep(1)  # 1秒执行一次
                
        except Exception as e:
            context.execution_status = "failed"
            context.error_message = str(e)
            logger.error(f"❌ 策略实盘执行异常: {strategy_id} - {e}")
    
    def stop_strategy(self, strategy_id: str) -> bool:
        """停止策略执行"""
        try:
            if strategy_id in self.execution_contexts:
                context = self.execution_contexts[strategy_id]
                context.execution_status = "stopped"
                context.end_time = datetime.now()
            
            if strategy_id in self.execution_tasks:
                task = self.execution_tasks[strategy_id]
                task.cancel()
                del self.execution_tasks[strategy_id]
            
            logger.info(f"🛑 策略停止成功: {strategy_id}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 策略停止失败: {strategy_id} - {e}")
            return False
    
    def remove_strategy(self, strategy_id: str) -> bool:
        """移除策略"""
        try:
            # 先停止策略
            self.stop_strategy(strategy_id)
            
            # 清理资源
            if strategy_id in self.running_strategies:
                del self.running_strategies[strategy_id]
            
            if strategy_id in self.execution_contexts:
                del self.execution_contexts[strategy_id]
            
            if strategy_id in self.performance_cache:
                del self.performance_cache[strategy_id]
            
            logger.info(f"🗑️ 策略移除成功: {strategy_id}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 策略移除失败: {strategy_id} - {e}")
            return False
    
    def get_strategy_status(self, strategy_id: str) -> Optional[Dict[str, Any]]:
        """获取策略状态"""
        if strategy_id not in self.execution_contexts:
            return None
        
        context = self.execution_contexts[strategy_id]
        strategy = self.running_strategies.get(strategy_id)
        performance = self.performance_cache.get(strategy_id)
        
        status = {
            'strategy_id': strategy_id,
            'strategy_name': context.strategy_name,
            'display_name': strategy.display_name if strategy else context.strategy_name,
            'execution_status': context.execution_status,
            'start_time': context.start_time.isoformat(),
            'end_time': context.end_time.isoformat() if context.end_time else None,
            'initial_capital': context.initial_capital,
            'current_capital': context.current_capital,
            'total_trades': context.total_trades,
            'successful_trades': context.successful_trades,
            'failed_trades': context.failed_trades,
            'error_message': context.error_message
        }
        
        if performance:
            status.update({
                'total_return': performance.total_return,
                'sharpe_ratio': performance.sharpe_ratio,
                'max_drawdown': performance.max_drawdown,
                'win_rate': performance.win_rate
            })
        
        return status
    
    def list_running_strategies(self) -> List[Dict[str, Any]]:
        """列出所有运行中的策略"""
        strategies = []
        
        for strategy_id in self.running_strategies.keys():
            status = self.get_strategy_status(strategy_id)
            if status:
                strategies.append(status)
        
        return strategies
    
    def _calculate_performance(self, strategy: MultiSignalStrategy,
                             context: StrategyExecutionContext) -> StrategyPerformance:
        """计算策略性能指标"""
        try:
            trades = strategy.trades
            daily_returns = strategy.daily_returns
            
            if not trades:
                return StrategyPerformance()
            
            # 基本指标
            total_return = (context.current_capital - context.initial_capital) / context.initial_capital
            
            # 胜率
            profitable_trades = len([t for t in trades if self._is_profitable_trade(t)])
            win_rate = profitable_trades / len(trades) if trades else 0
            
            # 最大回撤
            max_drawdown = context.max_drawdown
            
            # 夏普比率（简化计算）
            if daily_returns:
                returns = [r.get('daily_return', 0) for r in daily_returns]
                if returns:
                    avg_return = np.mean(returns)
                    std_return = np.std(returns)
                    sharpe_ratio = avg_return / std_return if std_return > 0 else 0
                else:
                    sharpe_ratio = 0
            else:
                sharpe_ratio = 0
            
            return StrategyPerformance(
                total_return=total_return,
                annual_return=total_return,  # 简化处理
                sharpe_ratio=sharpe_ratio,
                max_drawdown=max_drawdown,
                win_rate=win_rate,
                profit_factor=1.0,  # 简化处理
                avg_trade_return=total_return / len(trades) if trades else 0,
                volatility=std_return if 'std_return' in locals() else 0,
                calmar_ratio=total_return / abs(max_drawdown) if max_drawdown != 0 else 0
            )
            
        except Exception as e:
            logger.error(f"❌ 性能指标计算失败: {e}")
            return StrategyPerformance()
    
    def _is_profitable_trade(self, trade: Dict[str, Any]) -> bool:
        """判断交易是否盈利"""
        # 这里需要根据实际的交易记录结构来判断
        # 简化处理，假设有profit字段
        return trade.get('profit', 0) > 0
    
    def _create_backtest_result(self, strategy: MultiSignalStrategy,
                               context: StrategyExecutionContext,
                               performance: StrategyPerformance) -> BacktestResult:
        """创建回测结果"""
        return BacktestResult(
            task_id=context.strategy_id,
            strategy_name=strategy.display_name,
            start_date=context.start_time.strftime('%Y-%m-%d'),
            end_date=context.end_time.strftime('%Y-%m-%d') if context.end_time else datetime.now().strftime('%Y-%m-%d'),
            initial_capital=context.initial_capital,
            final_capital=context.current_capital,
            total_return=performance.total_return,
            annual_return=performance.annual_return,
            sharpe_ratio=performance.sharpe_ratio,
            max_drawdown=performance.max_drawdown,
            win_rate=performance.win_rate,
            total_trades=context.total_trades,
            profit_trades=context.successful_trades,
            loss_trades=context.failed_trades,
            avg_profit=performance.avg_trade_return * context.initial_capital if performance.avg_trade_return > 0 else 0,
            avg_loss=performance.avg_trade_return * context.initial_capital if performance.avg_trade_return < 0 else 0,
            profit_factor=performance.profit_factor,
            trades=strategy.trades,
            daily_returns=strategy.daily_returns,
            status="completed",
            created_at=context.start_time.isoformat(),
            completed_at=context.end_time.isoformat() if context.end_time else datetime.now().isoformat()
        )

# 全局多信号策略引擎实例
multi_signal_engine = MultiSignalEngine()
