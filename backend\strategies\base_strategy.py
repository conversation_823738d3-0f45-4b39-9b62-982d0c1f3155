"""
回测策略基类
基于backtrader的策略框架，支持配置驱动和真实数据回测
"""

import backtrader as bt
import pandas as pd
import numpy as np
from typing import Dict, Any, List, Optional
from abc import ABC, abstractmethod
from dataclasses import dataclass
import logging
from datetime import datetime

logger = logging.getLogger(__name__)

@dataclass
class TradeRecord:
    """交易记录"""
    id: int
    date: str
    time: str
    stock_code: str
    stock_name: str
    action: str  # 'buy' or 'sell'
    action_name: str  # '买入' or '卖出'
    price: float
    quantity: int
    amount: float
    commission: float
    reason: str
    # 买入特有字段
    total_cost: Optional[float] = None
    # 卖出特有字段
    net_amount: Optional[float] = None
    profit_loss: Optional[float] = None
    profit_loss_pct: Optional[float] = None
    hold_days: Optional[int] = None
    # 组合变化
    portfolio_value_before: Optional[float] = None
    portfolio_value_after: Optional[float] = None
    cash_change: Optional[float] = None
    position_change: Optional[str] = None


@dataclass
class DailyReturn:
    """每日收益记录"""
    date: str
    value: float
    return_rate: float
    cash: float
    positions_value: float


@dataclass
class BacktestMetrics:
    """回测指标"""
    total_return: float
    annual_return: float
    max_drawdown: float
    sharpe_ratio: float
    win_rate: float
    total_trades: int
    profit_trades: int
    loss_trades: int
    avg_profit: float
    avg_loss: float
    profit_factor: float


@dataclass
class BacktestContext:
    """回测上下文"""
    start_date: str
    end_date: str
    initial_capital: float
    commission: float
    stock_pool: List[str]
    strategy_config: Dict[str, Any]


class AbstractStrategy(ABC):
    """抽象策略基类"""

    def __init__(self):
        self.trades: List[TradeRecord] = []
        self.daily_returns: List[DailyReturn] = []
        self.current_capital = 0.0
        self.positions = {}  # {stock_code: {'quantity': int, 'avg_price': float, 'total_cost': float}}
        self.context: Optional[BacktestContext] = None

    @property
    @abstractmethod
    def strategy_name(self) -> str:
        """策略名称，用于注册和识别"""
        pass

    @property
    @abstractmethod
    def display_name(self) -> str:
        """策略显示名称"""
        pass

    @property
    @abstractmethod
    def description(self) -> str:
        """策略描述"""
        pass

    @abstractmethod
    def load_config(self, config: Dict[str, Any]) -> bool:
        """
        加载策略配置

        Args:
            config: 策略配置参数

        Returns:
            bool: 配置是否有效
        """
        pass

    @abstractmethod
    def initialize(self, context: BacktestContext) -> bool:
        """
        初始化策略

        Args:
            context: 回测上下文

        Returns:
            bool: 初始化是否成功
        """
        pass

    @abstractmethod
    async def run(self, stock_data: Dict[str, Any], progress_callback=None) -> bool:
        """
        执行回测策略

        Args:
            stock_data: 股票历史数据 {stock_code: DataFrame}
            progress_callback: 进度回调函数

        Returns:
            bool: 执行是否成功
        """
        pass

    def get_trades(self) -> List[TradeRecord]:
        """获取交易记录"""
        return self.trades

    def get_daily_returns(self) -> List[DailyReturn]:
        """获取每日收益"""
        return self.daily_returns

    def get_metrics(self) -> BacktestMetrics:
        """计算并返回回测指标"""
        if not self.daily_returns:
            return self._create_empty_metrics()

        # 计算基本指标
        initial_value = self.daily_returns[0].value
        final_value = self.daily_returns[-1].value
        total_return = (final_value - initial_value) / initial_value

        # 计算年化收益率
        days = len(self.daily_returns)
        years = days / 365.25
        annual_return = (1 + total_return) ** (1 / years) - 1 if years > 0 else total_return

        # 计算最大回撤
        values = [dr.value for dr in self.daily_returns]
        peak = values[0]
        max_drawdown = 0
        for value in values:
            if value > peak:
                peak = value
            drawdown = (peak - value) / peak
            if drawdown > max_drawdown:
                max_drawdown = drawdown

        # 计算夏普比率
        returns = [dr.return_rate for dr in self.daily_returns[1:]]
        if returns:
            avg_return = np.mean(returns)
            std_return = np.std(returns)
            sharpe_ratio = (avg_return * 252) / (std_return * np.sqrt(252)) if std_return > 0 else 0
        else:
            sharpe_ratio = 0

        # 交易统计
        sell_trades = [t for t in self.trades if t.action == 'sell']
        profit_trades = [t for t in sell_trades if t.profit_loss and t.profit_loss > 0]
        loss_trades = [t for t in sell_trades if t.profit_loss and t.profit_loss <= 0]

        win_rate = len(profit_trades) / len(sell_trades) if sell_trades else 0
        avg_profit = np.mean([t.profit_loss for t in profit_trades]) if profit_trades else 0
        avg_loss = np.mean([abs(t.profit_loss) for t in loss_trades]) if loss_trades else 0
        profit_factor = abs(avg_profit / avg_loss) if avg_loss > 0 else 0

        return BacktestMetrics(
            total_return=total_return,
            annual_return=annual_return,
            max_drawdown=-max_drawdown,
            sharpe_ratio=sharpe_ratio,
            win_rate=win_rate,
            total_trades=len(self.trades),
            profit_trades=len(profit_trades),
            loss_trades=len(loss_trades),
            avg_profit=avg_profit,
            avg_loss=avg_loss,
            profit_factor=profit_factor
        )

    def _create_empty_metrics(self) -> BacktestMetrics:
        """创建空的指标"""
        return BacktestMetrics(
            total_return=0.0,
            annual_return=0.0,
            max_drawdown=0.0,
            sharpe_ratio=0.0,
            win_rate=0.0,
            total_trades=0,
            profit_trades=0,
            loss_trades=0,
            avg_profit=0.0,
            avg_loss=0.0,
            profit_factor=0.0
        )

    def add_trade(self, trade: TradeRecord):
        """添加交易记录"""
        trade.id = len(self.trades) + 1
        self.trades.append(trade)

    def add_daily_return(self, daily_return: DailyReturn):
        """添加每日收益记录"""
        self.daily_returns.append(daily_return)

    def get_stock_name(self, stock_code: str) -> str:
        """获取股票名称"""
        # 简化的股票名称映射
        name_mapping = {
            '000001.SZ': '平安银行',
            '000002.SZ': '万科A',
            '000858.SZ': '五粮液',
            '600000.SH': '浦发银行',
            '600036.SH': '招商银行',
            '600519.SH': '贵州茅台',
            '600887.SH': '伊利股份'
        }

        if stock_code in name_mapping:
            return name_mapping[stock_code]
        else:
            if stock_code.endswith('.SZ'):
                return f"深股{stock_code[:6]}"
            elif stock_code.endswith('.SH'):
                return f"沪股{stock_code[:6]}"
            else:
                return f"股票{stock_code}"


# 兼容旧代码的类（保留以避免破坏现有代码）
class ConfigurableStrategy(AbstractStrategy):
    """
    可配置策略基类 - 兼容旧代码
    新策略应该直接继承AbstractStrategy
    """

    @property
    def strategy_name(self) -> str:
        return getattr(self, '_strategy_name', 'configurable_strategy')

    @property
    def display_name(self) -> str:
        return getattr(self, '_display_name', '可配置策略')

    @property
    def description(self) -> str:
        return getattr(self, '_description', '兼容旧代码的可配置策略基类')

    def load_config(self, config: Dict[str, Any]) -> bool:
        """加载配置 - 默认实现"""
        self.config = config
        return True

    def initialize(self, context: BacktestContext) -> bool:
        """初始化 - 默认实现"""
        self.context = context
        self.current_capital = context.initial_capital
        return True

    async def run(self, stock_data: Dict[str, Any], progress_callback=None) -> bool:
        """运行策略 - 默认实现"""
        logger.warning("使用默认的run方法，请在子类中实现具体逻辑")
        return True


# 策略注册表
STRATEGY_REGISTRY: Dict[str, type] = {}


def register_strategy(strategy_class: type):
    """
    注册策略类

    Args:
        strategy_class: 策略类
    """
    if not issubclass(strategy_class, AbstractStrategy):
        raise ValueError(f"策略类 {strategy_class.__name__} 必须继承自 AbstractStrategy")

    # 创建实例来获取策略名称
    instance = strategy_class()
    strategy_name = instance.strategy_name

    STRATEGY_REGISTRY[strategy_name] = strategy_class
    logger.info(f"注册策略: {strategy_name} -> {strategy_class.__name__}")


def get_strategy_class(strategy_name: str) -> Optional[type]:
    """
    根据策略名称获取策略类

    Args:
        strategy_name: 策略名称

    Returns:
        策略类或None
    """
    return STRATEGY_REGISTRY.get(strategy_name)


def list_strategies() -> List[Dict[str, str]]:
    """
    列出所有已注册的策略

    Returns:
        策略信息列表
    """
    strategies = []
    for strategy_name, strategy_class in STRATEGY_REGISTRY.items():
        instance = strategy_class()
        strategies.append({
            'name': strategy_name,
            'display_name': instance.display_name,
            'description': instance.description,
            'class_name': strategy_class.__name__
        })
    return strategies


def create_strategy(strategy_name: str) -> Optional[AbstractStrategy]:
    """
    创建策略实例

    Args:
        strategy_name: 策略名称

    Returns:
        策略实例或None
    """
    strategy_class = get_strategy_class(strategy_name)
    if strategy_class:
        return strategy_class()
    return None
