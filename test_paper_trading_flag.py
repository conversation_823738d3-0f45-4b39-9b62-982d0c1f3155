#!/usr/bin/env python3
"""
测试paper_trading标志处理
"""

import sys
import os
import requests
import json

# 添加项目路径
sys.path.append(os.path.dirname(__file__))

def test_paper_trading_flag():
    """测试paper_trading标志处理"""
    try:
        print("🔍 测试paper_trading标志处理...")
        
        # 测试1: 明确设置为True
        test1_data = {
            "strategy_name": "bollinger_bands",
            "initial_capital": 100000,
            "stock_codes": ["000001.SZ"],
            "paper_trading": True,
            "strategy_params": {"period": 20}
        }
        
        print("📤 测试1: paper_trading=True")
        print(f"   请求数据: {json.dumps(test1_data, indent=2)}")
        
        response = requests.post(
            'http://localhost:8000/api/live/start',
            json=test1_data,
            headers={'Content-Type': 'application/json'}
        )
        
        if response.status_code == 200:
            data = response.json()
            task_id_1 = data.get('task_id')
            print(f"   ✅ 启动成功: {task_id_1[:8] if task_id_1 else 'None'}...")
        else:
            print(f"   ❌ 启动失败: {response.status_code}")
            return
        
        # 测试2: 明确设置为False
        test2_data = {
            "strategy_name": "bollinger_bands",
            "initial_capital": 50000,
            "stock_codes": ["000002.SZ"],
            "paper_trading": False,
            "strategy_params": {"period": 15}
        }
        
        print("📤 测试2: paper_trading=False")
        print(f"   请求数据: {json.dumps(test2_data, indent=2)}")
        
        response = requests.post(
            'http://localhost:8000/api/live/start',
            json=test2_data,
            headers={'Content-Type': 'application/json'}
        )
        
        if response.status_code == 200:
            data = response.json()
            task_id_2 = data.get('task_id')
            print(f"   ✅ 启动成功: {task_id_2[:8] if task_id_2 else 'None'}...")
        else:
            print(f"   ❌ 启动失败: {response.status_code}")
            return
        
        # 测试3: 不设置paper_trading（应该默认为True）
        test3_data = {
            "strategy_name": "bollinger_bands",
            "initial_capital": 75000,
            "stock_codes": ["000003.SZ"],
            "strategy_params": {"period": 25}
        }
        
        print("📤 测试3: 不设置paper_trading")
        print(f"   请求数据: {json.dumps(test3_data, indent=2)}")
        
        response = requests.post(
            'http://localhost:8000/api/live/start',
            json=test3_data,
            headers={'Content-Type': 'application/json'}
        )
        
        if response.status_code == 200:
            data = response.json()
            task_id_3 = data.get('task_id')
            print(f"   ✅ 启动成功: {task_id_3[:8] if task_id_3 else 'None'}...")
        else:
            print(f"   ❌ 启动失败: {response.status_code}")
            return
        
        # 获取结果并验证
        print("\n📋 验证结果...")
        response = requests.get('http://localhost:8000/api/live/results')
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                results = data.get('data', [])
                
                print(f"✅ 获取到 {len(results)} 个策略结果:")
                print("   任务ID     | 初始资金 | paper_trading | 期望值")
                print("   " + "-" * 50)
                
                for result in results:
                    task_id = result.get('task_id', '')[:8]
                    initial_capital = result.get('initial_capital', 0)
                    paper_trading = result.get('paper_trading')
                    
                    # 根据初始资金判断期望值
                    if initial_capital == 100000:
                        expected = True
                        test_name = "测试1"
                    elif initial_capital == 50000:
                        expected = False
                        test_name = "测试2"
                    elif initial_capital == 75000:
                        expected = True  # 默认值
                        test_name = "测试3"
                    else:
                        expected = "未知"
                        test_name = "其他"
                    
                    status = "✅" if paper_trading == expected else "❌"
                    paper_trading_str = str(paper_trading) if paper_trading is not None else "None"
                    print(f"   {task_id}... | {initial_capital:8} | {paper_trading_str:13} | {expected} ({test_name}) {status}")
                
                # 清理测试数据
                print("\n🧹 清理测试数据...")
                for result in results:
                    task_id = result.get('task_id')
                    if task_id in [task_id_1, task_id_2, task_id_3]:
                        try:
                            requests.post(f'http://localhost:8000/api/live/stop/{task_id}')
                            print(f"   🗑️ 清理任务: {task_id[:8]}...")
                        except:
                            pass
                            
            else:
                print(f"❌ 获取结果失败: {data}")
        else:
            print(f"❌ 获取结果API错误: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    test_paper_trading_flag()
