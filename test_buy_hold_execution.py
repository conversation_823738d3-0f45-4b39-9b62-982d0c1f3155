#!/usr/bin/env python3
"""
测试buy_hold策略的实际执行
"""

import requests
import json
import time
import asyncio

BASE_URL = "http://localhost:8000"

def test_buy_hold_execution():
    """测试buy_hold策略的实际执行"""
    print("=== 测试buy_hold策略实际执行 ===")
    
    # 1. 清理现有策略
    print("\n1. 清理现有策略")
    response = requests.get(f"{BASE_URL}/api/live/strategies")
    if response.status_code == 200:
        strategies = response.json()
        for strategy in strategies:
            if 'buy_hold' in strategy['name'] or 'test' in strategy['name'].lower():
                print(f"停止现有策略: {strategy['name']}")
                requests.delete(f"{BASE_URL}/api/live/strategies/{strategy['id']}")
    
    # 2. 启动buy_hold策略
    print("\n2. 启动buy_hold策略")
    strategy_data = {
        'name': 'buy_hold执行测试',
        'strategy_type': 'buy_hold',
        'config': {
            'initial_capital': 100000,
            'paper_trading': True,
            'stock_codes': ['000001.SZ', '000002.SZ', '000858.SZ'],  # 确保有股票池
            'max_stocks': 3,
            'position_size': 0.33
        }
    }
    
    response = requests.post(f"{BASE_URL}/api/live/strategies", json=strategy_data)
    if response.status_code == 200:
        result = response.json()
        if result.get('success'):
            strategy_id = result['strategy_id']
            print(f"✅ buy_hold策略启动成功: {strategy_id}")
            
            # 3. 等待策略执行
            print("\n3. 等待策略执行...")
            for i in range(10):  # 等待10次，每次3秒
                time.sleep(3)
                print(f"⏳ 检查第 {i+1} 次...")
                
                # 获取策略状态
                response = requests.get(f"{BASE_URL}/api/live/strategies")
                if response.status_code == 200:
                    strategies = response.json()
                    for strategy in strategies:
                        if strategy['id'] == strategy_id:
                            print(f"📊 策略状态:")
                            print(f"   名称: {strategy['name']}")
                            print(f"   状态: {strategy['status']}")
                            print(f"   task_id: {strategy.get('task_id', 'N/A')}")
                            print(f"   持仓数: {strategy['positions']}")
                            print(f"   交易次数: {strategy['trade_count']}")
                            print(f"   盈亏: ¥{strategy['pnl']:.2f}")
                            print(f"   股票池: {len(strategy.get('config', {}).get('stock_codes', []))}只股票")
                            
                            recent_trades = strategy.get('recent_trades', [])
                            if recent_trades:
                                print(f"   最近交易: {len(recent_trades)}笔")
                                for trade in recent_trades[:3]:
                                    print(f"     - {trade}")
                            else:
                                print(f"   最近交易: 无")
                            
                            error_message = strategy.get('error_message')
                            if error_message:
                                print(f"   错误信息: {error_message}")
                            
                            # 如果有交易记录，说明策略正在工作
                            if strategy['trade_count'] > 0:
                                print(f"✅ 策略已开始交易！")
                                break
                            elif strategy['positions'] > 0:
                                print(f"✅ 策略已建立持仓！")
                                break
                            elif error_message:
                                print(f"❌ 策略执行出错: {error_message}")
                                break
                            else:
                                print(f"⏳ 策略尚未产生交易，继续等待...")
                            break
                    else:
                        print(f"❌ 找不到策略: {strategy_id}")
                        break
                else:
                    print(f"❌ 获取策略状态失败: {response.status_code}")
                    break
            
            # 4. 检查实盘引擎状态
            print(f"\n4. 检查实盘引擎状态")
            response = requests.get(f"{BASE_URL}/api/live/results")
            if response.status_code == 200:
                results = response.json()
                if results.get('success'):
                    live_results = results.get('data', [])
                    print(f"实盘引擎中的任务数量: {len(live_results)}")
                    
                    for result in live_results:
                        if result.get('strategy_name') == 'buy_hold':
                            print(f"找到buy_hold任务:")
                            print(f"  task_id: {result.get('task_id')}")
                            print(f"  状态: {result.get('status')}")
                            print(f"  持仓: {len(result.get('positions', []))}")
                            print(f"  交易: {len(result.get('trades', []))}")
                            print(f"  当前资金: ¥{result.get('current_capital', 0):,.2f}")
                            break
                    else:
                        print(f"❌ 实盘引擎中未找到buy_hold任务")
                else:
                    print(f"❌ 获取实盘引擎结果失败: {results.get('message')}")
            else:
                print(f"❌ 调用实盘引擎API失败: {response.status_code}")
            
            # 5. 停止策略
            print(f"\n5. 停止策略")
            response = requests.delete(f"{BASE_URL}/api/live/strategies/{strategy_id}")
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    print(f"✅ 策略停止成功: {result['message']}")
                else:
                    print(f"❌ 策略停止失败: {result['message']}")
            else:
                print(f"❌ 停止策略请求失败: {response.status_code}")
            
            return True
        else:
            print(f"❌ 策略启动失败: {result.get('message')}")
            return False
    else:
        print(f"❌ 策略启动请求失败: {response.status_code} - {response.text}")
        return False

def test_strategy_status_update():
    """测试策略状态更新"""
    print("\n=== 测试策略状态更新 ===")
    
    # 启动一个简单的策略
    strategy_data = {
        'name': '状态更新测试',
        'strategy_type': 'bollinger_bands',
        'config': {
            'initial_capital': 50000,
            'paper_trading': True,
            'stock_codes': ['000001.SZ'],
            'bb_period': 20,
            'bb_std': 2.0
        }
    }
    
    response = requests.post(f"{BASE_URL}/api/live/strategies", json=strategy_data)
    if response.status_code == 200:
        result = response.json()
        if result.get('success'):
            strategy_id = result['strategy_id']
            print(f"✅ 测试策略启动成功: {strategy_id}")
            
            # 等待几秒让策略开始执行
            time.sleep(5)
            
            # 检查策略状态是否正确更新
            response = requests.get(f"{BASE_URL}/api/live/strategies/{strategy_id}")
            if response.status_code == 200:
                strategy = response.json()
                print(f"📊 策略详细状态:")
                print(json.dumps(strategy, indent=2, ensure_ascii=False))
                
                # 检查关键字段
                has_task_id = strategy.get('task_id') is not None
                has_positions = strategy.get('positions', 0) >= 0
                has_pnl = 'pnl' in strategy
                has_trade_count = 'trade_count' in strategy
                
                print(f"\n状态检查:")
                print(f"  ✅ task_id存在: {has_task_id}")
                print(f"  ✅ positions字段: {has_positions}")
                print(f"  ✅ pnl字段: {has_pnl}")
                print(f"  ✅ trade_count字段: {has_trade_count}")
                
                if all([has_task_id, has_positions, has_pnl, has_trade_count]):
                    print(f"✅ 策略状态更新正常")
                else:
                    print(f"❌ 策略状态更新异常")
            
            # 清理
            requests.delete(f"{BASE_URL}/api/live/strategies/{strategy_id}")
            return True
        else:
            print(f"❌ 测试策略启动失败: {result.get('message')}")
            return False
    else:
        print(f"❌ 测试策略启动请求失败: {response.status_code}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试buy_hold策略执行修复")
    
    results = []
    
    # 测试1: buy_hold策略实际执行
    results.append(test_buy_hold_execution())
    
    # 测试2: 策略状态更新
    results.append(test_strategy_status_update())
    
    # 总结
    print(f"\n🎯 测试结果总结:")
    print(f"✅ 通过: {sum(results)}/{len(results)} 项测试")
    
    if all(results):
        print(f"\n🎉 buy_hold策略执行修复成功！")
        print(f"\n✅ 修复内容:")
        print(f"  1. 多策略服务现在会启动实际的执行引擎")
        print(f"  2. 策略状态会从实际执行引擎获取")
        print(f"  3. 停止策略时会同时停止执行引擎")
        print(f"  4. task_id正确关联策略实例和执行引擎")
        
        print(f"\n💡 现在buy_hold策略会:")
        print(f"  - 实际启动执行引擎并开始交易")
        print(f"  - 显示真实的持仓、盈亏和交易记录")
        print(f"  - 在策略实时状态中显示最新数据")
        print(f"  - 正确处理纸面交易和实盘交易")
    else:
        failed_tests = [i+1 for i, result in enumerate(results) if not result]
        print(f"\n❌ 部分测试失败，失败的测试: {failed_tests}")
        print(f"请检查:")
        print(f"  - 后端服务是否正常运行")
        print(f"  - 实盘交易引擎是否可用")
        print(f"  - 数据源是否正常")

if __name__ == "__main__":
    main()
