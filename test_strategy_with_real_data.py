#!/usr/bin/env python3
"""
使用真实xttrader数据测试策略执行
"""

import requests
import json
import time

BASE_URL = "http://localhost:8000"

def test_real_data_strategy():
    """使用真实数据测试策略"""
    print("=== 使用真实xttrader数据测试策略 ===")
    
    # 1. 获取股票列表
    print("1. 获取股票列表...")
    response = requests.get(f"{BASE_URL}/api/data/stocks?page=1&page_size=5")
    
    if response.status_code == 200:
        data = response.json()
        if data.get('success'):
            stocks = data.get('data', [])
            print(f"✅ 获取到 {len(stocks)} 只股票")
            
            # 显示股票信息
            for stock in stocks:
                print(f"  - {stock['name']}({stock['code']})")
            
            if stocks:
                # 2. 测试获取股票数据
                test_stock = stocks[0]
                stock_code = test_stock['code']
                print(f"\n2. 测试获取股票数据: {test_stock['name']}({stock_code})")
                
                response = requests.get(f"{BASE_URL}/api/data/{stock_code}?period=1d&count=5")
                if response.status_code == 200:
                    data = response.json()
                    if data.get('success'):
                        stock_data = data.get('data', [])
                        print(f"✅ 获取到 {len(stock_data)} 条历史数据")
                        
                        if stock_data:
                            latest = stock_data[-1]
                            print(f"最新数据: {latest['date']} 收盘价:{latest['close']}")
                            
                            # 3. 启动buy_hold策略
                            print(f"\n3. 启动buy_hold策略...")
                            strategy_data = {
                                'name': '真实数据buy_hold测试',
                                'strategy_type': 'buy_hold',
                                'config': {
                                    'initial_capital': 100000,
                                    'paper_trading': True,
                                    'stock_codes': [stock_code],  # 使用有数据的股票
                                    'max_stocks': 1,
                                    'position_size': 1.0
                                }
                            }
                            
                            response = requests.post(f"{BASE_URL}/api/live/strategies", json=strategy_data)
                            if response.status_code == 200:
                                result = response.json()
                                if result.get('success'):
                                    strategy_id = result['strategy_id']
                                    print(f"✅ buy_hold策略启动成功: {strategy_id}")
                                    
                                    # 4. 监控策略执行
                                    print(f"\n4. 监控策略执行 (30秒)...")
                                    for i in range(10):
                                        time.sleep(3)
                                        
                                        response = requests.get(f"{BASE_URL}/api/live/strategies/{strategy_id}")
                                        if response.status_code == 200:
                                            strategy = response.json()
                                            print(f"[{i+1:2d}/10] 持仓:{strategy['positions']} 交易:{strategy['trade_count']} 盈亏:¥{strategy['pnl']:.2f}")
                                            
                                            if strategy.get('error_message'):
                                                print(f"    ❌ 错误: {strategy['error_message']}")
                                                break
                                            
                                            if strategy['trade_count'] > 0:
                                                print(f"    ✅ 策略开始交易！")
                                                recent_trades = strategy.get('recent_trades', [])
                                                for trade in recent_trades[:3]:
                                                    print(f"      交易: {trade}")
                                                break
                                            elif strategy['positions'] > 0:
                                                print(f"    ✅ 策略建立持仓！")
                                                break
                                        else:
                                            print(f"    ❌ 获取策略状态失败")
                                            break
                                    
                                    # 5. 检查实盘引擎状态
                                    print(f"\n5. 检查实盘引擎状态...")
                                    response = requests.get(f"{BASE_URL}/api/live/results")
                                    if response.status_code == 200:
                                        data = response.json()
                                        if data.get('success'):
                                            results = data.get('data', [])
                                            print(f"实盘引擎任务数量: {len(results)}")
                                            
                                            for result in results:
                                                if result.get('strategy_name') == 'buy_hold':
                                                    print(f"找到buy_hold任务:")
                                                    print(f"  task_id: {result.get('task_id')}")
                                                    print(f"  状态: {result.get('status')}")
                                                    print(f"  股票池: {result.get('stock_codes', [])}")
                                                    print(f"  当前资金: ¥{result.get('current_capital', 0):,.2f}")
                                                    print(f"  持仓数: {len(result.get('positions', []))}")
                                                    print(f"  交易数: {len(result.get('trades', []))}")
                                                    
                                                    # 显示持仓详情
                                                    positions = result.get('positions', [])
                                                    if positions:
                                                        print(f"  持仓详情:")
                                                        for pos in positions:
                                                            print(f"    - {pos}")
                                                    
                                                    # 显示交易详情
                                                    trades = result.get('trades', [])
                                                    if trades:
                                                        print(f"  交易详情:")
                                                        for trade in trades[-3:]:  # 最近3笔
                                                            print(f"    - {trade}")
                                                    break
                                            else:
                                                print(f"❌ 未找到buy_hold任务")
                                    
                                    # 6. 启动bollinger_bands策略对比
                                    print(f"\n6. 启动bollinger_bands策略对比...")
                                    bb_strategy_data = {
                                        'name': '真实数据bollinger_bands测试',
                                        'strategy_type': 'bollinger_bands',
                                        'config': {
                                            'initial_capital': 100000,
                                            'paper_trading': True,
                                            'stock_codes': [stock_code],
                                            'bb_period': 10,
                                            'bb_std': 1.5
                                        }
                                    }
                                    
                                    response = requests.post(f"{BASE_URL}/api/live/strategies", json=bb_strategy_data)
                                    if response.status_code == 200:
                                        result = response.json()
                                        if result.get('success'):
                                            bb_strategy_id = result['strategy_id']
                                            print(f"✅ bollinger_bands策略启动成功: {bb_strategy_id}")
                                            
                                            # 短暂监控
                                            time.sleep(5)
                                            response = requests.get(f"{BASE_URL}/api/live/strategies/{bb_strategy_id}")
                                            if response.status_code == 200:
                                                bb_strategy = response.json()
                                                print(f"bollinger_bands状态: 持仓:{bb_strategy['positions']} 交易:{bb_strategy['trade_count']}")
                                            
                                            # 清理
                                            requests.delete(f"{BASE_URL}/api/live/strategies/{bb_strategy_id}")
                                    
                                    # 清理buy_hold策略
                                    print(f"\n清理测试策略...")
                                    requests.delete(f"{BASE_URL}/api/live/strategies/{strategy_id}")
                                    print(f"✅ 策略已清理")
                                    
                                    return True
                                else:
                                    print(f"❌ 策略启动失败: {result.get('message')}")
                            else:
                                print(f"❌ 策略启动请求失败: {response.status_code}")
                                print(f"响应: {response.text}")
                        else:
                            print(f"❌ 股票数据为空")
                    else:
                        print(f"❌ 获取股票数据失败: {data}")
                else:
                    print(f"❌ 股票数据API失败: {response.status_code}")
            else:
                print(f"❌ 股票列表为空")
        else:
            print(f"❌ API返回失败: {data}")
    else:
        print(f"❌ 股票列表API失败: {response.status_code}")
        print(f"响应: {response.text}")
    
    return False

def main():
    """主测试函数"""
    print("🚀 开始使用真实xttrader数据测试策略")
    
    success = test_real_data_strategy()
    
    print(f"\n🎯 测试结果: {'✅ 成功' if success else '❌ 失败'}")
    
    if success:
        print(f"\n🎉 真实数据策略测试成功！")
        print(f"✅ xttrader数据源正常工作")
        print(f"✅ 策略执行引擎正常工作")
        print(f"✅ 数据和策略正确集成")
    else:
        print(f"\n💡 建议:")
        print(f"1. 检查QMT是否正在运行")
        print(f"2. 检查网络连接")
        print(f"3. 查看后端日志获取详细信息")

if __name__ == "__main__":
    main()
