import React, { useState, useEffect } from 'react';
import { Card as ShadCard, <PERSON><PERSON>ontent as <PERSON>had<PERSON>ardContent, Card<PERSON><PERSON><PERSON> as <PERSON>had<PERSON><PERSON>Header, CardTitle as ShadCardTitle } from '../components/UI/card.jsx';
import { <PERSON><PERSON> as ShadButton } from '../components/UI/button.jsx';
import { Input } from '../components/UI/input.jsx';

import { DataTable } from '../components/UI/table.jsx';
import { Tag as Badge } from '../components/UI/tag.jsx';
import { RefreshCw, Download, Trash2, BarChart3, Eye, Calendar } from 'lucide-react';
import { toast } from 'sonner';

const API_BASE = 'http://localhost:8000/api';

const BacktestHistoryPage = () => {
  const [historyData, setHistoryData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [selectedResults, setSelectedResults] = useState([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0
  });
  
  // 筛选条件
  const [filters, setFilters] = useState({
    strategy_name: '',
    start_date: '',
    end_date: ''
  });

  // 详情模态框状态
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [detailData, setDetailData] = useState(null);
  const [detailLoading, setDetailLoading] = useState(false);

  // 加载历史数据
  const loadHistoryData = async () => {
    try {
      setLoading(true);
      
      const params = new URLSearchParams({
        limit: pagination.pageSize.toString(),
        offset: ((pagination.current - 1) * pagination.pageSize).toString()
      });
      
      if (filters.strategy_name) params.append('strategy_name', filters.strategy_name);
      if (filters.start_date) params.append('start_date', filters.start_date);
      if (filters.end_date) params.append('end_date', filters.end_date);
      
      const response = await fetch(`${API_BASE}/backtest/history?${params}`);
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const data = await response.json();
      setHistoryData(data.results || []);
      setPagination(prev => ({
        ...prev,
        total: data.total || 0
      }));
      
    } catch (error) {
      console.error('加载回测历史失败:', error);
      toast.error('加载回测历史失败: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  // 查看详情
  const viewDetail = async (taskId) => {
    try {
      setDetailLoading(true);
      setShowDetailModal(true);

      const response = await fetch(`${API_BASE}/backtest/result/${taskId}`);
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      setDetailData(result);
      toast.success('回测结果详情已加载');

    } catch (error) {
      console.error('获取回测详情失败:', error);
      toast.error('获取回测详情失败: ' + error.message);
      setShowDetailModal(false);
    } finally {
      setDetailLoading(false);
    }
  };

  // 删除结果
  const deleteResult = async (taskId) => {
    if (!window.confirm('确定要删除这个回测结果吗？此操作不可恢复。')) {
      return;
    }
    
    try {
      const response = await fetch(`${API_BASE}/backtest/result/${taskId}`, {
        method: 'DELETE'
      });
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      toast.success('回测结果删除成功');
      loadHistoryData(); // 重新加载数据
      
    } catch (error) {
      console.error('删除回测结果失败:', error);
      toast.error('删除回测结果失败: ' + error.message);
    }
  };

  // 导出结果
  const exportResult = async (taskId, format = 'json') => {
    try {
      const response = await fetch(`${API_BASE}/backtest/export/${taskId}?format=${format}`, {
        method: 'POST'
      });
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const result = await response.json();
      toast.success(`导出成功: ${result.file_path}`);
      
    } catch (error) {
      console.error('导出回测结果失败:', error);
      toast.error('导出回测结果失败: ' + error.message);
    }
  };

  // 添加到对比
  const addToCompare = (taskId) => {
    if (selectedResults.includes(taskId)) {
      setSelectedResults(prev => prev.filter(id => id !== taskId));
    } else {
      if (selectedResults.length >= 5) {
        toast.error('最多只能对比5个回测结果');
        return;
      }
      setSelectedResults(prev => [...prev, taskId]);
    }
  };

  // 对比分析
  const compareResults = () => {
    if (selectedResults.length < 2) {
      toast.error('至少选择2个回测结果进行对比');
      return;
    }

    // 跳转到对比页面
    const taskIds = selectedResults.join(',');
    window.open(`/backtest/comparison?task_ids=${taskIds}`, '_blank');
  };

  // 表格列定义
  const columns = [
    {
      accessorKey: 'task_id',
      header: '任务ID',
      cell: ({ row }) => (
        <div className="font-mono text-xs">
          {row.getValue('task_id').substring(0, 8)}...
        </div>
      ),
    },
    {
      accessorKey: 'strategy_name',
      header: '策略名称',
      cell: ({ row }) => (
        <Badge color="blue">
          {row.getValue('strategy_name')}
        </Badge>
      ),
    },
    {
      accessorKey: 'start_date',
      header: '回测期间',
      cell: ({ row }) => (
        <div className="text-sm">
          {row.getValue('start_date')} ~ {row.original.end_date}
        </div>
      ),
    },
    {
      accessorKey: 'total_return',
      header: '总收益率',
      cell: ({ row }) => {
        const value = row.getValue('total_return');
        const percentage = (value * 100).toFixed(2);
        return (
          <span className={value >= 0 ? 'text-green-600 font-semibold' : 'text-red-600 font-semibold'}>
            {percentage}%
          </span>
        );
      },
    },
    {
      accessorKey: 'sharpe_ratio',
      header: '夏普比率',
      cell: ({ row }) => {
        const value = row.getValue('sharpe_ratio');
        return <span className="font-mono">{value?.toFixed(3) || 'N/A'}</span>;
      },
    },
    {
      accessorKey: 'max_drawdown',
      header: '最大回撤',
      cell: ({ row }) => {
        const value = row.getValue('max_drawdown');
        const percentage = (Math.abs(value) * 100).toFixed(2);
        return <span className="text-red-600">-{percentage}%</span>;
      },
    },
    {
      accessorKey: 'win_rate',
      header: '胜率',
      cell: ({ row }) => {
        const value = row.getValue('win_rate');
        const percentage = (value * 100).toFixed(1);
        return <span>{percentage}%</span>;
      },
    },
    {
      accessorKey: 'total_trades',
      header: '交易次数',
      cell: ({ row }) => <span>{row.getValue('total_trades')}</span>,
    },
    {
      accessorKey: 'created_at',
      header: '创建时间',
      cell: ({ row }) => (
        <div className="text-xs text-gray-500">
          {new Date(row.getValue('created_at')).toLocaleString()}
        </div>
      ),
    },
    {
      id: 'actions',
      header: '操作',
      cell: ({ row }) => {
        const taskId = row.getValue('task_id');
        const isSelected = selectedResults.includes(taskId);
        
        return (
          <div className="flex items-center gap-1">
            <ShadButton
              variant="ghost"
              size="sm"
              onClick={() => viewDetail(taskId)}
              className="h-8 w-8 p-0"
            >
              <Eye className="h-4 w-4" />
            </ShadButton>
            <ShadButton
              variant="ghost"
              size="sm"
              onClick={() => addToCompare(taskId)}
              className={`h-8 w-8 p-0 ${isSelected ? 'bg-blue-100 text-blue-600' : ''}`}
            >
              <BarChart3 className="h-4 w-4" />
            </ShadButton>
            <ShadButton
              variant="ghost"
              size="sm"
              onClick={() => exportResult(taskId, 'json')}
              className="h-8 w-8 p-0"
            >
              <Download className="h-4 w-4" />
            </ShadButton>
            <ShadButton
              variant="ghost"
              size="sm"
              onClick={() => deleteResult(taskId)}
              className="h-8 w-8 p-0 text-red-600 hover:text-red-700"
            >
              <Trash2 className="h-4 w-4" />
            </ShadButton>
          </div>
        );
      },
    },
  ];

  // 页面加载时获取数据
  useEffect(() => {
    loadHistoryData();
  }, [pagination.current, pagination.pageSize]); // eslint-disable-line react-hooks/exhaustive-deps

  return (
    <div className="space-y-6 p-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">回测历史</h1>
        <ShadButton onClick={loadHistoryData} disabled={loading}>
          <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
          刷新
        </ShadButton>
      </div>

      {/* 搜索和筛选 */}
      <ShadCard>
        <ShadCardHeader>
          <ShadCardTitle>筛选条件</ShadCardTitle>
        </ShadCardHeader>
        <ShadCardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium mb-1">策略名称</label>
              <Input
                placeholder="输入策略名称"
                value={filters.strategy_name}
                onChange={(e) => setFilters(prev => ({ ...prev, strategy_name: e.target.value }))}
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">开始日期</label>
              <Input
                type="date"
                value={filters.start_date}
                onChange={(e) => setFilters(prev => ({ ...prev, start_date: e.target.value }))}
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">结束日期</label>
              <Input
                type="date"
                value={filters.end_date}
                onChange={(e) => setFilters(prev => ({ ...prev, end_date: e.target.value }))}
              />
            </div>
            <div className="flex items-end">
              <ShadButton onClick={loadHistoryData} className="w-full">
                <Calendar className="h-4 w-4 mr-2" />
                搜索
              </ShadButton>
            </div>
          </div>
        </ShadCardContent>
      </ShadCard>

      {/* 对比功能 */}
      {selectedResults.length > 0 && (
        <ShadCard>
          <ShadCardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <span className="text-sm text-gray-600">
                  已选择 {selectedResults.length} 个回测结果进行对比
                </span>
              </div>
              <div className="flex gap-2">
                <ShadButton
                  variant="outline"
                  onClick={() => setSelectedResults([])}
                >
                  清空选择
                </ShadButton>
                <ShadButton
                  onClick={compareResults}
                  disabled={selectedResults.length < 2}
                >
                  <BarChart3 className="h-4 w-4 mr-2" />
                  开始对比
                </ShadButton>
              </div>
            </div>
          </ShadCardContent>
        </ShadCard>
      )}

      {/* 历史记录表格 */}
      <ShadCard>
        <ShadCardHeader>
          <ShadCardTitle>
            历史回测记录 ({pagination.total} 条)
          </ShadCardTitle>
        </ShadCardHeader>
        <ShadCardContent>
          <DataTable
            data={historyData}
            columns={columns}
            loading={loading}
          />
          
          {/* 分页 */}
          {pagination.total > pagination.pageSize && (
            <div className="flex items-center justify-between mt-4">
              <div className="text-sm text-gray-600">
                显示 {((pagination.current - 1) * pagination.pageSize) + 1} - {Math.min(pagination.current * pagination.pageSize, pagination.total)} 条，共 {pagination.total} 条
              </div>
              <div className="flex gap-2">
                <ShadButton
                  variant="outline"
                  size="sm"
                  onClick={() => setPagination(prev => ({ ...prev, current: prev.current - 1 }))}
                  disabled={pagination.current <= 1}
                >
                  上一页
                </ShadButton>
                <ShadButton
                  variant="outline"
                  size="sm"
                  onClick={() => setPagination(prev => ({ ...prev, current: prev.current + 1 }))}
                  disabled={pagination.current * pagination.pageSize >= pagination.total}
                >
                  下一页
                </ShadButton>
              </div>
            </div>
          )}
        </ShadCardContent>
      </ShadCard>

      {/* 详情模态框 */}
      {showDetailModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg w-full max-w-6xl max-h-[90vh] overflow-hidden flex flex-col">
            <div className="p-6 border-b">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">回测结果详情</h3>
                <button
                  onClick={() => {
                    setShowDetailModal(false);
                    setDetailData(null);
                  }}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            </div>

            <div className="flex-1 overflow-y-auto p-6">
              {detailLoading ? (
                <div className="flex items-center justify-center h-64">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
                  <span className="ml-3 text-gray-600">加载中...</span>
                </div>
              ) : detailData ? (
                <div className="space-y-6">
                  {/* 基本信息 */}
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <ShadCard>
                      <ShadCardContent className="p-4">
                        <div className="text-center">
                          <p className="text-sm text-gray-600">总收益率</p>
                          <p className={`text-xl font-bold ${detailData.total_return >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                            {(detailData.total_return * 100).toFixed(2)}%
                          </p>
                        </div>
                      </ShadCardContent>
                    </ShadCard>

                    <ShadCard>
                      <ShadCardContent className="p-4">
                        <div className="text-center">
                          <p className="text-sm text-gray-600">年化收益率</p>
                          <p className={`text-xl font-bold ${detailData.annual_return >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                            {detailData.annual_return ? (detailData.annual_return * 100).toFixed(2) : 'N/A'}%
                          </p>
                        </div>
                      </ShadCardContent>
                    </ShadCard>

                    <ShadCard>
                      <ShadCardContent className="p-4">
                        <div className="text-center">
                          <p className="text-sm text-gray-600">最大回撤</p>
                          <p className="text-xl font-bold text-red-600">
                            {detailData.max_drawdown ? (detailData.max_drawdown * 100).toFixed(2) : 'N/A'}%
                          </p>
                        </div>
                      </ShadCardContent>
                    </ShadCard>

                    <ShadCard>
                      <ShadCardContent className="p-4">
                        <div className="text-center">
                          <p className="text-sm text-gray-600">夏普比率</p>
                          <p className="text-xl font-bold text-blue-600">
                            {detailData.sharpe_ratio !== null ? detailData.sharpe_ratio.toFixed(2) : 'N/A'}
                          </p>
                        </div>
                      </ShadCardContent>
                    </ShadCard>
                  </div>

                  {/* 详细信息 */}
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <ShadCard>
                      <ShadCardHeader>
                        <ShadCardTitle>回测概览</ShadCardTitle>
                      </ShadCardHeader>
                      <ShadCardContent>
                        <div className="space-y-3">
                          <div className="flex justify-between">
                            <span>任务ID:</span>
                            <span className="font-mono text-sm">{detailData.task_id}</span>
                          </div>
                          <div className="flex justify-between">
                            <span>策略名称:</span>
                            <span className="font-bold">{detailData.strategy_name}</span>
                          </div>
                          <div className="flex justify-between">
                            <span>回测期间:</span>
                            <span>{detailData.start_date} ~ {detailData.end_date}</span>
                          </div>
                          <div className="flex justify-between">
                            <span>初始资金:</span>
                            <span>¥{detailData.initial_capital?.toLocaleString()}</span>
                          </div>
                          <div className="flex justify-between">
                            <span>最终资金:</span>
                            <span>¥{detailData.final_capital?.toLocaleString()}</span>
                          </div>
                          <div className="flex justify-between">
                            <span>状态:</span>
                            <span className="px-2 py-1 bg-green-100 text-green-800 rounded text-sm">
                              {detailData.status}
                            </span>
                          </div>
                        </div>
                      </ShadCardContent>
                    </ShadCard>

                    <ShadCard>
                      <ShadCardHeader>
                        <ShadCardTitle>交易统计</ShadCardTitle>
                      </ShadCardHeader>
                      <ShadCardContent>
                        <div className="space-y-3">
                          <div className="flex justify-between">
                            <span>总交易次数:</span>
                            <span className="font-bold">{detailData.total_trades}</span>
                          </div>
                          <div className="flex justify-between">
                            <span>盈利交易:</span>
                            <span className="font-bold text-green-600">{detailData.profit_trades}</span>
                          </div>
                          <div className="flex justify-between">
                            <span>亏损交易:</span>
                            <span className="font-bold text-red-600">{detailData.loss_trades}</span>
                          </div>
                          <div className="flex justify-between">
                            <span>胜率:</span>
                            <span className="font-bold">{(detailData.win_rate * 100).toFixed(2)}%</span>
                          </div>
                          <div className="flex justify-between">
                            <span>平均盈利:</span>
                            <span className="font-bold text-green-600">
                              ¥{detailData.avg_profit?.toLocaleString()}
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span>平均亏损:</span>
                            <span className="font-bold text-red-600">
                              ¥{detailData.avg_loss?.toLocaleString()}
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span>盈亏比:</span>
                            <span className="font-bold">
                              {detailData.profit_factor !== null ? detailData.profit_factor.toFixed(2) : 'N/A'}
                            </span>
                          </div>
                        </div>
                      </ShadCardContent>
                    </ShadCard>
                  </div>

                  {/* 交易记录 */}
                  {detailData.trades && detailData.trades.length > 0 && (
                    <ShadCard>
                      <ShadCardHeader>
                        <ShadCardTitle>交易记录 ({detailData.trades.length} 笔)</ShadCardTitle>
                      </ShadCardHeader>
                      <ShadCardContent>
                        <div className="overflow-x-auto">
                          <table className="w-full text-sm">
                            <thead>
                              <tr className="border-b">
                                <th className="text-left p-2">日期</th>
                                <th className="text-left p-2">股票</th>
                                <th className="text-left p-2">操作</th>
                                <th className="text-right p-2">价格</th>
                                <th className="text-right p-2">数量</th>
                                <th className="text-right p-2">金额</th>
                                <th className="text-right p-2">盈亏</th>
                              </tr>
                            </thead>
                            <tbody>
                              {detailData.trades.slice(0, 10).map((trade, index) => (
                                <tr key={index} className="border-b hover:bg-gray-50">
                                  <td className="p-2">{trade.date}</td>
                                  <td className="p-2">
                                    <div>
                                      <div className="font-medium">{trade.stock_code}</div>
                                      <div className="text-gray-500 text-xs">{trade.stock_name}</div>
                                    </div>
                                  </td>
                                  <td className="p-2">
                                    <span className={`px-2 py-1 rounded text-xs ${
                                      trade.action === 'buy'
                                        ? 'bg-red-100 text-red-800'
                                        : 'bg-green-100 text-green-800'
                                    }`}>
                                      {trade.action_name}
                                    </span>
                                  </td>
                                  <td className="p-2 text-right">¥{trade.price?.toFixed(2)}</td>
                                  <td className="p-2 text-right">{trade.quantity?.toLocaleString()}</td>
                                  <td className="p-2 text-right">¥{trade.amount?.toLocaleString()}</td>
                                  <td className="p-2 text-right">
                                    {trade.profit_loss !== undefined ? (
                                      <span className={trade.profit_loss >= 0 ? 'text-green-600' : 'text-red-600'}>
                                        ¥{trade.profit_loss?.toLocaleString()}
                                      </span>
                                    ) : '-'}
                                  </td>
                                </tr>
                              ))}
                            </tbody>
                          </table>
                          {detailData.trades.length > 10 && (
                            <div className="text-center py-4 text-gray-500">
                              显示前10笔交易，共{detailData.trades.length}笔
                            </div>
                          )}
                        </div>
                      </ShadCardContent>
                    </ShadCard>
                  )}
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  暂无数据
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default BacktestHistoryPage;
