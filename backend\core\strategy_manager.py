#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
策略管理器
"""

import json
import os
import importlib
from typing import Dict, Any, List, Optional, Type
from datetime import datetime
import logging

logger = logging.getLogger(__name__)


class StrategyManager:
    """策略管理器"""
    
    def __init__(self, strategies_config_file: str = "backend/config/strategies.json"):
        # 处理相对路径，确保从项目根目录开始
        import os
        if not os.path.isabs(strategies_config_file):
            # 获取项目根目录
            current_dir = os.path.dirname(os.path.abspath(__file__))
            project_root = os.path.dirname(os.path.dirname(current_dir))
            self.strategies_config_file = os.path.join(project_root, strategies_config_file)
        else:
            self.strategies_config_file = strategies_config_file
        self.strategies_info = {}
        self.strategy_classes = {}
        self.load_strategies()
    
    def load_strategies(self):
        """加载策略配置"""
        try:
            if os.path.exists(self.strategies_config_file):
                with open(self.strategies_config_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.strategies_info = data.get('strategies', {})
                    logger.info(f"加载了 {len(self.strategies_info)} 个策略配置")
            else:
                logger.warning(f"策略配置文件不存在: {self.strategies_config_file}")
                self.strategies_info = {}
        except Exception as e:
            logger.error(f"加载策略配置失败: {e}")
            self.strategies_info = {}
    
    def get_strategy_info(self, strategy_name: str) -> Optional[Dict[str, Any]]:
        """获取策略基础信息"""
        return self.strategies_info.get(strategy_name)
    
    def get_all_strategies_info(self) -> Dict[str, Any]:
        """获取所有策略基础信息"""
        return self.strategies_info
    
    def get_strategy_config(self, strategy_name: str) -> Optional[Dict[str, Any]]:
        """获取策略详细配置"""
        strategy_info = self.get_strategy_info(strategy_name)
        if not strategy_info:
            return None
        
        config_file = strategy_info.get('config_file')
        if not config_file or not os.path.exists(config_file):
            logger.error(f"策略配置文件不存在: {config_file}")
            return None
        
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
                return config
        except Exception as e:
            logger.error(f"加载策略配置失败: {e}")
            return None
    
    def save_strategy_config(self, strategy_name: str, config: Dict[str, Any]) -> bool:
        """保存策略配置"""
        strategy_info = self.get_strategy_info(strategy_name)
        if not strategy_info:
            logger.error(f"策略不存在: {strategy_name}")
            return False
        
        config_file = strategy_info.get('config_file')
        if not config_file:
            logger.error(f"策略配置文件路径未配置: {strategy_name}")
            return False
        
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(config_file), exist_ok=True)
            
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            
            logger.info(f"策略配置保存成功: {strategy_name}")
            return True
        except Exception as e:
            logger.error(f"保存策略配置失败: {e}")
            return False
    
    def get_strategy_class(self, strategy_name: str) -> Optional[Type]:
        """获取策略类"""
        if strategy_name in self.strategy_classes:
            return self.strategy_classes[strategy_name]
        
        strategy_info = self.get_strategy_info(strategy_name)
        if not strategy_info:
            return None
        
        class_path = strategy_info.get('class_path')
        if not class_path:
            logger.error(f"策略类路径未配置: {strategy_name}")
            return None
        
        try:
            # 解析模块路径和类名
            module_path, class_name = class_path.rsplit('.', 1)
            
            # 动态导入模块
            module = importlib.import_module(module_path)
            strategy_class = getattr(module, class_name)
            
            # 缓存策略类
            self.strategy_classes[strategy_name] = strategy_class
            
            return strategy_class
        except Exception as e:
            logger.error(f"加载策略类失败: {strategy_name}, {e}")
            return None
    
    def create_strategy_instance(self, strategy_name: str, **kwargs) -> Optional[Any]:
        """创建策略实例"""
        strategy_class = self.get_strategy_class(strategy_name)
        if not strategy_class:
            return None
        
        try:
            # 获取策略配置
            config = self.get_strategy_config(strategy_name)
            if config:
                kwargs.update(config.get('parameters', {}))
            
            return strategy_class(**kwargs)
        except Exception as e:
            logger.error(f"创建策略实例失败: {strategy_name}, {e}")
            return None
    
    def get_enabled_strategies(self) -> List[str]:
        """获取启用的策略列表"""
        return [
            name for name, info in self.strategies_info.items()
            if info.get('enabled', True)
        ]
    
    def enable_strategy(self, strategy_name: str) -> bool:
        """启用策略"""
        return self._update_strategy_status(strategy_name, True)
    
    def disable_strategy(self, strategy_name: str) -> bool:
        """禁用策略"""
        return self._update_strategy_status(strategy_name, False)
    
    def _update_strategy_status(self, strategy_name: str, enabled: bool) -> bool:
        """更新策略状态"""
        if strategy_name not in self.strategies_info:
            return False
        
        try:
            self.strategies_info[strategy_name]['enabled'] = enabled
            self.strategies_info[strategy_name]['updated_at'] = datetime.now().isoformat()
            
            # 保存到文件
            with open(self.strategies_config_file, 'w', encoding='utf-8') as f:
                json.dump({'strategies': self.strategies_info}, f, indent=2, ensure_ascii=False)
            
            return True
        except Exception as e:
            logger.error(f"更新策略状态失败: {e}")
            return False
    
    def add_strategy(self, strategy_info: Dict[str, Any]) -> bool:
        """添加新策略"""
        strategy_name = strategy_info.get('name')
        if not strategy_name:
            logger.error("策略名称不能为空")
            return False
        
        if strategy_name in self.strategies_info:
            logger.error(f"策略已存在: {strategy_name}")
            return False
        
        try:
            # 添加时间戳
            strategy_info['created_at'] = datetime.now().isoformat()
            strategy_info['updated_at'] = datetime.now().isoformat()
            
            self.strategies_info[strategy_name] = strategy_info
            
            # 保存到文件
            with open(self.strategies_config_file, 'w', encoding='utf-8') as f:
                json.dump({'strategies': self.strategies_info}, f, indent=2, ensure_ascii=False)
            
            logger.info(f"添加策略成功: {strategy_name}")
            return True
        except Exception as e:
            logger.error(f"添加策略失败: {e}")
            return False
    
    def remove_strategy(self, strategy_name: str) -> bool:
        """移除策略"""
        if strategy_name not in self.strategies_info:
            return False
        
        try:
            del self.strategies_info[strategy_name]
            
            # 保存到文件
            with open(self.strategies_config_file, 'w', encoding='utf-8') as f:
                json.dump({'strategies': self.strategies_info}, f, indent=2, ensure_ascii=False)
            
            # 清除缓存
            if strategy_name in self.strategy_classes:
                del self.strategy_classes[strategy_name]
            
            logger.info(f"移除策略成功: {strategy_name}")
            return True
        except Exception as e:
            logger.error(f"移除策略失败: {e}")
            return False


# 全局策略管理器实例
strategy_manager = StrategyManager()
