import React, { useState, useEffect } from 'react';
import { Card as ShadCard, <PERSON><PERSON>ead<PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>eader, Card<PERSON><PERSON>le as <PERSON>had<PERSON><PERSON><PERSON>itle, CardContent as ShadCardContent } from './UI/card.jsx';
import { <PERSON><PERSON> as ShadButton } from './UI/button.jsx';
import { Alert as ShadAlert } from './UI/alert.jsx';

import { Select, SelectItem } from './UI/select.jsx';
import { Input } from './UI/input.jsx';
import { Switch } from './UI/switch.jsx';
import { Modal } from './UI/modal.jsx';
import { Tag } from './UI/tag.jsx';
import { Settings, AlertTriangle } from 'lucide-react';
import { backtestAPI } from '../services/api';
import StockUniverseSelector from './StockUniverseSelector';

const LiveTradingConfig = ({ 
  visible, 
  onClose, 
  onSubmit, 
  loading = false 
}) => {
  const [availableStrategies, setAvailableStrategies] = useState([]);
  const [formData, setFormData] = useState({
    strategy_name: '',
    initial_capital: 100000,
    commission: 0.001,
    stock_universe: {
      type: 'universe',
      universe_name: 'default_small',
      stock_codes: ['000001.SZ', '000002.SZ']
    },
    paper_trading: true,
    strategy_params: {}
  });

  // 加载可用策略
  useEffect(() => {
    const loadStrategies = async () => {
      try {
        const response = await backtestAPI.getAvailableStrategies();
        console.log('策略API响应:', response); // 调试日志

        if (response && response.success && Array.isArray(response.data)) {
          setAvailableStrategies(response.data);
        } else {
          console.warn('策略数据格式异常:', response);
          setAvailableStrategies([]);
        }
      } catch (err) {
        console.error('加载策略失败:', err);
        setAvailableStrategies([]);
      }
    };

    if (visible) {
      loadStrategies();
    }
  }, [visible]);



  // 处理策略参数变化
  const handleStrategyParamChange = (key, value) => {
    setFormData(prev => ({
      ...prev,
      strategy_params: {
        ...prev.strategy_params,
        [key]: value
      }
    }));
  };

  // 获取当前策略的参数配置
  const getCurrentStrategyParams = () => {
    if (!Array.isArray(availableStrategies)) {
      return [];
    }
    const strategy = availableStrategies.find(s => s && s.name === formData.strategy_name);
    const params = strategy?.parameters || {};

    // 转换参数格式：从 {param_name: {type, value, description}} 到数组格式
    if (typeof params === 'object' && !Array.isArray(params)) {
      return Object.entries(params).map(([name, config]) => ({
        name,
        type: config.type || 'text',
        default: config.value,
        description: config.description || '',
        min: config.min,
        max: config.max,
        options: config.options
      }));
    }

    return Array.isArray(params) ? params : [];
  };

  // 提交表单
  const handleSubmit = () => {
    // 验证必填字段
    if (!formData.strategy_name) {
      alert('请选择策略');
      return;
    }
    if (!formData.stock_universe?.stock_codes?.length) {
      alert('请选择股票池');
      return;
    }

    onSubmit(formData);
  };

  // 重置表单
  const resetForm = () => {
    setFormData({
      strategy_name: '',
      initial_capital: 100000,
      commission: 0.001,
      stock_universe: {
        type: 'universe',
        universe_name: 'default_small',
        stock_codes: ['000001.SZ', '000002.SZ']
      },
      paper_trading: true,
      strategy_params: {}
    });
  };

  return (
    <Modal
      title="启动实盘交易"
      open={visible}
      onClose={() => {
        onClose();
        resetForm();
      }}
      width="900px"
      className="max-h-[90vh]"
    >
      <div className="space-y-6 max-h-[75vh] overflow-y-auto pr-2">
        {/* 安全提醒 */}
        <ShadAlert
          title="重要提醒"
          description="实盘交易将使用真实资金进行交易。建议先进行充分的回测验证，并从小资金开始。"
          variant="warning"
          className="mb-4"
        />

        {/* 基本配置 */}
        <ShadCard>
          <ShadCardHeader>
            <ShadCardTitle className="flex items-center gap-2">
              <Settings className="w-4 h-4" />
              基本配置
            </ShadCardTitle>
          </ShadCardHeader>
          <ShadCardContent className="space-y-6">
            {/* 策略选择 */}
            <div className="space-y-2">
              <label className="text-sm font-medium">
                交易策略 <span className="text-red-500">*</span>
              </label>
              <Select
                value={formData.strategy_name}
                onValueChange={(value) => setFormData(prev => ({ ...prev, strategy_name: value }))}
                placeholder="选择交易策略"
                className="w-full"
              >
                {Array.isArray(availableStrategies) && availableStrategies.map(strategy => {
                  // 确保strategy是有效对象
                  if (!strategy || typeof strategy !== 'object' || !strategy.name) {
                    return null;
                  }

                  return (
                    <SelectItem key={strategy.name} value={strategy.name}>
                      <div className="flex flex-col items-start w-full py-1">
                        <div className="flex items-center justify-between w-full">
                          <span className="font-medium">{strategy.display_name || strategy.name}</span>
                          <Tag color="blue" size="sm">{strategy.name}</Tag>
                        </div>
                        {strategy.description && (
                          <span className="text-xs text-gray-500 mt-1 line-clamp-2">
                            {strategy.description}
                          </span>
                        )}
                      </div>
                    </SelectItem>
                  );
                })}
              </Select>
            </div>

            {/* 初始资金 */}
            <div className="space-y-2">
              <label className="text-sm font-medium">
                初始资金 <span className="text-red-500">*</span>
              </label>
              <Input
                type="number"
                value={formData.initial_capital}
                onChange={(e) => setFormData(prev => ({ ...prev, initial_capital: Number(e.target.value) }))}
                placeholder="请输入初始资金"
                suffix="元"
              />
            </div>

            {/* 手续费率 */}
            <div className="space-y-2">
              <label className="text-sm font-medium">手续费率</label>
              <Input
                type="number"
                step="0.0001"
                value={formData.commission}
                onChange={(e) => setFormData(prev => ({ ...prev, commission: Number(e.target.value) }))}
                placeholder="请输入手续费率"
                suffix="%"
              />
            </div>

            {/* 股票池选择 */}
            <div className="space-y-2">
              <label className="text-sm font-medium">
                股票池 <span className="text-red-500">*</span>
              </label>
              <StockUniverseSelector
                value={formData.stock_universe}
                onChange={(universeData) => {
                  setFormData(prev => ({
                    ...prev,
                    stock_universe: universeData,
                    stock_codes: universeData.stock_codes || []
                  }));
                }}
                allowCustom={true}
                allowCreate={true}
              />
            </div>

            {/* 纸上交易开关 */}
            <div className="space-y-2">
              <label className="text-sm font-medium">交易模式</label>
              <div className="flex items-center gap-4">
                <Switch
                  checked={formData.paper_trading}
                  onChange={(checked) => setFormData(prev => ({ ...prev, paper_trading: checked }))}
                />
                <span className="text-sm">
                  {formData.paper_trading ? (
                    <Tag color="green">纸上交易（模拟）</Tag>
                  ) : (
                    <Tag color="red">实盘交易（真实资金）</Tag>
                  )}
                </span>
              </div>
            </div>
          </ShadCardContent>
        </ShadCard>

        {/* 策略参数 */}
        {formData.strategy_name && getCurrentStrategyParams().length > 0 && (
          <ShadCard>
            <ShadCardHeader>
              <ShadCardTitle className="flex items-center gap-2">
                <AlertTriangle className="w-4 h-4" />
                策略参数
                <span className="text-sm font-normal text-gray-500">
                  ({getCurrentStrategyParams().length} 个参数)
                </span>
              </ShadCardTitle>
            </ShadCardHeader>
            <ShadCardContent className="space-y-4 max-h-60 overflow-y-auto">
              {getCurrentStrategyParams().map(param => {
                // 确保param是有效对象
                if (!param || typeof param !== 'object' || !param.name) {
                  return null;
                }

                return (
                  <div key={param.name} className="space-y-2 p-3 bg-gray-50 rounded-lg">
                    <label className="text-sm font-medium flex items-center justify-between">
                      <span>{param.display_name || param.name}</span>
                      <span className="text-xs text-gray-400 font-normal">
                        {param.type}
                      </span>
                    </label>
                    {param.type === 'boolean' ? (
                      <div className="flex items-center gap-3">
                        <Switch
                          checked={formData.strategy_params[param.name] !== undefined
                            ? formData.strategy_params[param.name]
                            : param.default}
                          onChange={(checked) => handleStrategyParamChange(param.name, checked)}
                        />
                        <span className="text-sm text-gray-600">
                          {formData.strategy_params[param.name] !== undefined
                            ? formData.strategy_params[param.name]
                            : param.default ? '启用' : '禁用'}
                        </span>
                      </div>
                    ) : param.type === 'select' ? (
                      <Select
                        value={formData.strategy_params[param.name] || param.default}
                        onValueChange={(value) => handleStrategyParamChange(param.name, value)}
                      >
                        {Array.isArray(param.options) && param.options.map(option => {
                          if (!option || typeof option !== 'object') return null;
                          return (
                            <SelectItem key={option.value} value={option.value}>
                              {option.label || option.value}
                            </SelectItem>
                          );
                        })}
                      </Select>
                    ) : (
                      <div className="space-y-1">
                        <Input
                          type={param.type === 'int' || param.type === 'float' ? 'number' : 'text'}
                          step={param.type === 'float' ? '0.01' : '1'}
                          min={param.min}
                          max={param.max}
                          value={formData.strategy_params[param.name] !== undefined
                            ? formData.strategy_params[param.name]
                            : param.default || ''}
                          onChange={(e) => handleStrategyParamChange(
                            param.name,
                            param.type === 'int' ? parseInt(e.target.value) || 0 :
                            param.type === 'float' ? parseFloat(e.target.value) || 0 :
                            e.target.value
                          )}
                          placeholder={param.description || ''}
                          className="w-full"
                        />
                        {(param.min !== undefined || param.max !== undefined) && (
                          <div className="text-xs text-gray-500">
                            范围: {param.min !== undefined ? param.min : '无限制'} - {param.max !== undefined ? param.max : '无限制'}
                          </div>
                        )}
                      </div>
                    )}
                    {param.description && typeof param.description === 'string' && (
                      <div className="text-xs text-gray-500 mt-1">
                        {param.description}
                      </div>
                    )}
                  </div>
                );
              })}
            </ShadCardContent>
          </ShadCard>
        )}
      </div>

      {/* 操作按钮 - 固定在底部 */}
      <div className="flex gap-3 pt-4 mt-6 border-t border-gray-200 bg-white">
        <ShadButton
          onClick={handleSubmit}
          disabled={loading || !formData.strategy_name}
          className="bg-blue-600 hover:bg-blue-700 text-white flex-1"
        >
          {loading ? '启动中...' : '启动交易'}
        </ShadButton>
        <ShadButton
          onClick={() => {
            onClose();
            resetForm();
          }}
          variant="outline"
          className="flex-1"
        >
          取消
        </ShadButton>
      </div>
    </Modal>
  );
};

export default LiveTradingConfig;
