import * as React from 'react'
import * as Popover from '@radix-ui/react-popover'
import { DayPicker } from 'react-day-picker'
import { format } from 'date-fns'
import 'react-day-picker/dist/style.css'

export function DatePicker({ value, onChange, placeholder = '选择日期', className = '' }) {
  const selectedDate = value ? (value.toDate ? value.toDate() : value) : undefined
  
  return (
    <Popover.Root>
      <Popover.Trigger asChild>
        <button className={`w-full h-9 px-3 rounded-md border bg-white text-left ${className}`}>
          {selectedDate ? format(selectedDate, 'yyyy-MM-dd') : placeholder}
        </button>
      </Popover.Trigger>
      <Popover.Content
        className="bg-white p-2 rounded-md shadow-lg border z-[9999]"
        sideOffset={8}
        align="start"
        side="bottom"
        avoidCollisions={true}
        collisionPadding={20}
      >
        <DayPicker
          mode="single"
          selected={selectedDate}
          onSelect={(date) => {
            if (onChange && date) {
              // 如果传入的是moment对象，返回moment对象
              if (value && value.format) {
                const moment = require('moment')
                onChange(moment(date))
              } else {
                onChange(date)
              }
            }
          }}
          numberOfMonths={1}
        />
      </Popover.Content>
    </Popover.Root>
  )
}
