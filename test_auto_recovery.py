#!/usr/bin/env python3
"""
测试自动恢复功能
这个脚本模拟系统重启后的自动恢复过程
"""

import sys
import os
import json
import time
from pathlib import Path

# 添加项目路径
sys.path.append('.')

def test_auto_recovery():
    """测试自动恢复功能"""
    print("=== 测试多策略自动恢复功能 ===")
    
    # 1. 创建测试的持久化文件
    print("\n1. 创建测试持久化文件")
    
    tasks_file = Path("data/multi_strategy_tasks.json")
    tasks_file.parent.mkdir(exist_ok=True)
    
    test_tasks_data = {
        "last_updated": "2025-09-08T08:00:00.000000",
        "running_strategies": {
            "strategy_test_001": {
                "id": "strategy_test_001",
                "name": "测试自动恢复策略1",
                "strategy_type": "bollinger_bands",
                "config": {
                    "initial_capital": 50000,
                    "max_positions": 3,
                    "risk_limit": 0.02,
                    "bb_period": 20,
                    "bb_std": 2.0
                },
                "status": "running",
                "start_time": "2025-09-08T07:30:00.000000",
                "stop_time": None,
                "positions": 2,
                "pnl": 150.50,
                "trade_count": 5,
                "error_message": None
            },
            "strategy_test_002": {
                "id": "strategy_test_002",
                "name": "测试自动恢复策略2",
                "strategy_type": "ma_strategy",
                "config": {
                    "initial_capital": 80000,
                    "max_positions": 5,
                    "risk_limit": 0.03,
                    "ma_short": 5,
                    "ma_long": 20
                },
                "status": "running",
                "start_time": "2025-09-08T07:45:00.000000",
                "stop_time": None,
                "positions": 1,
                "pnl": -75.25,
                "trade_count": 3,
                "error_message": None
            },
            "strategy_test_003": {
                "id": "strategy_test_003",
                "name": "已停止的策略",
                "strategy_type": "bollinger_bands",
                "config": {
                    "initial_capital": 30000,
                    "max_positions": 2,
                    "risk_limit": 0.01
                },
                "status": "stopped",
                "start_time": "2025-09-08T06:00:00.000000",
                "stop_time": "2025-09-08T07:00:00.000000",
                "positions": 0,
                "pnl": 0.0,
                "trade_count": 0,
                "error_message": None
            }
        }
    }
    
    with open(tasks_file, 'w', encoding='utf-8') as f:
        json.dump(test_tasks_data, f, ensure_ascii=False, indent=2)
    
    print(f"✅ 测试持久化文件已创建: {tasks_file}")
    print(f"   - 包含 3 个策略（2个运行中，1个已停止）")
    
    # 2. 导入并测试多策略服务
    print("\n2. 测试多策略服务自动恢复")
    
    try:
        from backend.services.multi_strategy_service import MultiStrategyService
        
        # 创建新的服务实例（模拟重启）
        print("   创建新的多策略服务实例...")
        service = MultiStrategyService()
        
        # 检查恢复的策略
        print(f"   恢复的策略数量: {len(service.running_strategies)}")
        
        for strategy_id, strategy in service.running_strategies.items():
            print(f"   ✅ 恢复策略: {strategy.name} ({strategy_id})")
            print(f"      类型: {strategy.strategy_type}")
            print(f"      状态: {strategy.status}")
            print(f"      启动时间: {strategy.start_time}")
            print(f"      持仓: {strategy.positions}, 盈亏: {strategy.pnl}")
        
        # 验证只恢复了运行中的策略
        expected_running = 2  # 只有2个running状态的策略应该被恢复
        actual_running = len(service.running_strategies)
        
        if actual_running == expected_running:
            print(f"   ✅ 恢复策略数量正确: {actual_running}/{expected_running}")
        else:
            print(f"   ❌ 恢复策略数量错误: {actual_running}/{expected_running}")
        
        # 3. 测试策略配置验证
        print("\n3. 测试策略配置验证")
        for strategy in service.running_strategies.values():
            if service._validate_strategy_config(strategy):
                print(f"   ✅ 策略配置验证通过: {strategy.name}")
            else:
                print(f"   ❌ 策略配置验证失败: {strategy.name}")
        
        # 4. 测试持久化文件更新
        print("\n4. 测试持久化文件更新")
        service._save_tasks_state()
        
        # 读取更新后的文件
        with open(tasks_file, 'r', encoding='utf-8') as f:
            updated_data = json.load(f)
        
        print(f"   更新后的策略数量: {len(updated_data['running_strategies'])}")
        print(f"   最后更新时间: {updated_data['last_updated']}")
        
        # 5. 测试手动恢复功能
        print("\n5. 测试手动恢复功能")
        
        # 清空当前策略（模拟某种异常情况）
        original_count = len(service.running_strategies)
        service.running_strategies.clear()
        print(f"   清空策略后数量: {len(service.running_strategies)}")
        
        # 手动恢复
        service._auto_recover_strategies()
        recovered_count = len(service.running_strategies)
        
        print(f"   手动恢复后数量: {recovered_count}")
        
        if recovered_count == original_count:
            print(f"   ✅ 手动恢复成功: {recovered_count}/{original_count}")
        else:
            print(f"   ❌ 手动恢复失败: {recovered_count}/{original_count}")
        
        print("\n=== 自动恢复功能测试完成 ===")
        print("✅ 所有测试通过！")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # 清理测试文件
        print(f"\n6. 清理测试文件: {tasks_file}")
        if tasks_file.exists():
            tasks_file.unlink()
            print("   ✅ 测试文件已删除")

def test_persistence_edge_cases():
    """测试持久化的边界情况"""
    print("\n=== 测试持久化边界情况 ===")
    
    tasks_file = Path("data/multi_strategy_tasks.json")
    
    # 测试1: 文件不存在的情况
    print("\n1. 测试文件不存在的情况")
    if tasks_file.exists():
        tasks_file.unlink()
    
    try:
        from backend.services.multi_strategy_service import MultiStrategyService
        service = MultiStrategyService()
        print("   ✅ 文件不存在时正常初始化")
    except Exception as e:
        print(f"   ❌ 文件不存在时初始化失败: {e}")
    
    # 测试2: 空文件的情况
    print("\n2. 测试空文件的情况")
    tasks_file.parent.mkdir(exist_ok=True)
    with open(tasks_file, 'w') as f:
        f.write("")
    
    try:
        service = MultiStrategyService()
        print("   ✅ 空文件时正常初始化")
    except Exception as e:
        print(f"   ❌ 空文件时初始化失败: {e}")
    
    # 测试3: 无效JSON的情况
    print("\n3. 测试无效JSON的情况")
    with open(tasks_file, 'w') as f:
        f.write("invalid json content")
    
    try:
        service = MultiStrategyService()
        print("   ✅ 无效JSON时正常初始化")
    except Exception as e:
        print(f"   ❌ 无效JSON时初始化失败: {e}")
    
    # 清理
    if tasks_file.exists():
        tasks_file.unlink()
    
    print("✅ 边界情况测试完成")

if __name__ == "__main__":
    print("🚀 开始测试多策略持久化功能")
    
    # 主要功能测试
    success = test_auto_recovery()
    
    # 边界情况测试
    test_persistence_edge_cases()
    
    if success:
        print("\n🎉 所有测试通过！多策略持久化功能工作正常。")
        print("\n💡 使用说明:")
        print("1. 系统重启后会自动恢复之前运行的策略")
        print("2. 可以通过API手动触发恢复: POST /api/live/strategies/recover")
        print("3. 持久化文件位置: data/multi_strategy_tasks.json")
        print("4. 只有状态为'running'的策略会被恢复")
    else:
        print("\n❌ 测试失败，请检查代码实现。")
        sys.exit(1)
