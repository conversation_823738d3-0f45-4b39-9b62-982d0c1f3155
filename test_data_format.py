#!/usr/bin/env python3
"""
测试数据格式修复
"""

import sys
import os
import pandas as pd
import logging
from datetime import datetime, timedelta

# 添加项目路径
sys.path.append(os.path.dirname(__file__))

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

def test_dataframe_format():
    """测试DataFrame格式化"""
    logger.info("=== 测试DataFrame格式化 ===")
    
    try:
        from backend.data.data_manager import DataManager
        
        # 创建DataManager实例
        data_manager = DataManager()
        
        # 创建模拟的QMT数据格式（可能的问题格式）
        dates = pd.date_range(start='2025-07-01', end='2025-07-31', freq='D')
        
        # 测试场景1: 字符串索引
        logger.info("\n--- 测试场景1: 字符串索引 ---")
        df_str_index = pd.DataFrame({
            'Open': [10.0, 10.1, 10.2],
            'High': [10.5, 10.6, 10.7],
            'Low': [9.8, 9.9, 10.0],
            'Close': [10.2, 10.3, 10.4],
            'Volume': [1000000, 1100000, 1200000]
        }, index=['2025-07-01', '2025-07-02', '2025-07-03'])
        
        logger.info(f"原始数据索引类型: {type(df_str_index.index)}")
        logger.info(f"原始数据列名: {list(df_str_index.columns)}")
        
        # 格式化数据
        formatted_df = data_manager._format_dataframe_for_backtrader(df_str_index, "TEST.SZ")
        
        logger.info(f"格式化后索引类型: {type(formatted_df.index)}")
        logger.info(f"格式化后列名: {list(formatted_df.columns)}")
        logger.info(f"索引是否为DatetimeIndex: {isinstance(formatted_df.index, pd.DatetimeIndex)}")
        
        # 测试backtrader兼容性
        if isinstance(formatted_df.index, pd.DatetimeIndex):
            # 测试to_pydatetime方法
            first_date = formatted_df.index[0]
            logger.info(f"第一个日期: {first_date}")
            logger.info(f"to_pydatetime(): {first_date.to_pydatetime()}")
            logger.info("✅ backtrader兼容性测试通过")
        else:
            logger.error("❌ 索引不是DatetimeIndex")
            return False
        
        # 测试场景2: 已经是DatetimeIndex但列名大写
        logger.info("\n--- 测试场景2: DatetimeIndex但列名大写 ---")
        df_datetime_index = pd.DataFrame({
            'OPEN': [10.0, 10.1, 10.2],
            'HIGH': [10.5, 10.6, 10.7],
            'LOW': [9.8, 9.9, 10.0],
            'CLOSE': [10.2, 10.3, 10.4],
            'VOLUME': [1000000, 1100000, 1200000]
        }, index=pd.to_datetime(['2025-07-01', '2025-07-02', '2025-07-03']))
        
        formatted_df2 = data_manager._format_dataframe_for_backtrader(df_datetime_index, "TEST2.SZ")
        
        logger.info(f"格式化后列名: {list(formatted_df2.columns)}")
        expected_columns = ['open', 'high', 'low', 'close', 'volume']
        if all(col in formatted_df2.columns for col in expected_columns):
            logger.info("✅ 列名格式化成功")
        else:
            logger.error("❌ 列名格式化失败")
            return False
        
        # 测试场景3: 缺少某些列
        logger.info("\n--- 测试场景3: 缺少某些列 ---")
        df_missing_cols = pd.DataFrame({
            'close': [10.2, 10.3, 10.4],
            'volume': [1000000, 1100000, 1200000]
        }, index=pd.to_datetime(['2025-07-01', '2025-07-02', '2025-07-03']))
        
        formatted_df3 = data_manager._format_dataframe_for_backtrader(df_missing_cols, "TEST3.SZ")
        
        if all(col in formatted_df3.columns for col in expected_columns):
            logger.info("✅ 缺失列补充成功")
        else:
            logger.error("❌ 缺失列补充失败")
            return False
        
        logger.info("🎉 所有DataFrame格式化测试通过！")
        return True
        
    except Exception as e:
        logger.error(f"❌ DataFrame格式化测试失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False

def test_backtrader_compatibility():
    """测试backtrader兼容性"""
    logger.info("\n=== 测试backtrader兼容性 ===")
    
    try:
        import backtrader as bt
        
        # 创建测试数据
        dates = pd.date_range(start='2025-07-01', end='2025-07-10', freq='D')
        df = pd.DataFrame({
            'open': [10.0 + i * 0.1 for i in range(len(dates))],
            'high': [10.5 + i * 0.1 for i in range(len(dates))],
            'low': [9.8 + i * 0.1 for i in range(len(dates))],
            'close': [10.2 + i * 0.1 for i in range(len(dates))],
            'volume': [1000000 + i * 10000 for i in range(len(dates))]
        }, index=dates)
        
        logger.info(f"测试数据索引类型: {type(df.index)}")
        logger.info(f"测试数据形状: {df.shape}")
        
        # 创建backtrader数据源
        data_feed = bt.feeds.PandasData(dataname=df)
        
        # 创建cerebro实例
        cerebro = bt.Cerebro()
        cerebro.adddata(data_feed)
        
        # 添加简单策略
        class TestStrategy(bt.Strategy):
            def __init__(self):
                self.data_count = 0
            
            def next(self):
                self.data_count += 1
                if self.data_count <= 3:  # 只记录前3条
                    logger.info(f"数据点 {self.data_count}: 日期={self.data.datetime.date(0)}, 收盘价={self.data.close[0]:.2f}")
        
        cerebro.addstrategy(TestStrategy)
        
        # 运行测试
        logger.info("开始运行backtrader测试...")
        results = cerebro.run()
        
        logger.info("✅ backtrader兼容性测试通过！")
        return True
        
    except Exception as e:
        logger.error(f"❌ backtrader兼容性测试失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False

def main():
    """主测试函数"""
    logger.info("🔍 开始数据格式修复测试")
    
    # 测试1: DataFrame格式化
    format_ok = test_dataframe_format()
    
    if format_ok:
        # 测试2: backtrader兼容性
        backtrader_ok = test_backtrader_compatibility()
        
        if backtrader_ok:
            logger.info("\n🎉 所有测试通过！")
            logger.info("✅ DataFrame格式化正常")
            logger.info("✅ backtrader兼容性正常")
            logger.info("✅ 数据格式问题已修复")
            logger.info("✅ 现在可以正常运行回测了")
        else:
            logger.error("\n❌ backtrader兼容性测试失败")
    else:
        logger.error("\n❌ DataFrame格式化测试失败")

if __name__ == "__main__":
    main()
