#!/usr/bin/env python3
"""
测试xttrader数据格式
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pandas as pd
from datetime import datetime, timedelta

def test_xttrader_data_format():
    """测试xttrader数据格式"""
    print("🔍 测试xttrader数据格式")
    print("=" * 50)
    
    try:
        import xtquant.xtdata as xt
        print("✅ xtquant模块导入成功")
        
        # 获取股票列表
        stock_list = xt.get_stock_list_in_sector('沪深A股')
        if not stock_list:
            print("❌ 无法获取股票列表")
            return
        
        test_stock = stock_list[0]
        print(f"📊 测试股票: {test_stock}")
        
        # 计算日期范围
        end_date = datetime.now()
        start_date = end_date - timedelta(days=60)
        
        print(f"📅 时间范围: {start_date.strftime('%Y%m%d')} 到 {end_date.strftime('%Y%m%d')}")
        
        # 获取数据
        data = xt.get_market_data(
            stock_list=[test_stock],
            period='1d',
            start_time=start_date.strftime('%Y%m%d'),
            end_time=end_date.strftime('%Y%m%d'),
            fill_data=True
        )
        
        print(f"\n📋 返回数据类型: {type(data)}")
        print(f"📋 返回数据键: {list(data.keys())}")
        
        # 检查每个字段
        for key in data.keys():
            field_data = data[key]
            print(f"\n🔍 字段 '{key}':")
            print(f"   类型: {type(field_data)}")
            print(f"   形状: {field_data.shape if hasattr(field_data, 'shape') else 'N/A'}")
            print(f"   索引: {field_data.index.tolist() if hasattr(field_data, 'index') else 'N/A'}")
            
            if hasattr(field_data, 'loc') and test_stock in field_data.index:
                stock_data = field_data.loc[test_stock]
                print(f"   {test_stock}数据类型: {type(stock_data)}")
                print(f"   {test_stock}数据长度: {len(stock_data) if hasattr(stock_data, '__len__') else 'N/A'}")
                print(f"   {test_stock}前5个值: {stock_data.iloc[:5].tolist() if hasattr(stock_data, 'iloc') else stock_data[:5] if hasattr(stock_data, '__getitem__') else 'N/A'}")
        
        # 尝试构建DataFrame
        print(f"\n🔧 尝试构建DataFrame...")
        
        if 'time' in data and 'open' in data and 'close' in data:
            time_data = data['time'].loc[test_stock]
            open_data = data['open'].loc[test_stock]
            close_data = data['close'].loc[test_stock]
            
            print(f"   时间数据: {type(time_data)}, 长度: {len(time_data)}")
            print(f"   开盘数据: {type(open_data)}, 长度: {len(open_data)}")
            print(f"   收盘数据: {type(close_data)}, 长度: {len(close_data)}")
            
            # 转换时间戳
            dates = pd.to_datetime(time_data, unit='ms')
            print(f"   转换后日期: {dates[:5].tolist()}")
            
            # 构建DataFrame
            df_data = {
                'open': open_data.values,
                'high': data['high'].loc[test_stock].values,
                'low': data['low'].loc[test_stock].values,
                'close': close_data.values,
                'volume': data['volume'].loc[test_stock].values
            }
            
            df = pd.DataFrame(df_data, index=dates)
            
            print(f"✅ DataFrame构建成功!")
            print(f"   形状: {df.shape}")
            print(f"   列: {list(df.columns)}")
            print(f"   索引类型: {type(df.index)}")
            print(f"   前5行:")
            print(df.head())
            
            # 检查数据质量
            print(f"\n📊 数据质量:")
            print(f"   空值: {df.isnull().sum().sum()}")
            print(f"   零值: {(df == 0).sum().sum()}")
            print(f"   价格范围: {df['close'].min():.2f} - {df['close'].max():.2f}")
            print(f"   成交量范围: {df['volume'].min():.0f} - {df['volume'].max():.0f}")
            
            return df
        else:
            print("❌ 缺少必要的数据字段")
            return None
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    test_xttrader_data_format()
