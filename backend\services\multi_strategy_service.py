"""
多策略实盘交易管理服务
支持同时运行多个策略，提供统一的管理接口
"""

import asyncio
import time
from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
import json
from pathlib import Path

from backend.core.logger import get_logger
from backend.trading.qmt_trader import qmt_trader
from backend.services.position_monitor_service import position_monitor_service

logger = get_logger(__name__)

@dataclass
class StrategyInstance:
    """策略实例"""
    id: str
    name: str
    strategy_type: str
    config: Dict[str, Any]
    status: str  # running, stopped, error
    start_time: Optional[str] = None
    stop_time: Optional[str] = None
    positions: int = 0
    pnl: float = 0.0
    trade_count: int = 0
    recent_trades: List[Dict[str, Any]] = None
    error_message: Optional[str] = None
    task_id: Optional[str] = None  # 实际执行引擎的任务ID

    def __post_init__(self):
        if self.recent_trades is None:
            self.recent_trades = []

class MultiStrategyService:
    """多策略管理服务"""
    
    def __init__(self):
        self.running_strategies: Dict[str, StrategyInstance] = {}
        self.is_monitoring = False
        self.monitor_task = None

        # 策略数据存储目录
        self.data_dir = Path("data/strategies")
        self.data_dir.mkdir(parents=True, exist_ok=True)

        # 持久化任务状态文件
        self.tasks_file = Path("data/multi_strategy_tasks.json")

        logger.info("🎯 多策略管理服务初始化完成")

        # 自动恢复之前运行的策略
        self._auto_recover_strategies()
    
    async def start_strategy(self, strategy_data: Dict[str, Any]) -> Dict[str, Any]:
        """启动新策略"""
        try:
            # 生成策略ID
            strategy_id = f"strategy_{int(time.time())}"
            
            # 创建策略实例
            strategy = StrategyInstance(
                id=strategy_id,
                name=strategy_data['name'],
                strategy_type=strategy_data['strategy_type'],
                config=strategy_data.get('config', {}),
                status='running',
                start_time=datetime.now().isoformat()
            )
            
            # 验证策略配置
            if not self._validate_strategy_config(strategy):
                return {
                    'success': False,
                    'message': '策略配置验证失败'
                }
            
            # 添加到运行列表
            self.running_strategies[strategy_id] = strategy

            # 保存策略配置
            self._save_strategy_config(strategy)

            # 保存任务状态到持久化文件
            self._save_tasks_state()

            # 启动实际的策略执行引擎
            await self._start_strategy_execution(strategy)

            # 启动监控（如果还没启动）
            if not self.is_monitoring:
                await self._start_monitoring()

            logger.info(f"🚀 策略启动成功: {strategy.name} ({strategy_id})")

            return {
                'success': True,
                'strategy_id': strategy_id,
                'message': f'策略 {strategy.name} 启动成功'
            }
            
        except Exception as e:
            logger.error(f"❌ 启动策略失败: {e}")
            return {
                'success': False,
                'message': f'启动策略失败: {str(e)}'
            }
    
    async def stop_strategy(self, strategy_id: str) -> Dict[str, Any]:
        """停止策略"""
        try:
            if strategy_id not in self.running_strategies:
                return {
                    'success': False,
                    'message': '策略不存在'
                }
            
            strategy = self.running_strategies[strategy_id]

            # 停止实际的执行引擎
            if strategy.task_id:
                try:
                    from backend.live.simple_live_engine import live_engine
                    await live_engine.stop_live_trading(strategy.task_id)
                    logger.info(f"✅ 停止策略执行引擎: {strategy.task_id}")
                except Exception as e:
                    logger.warning(f"⚠️ 停止策略执行引擎失败: {e}")

            strategy.status = 'stopped'
            strategy.stop_time = datetime.now().isoformat()

            # 保存策略状态
            self._save_strategy_config(strategy)

            # 从运行列表中移除
            del self.running_strategies[strategy_id]

            # 更新持久化任务状态
            self._save_tasks_state()
            
            logger.info(f"🛑 策略停止成功: {strategy.name} ({strategy_id})")
            
            return {
                'success': True,
                'message': f'策略 {strategy.name} 已停止'
            }
            
        except Exception as e:
            logger.error(f"❌ 停止策略失败: {e}")
            return {
                'success': False,
                'message': f'停止策略失败: {str(e)}'
            }
    
    def get_running_strategies(self) -> List[Dict[str, Any]]:
        """获取运行中的策略列表"""
        try:
            strategies = []
            for strategy in self.running_strategies.values():
                # 更新策略状态
                self._update_strategy_status(strategy)
                strategies.append(asdict(strategy))
            
            logger.debug(f"📋 获取运行策略列表: {len(strategies)} 个")
            return strategies
            
        except Exception as e:
            logger.error(f"❌ 获取策略列表失败: {e}")
            return []
    
    def get_strategy_detail(self, strategy_id: str) -> Optional[Dict[str, Any]]:
        """获取策略详情"""
        try:
            if strategy_id not in self.running_strategies:
                return None
            
            strategy = self.running_strategies[strategy_id]
            self._update_strategy_status(strategy)
            
            return asdict(strategy)
            
        except Exception as e:
            logger.error(f"❌ 获取策略详情失败: {e}")
            return None

    async def recover_strategies(self) -> Dict[str, Any]:
        """手动恢复策略（API接口）"""
        try:
            # 清空当前运行的策略
            old_count = len(self.running_strategies)
            self.running_strategies.clear()

            # 重新加载策略
            self._auto_recover_strategies()

            new_count = len(self.running_strategies)

            # 如果有恢复的策略且监控未启动，启动监控
            if new_count > 0 and not self.is_monitoring:
                await self._start_monitoring()

            return {
                'success': True,
                'message': f'策略恢复完成: 原有 {old_count} 个，恢复 {new_count} 个',
                'recovered_count': new_count,
                'previous_count': old_count
            }

        except Exception as e:
            logger.error(f"❌ 手动恢复策略失败: {e}")
            return {
                'success': False,
                'message': f'恢复策略失败: {str(e)}'
            }

    def _validate_strategy_config(self, strategy: StrategyInstance) -> bool:
        """验证策略配置"""
        try:
            config = strategy.config

            # 基本配置验证
            if 'initial_capital' not in config:
                config['initial_capital'] = 50000

            if 'max_positions' not in config:
                config['max_positions'] = 5

            if 'risk_limit' not in config:
                config['risk_limit'] = 0.02

            # 股票代码验证 - 这是关键修复
            stock_codes = config.get('stock_codes', [])
            if not stock_codes:
                logger.error("❌ 股票代码列表为空，策略无法运行")
                return False

            # 验证股票代码格式
            valid_codes = []
            for code in stock_codes:
                if code and isinstance(code, str) and '.' in code and len(code) >= 9:
                    valid_codes.append(code)
                else:
                    logger.warning(f"⚠️ 无效的股票代码: '{code}'")

            if not valid_codes:
                logger.error("❌ 没有有效的股票代码")
                return False

            # 更新配置中的股票代码列表
            config['stock_codes'] = valid_codes
            logger.info(f"✅ 验证通过的股票代码: {valid_codes}")

            # 策略类型特定验证
            if strategy.strategy_type == 'bollinger_bands':
                if 'bb_period' not in config:
                    config['bb_period'] = 20
                if 'bb_std' not in config:
                    config['bb_std'] = 2.0

            elif strategy.strategy_type == 'ma_strategy':
                if 'ma_short' not in config:
                    config['ma_short'] = 5
                if 'ma_long' not in config:
                    config['ma_long'] = 20

            elif strategy.strategy_type == 'buy_hold':
                if 'max_stocks' not in config:
                    config['max_stocks'] = min(3, len(valid_codes))
                if 'position_size' not in config:
                    config['position_size'] = 1.0 / len(valid_codes)

            return True

        except Exception as e:
            logger.error(f"❌ 策略配置验证失败: {e}")
            return False
    
    def _update_strategy_status(self, strategy: StrategyInstance):
        """更新策略状态"""
        try:
            if strategy.status == 'running' and strategy.task_id:
                # 从实际的策略执行器获取状态
                try:
                    from backend.live.simple_live_engine import live_engine
                    results = live_engine.get_all_live_trading_results()

                    # 查找对应的策略结果（使用task_id）
                    for result in results:
                        # 处理不同类型的result对象
                        if hasattr(result, 'task_id'):
                            # LiveTradingResult对象
                            result_task_id = result.task_id
                            positions = getattr(result, 'positions', [])
                            current_capital = getattr(result, 'current_capital', 0)
                            initial_capital = getattr(result, 'initial_capital', 0)
                            trades = getattr(result, 'trades', [])
                        elif isinstance(result, dict):
                            # 字典对象
                            result_task_id = result.get('task_id')
                            positions = result.get('positions', [])
                            current_capital = result.get('current_capital', 0)
                            initial_capital = result.get('initial_capital', 0)
                            trades = result.get('trades', [])
                        else:
                            continue

                        if result_task_id == strategy.task_id:
                            # 更新策略状态
                            strategy.positions = len(positions) if positions else 0
                            strategy.pnl = current_capital - initial_capital
                            strategy.trade_count = len(trades) if trades else 0

                            # 更新最近交易记录
                            recent_trades = trades[-5:] if trades else []  # 最近5笔交易
                            strategy.recent_trades = recent_trades

                            logger.debug(f"📊 更新策略状态: {strategy.name} - 持仓:{strategy.positions}, 盈亏:{strategy.pnl:.2f}, 交易:{strategy.trade_count}")
                            break
                    else:
                        # 如果没有找到对应的策略结果，可能策略还没开始执行
                        logger.debug(f"⚠️ 未找到策略执行结果: {strategy.name} (task_id: {strategy.task_id})")

                except Exception as e:
                    logger.warning(f"⚠️ 无法获取策略真实状态: {strategy.name} - {e}")
                    # 不修改现有状态，保持原值

        except Exception as e:
            logger.error(f"❌ 更新策略状态失败: {e}")
    
    def _save_strategy_config(self, strategy: StrategyInstance):
        """保存策略配置"""
        try:
            config_file = self.data_dir / f"{strategy.id}.json"
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(asdict(strategy), f, ensure_ascii=False, indent=2)
            
            logger.debug(f"💾 策略配置已保存: {strategy.id}")
            
        except Exception as e:
            logger.error(f"❌ 保存策略配置失败: {e}")
    
    def _load_strategy_configs(self):
        """加载策略配置"""
        try:
            config_files = list(self.data_dir.glob("*.json"))
            
            for config_file in config_files:
                with open(config_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # 只加载运行中的策略
                if data.get('status') == 'running':
                    strategy = StrategyInstance(**data)
                    self.running_strategies[strategy.id] = strategy
            
            logger.info(f"📂 加载策略配置完成: {len(self.running_strategies)} 个运行中的策略")
            
        except Exception as e:
            logger.error(f"❌ 加载策略配置失败: {e}")

    def _save_tasks_state(self):
        """保存任务状态到持久化文件"""
        try:
            tasks_data = {
                'last_updated': datetime.now().isoformat(),
                'running_strategies': {}
            }

            # 保存所有运行中的策略
            for strategy_id, strategy in self.running_strategies.items():
                tasks_data['running_strategies'][strategy_id] = {
                    'id': strategy.id,
                    'name': strategy.name,
                    'strategy_type': strategy.strategy_type,
                    'config': strategy.config,
                    'status': strategy.status,
                    'start_time': strategy.start_time,
                    'stop_time': strategy.stop_time,
                    'positions': strategy.positions,
                    'pnl': strategy.pnl,
                    'trade_count': strategy.trade_count,
                    'error_message': strategy.error_message
                }

            # 写入文件
            with open(self.tasks_file, 'w', encoding='utf-8') as f:
                json.dump(tasks_data, f, ensure_ascii=False, indent=2)

            logger.debug(f"💾 任务状态已保存: {len(self.running_strategies)} 个策略")

        except Exception as e:
            logger.error(f"❌ 保存任务状态失败: {e}")

    def _load_tasks_state(self) -> Dict[str, Any]:
        """从持久化文件加载任务状态"""
        try:
            if not self.tasks_file.exists():
                logger.info("📂 任务状态文件不存在，跳过恢复")
                return {}

            with open(self.tasks_file, 'r', encoding='utf-8') as f:
                tasks_data = json.load(f)

            logger.info(f"📂 任务状态文件加载成功，上次更新: {tasks_data.get('last_updated', 'N/A')}")
            return tasks_data

        except Exception as e:
            logger.error(f"❌ 加载任务状态失败: {e}")
            return {}

    def _auto_recover_strategies(self):
        """自动恢复之前运行的策略"""
        try:
            tasks_data = self._load_tasks_state()
            running_strategies = tasks_data.get('running_strategies', {})

            if not running_strategies:
                logger.info("📂 没有需要恢复的策略")
                return

            recovered_count = 0
            failed_count = 0

            for strategy_id, strategy_data in running_strategies.items():
                try:
                    # 只恢复状态为running的策略
                    if strategy_data.get('status') != 'running':
                        continue

                    # 创建策略实例
                    strategy = StrategyInstance(
                        id=strategy_data['id'],
                        name=strategy_data['name'],
                        strategy_type=strategy_data['strategy_type'],
                        config=strategy_data.get('config', {}),
                        status='running',
                        start_time=strategy_data.get('start_time'),
                        stop_time=strategy_data.get('stop_time'),
                        positions=strategy_data.get('positions', 0),
                        pnl=strategy_data.get('pnl', 0.0),
                        trade_count=strategy_data.get('trade_count', 0),
                        recent_trades=[]
                    )

                    # 验证策略配置
                    if self._validate_strategy_config(strategy):
                        self.running_strategies[strategy_id] = strategy
                        recovered_count += 1
                        logger.info(f"🔄 策略恢复成功: {strategy.name} ({strategy_id})")
                    else:
                        failed_count += 1
                        logger.warning(f"⚠️ 策略配置验证失败，跳过恢复: {strategy.name}")

                except Exception as e:
                    failed_count += 1
                    logger.error(f"❌ 恢复策略失败: {strategy_id} - {e}")

            if recovered_count > 0:
                logger.info(f"✅ 策略自动恢复完成: 成功 {recovered_count} 个，失败 {failed_count} 个")

                # 如果有恢复的策略，启动监控
                if self.running_strategies and not self.is_monitoring:
                    # 使用异步方式启动监控
                    import asyncio
                    try:
                        loop = asyncio.get_event_loop()
                        if loop.is_running():
                            # 如果事件循环正在运行，创建任务
                            asyncio.create_task(self._start_monitoring())
                        else:
                            # 如果事件循环未运行，直接运行
                            loop.run_until_complete(self._start_monitoring())
                    except RuntimeError:
                        # 如果没有事件循环，记录日志，稍后手动启动
                        logger.info("🔍 监控将在服务启动后自动开始")
            else:
                logger.info("📂 没有成功恢复的策略")

        except Exception as e:
            logger.error(f"❌ 自动恢复策略失败: {e}")

    async def _start_monitoring(self):
        """启动监控"""
        if self.is_monitoring:
            return
        
        self.is_monitoring = True
        self.monitor_task = asyncio.create_task(self._monitor_loop())
        logger.info("🔍 策略监控已启动")
    
    async def _monitor_loop(self):
        """监控循环"""
        while self.is_monitoring:
            try:
                # 更新所有策略状态
                for strategy in self.running_strategies.values():
                    self._update_strategy_status(strategy)
                
                # 检查策略健康状态
                await self._check_strategy_health()
                
                # 等待下次检查
                await asyncio.sleep(30)  # 每30秒检查一次
                
            except Exception as e:
                logger.error(f"❌ 策略监控循环异常: {e}")
                await asyncio.sleep(60)  # 出错时等待更长时间
    
    async def _check_strategy_health(self):
        """检查策略健康状态"""
        try:
            for strategy_id, strategy in list(self.running_strategies.items()):
                # 检查策略是否出现异常
                if strategy.status == 'error':
                    logger.warning(f"⚠️ 策略异常: {strategy.name} - {strategy.error_message}")
                
                # 检查策略运行时间
                if strategy.start_time:
                    start_time = datetime.fromisoformat(strategy.start_time)
                    running_hours = (datetime.now() - start_time).total_seconds() / 3600
                    
                    if running_hours > 24:  # 运行超过24小时
                        logger.info(f"📊 策略 {strategy.name} 已运行 {running_hours:.1f} 小时")
            
        except Exception as e:
            logger.error(f"❌ 检查策略健康状态失败: {e}")
    
    async def shutdown(self):
        """关闭服务"""
        logger.info("🔄 正在关闭多策略管理服务...")
        
        self.is_monitoring = False
        
        if self.monitor_task:
            self.monitor_task.cancel()
            try:
                await self.monitor_task
            except asyncio.CancelledError:
                pass
        
        # 保存所有策略状态
        for strategy in self.running_strategies.values():
            self._save_strategy_config(strategy)

        # 保存最终的任务状态
        self._save_tasks_state()

        logger.info("✅ 多策略管理服务已关闭")

    async def _start_strategy_execution(self, strategy: 'StrategyInstance'):
        """启动策略的实际执行"""
        try:
            # 根据策略类型启动相应的执行引擎
            if strategy.strategy_type == 'buy_hold':
                await self._start_buy_hold_strategy(strategy)
            elif strategy.strategy_type == 'bollinger_bands':
                await self._start_bollinger_bands_strategy(strategy)
            else:
                # 使用通用的实盘交易引擎
                await self._start_generic_strategy(strategy)

        except Exception as e:
            logger.error(f"❌ 启动策略执行失败: {strategy.name} - {e}")
            strategy.status = 'error'
            strategy.error_message = str(e)

    async def _start_buy_hold_strategy(self, strategy: 'StrategyInstance'):
        """启动buy_hold策略"""
        try:
            from backend.live.simple_live_engine import live_engine

            # 构建实盘交易配置
            config = {
                'strategy_name': 'buy_hold',
                'initial_capital': strategy.config.get('initial_capital', 100000),
                'commission': 0.001,
                'stock_codes': strategy.config.get('stock_codes', []),
                'paper_trading': strategy.config.get('paper_trading', True),
                'strategy_params': {
                    'max_stocks': strategy.config.get('max_stocks', 3),
                    'position_size': strategy.config.get('position_size', 0.33)
                }
            }

            # 启动实盘交易引擎
            task_id = await live_engine.start_live_trading(config)

            # 保存任务ID到策略实例
            strategy.task_id = task_id

            logger.info(f"✅ buy_hold策略执行引擎启动成功: {task_id}")

        except Exception as e:
            logger.error(f"❌ 启动buy_hold策略执行失败: {e}")
            raise

    async def _start_bollinger_bands_strategy(self, strategy: 'StrategyInstance'):
        """启动bollinger_bands策略"""
        try:
            from backend.live.simple_live_engine import live_engine

            # 构建实盘交易配置
            config = {
                'strategy_name': 'bollinger_bands',
                'initial_capital': strategy.config.get('initial_capital', 100000),
                'commission': 0.001,
                'stock_codes': strategy.config.get('stock_codes', []),
                'paper_trading': strategy.config.get('paper_trading', True),
                'strategy_params': {
                    'bb_period': strategy.config.get('bb_period', 20),
                    'bb_std': strategy.config.get('bb_std', 2.0)
                }
            }

            # 启动实盘交易引擎
            task_id = await live_engine.start_live_trading(config)

            # 保存任务ID到策略实例
            strategy.task_id = task_id

            logger.info(f"✅ bollinger_bands策略执行引擎启动成功: {task_id}")

        except Exception as e:
            logger.error(f"❌ 启动bollinger_bands策略执行失败: {e}")
            raise

    async def _start_generic_strategy(self, strategy: 'StrategyInstance'):
        """启动通用策略"""
        try:
            from backend.live.simple_live_engine import live_engine

            # 构建实盘交易配置
            config = {
                'strategy_name': strategy.strategy_type,
                'initial_capital': strategy.config.get('initial_capital', 100000),
                'commission': 0.001,
                'stock_codes': strategy.config.get('stock_codes', []),
                'paper_trading': strategy.config.get('paper_trading', True),
                'strategy_params': strategy.config
            }

            # 启动实盘交易引擎
            task_id = await live_engine.start_live_trading(config)

            # 保存任务ID到策略实例
            strategy.task_id = task_id

            logger.info(f"✅ {strategy.strategy_type}策略执行引擎启动成功: {task_id}")

        except Exception as e:
            logger.error(f"❌ 启动{strategy.strategy_type}策略执行失败: {e}")
            raise


# 全局服务实例
multi_strategy_service = MultiStrategyService()
