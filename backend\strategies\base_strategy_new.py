"""
回测策略基类
基于backtrader的策略框架，支持配置驱动和真实数据回测
"""

import backtrader as bt
import pandas as pd
import numpy as np
from typing import Dict, Any, List, Optional
from abc import ABC, abstractmethod
import logging
from datetime import datetime

logger = logging.getLogger(__name__)


class ConfigurableStrategy(bt.Strategy):
    """
    可配置策略基类
    基于backtrader.Strategy，支持配置驱动和详细的交易记录
    """
    
    # 策略参数
    params = (
        ('config', {}),  # 策略配置
    )
    
    def __init__(self):
        super().__init__()
        
        # 策略基本信息
        self.strategy_name = "base_strategy"
        self.display_name = "基础策略"
        self.description = "策略基类"
        
        # 从参数中获取配置
        self.config = self.params.config
        self.strategy_params = self.config.get('parameters', {})
        
        # 交易记录
        self.detailed_trades = []
        self.daily_returns = []
        
        # 策略状态
        self.positions_count = 0
        self.trade_id_counter = 0
        
        logger.info(f"策略 {self.strategy_name} 初始化完成")

    def init_strategy(self):
        """初始化策略 - 子类可以重写"""
        pass
    
    @abstractmethod
    def generate_signals(self, data):
        """生成交易信号 - 子类必须实现"""
        return {'buy': False, 'sell': False}
    
    def get_param_value(self, param_name: str, default_value=None):
        """获取参数值并进行验证"""
        param_config = self.strategy_params.get(param_name, default_value)

        if isinstance(param_config, dict) and 'value' in param_config:
            value = param_config['value']
        else:
            value = param_config

        # 参数验证
        return self._validate_param(param_name, value, default_value)

    def _validate_param(self, param_name: str, value, default_value):
        """验证参数值"""
        if value is None:
            return default_value

        # 通用验证规则
        validations = {
            'period': lambda x: max(1, min(100, int(x))),  # 周期参数：1-100
            'std_dev': lambda x: max(0.5, min(5.0, float(x))),  # 标准差：0.5-5.0
            'max_positions': lambda x: max(1, min(20, int(x))),  # 最大持仓：1-20
            'position_size': lambda x: max(0.01, min(1.0, float(x))),  # 仓位大小：1%-100%
            'short_period': lambda x: max(1, min(50, int(x))),  # 短期周期：1-50
            'long_period': lambda x: max(5, min(200, int(x))),  # 长期周期：5-200
            'max_stocks': lambda x: max(1, min(10, int(x))),  # 最大股票数：1-10
        }

        if param_name in validations:
            try:
                validated_value = validations[param_name](value)
                if validated_value != value:
                    logger.warning(f"参数 {param_name} 从 {value} 调整为 {validated_value}")
                return validated_value
            except (ValueError, TypeError) as e:
                logger.error(f"参数 {param_name} 验证失败: {e}, 使用默认值 {default_value}")
                return default_value

        return value
    
    def next(self):
        """主要的策略逻辑"""
        try:
            # 记录每日收益
            self.record_daily_return()

            # 遍历所有数据源
            for i, data in enumerate(self.datas):
                # 更严格的数据检查
                if not self._is_data_ready(data):
                    continue

                try:
                    # 生成交易信号
                    signals = self.generate_signals(data)
                    if signals:
                        # 处理信号
                        self.process_signals(data, signals)
                except Exception as e:
                    stock_code = getattr(data, '_name', f'data_{i}')
                    logger.warning(f"处理数据源 {stock_code} 时出错: {e}")
                    continue

        except Exception as e:
            logger.error(f"策略执行异常: {e}")
            # 不重新抛出异常，避免策略崩溃

    def _is_data_ready(self, data) -> bool:
        """检查数据是否准备就绪"""
        try:
            # 基本检查
            if data is None:
                return False

            # 检查数据长度
            if len(data) < 2:
                return False

            # 检查必要的数据字段
            required_fields = ['open', 'high', 'low', 'close', 'volume']
            for field in required_fields:
                if not hasattr(data, field):
                    return False
                field_data = getattr(data, field)
                if len(field_data) == 0:
                    return False
                # 检查最新数据是否有效
                try:
                    latest_value = field_data[0]
                    if latest_value is None or (hasattr(latest_value, '__len__') and len(latest_value) == 0):
                        return False
                except (IndexError, TypeError):
                    return False

            # 检查datetime
            if hasattr(data, 'datetime'):
                try:
                    data.datetime.date(0)
                except (IndexError, TypeError):
                    return False

            return True

        except Exception as e:
            logger.warning(f"数据检查异常: {e}")
            return False

    def process_signals(self, data, signals: Dict[str, Any]):
        """处理交易信号"""
        if not signals:
            return

        try:
            stock_code = getattr(data, '_name', 'Unknown')

            # 安全获取价格和日期
            try:
                current_price = data.close[0] if len(data.close) > 0 else 0
                current_date = data.datetime.date(0).strftime('%Y-%m-%d') if hasattr(data, 'datetime') else 'Unknown'
            except (IndexError, TypeError, AttributeError) as e:
                logger.warning(f"{stock_code}: 获取价格或日期失败: {e}")
                return

            # 处理买入信号
            if signals.get('buy', False):
                self.process_buy_signal(data, signals)

            # 处理卖出信号
            if signals.get('sell', False):
                self.process_sell_signal(data, signals)

        except Exception as e:
            logger.error(f"处理交易信号异常: {e}")
            return
    
    def process_buy_signal(self, data, signals: Dict[str, Any]):
        """处理买入信号 - 优化资金管理"""
        stock_code = getattr(data, '_name', 'Unknown')

        # 检查是否已持仓
        position = self.getposition(data)
        if position.size > 0:
            logger.debug(f"{stock_code} 已持仓，跳过买入信号")
            return

        # 检查持仓数量限制
        max_positions = self.get_param_value('max_positions', 5)
        if self.positions_count >= max_positions:
            logger.debug(f"已达最大持仓数限制: {self.positions_count}/{max_positions}")
            return

        # 优化的资金管理
        cash = self.broker.get_cash()
        if cash <= 1000:  # 保留最少1000元现金
            logger.debug(f"现金不足，跳过买入: 可用现金={cash:.2f}")
            return

        # 计算买入数量 - 考虑手续费
        position_size = self.get_param_value('position_size', 0.1)
        current_price = data.close[0]

        # 预留手续费空间
        available_cash = cash * 0.95  # 预留5%作为手续费缓冲
        target_value = available_cash * position_size

        # 按手买入，确保有足够资金
        size = int(target_value / current_price / 100) * 100
        actual_cost = size * current_price * 1.0003  # 包含手续费

        if size >= 100 and actual_cost <= cash:  # 至少买入1手且资金充足
            order = self.buy(data=data, size=size)

            # 安全的格式化
            safe_current_price = current_price if current_price is not None else 0
            safe_actual_cost = actual_cost if actual_cost is not None else 0

            logger.info(f"买入信号: {stock_code} {size}股 @{safe_current_price:.2f}, 成本={safe_actual_cost:.2f}")

            # 记录详细交易信息
            self.record_trade(
                date=data.datetime.date(0).strftime('%Y-%m-%d'),
                time=data.datetime.time(0).strftime('%H:%M:%S'),
                stock_code=stock_code,
                action='buy',
                price=safe_current_price,
                quantity=size,
                reason=signals.get('reason', '买入信号'),
                order_ref=order.ref if order else None
            )
        else:
            # 安全的格式化
            safe_actual_cost = actual_cost if actual_cost is not None else 0
            safe_cash = cash if cash is not None else 0
            logger.debug(f"{stock_code} 资金不足或数量不够: size={size}, cost={safe_actual_cost:.2f}, cash={safe_cash:.2f}")
    
    def process_sell_signal(self, data, signals: Dict[str, Any]):
        """处理卖出信号"""
        stock_code = getattr(data, '_name', 'Unknown')
        
        # 检查是否持仓
        position = self.getposition(data)
        if position.size <= 0:
            return
        
        # 卖出全部持仓
        order = self.sell(data=data, size=position.size)

        # 安全的格式化
        safe_price = data.close[0] if data.close[0] is not None else 0
        safe_size = position.size if position.size is not None else 0

        logger.info(f"卖出信号: {stock_code} {safe_size}股 @{safe_price:.2f}")

        # 记录详细交易信息
        self.record_trade(
            date=data.datetime.date(0).strftime('%Y-%m-%d'),
            time=data.datetime.time(0).strftime('%H:%M:%S'),
            stock_code=stock_code,
            action='sell',
            price=safe_price,
            quantity=safe_size,
            reason=signals.get('reason', '卖出信号'),
            order_ref=order.ref if order else None
        )
    
    def record_trade(self, date: str, time: str, stock_code: str, action: str,
                    price: float, quantity: int, reason: str, order_ref=None):
        """记录详细交易信息"""
        logger.debug(f"record_trade called - price: {price}, quantity: {quantity}, action: {action}")

        self.trade_id_counter += 1

        # 安全处理可能的None值
        safe_price = price if price is not None else 0.0
        safe_quantity = quantity if quantity is not None else 0

        logger.debug(f"Processed trade data - safe_price: {safe_price}, safe_quantity: {safe_quantity}")

        amount = safe_quantity * safe_price
        commission = amount * 0.0003  # 默认手续费

        logger.debug(f"Calculated - amount: {amount}, commission: {commission}")
        
        trade_record = {
            'id': self.trade_id_counter,
            'date': date or '',
            'time': time or '',
            'stock_code': stock_code or '',
            'stock_name': self.get_stock_name(stock_code) if stock_code else '',
            'action': action or '',
            'action_name': '买入' if action == 'buy' else '卖出',
            'price': round(safe_price, 2),
            'quantity': safe_quantity,
            'amount': round(amount, 2),
            'commission': round(commission, 2),
            'reason': reason or '',
            'order_ref': order_ref,
            'portfolio_value': self.broker.getvalue() if self.broker else 0,
            'cash': self.broker.get_cash() if self.broker else 0
        }
        
        # 如果是买入，记录总成本
        if action == 'buy':
            trade_record['total_cost'] = round(amount + commission, 2)
            trade_record['cash_change'] = -trade_record['total_cost']
            trade_record['position_change'] = f"+{safe_quantity}股"

        # 如果是卖出，计算盈亏
        elif action == 'sell':
            net_amount = amount - commission
            trade_record['net_amount'] = round(net_amount, 2)
            trade_record['cash_change'] = net_amount
            trade_record['position_change'] = f"-{safe_quantity}股"
            
            # 查找对应的买入记录计算盈亏
            buy_trade = self.find_buy_trade(stock_code)
            if buy_trade:
                profit_loss = net_amount - buy_trade['total_cost']
                profit_loss_pct = profit_loss / buy_trade['total_cost'] * 100
                trade_record['profit_loss'] = round(profit_loss, 2)
                trade_record['profit_loss_pct'] = round(profit_loss_pct, 2)
                
                # 计算持有天数
                buy_date = datetime.strptime(buy_trade['date'], '%Y-%m-%d')
                sell_date = datetime.strptime(date, '%Y-%m-%d')
                trade_record['hold_days'] = (sell_date - buy_date).days
        
        self.detailed_trades.append(trade_record)
    
    def find_buy_trade(self, stock_code: str) -> Optional[Dict]:
        """查找对应的买入交易记录"""
        for trade in reversed(self.detailed_trades):
            if trade['stock_code'] == stock_code and trade['action'] == 'buy':
                return trade
        return None
    
    def record_daily_return(self):
        """记录每日收益"""
        current_date = self.datas[0].datetime.date(0).strftime('%Y-%m-%d')
        portfolio_value = self.broker.getvalue()
        cash = self.broker.get_cash()
        positions_value = portfolio_value - cash
        
        # 计算日收益率
        return_rate = 0.0
        if self.daily_returns:
            prev_value = self.daily_returns[-1]['value']
            return_rate = (portfolio_value - prev_value) / prev_value if prev_value > 0 else 0.0
        
        daily_return = {
            'date': current_date,
            'value': portfolio_value,
            'return': return_rate,
            'cash': cash,
            'positions_value': positions_value
        }
        
        # 避免重复记录同一天
        if not self.daily_returns or self.daily_returns[-1]['date'] != current_date:
            self.daily_returns.append(daily_return)
    
    def get_stock_name(self, stock_code: str) -> str:
        """获取股票名称"""
        name_mapping = {
            '000001.SZ': '平安银行',
            '000002.SZ': '万科A',
            '000858.SZ': '五粮液',
            '600000.SH': '浦发银行',
            '600036.SH': '招商银行',
            '600519.SH': '贵州茅台',
            '600887.SH': '伊利股份'
        }
        
        if stock_code in name_mapping:
            return name_mapping[stock_code]
        else:
            if stock_code.endswith('.SZ'):
                return f"深股{stock_code[:6]}"
            elif stock_code.endswith('.SH'):
                return f"沪股{stock_code[:6]}"
            else:
                return f"股票{stock_code}"
    
    def notify_order(self, order):
        """订单状态通知"""
        logger.debug(f"notify_order called - order.status: {order.status}")

        if order.status in [order.Submitted, order.Accepted]:
            return

        if order.status in [order.Completed]:
            logger.debug(f"Order completed - isbuy: {order.isbuy()}, executed: {order.executed}")

            if order.isbuy():
                self.positions_count += 1
                # 安全地获取订单执行信息
                logger.debug(f"Buy order executed data - size: {order.executed.size if order.executed else None}, price: {order.executed.price if order.executed else None}")

                size = order.executed.size if (order.executed and order.executed.size is not None) else 0
                price = order.executed.price if (order.executed and order.executed.price is not None) else 0

                logger.debug(f"Processed buy data - size: {size}, price: {price}")
                logger.info(f"买入完成: {size}股 @{price:.2f}")
            else:
                self.positions_count -= 1
                # 安全地获取订单执行信息
                logger.debug(f"Sell order executed data - size: {order.executed.size if order.executed else None}, price: {order.executed.price if order.executed else None}")

                size = order.executed.size if (order.executed and order.executed.size is not None) else 0
                price = order.executed.price if (order.executed and order.executed.price is not None) else 0

                logger.debug(f"Processed sell data - size: {size}, price: {price}")
                logger.info(f"卖出完成: {size}股 @{price:.2f}")

        elif order.status in [order.Canceled, order.Margin, order.Rejected]:
            logger.warning(f"订单失败: {order.status}")
        else:
            logger.debug(f"订单状态: {order.status}")
    
    def notify_trade(self, trade):
        """交易完成通知"""
        logger.debug(f"notify_trade called - trade.isclosed: {trade.isclosed}")

        if not trade.isclosed:
            return

        # 安全地获取交易数据，避免None值导致格式化错误
        logger.debug(f"Trade data - pnl: {trade.pnl}, pnlcomm: {trade.pnlcomm}, value: {trade.value}")

        profit = trade.pnl if trade.pnl is not None else 0
        pnlcomm = trade.pnlcomm if trade.pnlcomm is not None else 0
        value = trade.value if trade.value is not None else 0

        logger.debug(f"Processed data - profit: {profit}, pnlcomm: {pnlcomm}, value: {value}")

        # 计算盈亏比例，避免除零错误
        if value > 0:
            profit_rate = (pnlcomm / value * 100)
        else:
            profit_rate = 0

        logger.debug(f"Calculated profit_rate: {profit_rate}")
        logger.info(f"交易完成: 盈亏 {profit:.2f} ({profit_rate:.2f}%)")
    
    def get_analysis_data(self) -> Dict[str, Any]:
        """获取分析数据"""
        return {
            'strategy_name': self.strategy_name,
            'display_name': self.display_name,
            'description': self.description,
            'detailed_trades': self.detailed_trades,
            'daily_returns': self.daily_returns,
            'positions_count': self.positions_count,
            'config': self.config
        }


# 策略注册表
STRATEGY_REGISTRY: Dict[str, type] = {}


def register_strategy(strategy_class: type, strategy_name: str = None,
                     display_name: str = None, description: str = None):
    """注册策略类"""
    if not issubclass(strategy_class, ConfigurableStrategy):
        raise ValueError(f"策略类 {strategy_class.__name__} 必须继承自 ConfigurableStrategy")

    # 使用提供的信息或类名作为策略名称
    if strategy_name is None:
        strategy_name = strategy_class.__name__.lower().replace('strategy', '')

    # 存储策略信息
    STRATEGY_REGISTRY[strategy_name] = {
        'class': strategy_class,
        'display_name': display_name or strategy_class.__name__,
        'description': description or f"{strategy_class.__name__} 策略",
        'class_name': strategy_class.__name__
    }

    logger.info(f"注册策略: {strategy_name} -> {strategy_class.__name__}")


def get_strategy_class(strategy_name: str) -> Optional[type]:
    """根据策略名称获取策略类"""
    strategy_info = STRATEGY_REGISTRY.get(strategy_name)
    return strategy_info['class'] if strategy_info else None


def list_strategies() -> List[Dict[str, str]]:
    """列出所有已注册的策略"""
    strategies = []
    for strategy_name, strategy_info in STRATEGY_REGISTRY.items():
        strategies.append({
            'name': strategy_name,
            'display_name': strategy_info['display_name'],
            'description': strategy_info['description'],
            'class_name': strategy_info['class_name']
        })
    return strategies
