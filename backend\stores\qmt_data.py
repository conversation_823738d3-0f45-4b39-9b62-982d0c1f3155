#!/usr/bin/env python3
"""
QMT Data - 数据适配器
将QMT数据转换为backtrader格式
"""

import pandas as pd
import backtrader as bt
from datetime import datetime, timedelta
from typing import Optional

from backend.core.logger import get_logger

logger = get_logger(__name__)

class QMTData(bt.feeds.PandasData):
    """
    QMT数据适配器
    
    将QMT的实时/历史数据转换为backtrader可以使用的格式
    支持：
    1. 历史数据回测
    2. 实时数据交易
    3. 数据状态通知
    """
    
    params = (
        ('dataname', ''),           # 股票代码或DataFrame
        ('fromdate', None),         # 开始日期
        ('todate', None),           # 结束日期
        ('timeframe', bt.TimeFrame.Days),  # 时间框架
        ('compression', 1),         # 压缩比例
        ('historical', True),       # 是否历史数据
        ('live', False),           # 是否实时数据
        ('stock_code', ''),        # 股票代码（用于从QMT获取数据）
    )
    
    def __init__(self, store=None, **kwargs):
        """
        初始化QMT数据源

        Args:
            store: QMTStore实例
            **kwargs: 其他参数
        """
        self.store = store

        # 获取股票代码
        self.stock_code = kwargs.get('stock_code', '')

        # 状态标志
        self.live_mode = kwargs.get('live', False)
        self.historical_mode = kwargs.get('historical', True)

        logger.info(f"📊 初始化QMT数据源: {self.stock_code}")
        logger.info(f"   模式: {'实时' if self.live_mode else '历史'}")
        logger.info(f"   数据类型: {type(kwargs.get('dataname', 'None'))}")

        # 调用父类初始化（PandasData需要DataFrame作为dataname）
        super().__init__(**kwargs)

    def _load_historical_data_sync(self):
        """同步加载历史数据"""
        try:
            logger.info(f"📈 加载历史数据: {self.stock_code}")

            # 确定日期范围
            if hasattr(self, 'p') and self.p.fromdate:
                start_date = self.p.fromdate.strftime('%Y-%m-%d')
            else:
                # 默认获取最近1年数据
                start_date = (datetime.now() - timedelta(days=365)).strftime('%Y-%m-%d')

            if hasattr(self, 'p') and self.p.todate:
                end_date = self.p.todate.strftime('%Y-%m-%d')
            else:
                end_date = datetime.now().strftime('%Y-%m-%d')

            logger.info(f"   日期范围: {start_date} 到 {end_date}")

            # 从QMT获取数据
            if self.store:
                data_manager = self.store.get_data_manager()
                df = data_manager.get_stock_data(
                    stock_code=self.stock_code,
                    start_date=start_date,
                    end_date=end_date
                )

                if df is not None and len(df) > 0:
                    # 确保数据格式正确
                    return self._format_dataframe(df)
                else:
                    logger.warning(f"⚠️ 未获取到数据: {self.stock_code}")
                    return None
            else:
                logger.error("❌ QMTStore未初始化")
                return None

        except Exception as e:
            logger.error(f"❌ 加载历史数据失败: {e}")
            return None

    def start(self):
        """启动数据源"""
        logger.info(f"🔄 启动数据源: {self.stock_code}")

        # 调用父类的start方法
        super().start()

        # 如果是实时模式，启动数据更新
        if self.live_mode:
            self._start_live_updates()

        # 通知策略数据已连接
        self.put_notification(self.CONNECTED)

    def _start_live_updates(self):
        """启动实时数据更新 - QMT Data不支持实时更新，应使用QMT Live Data"""
        logger.warning(f"⚠️ QMT Data不支持实时更新，请使用QMT Live Data: {self.stock_code}")
        # QMT Data主要用于历史数据，实时数据应该使用QMT Live Data



    def stop(self):
        """停止数据源"""
        logger.info(f"🛑 停止数据源: {self.stock_code}")

        # 停止实时数据更新
        if hasattr(self, '_running'):
            self._running = False
            logger.info(f"📡 停止实时数据更新: {self.stock_code}")

        # 调用父类的stop方法
        super().stop()

    def islive(self):
        """是否为实时数据"""
        return self.live_mode

    
    def _format_dataframe(self, df):
        """格式化DataFrame为backtrader标准格式"""
        try:
            # 确保必要的列存在
            required_columns = ['open', 'high', 'low', 'close', 'volume']
            for col in required_columns:
                if col not in df.columns:
                    if col == 'volume':
                        df[col] = 0  # 成交量默认为0
                    else:
                        df[col] = df.get('close', 0)  # 其他价格列默认使用收盘价
            
            # 确保数据类型正确
            for col in ['open', 'high', 'low', 'close']:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            df['volume'] = pd.to_numeric(df['volume'], errors='coerce').fillna(0)
            
            # 处理缺失值
            df = df.fillna(method='ffill').fillna(method='bfill')
            
            # 确保索引是日期类型
            if not isinstance(df.index, pd.DatetimeIndex):
                df.index = pd.to_datetime(df.index)
            
            # 按日期排序
            df = df.sort_index()
            
            logger.debug(f"   格式化后数据形状: {df.shape}")
            return df
            
        except Exception as e:
            logger.error(f"❌ 数据格式化失败: {e}")
            return pd.DataFrame()

