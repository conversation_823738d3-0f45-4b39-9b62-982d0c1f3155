#!/usr/bin/env python3
"""
测试数据获取修复
"""

import sys
import os
import logging
from datetime import datetime, timedelta

# 添加项目路径
sys.path.append(os.path.dirname(__file__))

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

def test_single_stock_data():
    """测试单只股票数据获取"""
    logger.info("=== 测试单只股票数据获取 ===")
    
    from backend.data.data_manager import data_manager
    
    # 测试知名股票
    test_stocks = ['000001.SZ', '600000.SH', '603237.SH']
    
    for stock_code in test_stocks:
        logger.info(f"\n--- 测试 {stock_code} ---")
        
        # 测试1: 使用count方式
        logger.info("使用count方式获取数据...")
        df1 = data_manager.get_stock_data(stock_code=stock_code, count=100)
        logger.info(f"Count方式: {len(df1)}条记录")
        if len(df1) > 0:
            logger.info(f"日期范围: {df1.index[0]} 到 {df1.index[-1]}")
            logger.info(f"列名: {list(df1.columns)}")
        
        # 测试2: 使用日期范围
        logger.info("使用日期范围获取数据...")
        end_date = datetime.now().strftime('%Y-%m-%d')
        start_date = (datetime.now() - timedelta(days=90)).strftime('%Y-%m-%d')
        
        df2 = data_manager.get_stock_data(
            stock_code=stock_code,
            start_date=start_date,
            end_date=end_date,
            count=-1  # 禁用count，强制使用日期范围
        )
        logger.info(f"日期范围方式: {len(df2)}条记录")
        if len(df2) > 0:
            logger.info(f"日期范围: {df2.index[0]} 到 {df2.index[-1]}")
        
        # 比较两种方式
        if len(df1) > len(df2):
            logger.info(f"✅ Count方式获取更多数据: {len(df1)} vs {len(df2)}")
        else:
            logger.info(f"⚠️ 日期范围方式更好: {len(df2)} vs {len(df1)}")

def test_data_format():
    """测试数据格式"""
    logger.info("\n=== 测试数据格式 ===")
    
    from backend.data.data_manager import data_manager
    
    # 获取一只股票的数据
    df = data_manager.get_stock_data('000001.SZ', count=50)
    
    logger.info(f"数据形状: {df.shape}")
    logger.info(f"索引类型: {type(df.index)}")
    logger.info(f"列名: {list(df.columns)}")
    
    # 检查必要列
    required_cols = ['open', 'high', 'low', 'close', 'volume']
    missing_cols = [col for col in required_cols if col not in df.columns]
    
    if missing_cols:
        logger.error(f"❌ 缺少必要列: {missing_cols}")
    else:
        logger.info("✅ 包含所有必要列")
    
    # 检查数据质量
    logger.info(f"空值统计: {df.isnull().sum().to_dict()}")
    logger.info(f"价格范围: {df['close'].min():.2f} - {df['close'].max():.2f}")

def main():
    """主测试函数"""
    logger.info("🔍 开始数据获取修复测试")
    
    test_single_stock_data()
    test_data_format()
    
    logger.info("\n📊 测试完成")
    logger.info("如果看到足够的数据记录，说明修复成功")

if __name__ == "__main__":
    main()
